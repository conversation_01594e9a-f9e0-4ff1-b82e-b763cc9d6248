#!/usr/bin/env python3
"""
数据库连接测试脚本
检查数据库配置和连接状态
"""

import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_models import db_manager, Base
from sqlalchemy import text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    try:
        logger.info("🔍 开始数据库连接测试...")
        
        # 1. 测试基本连接
        logger.info("1️⃣ 测试基本连接...")
        if db_manager.test_connection():
            logger.info("✅ 数据库连接成功")
        else:
            logger.error("❌ 数据库连接失败")
            return False
        
        # 2. 获取数据库信息
        logger.info("2️⃣ 获取数据库信息...")
        db_info = db_manager.get_database_info()
        if 'error' in db_info:
            logger.error(f"❌ 获取数据库信息失败: {db_info['error']}")
        else:
            logger.info(f"✅ 数据库信息: {db_info}")
        
        # 3. 测试表创建
        logger.info("3️⃣ 测试表创建...")
        try:
            db_manager.create_all_tables()
            logger.info("✅ 表创建成功")
        except Exception as e:
            logger.error(f"❌ 表创建失败: {e}")
            return False
        
        # 4. 测试基本CRUD操作
        logger.info("4️⃣ 测试基本CRUD操作...")
        if test_crud_operations():
            logger.info("✅ CRUD操作测试成功")
        else:
            logger.error("❌ CRUD操作测试失败")
            return False
        
        # 5. 检查表结构
        logger.info("5️⃣ 检查表结构...")
        if check_table_structure():
            logger.info("✅ 表结构检查通过")
        else:
            logger.error("❌ 表结构检查失败")
            return False
        
        logger.info("🎉 数据库连接测试全部通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接测试异常: {e}")
        return False

def test_crud_operations():
    """测试基本CRUD操作"""
    try:
        from database_models import SystemConfig
        
        with db_manager.get_session_context() as session:
            # 创建测试配置
            test_config = SystemConfig(
                config_key='test_key',
                config_value='test_value',
                config_type='STRING',
                description='测试配置'
            )
            
            # 插入
            session.add(test_config)
            session.flush()  # 获取ID
            
            # 查询
            found_config = session.query(SystemConfig).filter(
                SystemConfig.config_key == 'test_key'
            ).first()
            
            if not found_config:
                logger.error("❌ 查询测试配置失败")
                return False
            
            # 更新
            found_config.config_value = 'updated_value'
            session.flush()
            
            # 验证更新
            updated_config = session.query(SystemConfig).filter(
                SystemConfig.config_key == 'test_key'
            ).first()
            
            if updated_config.config_value != 'updated_value':
                logger.error("❌ 更新测试配置失败")
                return False
            
            # 删除
            session.delete(updated_config)
            session.flush()
            
            # 验证删除
            deleted_config = session.query(SystemConfig).filter(
                SystemConfig.config_key == 'test_key'
            ).first()
            
            if deleted_config:
                logger.error("❌ 删除测试配置失败")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ CRUD操作测试异常: {e}")
        return False

def check_table_structure():
    """检查表结构"""
    try:
        # 检查关键表是否存在
        key_tables = [
            'stock_info',
            'daily_market',
            'minute_1_market',
            'financial_data',
            'factor_config',
            'stock_scores',
            'trading_signals',
            'portfolios',
            'backtest_strategies',
            'system_config'
        ]
        
        with db_manager.get_session_context() as session:
            for table_name in key_tables:
                try:
                    # 检查表是否存在
                    if db_manager.engine.url.drivername == 'sqlite':
                        sql = text("""
                            SELECT COUNT(*) as count
                            FROM sqlite_master
                            WHERE type='table'
                            AND name = :table_name
                        """)
                    else:
                        sql = text("""
                            SELECT COUNT(*) as count
                            FROM information_schema.tables
                            WHERE table_name = :table_name
                        """)

                    result = session.execute(sql, {"table_name": table_name}).fetchone()

                    if result and result.count > 0:
                        logger.info(f"✅ 表 {table_name} 存在")
                    else:
                        logger.warning(f"⚠️ 表 {table_name} 不存在")
                        
                except Exception as e:
                    logger.error(f"❌ 检查表 {table_name} 失败: {e}")
                    return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查表结构异常: {e}")
        return False

def show_database_status():
    """显示数据库状态"""
    try:
        logger.info("📊 数据库状态信息:")
        
        # 数据库基本信息
        db_info = db_manager.get_database_info()
        logger.info(f"  数据库类型: {db_info.get('type', 'Unknown')}")
        logger.info(f"  连接URL: {db_info.get('url', 'Unknown')}")
        
        if 'version' in db_info:
            logger.info(f"  版本: {db_info['version']}")
        
        # 表统计信息
        with db_manager.get_session_context() as session:
            if db_manager.engine.url.drivername == 'sqlite':
                sql = text("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")
            else:
                sql = text("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'public'")
            
            result = session.execute(sql).fetchone()
            table_count = result.count if result else 0
            logger.info(f"  表数量: {table_count}")
        
        # 连接池信息
        if hasattr(db_manager.engine.pool, 'size'):
            logger.info(f"  连接池大小: {db_manager.engine.pool.size()}")
            logger.info(f"  活跃连接: {db_manager.engine.pool.checkedout()}")
        
    except Exception as e:
        logger.error(f"❌ 获取数据库状态失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 开始数据库配置检查...")
    
    # 显示数据库状态
    show_database_status()
    
    # 测试数据库连接
    if test_database_connection():
        logger.info("🎉 数据库配置检查完成，一切正常！")
        return 0
    else:
        logger.error("❌ 数据库配置检查失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
