#!/usr/bin/env python3
"""
VeighNa量化交易系统 - 生产级版本
完整的前后台分离架构，集成ADATA数据源，支持5000+股票智能选股
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import sqlite3
import logging
import threading
import time
import asyncio
from datetime import datetime, timedelta
import json
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from data_sources.adata_integration import ADataIntegration
from engines.high_performance_engine import HighPerformanceEngine
from algorithms.intelligent_stock_selector import IntelligentStockSelector, SelectionCriteria

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vnpy_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

class ProductionVeighNaSystem:
    """生产级VeighNa量化交易系统"""
    
    def __init__(self):
        self.db_path = 'vnpy_trading.db'
        
        # 初始化核心组件
        self.adata_integration = ADataIntegration(self.db_path)
        self.performance_engine = HighPerformanceEngine(self.db_path)
        self.stock_selector = IntelligentStockSelector(self.db_path)
        
        # 系统状态
        self.system_status = {
            'initialization': 'pending',
            'data_collection': 'pending',
            'stock_selection': 'pending',
            'performance_engine': 'pending',
            'web_server': 'running',
            'system_health': 'initializing'
        }
        
        # 系统统计
        self.system_stats = {
            'total_stocks': 0,
            'selected_stocks': 0,
            'data_update_time': None,
            'selection_update_time': None,
            'system_start_time': datetime.now()
        }
        
        # 初始化标志
        self.is_initialized = False
        self.initialization_thread = None
        
        self.setup_routes()
        logger.info("✅ 生产级VeighNa系统初始化完成")
    
    def setup_routes(self):
        """设置API路由"""
        
        @app.route('/')
        def index():
            """主页"""
            return render_template('index.html')
        
        @app.route('/api/system/initialize', methods=['POST'])
        def initialize_system():
            """初始化系统API"""
            try:
                if self.is_initialized:
                    return jsonify({
                        'success': True,
                        'message': '系统已初始化',
                        'status': self.system_status
                    })
                
                if self.initialization_thread and self.initialization_thread.is_alive():
                    return jsonify({
                        'success': True,
                        'message': '系统正在初始化中...',
                        'status': self.system_status
                    })
                
                # 启动初始化线程
                self.initialization_thread = threading.Thread(
                    target=self._initialize_system_async,
                    daemon=True
                )
                self.initialization_thread.start()
                
                return jsonify({
                    'success': True,
                    'message': '系统初始化已启动',
                    'status': self.system_status
                })
                
            except Exception as e:
                logger.error(f"初始化系统失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/system/status')
        def get_system_status():
            """获取系统状态API"""
            try:
                # 更新统计信息
                if self.is_initialized:
                    self._update_system_stats()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'system_status': self.system_status,
                        'system_stats': self.system_stats,
                        'is_initialized': self.is_initialized,
                        'performance_metrics': self.performance_engine.get_performance_metrics() if self.is_initialized else {},
                        'adata_status': self.adata_integration.get_collection_status() if self.is_initialized else {},
                        'timestamp': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/stocks/realtime')
        def get_realtime_stocks():
            """获取实时股票数据API"""
            try:
                if not self.is_initialized:
                    return jsonify({
                        'success': False,
                        'error': '系统未初始化，请先初始化系统'
                    }), 400
                
                # 获取实时股票数据
                limit = request.args.get('limit', 50, type=int)
                stocks_data = self.adata_integration.get_complete_stock_data(limit=limit)
                
                # 模拟实时价格变动
                import random
                for stock in stocks_data:
                    if stock.get('close_price'):
                        fluctuation = random.uniform(-0.02, 0.02)
                        new_price = stock['close_price'] * (1 + fluctuation)
                        change = new_price - stock['close_price']
                        change_pct = (change / stock['close_price']) * 100
                        
                        stock['current_price'] = round(new_price, 2)
                        stock['change'] = round(change, 2)
                        stock['change_pct'] = round(change_pct, 2)
                
                return jsonify({
                    'success': True,
                    'data': {
                        'stocks': stocks_data,
                        'total_count': len(stocks_data),
                        'update_time': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                logger.error(f"获取实时股票数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/stocks/selection')
        def get_stock_selection():
            """获取智能选股结果API"""
            try:
                if not self.is_initialized:
                    return jsonify({
                        'success': False,
                        'error': '系统未初始化，请先初始化系统'
                    }), 400
                
                # 从数据库获取最新选股结果
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT sr.symbol, si.name, sr.total_score, sr.technical_score,
                           sr.fundamental_score, sr.market_score, sr.rank,
                           sr.recommendation, sr.reason, sr.selection_date
                    FROM stock_selection_results sr
                    INNER JOIN stock_basic_info si ON sr.symbol = si.symbol
                    ORDER BY sr.rank
                    LIMIT 50
                """)
                
                selected_stocks = []
                for row in cursor.fetchall():
                    selected_stocks.append({
                        'symbol': row[0],
                        'name': row[1],
                        'total_score': row[2],
                        'technical_score': row[3],
                        'fundamental_score': row[4],
                        'market_score': row[5],
                        'rank': row[6],
                        'recommendation': row[7],
                        'reason': row[8],
                        'selection_date': row[9]
                    })
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'selected_stocks': selected_stocks,
                        'total_selected': len(selected_stocks),
                        'selection_criteria': {
                            'min_score': 70,
                            'max_selections': 50,
                            'factors': ['技术面', '基本面', '市场面', '动量面', '质量面']
                        },
                        'update_time': self.system_stats.get('selection_update_time')
                    }
                })
                
            except Exception as e:
                logger.error(f"获取选股结果失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/stocks/selection/run', methods=['POST'])
        def run_stock_selection():
            """运行智能选股API"""
            try:
                if not self.is_initialized:
                    return jsonify({
                        'success': False,
                        'error': '系统未初始化，请先初始化系统'
                    }), 400
                
                # 获取选股参数
                params = request.json or {}
                
                # 创建选股标准
                criteria = SelectionCriteria(
                    min_market_cap=params.get('min_market_cap', 2000000000),
                    max_pe_ratio=params.get('max_pe_ratio', 50),
                    min_roe=params.get('min_roe', 5),
                    min_volume=params.get('min_volume', 1000000),
                    min_score=params.get('min_score', 70),
                    max_selections=params.get('max_selections', 50)
                )
                
                # 在后台线程运行选股
                def run_selection():
                    try:
                        self.system_status['stock_selection'] = 'running'
                        selected_stocks = self.stock_selector.run_intelligent_selection(criteria)
                        self.system_stats['selected_stocks'] = len(selected_stocks)
                        self.system_stats['selection_update_time'] = datetime.now().isoformat()
                        self.system_status['stock_selection'] = 'completed'
                        logger.info(f"✅ 智能选股完成，选出 {len(selected_stocks)} 只股票")
                    except Exception as e:
                        logger.error(f"智能选股失败: {e}")
                        self.system_status['stock_selection'] = 'error'
                
                selection_thread = threading.Thread(target=run_selection, daemon=True)
                selection_thread.start()
                
                return jsonify({
                    'success': True,
                    'message': '智能选股已启动',
                    'criteria': {
                        'min_market_cap': criteria.min_market_cap,
                        'max_pe_ratio': criteria.max_pe_ratio,
                        'min_roe': criteria.min_roe,
                        'min_score': criteria.min_score,
                        'max_selections': criteria.max_selections
                    }
                })
                
            except Exception as e:
                logger.error(f"运行智能选股失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/data/update', methods=['POST'])
        def update_data():
            """更新数据API"""
            try:
                if not self.is_initialized:
                    return jsonify({
                        'success': False,
                        'error': '系统未初始化，请先初始化系统'
                    }), 400
                
                # 在后台线程更新数据
                def update_realtime_data():
                    try:
                        self.system_status['data_collection'] = 'running'
                        updated_count = self.adata_integration.update_realtime_data()
                        self.system_stats['data_update_time'] = datetime.now().isoformat()
                        self.system_status['data_collection'] = 'completed'
                        logger.info(f"✅ 实时数据更新完成，更新 {updated_count} 只股票")
                    except Exception as e:
                        logger.error(f"数据更新失败: {e}")
                        self.system_status['data_collection'] = 'error'
                
                update_thread = threading.Thread(target=update_realtime_data, daemon=True)
                update_thread.start()
                
                return jsonify({
                    'success': True,
                    'message': '数据更新已启动'
                })
                
            except Exception as e:
                logger.error(f"更新数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
    
    def _initialize_system_async(self):
        """异步初始化系统"""
        try:
            logger.info("🚀 开始系统初始化...")
            self.system_status['initialization'] = 'running'
            
            # 1. 启动高性能引擎
            logger.info("⚡ 启动高性能引擎...")
            self.performance_engine.start_engine()
            self.system_status['performance_engine'] = 'running'
            
            # 2. 初始化ADATA数据
            logger.info("📊 初始化ADATA数据...")
            self.system_status['data_collection'] = 'running'
            
            success = self.adata_integration.initialize_all_data()
            
            if success:
                self.system_status['data_collection'] = 'completed'
                self.system_stats['total_stocks'] = self.adata_integration.get_stock_count()
                logger.info(f"✅ 数据初始化完成，共 {self.system_stats['total_stocks']} 只股票")
            else:
                self.system_status['data_collection'] = 'error'
                logger.error("❌ 数据初始化失败")
                return
            
            # 3. 运行初始选股
            logger.info("🎯 运行初始智能选股...")
            self.system_status['stock_selection'] = 'running'
            
            criteria = SelectionCriteria(
                min_market_cap=2000000000,
                max_pe_ratio=50,
                min_roe=5,
                min_volume=1000000,
                min_score=70,
                max_selections=50
            )
            
            selected_stocks = self.stock_selector.run_intelligent_selection(criteria)
            
            if selected_stocks:
                self.system_status['stock_selection'] = 'completed'
                self.system_stats['selected_stocks'] = len(selected_stocks)
                self.system_stats['selection_update_time'] = datetime.now().isoformat()
                logger.info(f"✅ 初始选股完成，选出 {len(selected_stocks)} 只股票")
            else:
                self.system_status['stock_selection'] = 'error'
                logger.error("❌ 初始选股失败")
            
            # 4. 系统初始化完成
            self.is_initialized = True
            self.system_status['initialization'] = 'completed'
            self.system_status['system_health'] = 'healthy'
            self.system_stats['data_update_time'] = datetime.now().isoformat()
            
            logger.info("🎉 系统初始化完成！")
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            self.system_status['initialization'] = 'error'
            self.system_status['system_health'] = 'error'
    
    def _update_system_stats(self):
        """更新系统统计信息"""
        try:
            # 更新股票总数
            if hasattr(self.adata_integration, 'stock_list'):
                self.system_stats['total_stocks'] = len(self.adata_integration.stock_list)
            
            # 更新运行时间
            uptime = datetime.now() - self.system_stats['system_start_time']
            self.system_stats['uptime_seconds'] = int(uptime.total_seconds())
            
        except Exception as e:
            logger.error(f"更新系统统计失败: {e}")
    
    def start_server(self, host='0.0.0.0', port=8080, debug=False):
        """启动服务器"""
        logger.info(f"🌐 启动生产级VeighNa服务器: http://{host}:{port}")
        logger.info("📋 API接口列表:")
        logger.info("  POST /api/system/initialize - 初始化系统")
        logger.info("  GET  /api/system/status - 获取系统状态")
        logger.info("  GET  /api/stocks/realtime - 获取实时股票数据")
        logger.info("  GET  /api/stocks/selection - 获取智能选股结果")
        logger.info("  POST /api/stocks/selection/run - 运行智能选股")
        logger.info("  POST /api/data/update - 更新数据")
        
        app.run(host=host, port=port, debug=debug, threaded=True)

# 全局系统实例
production_system = ProductionVeighNaSystem()

def main():
    """主函数"""
    print("🚀 VeighNa量化交易系统 - 生产级版本")
    print("=" * 80)
    print("📊 ADATA数据源: 全部A股股票数据")
    print("🧠 智能选股: 5000+股票多因子筛选")
    print("⚡ 高性能引擎: 多线程并行处理")
    print("🎯 机器学习: 评分增强算法")
    print("📱 前后台分离: RESTful API架构")
    print("🔄 实时更新: 自动数据采集")
    print("=" * 80)
    print("🌐 Web界面: http://localhost:8080")
    print("🔧 系统初始化: POST /api/system/initialize")
    print("📊 系统状态: GET /api/system/status")
    print("📈 实时数据: GET /api/stocks/realtime")
    print("🎯 智能选股: GET /api/stocks/selection")
    print("=" * 80)
    print("⚠️  首次启动需要初始化数据，请耐心等待...")
    print("💡 建议先调用 /api/system/initialize 初始化系统")
    print("=" * 80)
    
    try:
        production_system.start_server()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断，系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")

if __name__ == "__main__":
    main()
