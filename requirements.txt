# 量化交易系统依赖包

# 数据库相关
SQLAlchemy>=2.0.0
PyMySQL>=1.0.0
psycopg2-binary>=2.9.0  # PostgreSQL支持
alembic>=1.12.0  # 数据库迁移

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# ADATA数据接口
akshare>=1.12.0
tushare>=1.2.0
yfinance>=0.2.0

# 技术分析
talib>=0.4.0
ta>=0.10.0

# VeighNa相关
vnpy>=3.8.0
vnpy-ctp>=6.6.0
vnpy-ctastrategy>=1.0.0
vnpy-ctabacktester>=1.0.0
vnpy-portfoliostrategy>=1.0.0
vnpy-portfoliobacktester>=1.0.0

# Web框架
Flask>=2.3.3
Flask-CORS>=4.0.0
FastAPI>=0.100.0
uvicorn>=0.23.0
Jinja2>=3.1.0
python-multipart>=0.0.6

# 任务调度
celery>=5.3.0
redis>=4.6.0
APScheduler>=3.10.0

# 数据可视化
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# 机器学习
scikit-learn>=1.3.0
xgboost>=1.7.0
lightgbm>=4.0.0

# 工具库
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=2.0.0
loguru>=0.7.0
click>=8.1.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 开发工具
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# 配置管理
PyYAML>=6.0
configparser>=5.3.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 加密和安全
cryptography>=41.0.0
bcrypt>=4.0.0

# 监控和日志
prometheus-client>=0.17.0
structlog>=23.1.0

# 缓存
cachetools>=5.3.0

# 并发处理
asyncio>=3.4.3
aiofiles>=23.2.0
httpx>=0.24.0
