#!/usr/bin/env python3
"""
数据采集层测试脚本
测试ADATA客户端和数据采集功能
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from data_collection import AdataClient, MarketDataCollector, CollectionScheduler, DataValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_adata_client():
    """测试ADATA客户端"""
    logger.info("🧪 测试ADATA客户端...")
    
    client = AdataClient()
    
    # 测试获取股票列表
    logger.info("📋 测试获取股票列表...")
    stock_list = client.get_stock_list()
    if stock_list is not None:
        logger.info(f"✅ 获取股票列表成功: {len(stock_list)} 只股票")
        print(stock_list.head())
    else:
        logger.error("❌ 获取股票列表失败")
    
    # 测试获取K线数据
    logger.info("📈 测试获取K线数据...")
    kline_data = client.get_kline_data(
        symbol="000001",
        period="daily",
        start_date="20240101",
        end_date="20241231"
    )
    if kline_data is not None:
        logger.info(f"✅ 获取K线数据成功: {len(kline_data)} 条记录")
        print(kline_data.head())
    else:
        logger.error("❌ 获取K线数据失败")
    
    # 测试获取实时数据
    logger.info("⚡ 测试获取实时数据...")
    realtime_data = client.get_realtime_data(['000001', '000002', '600000'])
    if realtime_data is not None:
        logger.info(f"✅ 获取实时数据成功: {len(realtime_data)} 只股票")
        print(realtime_data.head())
    else:
        logger.error("❌ 获取实时数据失败")

async def test_data_validator():
    """测试数据验证器"""
    logger.info("🔍 测试数据验证器...")
    
    validator = DataValidator()
    client = AdataClient()
    
    # 获取测试数据
    stock_list = client.get_stock_list()
    if stock_list is not None:
        # 测试股票列表验证
        is_valid = validator.validate_stock_list(stock_list)
        logger.info(f"📋 股票列表验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        # 生成数据质量报告
        quality_report = validator.get_data_quality_report(stock_list)
        logger.info(f"📊 数据质量评分: {quality_report.get('quality_score', 0):.2f}")
    
    # 测试K线数据验证
    kline_data = client.get_kline_data("000001", "daily")
    if kline_data is not None:
        is_valid = validator.validate_kline_data(kline_data)
        logger.info(f"📈 K线数据验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")

async def test_market_collector():
    """测试市场数据采集器"""
    logger.info("📊 测试市场数据采集器...")
    
    collector = MarketDataCollector()
    
    # 测试采集股票列表
    logger.info("📋 测试采集股票列表...")
    success = await collector.collect_stock_list()
    logger.info(f"股票列表采集结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 测试采集K线数据
    logger.info("📈 测试采集K线数据...")
    test_symbols = ['000001', '000002', '600000']
    test_periods = ['daily']
    
    result = await collector.collect_kline_data(
        symbols=test_symbols,
        periods=test_periods
    )
    
    logger.info(f"K线数据采集结果: 成功{result.get('total_success', 0)}, 失败{result.get('total_failed', 0)}")
    
    # 测试采集实时数据
    logger.info("⚡ 测试采集实时数据...")
    success = await collector.collect_realtime_data(test_symbols)
    logger.info(f"实时数据采集结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 获取采集状态
    status = await collector.get_collection_status()
    logger.info(f"📊 采集状态: {status}")

async def test_collection_scheduler():
    """测试采集调度器"""
    logger.info("⏰ 测试采集调度器...")
    
    scheduler = CollectionScheduler()
    
    # 获取任务状态
    status = scheduler.get_task_status()
    logger.info(f"📋 调度器状态: 总任务数{status.get('total_tasks', 0)}")
    
    # 添加测试任务
    success = scheduler.add_task(
        task_id="test_task",
        name="测试任务",
        task_type="kline",
        schedule="*/1 * * * *",  # 每分钟
        target_symbols=['000001'],
        parameters={"periods": ["daily"]}
    )
    logger.info(f"添加测试任务: {'✅ 成功' if success else '❌ 失败'}")
    
    # 立即运行测试任务
    if success:
        logger.info("🚀 立即运行测试任务...")
        run_success = await scheduler.run_task_now("test_task")
        logger.info(f"运行测试任务: {'✅ 成功' if run_success else '❌ 失败'}")
        
        # 等待任务完成
        await asyncio.sleep(5)
        
        # 获取任务状态
        task_status = scheduler.get_task_status("test_task")
        logger.info(f"📊 测试任务状态: {task_status.get('status', 'unknown')}")
        
        # 移除测试任务
        scheduler.remove_task("test_task")

async def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🧪 开始数据采集层综合测试...")
    logger.info("=" * 60)
    
    try:
        # 1. 测试ADATA客户端
        await test_adata_client()
        logger.info("=" * 60)
        
        # 2. 测试数据验证器
        await test_data_validator()
        logger.info("=" * 60)
        
        # 3. 测试市场数据采集器
        await test_market_collector()
        logger.info("=" * 60)
        
        # 4. 测试采集调度器
        await test_collection_scheduler()
        logger.info("=" * 60)
        
        logger.info("🎉 数据采集层综合测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")

async def main():
    """主函数"""
    print("🚀 数据采集层测试")
    print("=" * 60)
    print("基于ADATA接口文档的数据采集系统测试")
    print("=" * 60)
    
    await run_comprehensive_test()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
