#!/usr/bin/env python3
"""
VeighNa量化交易系统投资组合管理
实现持仓管理、权重分配、风险控制、绩效分析等功能
"""

import numpy as np
import pandas as pd
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import random

from core_data_engine import DatabaseManager, PortfolioPosition, data_engine

logger = logging.getLogger(__name__)

@dataclass
class PortfolioConfig:
    """投资组合配置"""
    total_capital: float = 1000000.0  # 总资金100万
    max_position_weight: float = 0.15  # 单只股票最大权重15%
    min_position_weight: float = 0.02  # 单只股票最小权重2%
    max_positions: int = 20  # 最大持仓数量
    risk_tolerance: str = "moderate"  # 风险偏好: conservative, moderate, aggressive
    rebalance_threshold: float = 0.05  # 再平衡阈值5%
    stop_loss_pct: float = -0.08  # 止损线-8%
    take_profit_pct: float = 0.20  # 止盈线+20%

@dataclass
class RiskMetrics:
    """风险指标"""
    portfolio_value: float
    total_pnl: float
    total_pnl_pct: float
    daily_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    var_95: float  # 95%置信度VaR
    beta: float
    
    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class PerformanceMetrics:
    """绩效指标"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    
    def to_dict(self) -> Dict:
        return asdict(self)

class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: PortfolioConfig):
        self.config = config
        self.position_limits = {}
        self.risk_alerts = []
    
    def calculate_position_size(self, symbol: str, price: float, signal_score: float) -> int:
        """计算建仓数量"""
        # 基于信号强度和风险偏好计算权重
        base_weight = self.config.min_position_weight
        
        # 根据信号评分调整权重
        score_multiplier = min(2.0, signal_score / 50.0)  # 评分越高权重越大
        target_weight = min(self.config.max_position_weight, base_weight * score_multiplier)
        
        # 计算目标金额和股数
        target_amount = self.config.total_capital * target_weight
        quantity = int(target_amount / price / 100) * 100  # 按手数取整
        
        return max(100, quantity)  # 最少1手
    
    def check_risk_limits(self, positions: List[PortfolioPosition]) -> List[str]:
        """检查风险限制"""
        alerts = []
        
        total_value = sum(pos.market_value for pos in positions)
        
        for position in positions:
            # 检查单只股票权重
            weight = position.market_value / total_value if total_value > 0 else 0
            if weight > self.config.max_position_weight:
                alerts.append(f"{position.symbol} 权重{weight:.1%}超过限制{self.config.max_position_weight:.1%}")
            
            # 检查止损
            if position.pnl_pct < self.config.stop_loss_pct:
                alerts.append(f"{position.symbol} 亏损{position.pnl_pct:.1%}触及止损线")
            
            # 检查止盈
            if position.pnl_pct > self.config.take_profit_pct:
                alerts.append(f"{position.symbol} 盈利{position.pnl_pct:.1%}可考虑止盈")
        
        # 检查持仓数量
        if len(positions) > self.config.max_positions:
            alerts.append(f"持仓数量{len(positions)}超过限制{self.config.max_positions}")
        
        return alerts
    
    def calculate_var(self, returns: List[float], confidence: float = 0.95) -> float:
        """计算风险价值VaR"""
        if not returns:
            return 0.0
        
        returns_array = np.array(returns)
        return np.percentile(returns_array, (1 - confidence) * 100)

class PerformanceAnalyzer:
    """绩效分析器"""
    
    def __init__(self):
        self.benchmark_return = 0.08  # 基准年化收益率8%
        self.risk_free_rate = 0.03   # 无风险利率3%
    
    def calculate_returns(self, positions: List[PortfolioPosition]) -> List[float]:
        """计算收益率序列"""
        # 模拟历史收益率数据
        returns = []
        for _ in range(252):  # 一年交易日
            daily_return = random.normalvariate(0.0008, 0.02)  # 日均收益0.08%，波动2%
            returns.append(daily_return)
        return returns
    
    def calculate_performance_metrics(self, positions: List[PortfolioPosition]) -> PerformanceMetrics:
        """计算绩效指标"""
        if not positions:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)
        
        # 计算总收益
        total_pnl = sum(pos.pnl for pos in positions)
        total_value = sum(pos.market_value for pos in positions)
        total_cost = total_value - total_pnl
        total_return = total_pnl / total_cost if total_cost > 0 else 0
        
        # 获取收益率序列
        returns = self.calculate_returns(positions)
        returns_array = np.array(returns)
        
        # 计算各项指标
        annualized_return = np.mean(returns_array) * 252
        volatility = np.std(returns_array) * np.sqrt(252)
        sharpe_ratio = (annualized_return - self.risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        cumulative_returns = np.cumprod(1 + returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        # 卡尔玛比率
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 胜率和盈亏比
        win_rate = len([r for r in returns if r > 0]) / len(returns) if returns else 0
        positive_returns = [r for r in returns if r > 0]
        negative_returns = [r for r in returns if r < 0]
        
        avg_win = np.mean(positive_returns) if positive_returns else 0
        avg_loss = abs(np.mean(negative_returns)) if negative_returns else 1
        profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor
        )

class PortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, db_manager: DatabaseManager, config: PortfolioConfig = None):
        self.db_manager = db_manager
        self.config = config or PortfolioConfig()
        self.risk_manager = RiskManager(self.config)
        self.performance_analyzer = PerformanceAnalyzer()
        self.is_running = False
        self.management_thread = None
        
        # 初始化模拟持仓
        self._init_sample_positions()
        
        logger.info("✅ 投资组合管理器初始化完成")
    
    def _init_sample_positions(self):
        """初始化示例持仓数据"""
        sample_positions = [
            PortfolioPosition(
                symbol="000001",
                name="平安银行",
                quantity=10000,
                avg_price=12.50,
                current_price=12.85,
                market_value=128500,
                pnl=3500,
                pnl_pct=2.80,
                weight=0.128
            ),
            PortfolioPosition(
                symbol="600519",
                name="贵州茅台",
                quantity=100,
                avg_price=1650.00,
                current_price=1680.00,
                market_value=168000,
                pnl=3000,
                pnl_pct=1.82,
                weight=0.168
            ),
            PortfolioPosition(
                symbol="000858",
                name="五粮液",
                quantity=800,
                avg_price=125.00,
                current_price=128.50,
                market_value=102800,
                pnl=2800,
                pnl_pct=2.80,
                weight=0.103
            ),
            PortfolioPosition(
                symbol="600036",
                name="招商银行",
                quantity=2000,
                avg_price=44.00,
                current_price=45.20,
                market_value=90400,
                pnl=2400,
                pnl_pct=2.73,
                weight=0.090
            ),
            PortfolioPosition(
                symbol="002415",
                name="海康威视",
                quantity=3000,
                avg_price=27.50,
                current_price=28.90,
                market_value=86700,
                pnl=4200,
                pnl_pct=5.09,
                weight=0.087
            )
        ]
        
        # 保存到数据库
        for position in sample_positions:
            self.db_manager.save_portfolio_position(position)
    
    def update_positions(self):
        """更新持仓数据"""
        try:
            # 获取最新股票价格
            latest_stocks = self.db_manager.get_latest_stock_data()
            stock_prices = {stock['symbol']: stock['price'] for stock in latest_stocks}
            
            # 获取当前持仓
            positions = self.db_manager.get_portfolio_positions()
            
            total_value = 0
            updated_positions = []
            
            for pos_data in positions:
                symbol = pos_data['symbol']
                current_price = stock_prices.get(symbol, pos_data['current_price'])
                
                # 更新市值和盈亏
                market_value = pos_data['quantity'] * current_price
                pnl = market_value - (pos_data['quantity'] * pos_data['avg_price'])
                pnl_pct = (pnl / (pos_data['quantity'] * pos_data['avg_price'])) * 100
                
                total_value += market_value
                
                updated_position = PortfolioPosition(
                    symbol=symbol,
                    name=pos_data['name'],
                    quantity=pos_data['quantity'],
                    avg_price=pos_data['avg_price'],
                    current_price=current_price,
                    market_value=market_value,
                    pnl=pnl,
                    pnl_pct=pnl_pct,
                    weight=0  # 稍后计算
                )
                updated_positions.append(updated_position)
            
            # 计算权重
            for position in updated_positions:
                position.weight = position.market_value / total_value if total_value > 0 else 0
                self.db_manager.save_portfolio_position(position)
            
            logger.info(f"📊 更新了 {len(updated_positions)} 个持仓")
            
        except Exception as e:
            logger.error(f"更新持仓失败: {e}")
    
    def calculate_risk_metrics(self) -> RiskMetrics:
        """计算风险指标"""
        positions = self.db_manager.get_portfolio_positions()
        
        if not positions:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 1.0)
        
        # 计算组合指标
        portfolio_value = sum(pos['market_value'] for pos in positions)
        total_pnl = sum(pos['pnl'] for pos in positions)
        total_cost = portfolio_value - total_pnl
        total_pnl_pct = (total_pnl / total_cost * 100) if total_cost > 0 else 0
        
        # 模拟其他风险指标
        daily_return = random.normalvariate(0.0008, 0.02)
        volatility = 0.18  # 年化波动率18%
        sharpe_ratio = (0.12 - 0.03) / volatility  # 假设年化收益12%
        max_drawdown = -0.08  # 最大回撤8%
        var_95 = -0.025  # 95%置信度VaR
        beta = 1.1  # 相对市场的贝塔值
        
        return RiskMetrics(
            portfolio_value=portfolio_value,
            total_pnl=total_pnl,
            total_pnl_pct=total_pnl_pct,
            daily_return=daily_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            var_95=var_95,
            beta=beta
        )
    
    def get_rebalance_suggestions(self) -> List[Dict]:
        """获取再平衡建议"""
        positions_data = self.db_manager.get_portfolio_positions()
        positions = [PortfolioPosition(**pos) for pos in positions_data]
        
        suggestions = []
        
        for position in positions:
            # 检查权重偏离
            target_weight = 1.0 / len(positions)  # 等权重目标
            weight_deviation = abs(position.weight - target_weight)
            
            if weight_deviation > self.config.rebalance_threshold:
                action = "减仓" if position.weight > target_weight else "加仓"
                suggestions.append({
                    "symbol": position.symbol,
                    "name": position.name,
                    "current_weight": position.weight,
                    "target_weight": target_weight,
                    "action": action,
                    "deviation": weight_deviation,
                    "priority": "高" if weight_deviation > 0.1 else "中"
                })
        
        return sorted(suggestions, key=lambda x: x['deviation'], reverse=True)
    
    def start_management(self):
        """启动组合管理"""
        self.is_running = True
        logger.info("🚀 启动投资组合管理")
        
        def management_loop():
            while self.is_running:
                try:
                    self.update_positions()
                    time.sleep(60)  # 每分钟更新一次
                except Exception as e:
                    logger.error(f"组合管理循环错误: {e}")
                    time.sleep(120)
        
        self.management_thread = threading.Thread(target=management_loop, daemon=True)
        self.management_thread.start()
    
    def stop_management(self):
        """停止组合管理"""
        self.is_running = False
        logger.info("⏹️ 停止投资组合管理")
    
    def get_portfolio_summary(self) -> Dict:
        """获取投资组合摘要"""
        positions_data = self.db_manager.get_portfolio_positions()
        positions = [PortfolioPosition(**pos) for pos in positions_data]
        
        risk_metrics = self.calculate_risk_metrics()
        performance_metrics = self.performance_analyzer.calculate_performance_metrics(positions)
        risk_alerts = self.risk_manager.check_risk_limits(positions)
        rebalance_suggestions = self.get_rebalance_suggestions()
        
        return {
            "positions": [pos.to_dict() for pos in positions],
            "risk_metrics": risk_metrics.to_dict(),
            "performance_metrics": performance_metrics.to_dict(),
            "risk_alerts": risk_alerts,
            "rebalance_suggestions": rebalance_suggestions,
            "portfolio_config": {
                "total_capital": self.config.total_capital,
                "max_positions": self.config.max_positions,
                "risk_tolerance": self.config.risk_tolerance
            },
            "updated_at": datetime.now().isoformat()
        }

# 全局投资组合管理器实例
portfolio_manager = PortfolioManager(data_engine.db_manager)

def main():
    """主函数 - 测试投资组合管理"""
    try:
        print("🚀 启动VeighNa投资组合管理测试")
        
        # 启动数据引擎
        data_engine.start()
        
        # 启动组合管理
        portfolio_manager.start_management()
        
        # 等待数据更新
        import time
        time.sleep(65)
        
        # 获取组合摘要
        summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n📊 投资组合摘要:")
        print(f"  组合价值: ¥{summary['risk_metrics']['portfolio_value']:,.2f}")
        print(f"  总盈亏: ¥{summary['risk_metrics']['total_pnl']:,.2f} ({summary['risk_metrics']['total_pnl_pct']:+.2f}%)")
        print(f"  持仓数量: {len(summary['positions'])}")
        print(f"  夏普比率: {summary['risk_metrics']['sharpe_ratio']:.3f}")
        print(f"  最大回撤: {summary['risk_metrics']['max_drawdown']:.1%}")
        
        print(f"\n📈 持仓明细:")
        for pos in summary['positions'][:5]:
            print(f"  {pos['symbol']} {pos['name']}: "
                  f"¥{pos['market_value']:,.0f} ({pos['weight']:.1%}) "
                  f"盈亏{pos['pnl_pct']:+.2f}%")
        
        print(f"\n⚠️ 风险提醒:")
        for alert in summary['risk_alerts'][:3]:
            print(f"  • {alert}")
        
        print(f"\n🔄 再平衡建议:")
        for suggestion in summary['rebalance_suggestions'][:3]:
            print(f"  • {suggestion['symbol']} {suggestion['name']}: {suggestion['action']} "
                  f"(当前{suggestion['current_weight']:.1%} → 目标{suggestion['target_weight']:.1%})")
        
        # 停止管理
        portfolio_manager.stop_management()
        data_engine.stop()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        portfolio_manager.stop_management()
        data_engine.stop()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        portfolio_manager.stop_management()
        data_engine.stop()

if __name__ == "__main__":
    main()
