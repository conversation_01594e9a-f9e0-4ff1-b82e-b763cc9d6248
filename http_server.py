#!/usr/bin/env python3
"""
使用Python内置HTTP服务器的Web应用
"""

import http.server
import socketserver
import json
import urllib.parse
from datetime import datetime
import threading
import webbrowser
import time

class QuantTradingHandler(http.server.BaseHTTPRequestHandler):
    """量化交易系统HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        try:
            if self.path == '/' or self.path == '/index.html':
                self.send_html_response(self.get_main_page())
            elif self.path == '/api/status':
                self.send_json_response(self.get_system_status())
            elif self.path == '/api/stocks':
                self.send_json_response(self.get_stock_data())
            elif self.path == '/api/signals':
                self.send_json_response(self.get_trading_signals())
            elif self.path == '/api/portfolio':
                self.send_json_response(self.get_portfolio_data())
            else:
                self.send_404()
        except Exception as e:
            self.send_error_response(str(e))
    
    def send_html_response(self, content):
        """发送HTML响应"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(content.encode('utf-8'))
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(b'<h1>404 - Page Not Found</h1>')
    
    def send_error_response(self, error_msg):
        """发送错误响应"""
        self.send_response(500)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        error_data = {"error": error_msg, "timestamp": datetime.now().isoformat()}
        self.wfile.write(json.dumps(error_data, ensure_ascii=False).encode('utf-8'))
    
    def get_main_page(self):
        """获取主页HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易系统 V2.0</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d); 
            color: #ffffff; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            text-align: center; 
            padding: 40px 20px; 
            background: linear-gradient(135deg, #2d2d2d, #3d3d3d); 
            border-radius: 15px; 
            margin-bottom: 30px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        .header h1 { 
            font-size: 2.5em; 
            margin-bottom: 10px; 
            background: linear-gradient(45deg, #4CAF50, #45a049);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .status-bar {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .api-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px;
        }
        .api-card { 
            background: linear-gradient(135deg, #3d3d3d, #4d4d4d); 
            padding: 25px; 
            border-radius: 12px; 
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
        }
        .api-card:hover { 
            border-color: #4CAF50;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(76, 175, 80, 0.2);
        }
        .api-card h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .api-card .description {
            color: #ccc;
            margin-bottom: 15px;
        }
        .api-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .api-button:hover {
            background: #45a049;
        }
        .data-display {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .data-display pre {
            color: #4CAF50;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #888;
            border-top: 1px solid #444;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化交易系统 V2.0</h1>
            <p>专业量化交易系统 Web 控制台</p>
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>系统运行正常</span>
                </div>
                <div id="current-time">""" + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</div>
            </div>
        </div>
        
        <div class="api-grid">
            <div class="api-card" onclick="loadData('/api/status')">
                <h3>🔧 系统状态</h3>
                <p class="description">查看系统运行状态和模块健康度</p>
                <button class="api-button">获取状态</button>
            </div>
            
            <div class="api-card" onclick="loadData('/api/stocks')">
                <h3>📈 股票数据</h3>
                <p class="description">获取实时股票价格和评分信息</p>
                <button class="api-button">查看股票</button>
            </div>
            
            <div class="api-card" onclick="loadData('/api/signals')">
                <h3>📊 交易信号</h3>
                <p class="description">获取买入卖出信号和策略建议</p>
                <button class="api-button">查看信号</button>
            </div>
            
            <div class="api-card" onclick="loadData('/api/portfolio')">
                <h3>💼 投资组合</h3>
                <p class="description">查看当前持仓和组合表现</p>
                <button class="api-button">查看组合</button>
            </div>
        </div>
        
        <div id="data-container" class="data-display" style="display: none;">
            <h3>数据响应:</h3>
            <pre id="data-content"></pre>
        </div>
        
        <div class="footer">
            <p>量化交易系统 V2.0 | 基于9层架构的智能交易平台</p>
            <p>🌐 Web服务器: Python HTTP Server | 📍 端口: 8080</p>
        </div>
    </div>
    
    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = 
                now.toLocaleString('zh-CN');
        }
        setInterval(updateTime, 1000);
        
        // 加载数据
        async function loadData(endpoint) {
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                document.getElementById('data-content').textContent = 
                    JSON.stringify(data, null, 2);
                document.getElementById('data-container').style.display = 'block';
                
                // 滚动到数据区域
                document.getElementById('data-container').scrollIntoView({
                    behavior: 'smooth'
                });
            } catch (error) {
                document.getElementById('data-content').textContent = 
                    '错误: ' + error.message;
                document.getElementById('data-container').style.display = 'block';
            }
        }
    </script>
</body>
</html>
        """
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "modules": {
                "web_server": True,
                "database": True,
                "data_collection": True,
                "stock_selector": True,
                "backtesting_engine": True,
                "trading_strategies": True,
                "portfolio_management": True
            },
            "health_score": 100.0,
            "uptime": "运行中",
            "memory_usage": "正常",
            "cpu_usage": "正常"
        }
    
    def get_stock_data(self):
        """获取股票数据"""
        return {
            "stocks": [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "price": 12.85,
                    "change": 0.38,
                    "change_pct": 3.05,
                    "volume": 1250000,
                    "score": 88.5,
                    "signal": "BUY"
                },
                {
                    "symbol": "600519",
                    "name": "贵州茅台",
                    "price": 1680.00,
                    "change": 20.50,
                    "change_pct": 1.23,
                    "volume": 890000,
                    "score": 92.3,
                    "signal": "BUY"
                },
                {
                    "symbol": "000858",
                    "name": "五粮液",
                    "price": 128.50,
                    "change": -2.30,
                    "change_pct": -1.76,
                    "volume": 1100000,
                    "score": 75.6,
                    "signal": "OBSERVE"
                }
            ],
            "total_count": 3,
            "updated_at": datetime.now().isoformat()
        }
    
    def get_trading_signals(self):
        """获取交易信号"""
        return {
            "buy_signals": [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "price": 12.85,
                    "score": 88.5,
                    "reason": "技术指标多头排列，成交量放大",
                    "confidence": 0.85,
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "sell_signals": [
                {
                    "symbol": "002304",
                    "name": "洋河股份",
                    "price": 98.50,
                    "score": 42.1,
                    "reason": "技术指标转弱，跌破支撑位",
                    "confidence": 0.78,
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "total_signals": 2,
            "updated_at": datetime.now().isoformat()
        }
    
    def get_portfolio_data(self):
        """获取投资组合数据"""
        return {
            "total_value": 1000000.0,
            "cash": 200000.0,
            "positions": [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "quantity": 10000,
                    "avg_price": 12.50,
                    "current_price": 12.85,
                    "market_value": 128500.0,
                    "pnl": 3500.0,
                    "pnl_pct": 2.8,
                    "weight": 12.85
                },
                {
                    "symbol": "600519",
                    "name": "贵州茅台",
                    "quantity": 300,
                    "avg_price": 1650.00,
                    "current_price": 1680.00,
                    "market_value": 504000.0,
                    "pnl": 9000.0,
                    "pnl_pct": 1.8,
                    "weight": 50.4
                }
            ],
            "performance": {
                "total_pnl": 12500.0,
                "total_pnl_pct": 1.25,
                "today_pnl": 2800.0,
                "today_pnl_pct": 0.28
            },
            "updated_at": datetime.now().isoformat()
        }
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server(port=8080):
    """启动HTTP服务器"""
    try:
        with socketserver.TCPServer(("", port), QuantTradingHandler) as httpd:
            print("🌐 量化交易系统 Web 服务器启动成功!")
            print("=" * 60)
            print(f"📍 访问地址: http://localhost:{port}")
            print(f"📍 本地访问: http://127.0.0.1:{port}")
            print("=" * 60)
            print("🎯 可用接口:")
            print(f"  - 主页: http://localhost:{port}/")
            print(f"  - 系统状态: http://localhost:{port}/api/status")
            print(f"  - 股票数据: http://localhost:{port}/api/stocks")
            print(f"  - 交易信号: http://localhost:{port}/api/signals")
            print(f"  - 投资组合: http://localhost:{port}/api/portfolio")
            print("=" * 60)
            print("按 Ctrl+C 停止服务器")
            print()
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n⚠️ 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    start_server(8080)
