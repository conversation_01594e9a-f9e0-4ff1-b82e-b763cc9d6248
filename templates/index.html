<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeighNa量化交易系统 - 专业版</title>
    <style>
        /* VeighNa专业风格样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #2b2b2b;
            color: #ffffff;
            overflow: hidden;
        }

        /* 顶部标题栏 */
        .vnpy-header {
            height: 60px;
            background: #3c3c3c;
            border-bottom: 1px solid #555555;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .system-title {
            font-size: 18px;
            font-weight: bold;
            color: #4a9eff;
        }

        .version-info {
            font-size: 12px;
            color: #999;
            background: #555;
            padding: 2px 8px;
            border-radius: 3px;
        }

        .header-center {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #52c41a;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 12px;
        }

        .current-time {
            color: #4a9eff;
            font-weight: bold;
        }

        .system-info span {
            margin-left: 15px;
        }

        /* 主要内容区域 */
        .vnpy-main {
            display: flex;
            height: calc(100vh - 90px);
        }

        /* 左侧导航栏 */
        .vnpy-sidebar {
            width: 250px;
            background: #3c3c3c;
            border-right: 1px solid #555555;
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section h3 {
            padding: 15px 20px 10px;
            font-size: 14px;
            color: #999;
            border-bottom: 1px solid #555;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background: #4a4a4a;
        }

        .nav-item.active {
            background: #4a9eff20;
            border-left-color: #4a9eff;
            color: #4a9eff;
        }

        .nav-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .nav-text {
            font-size: 14px;
        }

        /* 中央工作区 */
        .vnpy-content {
            flex: 1;
            background: #2b2b2b;
            overflow-y: auto;
        }

        .content-module {
            display: none;
            padding: 20px;
        }

        .content-module.active {
            display: block;
        }

        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #555;
        }

        .module-header h2 {
            color: #4a9eff;
            font-size: 20px;
        }

        .module-controls {
            display: flex;
            gap: 10px;
        }

        .vnpy-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .vnpy-btn.primary {
            background: #4a9eff;
            color: white;
        }

        .vnpy-btn.primary:hover {
            background: #3a8eef;
        }

        .vnpy-btn {
            background: #555;
            color: white;
        }

        .vnpy-btn:hover {
            background: #666;
        }

        /* 信息卡片 */
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #3c3c3c;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #555;
            text-align: center;
        }

        .card-title {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #4a9eff;
            margin-bottom: 5px;
        }

        .card-change {
            font-size: 12px;
        }

        .card-change.positive {
            color: #52c41a;
        }

        .card-change.negative {
            color: #ff4d4f;
        }

        /* 数据表格 */
        .data-table-container {
            background: #3c3c3c;
            border-radius: 8px;
            border: 1px solid #555;
            overflow: hidden;
        }

        .vnpy-table {
            width: 100%;
            border-collapse: collapse;
        }

        .vnpy-table th {
            background: #4a4a4a;
            padding: 12px 8px;
            text-align: left;
            font-size: 12px;
            color: #ccc;
            border-bottom: 1px solid #555;
        }

        .vnpy-table td {
            padding: 10px 8px;
            font-size: 12px;
            border-bottom: 1px solid #555;
        }

        .vnpy-table tbody tr:hover {
            background: #4a4a4a;
        }

        .price-up {
            color: #52c41a;
        }

        .price-down {
            color: #ff4d4f;
        }

        /* 右侧信息面板 */
        .vnpy-sidebar-right {
            width: 300px;
            background: #3c3c3c;
            border-left: 1px solid #555;
            padding: 20px;
            overflow-y: auto;
        }

        .info-panel {
            background: #2b2b2b;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-panel h3 {
            color: #4a9eff;
            font-size: 14px;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #555;
        }

        .status-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .status-value.running {
            color: #52c41a;
        }

        .status-value.stopped {
            color: #ff4d4f;
        }

        /* 底部状态栏 */
        .vnpy-footer {
            height: 30px;
            background: #3c3c3c;
            border-top: 1px solid #555;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 11px;
            color: #999;
        }

        /* 加载动画 */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #555;
            border-top: 4px solid #4a9eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-placeholder {
            text-align: center;
            padding: 40px;
            color: #999;
        }
    </style>
</head>
<body>
    <!-- 顶部标题栏 -->
    <div class="vnpy-header">
        <div class="header-left">
            <h1 class="system-title">🚀 VeighNa量化交易系统</h1>
            <span class="version-info">v2.0.1 Professional</span>
        </div>
        <div class="header-center">
            <div class="connection-status">
                <span class="status-dot"></span>
                <span class="status-text">系统运行中</span>
            </div>
        </div>
        <div class="header-right">
            <div class="current-time" id="current-time"></div>
            <div class="system-info">
                <span>CPU: <span id="cpu-usage">--</span>%</span>
                <span>内存: <span id="memory-usage">--</span>%</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="vnpy-main">
        <!-- 左侧导航栏 -->
        <div class="vnpy-sidebar">
            <div class="nav-section">
                <h3>📊 数据监控</h3>
                <ul class="nav-menu">
                    <li class="nav-item active" data-module="realtime-monitor">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">实时行情</span>
                    </li>
                    <li class="nav-item" data-module="market-overview">
                        <span class="nav-icon">🌐</span>
                        <span class="nav-text">市场概览</span>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3>🧠 智能分析</h3>
                <ul class="nav-menu">
                    <li class="nav-item" data-module="stock-selection">
                        <span class="nav-icon">🎯</span>
                        <span class="nav-text">智能选股</span>
                    </li>
                    <li class="nav-item" data-module="technical-analysis">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">技术分析</span>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3>🚀 VeighNa回测</h3>
                <ul class="nav-menu">
                    <li class="nav-item" data-module="backtest-engine">
                        <span class="nav-icon">⚡</span>
                        <span class="nav-text">回测引擎</span>
                    </li>
                    <li class="nav-item" data-module="strategy-optimization">
                        <span class="nav-icon">🔧</span>
                        <span class="nav-text">策略优化</span>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3>📈 交易管理</h3>
                <ul class="nav-menu">
                    <li class="nav-item" data-module="trading-signals">
                        <span class="nav-icon">🔔</span>
                        <span class="nav-text">交易信号</span>
                    </li>
                    <li class="nav-item" data-module="portfolio-management">
                        <span class="nav-icon">💼</span>
                        <span class="nav-text">投资组合</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 中央工作区 -->
        <div class="vnpy-content">
            <!-- 实时行情模块 -->
            <div class="content-module active" id="realtime-monitor">
                <div class="module-header">
                    <h2>📈 实时行情监控</h2>
                    <div class="module-controls">
                        <button class="vnpy-btn primary" onclick="refreshMarketData()">刷新数据</button>
                        <button class="vnpy-btn" onclick="exportData()">导出数据</button>
                    </div>
                </div>

                <div class="module-content">
                    <!-- 市场概览卡片 -->
                    <div class="info-cards">
                        <div class="info-card">
                            <div class="card-title">上证指数</div>
                            <div class="card-value" id="sh-index">--</div>
                            <div class="card-change" id="sh-change">--</div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">深证成指</div>
                            <div class="card-value" id="sz-index">--</div>
                            <div class="card-change" id="sz-change">--</div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">创业板指</div>
                            <div class="card-value" id="cy-index">--</div>
                            <div class="card-change" id="cy-change">--</div>
                        </div>
                        <div class="info-card">
                            <div class="card-title">跟踪股票</div>
                            <div class="card-value" id="tracked-stocks">--</div>
                            <div class="card-change">只股票</div>
                        </div>
                    </div>

                    <!-- 股票列表 -->
                    <div class="data-table-container">
                        <table class="vnpy-table" id="stock-table">
                            <thead>
                                <tr>
                                    <th>代码</th>
                                    <th>名称</th>
                                    <th>最新价</th>
                                    <th>涨跌</th>
                                    <th>涨跌幅</th>
                                    <th>成交量(万手)</th>
                                    <th>成交额(亿)</th>
                                    <th>换手率</th>
                                    <th>市盈率</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="stock-table-body">
                                <!-- 动态加载股票数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 智能选股模块 -->
            <div class="content-module" id="stock-selection">
                <div class="module-header">
                    <h2>🎯 智能选股系统</h2>
                    <div class="module-controls">
                        <button class="vnpy-btn primary" onclick="runStockSelection()">开始选股</button>
                        <button class="vnpy-btn" onclick="viewSelectionHistory()">历史记录</button>
                    </div>
                </div>

                <div class="module-content">
                    <!-- 选股结果 -->
                    <div class="selection-results" id="selection-results">
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                            <p>点击"开始选股"运行智能选股算法</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VeighNa回测模块 -->
            <div class="content-module" id="backtest-engine">
                <div class="module-header">
                    <h2>⚡ VeighNa回测引擎</h2>
                    <div class="module-controls">
                        <button class="vnpy-btn primary" onclick="startBacktest()">开始回测</button>
                        <button class="vnpy-btn" onclick="loadStrategy()">加载策略</button>
                    </div>
                </div>

                <div class="module-content">
                    <!-- 回测结果展示区域 -->
                    <div class="backtest-results" id="backtest-results">
                        <div class="result-placeholder">
                            <p>配置回测参数后点击"开始回测"查看结果</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易信号模块 -->
            <div class="content-module" id="trading-signals">
                <div class="module-header">
                    <h2>🔔 交易信号监控</h2>
                    <div class="module-controls">
                        <button class="vnpy-btn primary" onclick="refreshSignals()">刷新信号</button>
                        <button class="vnpy-btn" onclick="configSignals()">信号配置</button>
                    </div>
                </div>

                <div class="module-content">
                    <!-- 信号列表 -->
                    <div class="signals-container" id="signals-container">
                        <!-- 动态加载信号数据 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="vnpy-sidebar-right">
            <!-- 系统状态 -->
            <div class="info-panel">
                <h3>⚙️ 系统状态</h3>
                <div class="status-items">
                    <div class="status-item">
                        <span class="status-label">数据采集:</span>
                        <span class="status-value running" id="data-collection-status">运行中</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">选股引擎:</span>
                        <span class="status-value" id="selection-engine-status">就绪</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">回测引擎:</span>
                        <span class="status-value" id="backtest-engine-status">就绪</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">信号跟踪:</span>
                        <span class="status-value running" id="signal-tracking-status">运行中</span>
                    </div>
                </div>
            </div>

            <!-- 最新消息 -->
            <div class="info-panel">
                <h3>📢 系统消息</h3>
                <div class="message-list" id="system-messages">
                    <!-- 动态加载系统消息 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="vnpy-footer">
        <div class="footer-left">
            <span>系统运行时间: <span id="system-uptime">--</span></span>
            <span>最后更新: <span id="last-update">--</span></span>
        </div>
        <div class="footer-right">
            <span>VeighNa量化交易系统 v2.0.1 Professional</span>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全局变量
        let currentModule = 'realtime-monitor';
        let updateInterval;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            setupNavigation();
            startDataUpdates();
        });

        // 系统初始化
        function initializeSystem() {
            updateTime();
            setInterval(updateTime, 1000);

            // 初始化系统状态
            updateSystemStatus();

            // 加载初始数据
            setTimeout(() => {
                loadRealtimeData();
            }, 1000);
        }

        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }

        // 设置导航
        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const module = this.getAttribute('data-module');
                    switchModule(module);
                });
            });
        }

        // 切换模块
        function switchModule(moduleId) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-module="${moduleId}"]`).classList.add('active');

            // 更新内容区域
            document.querySelectorAll('.content-module').forEach(module => {
                module.classList.remove('active');
            });

            const targetModule = document.getElementById(moduleId);
            if (targetModule) {
                targetModule.classList.add('active');
                currentModule = moduleId;

                // 根据模块加载相应数据
                loadModuleData(moduleId);
            }
        }

        // 加载模块数据
        function loadModuleData(moduleId) {
            switch(moduleId) {
                case 'realtime-monitor':
                    loadRealtimeData();
                    break;
                case 'stock-selection':
                    loadSelectionData();
                    break;
                case 'backtest-engine':
                    loadBacktestData();
                    break;
                case 'trading-signals':
                    loadSignalData();
                    break;
            }
        }

        // 开始数据更新
        function startDataUpdates() {
            // 每30秒更新一次实时数据
            updateInterval = setInterval(() => {
                if (currentModule === 'realtime-monitor') {
                    loadRealtimeData();
                }
                updateSystemStatus();
            }, 30000);
        }

        // 加载实时数据
        async function loadRealtimeData() {
            try {
                const response = await fetch('/api/realtime-data');
                const data = await response.json();

                if (data.success) {
                    updateMarketIndices(data.data);
                    updateStockTable(data.data.stocks);
                    updateTrackedStocks(data.data.stocks.length);
                }
            } catch (error) {
                console.error('加载实时数据失败:', error);
            }
        }

        // 更新市场指数
        function updateMarketIndices(data) {
            // 模拟市场指数数据
            const indices = {
                sh: { value: 3245.67, change: 15.23, changePct: 0.47 },
                sz: { value: 10876.45, change: -23.45, changePct: -0.22 },
                cy: { value: 2234.56, change: 8.90, changePct: 0.40 }
            };

            updateIndexCard('sh-index', 'sh-change', indices.sh);
            updateIndexCard('sz-index', 'sz-change', indices.sz);
            updateIndexCard('cy-index', 'cy-change', indices.cy);
        }

        // 更新指数卡片
        function updateIndexCard(valueId, changeId, data) {
            document.getElementById(valueId).textContent = data.value.toFixed(2);
            const changeElement = document.getElementById(changeId);
            const changeText = `${data.change > 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePct > 0 ? '+' : ''}${data.changePct.toFixed(2)}%)`;
            changeElement.textContent = changeText;
            changeElement.className = `card-change ${data.change > 0 ? 'positive' : 'negative'}`;
        }

        // 更新股票表格
        function updateStockTable(stocks) {
            const tbody = document.getElementById('stock-table-body');
            tbody.innerHTML = '';

            stocks.slice(0, 20).forEach(stock => {
                const row = document.createElement('tr');
                const changeClass = stock.change > 0 ? 'price-up' : stock.change < 0 ? 'price-down' : '';
                const changeSign = stock.change > 0 ? '+' : '';

                row.innerHTML = `
                    <td>${stock.symbol}</td>
                    <td>${stock.name}</td>
                    <td class="${changeClass}">¥${stock.price}</td>
                    <td class="${changeClass}">${changeSign}${stock.change}</td>
                    <td class="${changeClass}">${changeSign}${stock.change_pct}%</td>
                    <td>${stock.volume || '--'}</td>
                    <td>${stock.turnover || '--'}</td>
                    <td>${stock.turnover_rate || '--'}%</td>
                    <td>${stock.pe_ratio || '--'}</td>
                    <td><button class="vnpy-btn" onclick="viewStockDetail('${stock.symbol}')">详情</button></td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新跟踪股票数量
        function updateTrackedStocks(count) {
            document.getElementById('tracked-stocks').textContent = count;
        }

        // 更新系统状态
        function updateSystemStatus() {
            // 模拟系统状态
            const statuses = ['data-collection-status', 'selection-engine-status', 'backtest-engine-status', 'signal-tracking-status'];
            statuses.forEach(statusId => {
                const element = document.getElementById(statusId);
                if (element) {
                    element.className = 'status-value running';
                }
            });

            // 更新系统运行时间
            document.getElementById('system-uptime').textContent = '2天15小时32分钟';
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }

        // 刷新市场数据
        function refreshMarketData() {
            loadRealtimeData();
        }

        // 导出数据
        function exportData() {
            alert('数据导出功能开发中...');
        }

        // 运行智能选股
        function runStockSelection() {
            alert('智能选股功能开发中...');
        }

        // 查看选股历史
        function viewSelectionHistory() {
            alert('选股历史功能开发中...');
        }

        // 开始回测
        function startBacktest() {
            alert('回测功能开发中...');
        }

        // 加载策略
        function loadStrategy() {
            alert('策略加载功能开发中...');
        }

        // 刷新信号
        function refreshSignals() {
            alert('信号刷新功能开发中...');
        }

        // 配置信号
        function configSignals() {
            alert('信号配置功能开发中...');
        }

        // 查看股票详情
        function viewStockDetail(symbol) {
            alert(`查看股票 ${symbol} 详情功能开发中...`);
        }

        // 加载选股数据
        function loadSelectionData() {
            // 实现选股数据加载
        }

        // 加载回测数据
        function loadBacktestData() {
            // 实现回测数据加载
        }

        // 加载信号数据
        function loadSignalData() {
            // 实现信号数据加载
        }
    </script>
</body>
</html>
