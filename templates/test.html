<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeighNa系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #1a1f2e;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .api-test {
            background: #252b3d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #3a4158;
        }
        .btn {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0099cc;
        }
        .result {
            background: #1a1f2e;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            border: 1px solid #3a4158;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #00ff88; }
        .error { color: #ff4757; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 VeighNa量化交易系统</h1>
            <h2>系统测试页面</h2>
            <p>当前时间: <span id="current-time"></span></p>
        </div>

        <div class="api-test">
            <h3>📊 实时数据API测试</h3>
            <button class="btn" onclick="testAPI('/api/realtime-data', 'realtime-result')">测试实时数据</button>
            <div id="realtime-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>🧠 智能选股API测试</h3>
            <button class="btn" onclick="testAPI('/api/stock-selection', 'selection-result')">测试智能选股</button>
            <div id="selection-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>📈 交易信号API测试</h3>
            <button class="btn" onclick="testAPI('/api/trading-signals', 'signals-result')">测试交易信号</button>
            <div id="signals-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>💼 投资组合API测试</h3>
            <button class="btn" onclick="testAPI('/api/portfolio', 'portfolio-result')">测试投资组合</button>
            <div id="portfolio-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>⚙️ 系统状态API测试</h3>
            <button class="btn" onclick="testAPI('/api/system/status', 'status-result')">测试系统状态</button>
            <div id="status-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>🌐 快速导航</h3>
            <button class="btn" onclick="window.location.href='/'">返回主界面</button>
            <button class="btn" onclick="testAllAPIs()">测试所有API</button>
            <button class="btn" onclick="clearResults()">清除结果</button>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 测试API
        async function testAPI(endpoint, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.innerHTML = '<span style="color: #ffa726;">正在请求...</span>';
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<span class="success">✅ 成功 (${response.status})</span><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 错误 (${response.status})</span><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 测试所有API
        async function testAllAPIs() {
            const apis = [
                ['/api/realtime-data', 'realtime-result'],
                ['/api/stock-selection', 'selection-result'],
                ['/api/trading-signals', 'signals-result'],
                ['/api/portfolio', 'portfolio-result'],
                ['/api/system/status', 'status-result']
            ];

            for (const [endpoint, resultId] of apis) {
                await testAPI(endpoint, resultId);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
        }

        // 清除结果
        function clearResults() {
            const results = document.querySelectorAll('.result');
            results.forEach(result => result.innerHTML = '');
        }

        // 页面加载完成后自动测试系统状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                testAPI('/api/system/status', 'status-result');
            }, 1000);
        });
    </script>
</body>
</html>
