"""
数据适配器 - VeighNa数据格式适配
将系统数据格式转换为VeighNa标准格式
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class DataAdapter:
    """VeighNa数据适配器"""
    
    def __init__(self):
        logger.info("🔄 VeighNa数据适配器初始化完成")
    
    def convert_to_vnpy_format(self, data: pd.DataFrame, symbol: str) -> List[Dict[str, Any]]:
        """
        将系统数据格式转换为VeighNa格式
        
        Args:
            data: 系统格式的市场数据
            symbol: 股票代码
            
        Returns:
            VeighNa格式的数据列表
        """
        try:
            vnpy_data = []
            
            for index, row in data.iterrows():
                bar_data = {
                    'symbol': symbol,
                    'exchange': 'SSE' if symbol.startswith('6') else 'SZSE',
                    'datetime': index if isinstance(index, datetime) else datetime.now(),
                    'interval': '1m',  # 默认1分钟
                    'volume': float(row.get('volume', 0)),
                    'open_price': float(row.get('open_price', 0)),
                    'high_price': float(row.get('high_price', 0)),
                    'low_price': float(row.get('low_price', 0)),
                    'close_price': float(row.get('close_price', 0)),
                    'open_interest': 0  # 股票无持仓量
                }
                vnpy_data.append(bar_data)
            
            logger.debug(f"✅ 数据格式转换完成: {symbol} {len(vnpy_data)}条")
            return vnpy_data
            
        except Exception as e:
            logger.error(f"❌ 数据格式转换失败: {symbol} - {e}")
            return []
    
    def convert_from_vnpy_format(self, vnpy_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        将VeighNa格式转换为系统格式
        
        Args:
            vnpy_data: VeighNa格式的数据
            
        Returns:
            系统格式的DataFrame
        """
        try:
            if not vnpy_data:
                return pd.DataFrame()
            
            data_list = []
            for bar in vnpy_data:
                data_list.append({
                    'timestamp': bar.get('datetime'),
                    'open_price': bar.get('open_price', 0),
                    'high_price': bar.get('high_price', 0),
                    'low_price': bar.get('low_price', 0),
                    'close_price': bar.get('close_price', 0),
                    'volume': bar.get('volume', 0),
                    'amount': bar.get('volume', 0) * bar.get('close_price', 0)
                })
            
            df = pd.DataFrame(data_list)
            if not df.empty and 'timestamp' in df.columns:
                df.set_index('timestamp', inplace=True)
            
            logger.debug(f"✅ VeighNa格式转换完成: {len(df)}条")
            return df
            
        except Exception as e:
            logger.error(f"❌ VeighNa格式转换失败: {e}")
            return pd.DataFrame()
    
    def validate_data_format(self, data: Any) -> bool:
        """
        验证数据格式
        
        Args:
            data: 待验证的数据
            
        Returns:
            验证结果
        """
        try:
            if isinstance(data, pd.DataFrame):
                required_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
                return all(col in data.columns for col in required_columns)
            elif isinstance(data, list) and data:
                required_keys = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
                return all(key in data[0] for key in required_keys)
            else:
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据格式验证失败: {e}")
            return False
    
    def get_supported_intervals(self) -> List[str]:
        """获取支持的时间间隔"""
        return ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
    
    def normalize_symbol(self, symbol: str) -> str:
        """标准化股票代码"""
        try:
            # 移除可能的后缀
            symbol = symbol.split('.')[0]
            
            # 确保6位数字
            if len(symbol) < 6:
                symbol = symbol.zfill(6)
            
            return symbol
            
        except Exception as e:
            logger.error(f"❌ 股票代码标准化失败: {symbol} - {e}")
            return symbol
