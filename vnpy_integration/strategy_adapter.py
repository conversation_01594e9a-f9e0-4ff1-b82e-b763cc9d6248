"""
策略适配器 - 按照产品设计要求实现
将系统策略适配到VeighNa回测引擎
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd

from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector as StockSelector

logger = logging.getLogger(__name__)

@dataclass
class StrategySignal:
    """策略信号"""
    symbol: str
    action: str  # 'BUY' or 'SELL'
    quantity: int
    price: float
    confidence: float
    reason: str
    timestamp: datetime

class StrategyAdapter:
    """策略适配器"""
    
    def __init__(self):
        # 策略实例
        self.buy_strategy = BuyStrategy()
        self.sell_strategy = SellStrategy()
        self.stock_selector = StockSelector()
        
        # 适配器配置
        self.position_size_ratio = 0.1  # 单股仓位比例
        self.max_positions = 10         # 最大持仓数量
        self.min_signal_strength = 0.7  # 最小信号强度
        
        logger.info("🔄 策略适配器初始化完成")
    
    def create_backtest_strategy(self, strategy_name: str = "多因子量化策略") -> Callable:
        """
        创建回测策略函数 - 产品设计核心功能
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            回测策略函数
        """
        try:
            logger.info(f"🎯 创建回测策略: {strategy_name}")
            
            def backtest_strategy_func(daily_data: Dict[str, Any], 
                                     positions: Dict[str, int], 
                                     current_capital: float) -> List[Dict[str, Any]]:
                """
                回测策略函数
                
                Args:
                    daily_data: 当日市场数据
                    positions: 当前持仓
                    current_capital: 当前资金
                    
                Returns:
                    交易信号列表
                """
                try:
                    signals = []
                    
                    # 1. 生成买入信号
                    buy_signals = self._generate_buy_signals(daily_data, positions, current_capital)
                    signals.extend(buy_signals)
                    
                    # 2. 生成卖出信号
                    sell_signals = self._generate_sell_signals(daily_data, positions)
                    signals.extend(sell_signals)
                    
                    return signals
                    
                except Exception as e:
                    logger.error(f"❌ 策略执行失败: {e}")
                    return []
            
            return backtest_strategy_func
            
        except Exception as e:
            logger.error(f"❌ 创建回测策略失败: {e}")
            return None
    
    def _generate_buy_signals(self, 
                            daily_data: Dict[str, Any], 
                            positions: Dict[str, int], 
                            current_capital: float) -> List[Dict[str, Any]]:
        """生成买入信号"""
        try:
            buy_signals = []
            
            # 检查是否还能开新仓
            if len(positions) >= self.max_positions:
                return buy_signals
            
            # 为每只股票生成买入信号
            for symbol, data in daily_data.items():
                try:
                    # 跳过已持仓的股票
                    if symbol in positions:
                        continue
                    
                    # 检查数据完整性
                    if not data.get('historical') or data['historical'].empty:
                        continue
                    
                    # 使用买入策略评估
                    market_data = data['historical']
                    
                    # 计算因子评分
                    factor_score = self._calculate_factor_score(symbol, market_data)
                    
                    # 检查买入条件
                    if self._check_buy_conditions(symbol, market_data, factor_score):
                        # 计算买入数量
                        quantity = self._calculate_buy_quantity(symbol, current_price, current_capital)
                        
                        if quantity > 0:
                            current_price = data['current']['close_price'] if data.get('current') else market_data.iloc[-1]['close_price']
                            
                            signal = {
                                'symbol': symbol,
                                'action': 'BUY',
                                'quantity': quantity,
                                'price': current_price,
                                'confidence': factor_score / 100.0,
                                'reason': f"因子评分{factor_score:.1f}分"
                            }
                            
                            buy_signals.append(signal)
                            
                            logger.debug(f"🔴 生成买入信号: {symbol} {quantity}股 @{current_price:.2f}")
                    
                except Exception as e:
                    logger.error(f"❌ 生成买入信号失败: {symbol} - {e}")
                    continue
            
            return buy_signals
            
        except Exception as e:
            logger.error(f"❌ 生成买入信号异常: {e}")
            return []
    
    def _generate_sell_signals(self, 
                             daily_data: Dict[str, Any], 
                             positions: Dict[str, int]) -> List[Dict[str, Any]]:
        """生成卖出信号"""
        try:
            sell_signals = []
            
            # 为每个持仓生成卖出信号
            for symbol, quantity in positions.items():
                try:
                    if quantity <= 0:
                        continue
                    
                    # 检查是否有对应的市场数据
                    if symbol not in daily_data:
                        continue
                    
                    data = daily_data[symbol]
                    if not data.get('historical') or data['historical'].empty:
                        continue
                    
                    market_data = data['historical']
                    current_price = data['current']['close_price'] if data.get('current') else market_data.iloc[-1]['close_price']
                    
                    # 使用卖出策略评估
                    if self._check_sell_conditions(symbol, market_data, current_price):
                        signal = {
                            'symbol': symbol,
                            'action': 'SELL',
                            'quantity': quantity,
                            'price': current_price,
                            'confidence': 0.8,  # 卖出信号置信度
                            'reason': "卖出条件触发"
                        }
                        
                        sell_signals.append(signal)
                        
                        logger.debug(f"🟢 生成卖出信号: {symbol} {quantity}股 @{current_price:.2f}")
                
                except Exception as e:
                    logger.error(f"❌ 生成卖出信号失败: {symbol} - {e}")
                    continue
            
            return sell_signals
            
        except Exception as e:
            logger.error(f"❌ 生成卖出信号异常: {e}")
            return []
    
    def _calculate_factor_score(self, symbol: str, market_data: pd.DataFrame) -> float:
        """计算因子评分"""
        try:
            # 简化的因子评分计算
            # 实际应该调用完整的因子计算系统
            
            if len(market_data) < 20:
                return 0.0
            
            # 技术面评分
            technical_score = self._calculate_technical_score(market_data)
            
            # 基本面评分（简化）
            fundamental_score = 70.0  # 默认基本面评分
            
            # 市场表现评分
            market_score = self._calculate_market_performance_score(market_data)
            
            # 综合评分
            total_score = (technical_score * 0.4 + 
                          fundamental_score * 0.3 + 
                          market_score * 0.3)
            
            return min(100, max(0, total_score))
            
        except Exception as e:
            logger.error(f"❌ 计算因子评分失败: {symbol} - {e}")
            return 0.0
    
    def _calculate_technical_score(self, market_data: pd.DataFrame) -> float:
        """计算技术面评分"""
        try:
            score = 50.0  # 基础分数
            
            close_prices = market_data['close_price']
            
            # MA趋势评分
            if len(close_prices) >= 20:
                ma5 = close_prices.rolling(5).mean().iloc[-1]
                ma10 = close_prices.rolling(10).mean().iloc[-1]
                ma20 = close_prices.rolling(20).mean().iloc[-1]
                
                # MA多头排列加分
                if ma5 > ma10 > ma20:
                    score += 20
                elif ma5 > ma10:
                    score += 10
            
            # 价格趋势评分
            if len(close_prices) >= 10:
                recent_trend = (close_prices.iloc[-1] - close_prices.iloc[-10]) / close_prices.iloc[-10]
                if recent_trend > 0.05:  # 上涨超过5%
                    score += 15
                elif recent_trend > 0:
                    score += 5
            
            # 成交量评分
            if 'volume' in market_data.columns and len(market_data) >= 20:
                recent_volume = market_data['volume'].tail(5).mean()
                avg_volume = market_data['volume'].rolling(20).mean().iloc[-1]
                
                if recent_volume > avg_volume * 1.5:  # 成交量放大
                    score += 15
            
            return min(100, max(0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算技术面评分失败: {e}")
            return 50.0
    
    def _calculate_market_performance_score(self, market_data: pd.DataFrame) -> float:
        """计算市场表现评分"""
        try:
            score = 50.0  # 基础分数
            
            close_prices = market_data['close_price']
            
            # 近期表现
            if len(close_prices) >= 20:
                recent_return = (close_prices.iloc[-1] - close_prices.iloc[-20]) / close_prices.iloc[-20]
                
                if recent_return > 0.1:  # 20日涨幅超过10%
                    score += 25
                elif recent_return > 0.05:  # 20日涨幅超过5%
                    score += 15
                elif recent_return > 0:
                    score += 5
            
            # 波动率评分
            if len(close_prices) >= 20:
                returns = close_prices.pct_change().dropna()
                volatility = returns.std()
                
                # 适中波动率加分
                if 0.01 < volatility < 0.05:
                    score += 10
            
            return min(100, max(0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算市场表现评分失败: {e}")
            return 50.0
    
    def _check_buy_conditions(self, symbol: str, market_data: pd.DataFrame, factor_score: float) -> bool:
        """检查买入条件"""
        try:
            # 产品设计买入条件：
            # 1. 因子评分≥70分
            if factor_score < 70:
                return False
            
            # 2. 技术指标确认
            if not self._check_technical_buy_conditions(market_data):
                return False
            
            # 3. 成交量确认
            if not self._check_volume_buy_conditions(market_data):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查买入条件失败: {symbol} - {e}")
            return False
    
    def _check_technical_buy_conditions(self, market_data: pd.DataFrame) -> bool:
        """检查技术买入条件"""
        try:
            if len(market_data) < 20:
                return False
            
            close_prices = market_data['close_price']
            
            # MA多头排列
            ma5 = close_prices.rolling(5).mean().iloc[-1]
            ma10 = close_prices.rolling(10).mean().iloc[-1]
            ma20 = close_prices.rolling(20).mean().iloc[-1]
            
            if not (ma5 > ma10 > ma20):
                return False
            
            # 价格在MA之上
            current_price = close_prices.iloc[-1]
            if current_price < ma5:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查技术买入条件失败: {e}")
            return False
    
    def _check_volume_buy_conditions(self, market_data: pd.DataFrame) -> bool:
        """检查成交量买入条件"""
        try:
            if 'volume' not in market_data.columns or len(market_data) < 20:
                return True  # 没有成交量数据时默认通过
            
            volumes = market_data['volume']
            current_volume = volumes.iloc[-1]
            avg_volume = volumes.rolling(20).mean().iloc[-1]
            
            # 成交量放大1.5倍以上
            if current_volume < avg_volume * 1.5:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查成交量买入条件失败: {e}")
            return True
    
    def _check_sell_conditions(self, symbol: str, market_data: pd.DataFrame, current_price: float) -> bool:
        """检查卖出条件"""
        try:
            # 简化的卖出条件
            # 实际应该调用完整的卖出策略
            
            if len(market_data) < 20:
                return False
            
            close_prices = market_data['close_price']
            
            # 技术指标转弱
            ma5 = close_prices.rolling(5).mean().iloc[-1]
            ma10 = close_prices.rolling(10).mean().iloc[-1]
            
            # MA死叉
            if ma5 < ma10:
                return True
            
            # 价格跌破MA5
            if current_price < ma5 * 0.95:
                return True
            
            # 大幅下跌
            if len(close_prices) >= 5:
                recent_return = (current_price - close_prices.iloc[-5]) / close_prices.iloc[-5]
                if recent_return < -0.05:  # 5日跌幅超过5%
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 检查卖出条件失败: {symbol} - {e}")
            return False
    
    def _calculate_buy_quantity(self, symbol: str, price: float, current_capital: float) -> int:
        """计算买入数量"""
        try:
            # 按固定仓位比例计算
            position_value = current_capital * self.position_size_ratio
            quantity = int(position_value / price)
            
            # 确保数量为100的整数倍（A股交易规则）
            quantity = (quantity // 100) * 100
            
            return max(0, quantity)
            
        except Exception as e:
            logger.error(f"❌ 计算买入数量失败: {symbol} - {e}")
            return 0
    
    def get_strategy_config(self) -> Dict[str, Any]:
        """获取策略配置"""
        return {
            'position_size_ratio': self.position_size_ratio,
            'max_positions': self.max_positions,
            'min_signal_strength': self.min_signal_strength,
            'buy_conditions': {
                'min_factor_score': 70,
                'require_ma_bullish': True,
                'require_volume_expansion': True
            },
            'sell_conditions': {
                'max_factor_score': 75,
                'check_ma_bearish': True,
                'check_price_breakdown': True
            }
        }
    
    def update_strategy_config(self, config: Dict[str, Any]):
        """更新策略配置"""
        try:
            if 'position_size_ratio' in config:
                self.position_size_ratio = config['position_size_ratio']
            
            if 'max_positions' in config:
                self.max_positions = config['max_positions']
            
            if 'min_signal_strength' in config:
                self.min_signal_strength = config['min_signal_strength']
            
            logger.info("✅ 策略配置已更新")
            
        except Exception as e:
            logger.error(f"❌ 更新策略配置失败: {e}")
