"""
结果分析器 - VeighNa回测结果分析
分析VeighNa回测结果，生成专业的分析报告
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """分析结果"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    volatility: float
    calmar_ratio: float

class ResultAnalyzer:
    """VeighNa结果分析器"""
    
    def __init__(self):
        self.risk_free_rate = 0.03  # 无风险利率3%
        logger.info("📊 VeighNa结果分析器初始化完成")
    
    def analyze_backtest_result(self, 
                              equity_curve: List[float],
                              trade_records: List[Dict[str, Any]],
                              start_date: datetime,
                              end_date: datetime) -> AnalysisResult:
        """
        分析回测结果
        
        Args:
            equity_curve: 资金曲线
            trade_records: 交易记录
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            分析结果
        """
        try:
            logger.info("📊 开始分析回测结果...")
            
            if not equity_curve or len(equity_curve) < 2:
                logger.warning("⚠️ 资金曲线数据不足")
                return self._create_empty_result()
            
            # 基础收益指标
            initial_capital = equity_curve[0]
            final_capital = equity_curve[-1]
            total_return = (final_capital - initial_capital) / initial_capital
            
            # 年化收益率
            days = (end_date - start_date).days
            years = days / 365.0
            annual_return = (final_capital / initial_capital) ** (1 / years) - 1 if years > 0 else 0
            
            # 最大回撤
            max_drawdown = self._calculate_max_drawdown(equity_curve)
            
            # 夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(equity_curve)
            
            # 交易统计
            trade_stats = self._analyze_trades(trade_records)
            
            # 波动率
            volatility = self._calculate_volatility(equity_curve)
            
            # 卡玛比率
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
            
            result = AnalysisResult(
                total_return=total_return,
                annual_return=annual_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=trade_stats['win_rate'],
                profit_factor=trade_stats['profit_factor'],
                total_trades=trade_stats['total_trades'],
                avg_trade_duration=trade_stats['avg_duration'],
                volatility=volatility,
                calmar_ratio=calmar_ratio
            )
            
            logger.info("✅ 回测结果分析完成")
            logger.info(f"  - 总收益率: {total_return:.2%}")
            logger.info(f"  - 年化收益率: {annual_return:.2%}")
            logger.info(f"  - 最大回撤: {max_drawdown:.2%}")
            logger.info(f"  - 夏普比率: {sharpe_ratio:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 分析回测结果失败: {e}")
            return self._create_empty_result()
    
    def _calculate_max_drawdown(self, equity_curve: List[float]) -> float:
        """计算最大回撤"""
        try:
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            return abs(max_drawdown)
            
        except Exception as e:
            logger.error(f"❌ 计算最大回撤失败: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self, equity_curve: List[float]) -> float:
        """计算夏普比率"""
        try:
            if len(equity_curve) < 2:
                return 0.0
            
            # 计算日收益率
            returns = []
            for i in range(1, len(equity_curve)):
                daily_return = (equity_curve[i] - equity_curve[i-1]) / equity_curve[i-1]
                returns.append(daily_return)
            
            if not returns:
                return 0.0
            
            returns_series = pd.Series(returns)
            
            # 计算年化收益率和波动率
            annual_return = returns_series.mean() * 252  # 假设252个交易日
            annual_volatility = returns_series.std() * np.sqrt(252)
            
            if annual_volatility == 0:
                return 0.0
            
            sharpe_ratio = (annual_return - self.risk_free_rate) / annual_volatility
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"❌ 计算夏普比率失败: {e}")
            return 0.0
    
    def _calculate_volatility(self, equity_curve: List[float]) -> float:
        """计算波动率"""
        try:
            if len(equity_curve) < 2:
                return 0.0
            
            # 计算日收益率
            returns = []
            for i in range(1, len(equity_curve)):
                daily_return = (equity_curve[i] - equity_curve[i-1]) / equity_curve[i-1]
                returns.append(daily_return)
            
            if not returns:
                return 0.0
            
            returns_series = pd.Series(returns)
            annual_volatility = returns_series.std() * np.sqrt(252)
            
            return annual_volatility
            
        except Exception as e:
            logger.error(f"❌ 计算波动率失败: {e}")
            return 0.0
    
    def _analyze_trades(self, trade_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析交易记录"""
        try:
            if not trade_records:
                return {
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'avg_duration': 0.0
                }
            
            # 配对交易记录
            trades_pnl = self._calculate_trades_pnl(trade_records)
            
            total_trades = len(trades_pnl)
            winning_trades = len([pnl for pnl in trades_pnl if pnl > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 盈亏比
            winning_pnl = [pnl for pnl in trades_pnl if pnl > 0]
            losing_pnl = [pnl for pnl in trades_pnl if pnl < 0]
            
            total_profit = sum(winning_pnl) if winning_pnl else 0
            total_loss = abs(sum(losing_pnl)) if losing_pnl else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else 0
            
            # 平均持仓时间（简化处理）
            avg_duration = 5.0  # 默认5天
            
            return {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'avg_duration': avg_duration
            }
            
        except Exception as e:
            logger.error(f"❌ 分析交易记录失败: {e}")
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_duration': 0.0
            }
    
    def _calculate_trades_pnl(self, trade_records: List[Dict[str, Any]]) -> List[float]:
        """计算每笔交易的盈亏"""
        try:
            trades_pnl = []
            
            # 简化处理：假设每次买入后都有对应的卖出
            buy_records = {}
            
            for record in trade_records:
                symbol = record.get('symbol', '')
                action = record.get('action', '')
                quantity = record.get('quantity', 0)
                price = record.get('price', 0)
                cost = record.get('cost', 0)
                
                if action == 'BUY':
                    if symbol not in buy_records:
                        buy_records[symbol] = []
                    buy_records[symbol].append({
                        'quantity': quantity,
                        'price': price,
                        'cost': cost
                    })
                    
                elif action == 'SELL':
                    if symbol in buy_records and buy_records[symbol]:
                        # 匹配最早的买入记录
                        buy_record = buy_records[symbol].pop(0)
                        
                        # 计算盈亏
                        buy_value = buy_record['quantity'] * buy_record['price'] + buy_record['cost']
                        sell_value = quantity * price - cost
                        pnl = sell_value - buy_value
                        
                        trades_pnl.append(pnl)
            
            return trades_pnl
            
        except Exception as e:
            logger.error(f"❌ 计算交易盈亏失败: {e}")
            return []
    
    def _create_empty_result(self) -> AnalysisResult:
        """创建空的分析结果"""
        return AnalysisResult(
            total_return=0.0,
            annual_return=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            avg_trade_duration=0.0,
            volatility=0.0,
            calmar_ratio=0.0
        )
    
    def generate_analysis_report(self, result: AnalysisResult) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            report = {
                'summary': {
                    'total_return': f"{result.total_return:.2%}",
                    'annual_return': f"{result.annual_return:.2%}",
                    'max_drawdown': f"{result.max_drawdown:.2%}",
                    'sharpe_ratio': f"{result.sharpe_ratio:.2f}",
                    'volatility': f"{result.volatility:.2%}"
                },
                'trading_stats': {
                    'total_trades': result.total_trades,
                    'win_rate': f"{result.win_rate:.2%}",
                    'profit_factor': f"{result.profit_factor:.2f}",
                    'avg_trade_duration': f"{result.avg_trade_duration:.1f}天"
                },
                'risk_metrics': {
                    'max_drawdown': f"{result.max_drawdown:.2%}",
                    'calmar_ratio': f"{result.calmar_ratio:.2f}",
                    'volatility': f"{result.volatility:.2%}"
                },
                'performance_rating': self._rate_performance(result)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 生成分析报告失败: {e}")
            return {}
    
    def _rate_performance(self, result: AnalysisResult) -> str:
        """评级策略表现"""
        try:
            score = 0
            
            # 收益率评分
            if result.annual_return > 0.2:
                score += 30
            elif result.annual_return > 0.1:
                score += 20
            elif result.annual_return > 0.05:
                score += 10
            
            # 夏普比率评分
            if result.sharpe_ratio > 2.0:
                score += 25
            elif result.sharpe_ratio > 1.0:
                score += 15
            elif result.sharpe_ratio > 0.5:
                score += 10
            
            # 最大回撤评分
            if result.max_drawdown < 0.05:
                score += 25
            elif result.max_drawdown < 0.1:
                score += 15
            elif result.max_drawdown < 0.2:
                score += 10
            
            # 胜率评分
            if result.win_rate > 0.6:
                score += 20
            elif result.win_rate > 0.5:
                score += 10
            elif result.win_rate > 0.4:
                score += 5
            
            # 评级
            if score >= 80:
                return "优秀"
            elif score >= 60:
                return "良好"
            elif score >= 40:
                return "一般"
            else:
                return "较差"
                
        except Exception as e:
            logger.error(f"❌ 评级策略表现失败: {e}")
            return "未知"
