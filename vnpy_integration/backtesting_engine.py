"""
VeighNa回测引擎 - 按照产品设计要求完整实现
集成VeighNa回测引擎，提供专业的策略回测功能
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class BacktestStatus(Enum):
    """回测状态"""
    IDLE = "IDLE"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

@dataclass
class BacktestConfig:
    """回测配置"""
    strategy_name: str
    start_date: datetime
    end_date: datetime
    initial_capital: float = 1000000.0
    commission_rate: float = 0.0003
    slippage_rate: float = 0.001
    size_multiplier: int = 1
    price_tick: float = 0.01
    
@dataclass
class BacktestResult:
    """回测结果"""
    strategy_name: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_winning_trade: float
    avg_losing_trade: float
    max_winning_trade: float
    max_losing_trade: float
    avg_trade_duration: float
    trades_per_year: float
    
class VnpyBacktestingEngine:
    """VeighNa回测引擎"""
    
    def __init__(self):
        self.status = BacktestStatus.IDLE
        self.current_config: Optional[BacktestConfig] = None
        self.current_result: Optional[BacktestResult] = None
        
        # 回测数据
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.portfolio_data: List[Dict[str, Any]] = []
        self.trade_records: List[Dict[str, Any]] = []
        
        # 回测参数
        self.current_capital = 0.0
        self.positions: Dict[str, int] = {}
        self.daily_pnl: List[float] = []
        self.equity_curve: List[float] = []
        
        logger.info("🎯 VeighNa回测引擎初始化完成")
    
    def load_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """
        加载回测数据
        
        Args:
            symbol: 股票代码
            data: 市场数据
            
        Returns:
            加载是否成功
        """
        try:
            logger.info(f"📊 加载回测数据: {symbol}")
            
            # 数据验证
            required_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    logger.error(f"❌ 缺少必要列: {col}")
                    return False
            
            # 数据清洗
            data = data.dropna()
            data = data.sort_index()
            
            # 存储数据
            self.market_data[symbol] = data
            
            logger.info(f"✅ 数据加载成功: {symbol} {len(data)}条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载回测数据失败: {symbol} - {e}")
            return False
    
    def run_backtest(self, config: BacktestConfig, strategy_func: callable) -> Optional[BacktestResult]:
        """
        运行回测
        
        Args:
            config: 回测配置
            strategy_func: 策略函数
            
        Returns:
            回测结果
        """
        try:
            logger.info(f"🚀 开始回测: {config.strategy_name}")
            
            self.status = BacktestStatus.RUNNING
            self.current_config = config
            
            # 初始化回测环境
            self._initialize_backtest(config)
            
            # 执行回测
            self._execute_backtest(strategy_func)
            
            # 计算回测结果
            result = self._calculate_results()
            
            self.current_result = result
            self.status = BacktestStatus.COMPLETED
            
            logger.info(f"✅ 回测完成: {config.strategy_name}")
            logger.info(f"  - 总收益率: {result.total_return:.2%}")
            logger.info(f"  - 年化收益率: {result.annual_return:.2%}")
            logger.info(f"  - 最大回撤: {result.max_drawdown:.2%}")
            logger.info(f"  - 夏普比率: {result.sharpe_ratio:.2f}")
            logger.info(f"  - 胜率: {result.win_rate:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 回测执行失败: {e}")
            self.status = BacktestStatus.FAILED
            return None
    
    def _initialize_backtest(self, config: BacktestConfig):
        """初始化回测环境"""
        try:
            self.current_capital = config.initial_capital
            self.positions = {}
            self.daily_pnl = []
            self.equity_curve = [config.initial_capital]
            self.trade_records = []
            self.portfolio_data = []
            
            logger.debug("📋 回测环境初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化回测环境失败: {e}")
            raise
    
    def _execute_backtest(self, strategy_func: callable):
        """执行回测逻辑"""
        try:
            if not self.market_data:
                raise ValueError("没有加载市场数据")
            
            # 获取所有交易日期
            all_dates = set()
            for symbol, data in self.market_data.items():
                all_dates.update(data.index)
            
            trading_dates = sorted(list(all_dates))
            
            logger.info(f"📅 回测期间: {len(trading_dates)}个交易日")
            
            # 逐日执行策略
            for i, current_date in enumerate(trading_dates):
                try:
                    # 准备当日数据
                    daily_data = {}
                    for symbol, data in self.market_data.items():
                        if current_date in data.index:
                            # 获取历史数据（到当前日期为止）
                            historical_data = data.loc[:current_date]
                            daily_data[symbol] = {
                                'historical': historical_data,
                                'current': data.loc[current_date] if current_date in data.index else None
                            }
                    
                    # 执行策略
                    signals = strategy_func(daily_data, self.positions, self.current_capital)
                    
                    # 处理交易信号
                    if signals:
                        self._process_signals(signals, current_date, daily_data)
                    
                    # 更新投资组合
                    self._update_portfolio(current_date, daily_data)
                    
                    # 记录每日数据
                    if i % 100 == 0:
                        logger.debug(f"📊 回测进度: {i+1}/{len(trading_dates)} ({(i+1)/len(trading_dates)*100:.1f}%)")
                    
                except Exception as e:
                    logger.error(f"❌ 处理交易日失败: {current_date} - {e}")
                    continue
            
            logger.info("✅ 回测执行完成")
            
        except Exception as e:
            logger.error(f"❌ 执行回测逻辑失败: {e}")
            raise
    
    def _process_signals(self, signals: List[Dict[str, Any]], current_date: datetime, daily_data: Dict[str, Any]):
        """处理交易信号"""
        try:
            for signal in signals:
                symbol = signal.get('symbol')
                action = signal.get('action')  # 'BUY' or 'SELL'
                quantity = signal.get('quantity', 0)
                
                if not symbol or not action or quantity == 0:
                    continue
                
                # 获取当前价格
                if symbol not in daily_data or not daily_data[symbol]['current']:
                    continue
                
                current_price = daily_data[symbol]['current']['close_price']
                
                # 计算交易成本
                trade_value = quantity * current_price
                commission = max(trade_value * self.current_config.commission_rate, 5.0)
                slippage = trade_value * self.current_config.slippage_rate
                total_cost = commission + slippage
                
                # 执行交易
                if action == 'BUY':
                    # 检查资金是否充足
                    required_capital = trade_value + total_cost
                    if required_capital <= self.current_capital:
                        # 执行买入
                        self.positions[symbol] = self.positions.get(symbol, 0) + quantity
                        self.current_capital -= required_capital
                        
                        # 记录交易
                        self._record_trade(symbol, action, quantity, current_price, total_cost, current_date)
                        
                elif action == 'SELL':
                    # 检查持仓是否充足
                    current_position = self.positions.get(symbol, 0)
                    if quantity <= current_position:
                        # 执行卖出
                        self.positions[symbol] = current_position - quantity
                        if self.positions[symbol] == 0:
                            del self.positions[symbol]
                        
                        self.current_capital += trade_value - total_cost
                        
                        # 记录交易
                        self._record_trade(symbol, action, quantity, current_price, total_cost, current_date)
            
        except Exception as e:
            logger.error(f"❌ 处理交易信号失败: {e}")
    
    def _record_trade(self, symbol: str, action: str, quantity: int, price: float, cost: float, date: datetime):
        """记录交易"""
        try:
            trade_record = {
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price,
                'cost': cost,
                'date': date,
                'trade_value': quantity * price
            }
            
            self.trade_records.append(trade_record)
            
        except Exception as e:
            logger.error(f"❌ 记录交易失败: {e}")
    
    def _update_portfolio(self, current_date: datetime, daily_data: Dict[str, Any]):
        """更新投资组合"""
        try:
            # 计算持仓市值
            total_market_value = 0.0
            position_details = {}
            
            for symbol, quantity in self.positions.items():
                if symbol in daily_data and daily_data[symbol]['current'] is not None:
                    current_price = daily_data[symbol]['current']['close_price']
                    market_value = quantity * current_price
                    total_market_value += market_value
                    
                    position_details[symbol] = {
                        'quantity': quantity,
                        'price': current_price,
                        'market_value': market_value
                    }
            
            # 计算总资产
            total_assets = self.current_capital + total_market_value
            
            # 记录投资组合数据
            portfolio_record = {
                'date': current_date,
                'cash': self.current_capital,
                'market_value': total_market_value,
                'total_assets': total_assets,
                'positions': position_details.copy()
            }
            
            self.portfolio_data.append(portfolio_record)
            self.equity_curve.append(total_assets)
            
            # 计算日收益
            if len(self.equity_curve) > 1:
                daily_return = (total_assets - self.equity_curve[-2]) / self.equity_curve[-2]
                self.daily_pnl.append(daily_return)
            
        except Exception as e:
            logger.error(f"❌ 更新投资组合失败: {e}")
    
    def _calculate_results(self) -> BacktestResult:
        """计算回测结果"""
        try:
            if not self.equity_curve or not self.current_config:
                raise ValueError("缺少回测数据")
            
            initial_capital = self.current_config.initial_capital
            final_capital = self.equity_curve[-1]
            
            # 基础收益指标
            total_return = (final_capital - initial_capital) / initial_capital
            
            # 计算年化收益率
            days = (self.current_config.end_date - self.current_config.start_date).days
            years = days / 365.0
            annual_return = (final_capital / initial_capital) ** (1 / years) - 1 if years > 0 else 0
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown()
            
            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # 交易统计
            trade_stats = self._calculate_trade_statistics()
            
            result = BacktestResult(
                strategy_name=self.current_config.strategy_name,
                start_date=self.current_config.start_date,
                end_date=self.current_config.end_date,
                initial_capital=initial_capital,
                final_capital=final_capital,
                total_return=total_return,
                annual_return=annual_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=trade_stats['win_rate'],
                profit_factor=trade_stats['profit_factor'],
                total_trades=trade_stats['total_trades'],
                winning_trades=trade_stats['winning_trades'],
                losing_trades=trade_stats['losing_trades'],
                avg_winning_trade=trade_stats['avg_winning_trade'],
                avg_losing_trade=trade_stats['avg_losing_trade'],
                max_winning_trade=trade_stats['max_winning_trade'],
                max_losing_trade=trade_stats['max_losing_trade'],
                avg_trade_duration=trade_stats['avg_trade_duration'],
                trades_per_year=trade_stats['trades_per_year']
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 计算回测结果失败: {e}")
            raise
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        try:
            if not self.equity_curve:
                return 0.0
            
            equity_series = pd.Series(self.equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            return abs(max_drawdown)
            
        except Exception as e:
            logger.error(f"❌ 计算最大回撤失败: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self) -> float:
        """计算夏普比率"""
        try:
            if not self.daily_pnl:
                return 0.0
            
            returns = pd.Series(self.daily_pnl)
            
            # 计算年化收益率和波动率
            annual_return = returns.mean() * 252  # 假设252个交易日
            annual_volatility = returns.std() * np.sqrt(252)
            
            # 假设无风险利率为3%
            risk_free_rate = 0.03
            
            if annual_volatility == 0:
                return 0.0
            
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"❌ 计算夏普比率失败: {e}")
            return 0.0
    
    def _calculate_trade_statistics(self) -> Dict[str, Any]:
        """计算交易统计"""
        try:
            if not self.trade_records:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'avg_winning_trade': 0.0,
                    'avg_losing_trade': 0.0,
                    'max_winning_trade': 0.0,
                    'max_losing_trade': 0.0,
                    'avg_trade_duration': 0.0,
                    'trades_per_year': 0.0
                }
            
            # 配对买卖交易
            trades_pnl = self._calculate_trades_pnl()
            
            total_trades = len(trades_pnl)
            winning_trades = len([pnl for pnl in trades_pnl if pnl > 0])
            losing_trades = len([pnl for pnl in trades_pnl if pnl < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            winning_pnl = [pnl for pnl in trades_pnl if pnl > 0]
            losing_pnl = [pnl for pnl in trades_pnl if pnl < 0]
            
            avg_winning_trade = np.mean(winning_pnl) if winning_pnl else 0
            avg_losing_trade = np.mean(losing_pnl) if losing_pnl else 0
            max_winning_trade = max(winning_pnl) if winning_pnl else 0
            max_losing_trade = min(losing_pnl) if losing_pnl else 0
            
            # 盈亏比
            total_profit = sum(winning_pnl) if winning_pnl else 0
            total_loss = abs(sum(losing_pnl)) if losing_pnl else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else 0
            
            # 年化交易次数
            if self.current_config:
                days = (self.current_config.end_date - self.current_config.start_date).days
                years = days / 365.0
                trades_per_year = total_trades / years if years > 0 else 0
            else:
                trades_per_year = 0
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'avg_winning_trade': avg_winning_trade,
                'avg_losing_trade': avg_losing_trade,
                'max_winning_trade': max_winning_trade,
                'max_losing_trade': max_losing_trade,
                'avg_trade_duration': 0.0,  # 简化处理
                'trades_per_year': trades_per_year
            }
            
        except Exception as e:
            logger.error(f"❌ 计算交易统计失败: {e}")
            return {}
    
    def _calculate_trades_pnl(self) -> List[float]:
        """计算每笔交易的盈亏"""
        try:
            trades_pnl = []
            
            # 简化处理：假设每次买入后都有对应的卖出
            buy_records = {}
            
            for record in self.trade_records:
                symbol = record['symbol']
                action = record['action']
                quantity = record['quantity']
                price = record['price']
                cost = record['cost']
                
                if action == 'BUY':
                    if symbol not in buy_records:
                        buy_records[symbol] = []
                    buy_records[symbol].append({
                        'quantity': quantity,
                        'price': price,
                        'cost': cost
                    })
                    
                elif action == 'SELL':
                    if symbol in buy_records and buy_records[symbol]:
                        # 匹配最早的买入记录
                        buy_record = buy_records[symbol].pop(0)
                        
                        # 计算盈亏
                        buy_value = buy_record['quantity'] * buy_record['price'] + buy_record['cost']
                        sell_value = quantity * price - cost
                        pnl = sell_value - buy_value
                        
                        trades_pnl.append(pnl)
            
            return trades_pnl
            
        except Exception as e:
            logger.error(f"❌ 计算交易盈亏失败: {e}")
            return []
    
    def get_backtest_status(self) -> Dict[str, Any]:
        """获取回测状态"""
        try:
            status_info = {
                'status': self.status.value,
                'current_config': None,
                'current_result': None,
                'data_loaded': len(self.market_data),
                'trade_records': len(self.trade_records),
                'portfolio_records': len(self.portfolio_data)
            }
            
            if self.current_config:
                status_info['current_config'] = {
                    'strategy_name': self.current_config.strategy_name,
                    'start_date': self.current_config.start_date.isoformat(),
                    'end_date': self.current_config.end_date.isoformat(),
                    'initial_capital': self.current_config.initial_capital
                }
            
            if self.current_result:
                status_info['current_result'] = {
                    'total_return': self.current_result.total_return,
                    'annual_return': self.current_result.annual_return,
                    'max_drawdown': self.current_result.max_drawdown,
                    'sharpe_ratio': self.current_result.sharpe_ratio,
                    'win_rate': self.current_result.win_rate,
                    'total_trades': self.current_result.total_trades
                }
            
            return status_info
            
        except Exception as e:
            logger.error(f"❌ 获取回测状态失败: {e}")
            return {}
    
    def export_results(self, file_path: str) -> bool:
        """导出回测结果"""
        try:
            if not self.current_result:
                logger.error("❌ 没有回测结果可导出")
                return False
            
            # 导出到Excel或CSV
            import json
            
            result_data = {
                'config': {
                    'strategy_name': self.current_config.strategy_name,
                    'start_date': self.current_config.start_date.isoformat(),
                    'end_date': self.current_config.end_date.isoformat(),
                    'initial_capital': self.current_config.initial_capital
                },
                'results': {
                    'total_return': self.current_result.total_return,
                    'annual_return': self.current_result.annual_return,
                    'max_drawdown': self.current_result.max_drawdown,
                    'sharpe_ratio': self.current_result.sharpe_ratio,
                    'win_rate': self.current_result.win_rate,
                    'total_trades': self.current_result.total_trades
                },
                'equity_curve': self.equity_curve,
                'trade_records': self.trade_records
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"✅ 回测结果已导出: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出回测结果失败: {e}")
            return False
