#!/usr/bin/env python3
"""
VeighNa量化交易系统启动脚本
确保系统正常启动并提供访问信息
"""

import subprocess
import time
import requests
import webbrowser
import sys
import os

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://localhost:8080/api/system/status", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, None
    except:
        return False, None

def main():
    print("🚀 VeighNa量化交易系统启动器")
    print("=" * 60)
    
    # 检查服务器是否已经运行
    is_running, status_data = check_server_status()
    
    if is_running:
        print("✅ 系统已经在运行中!")
        print(f"📊 系统状态: {status_data.get('data', {}).get('business_flow', {}).get('system_health', 'unknown')}")
        print(f"🌐 Web界面: http://localhost:8080")
        print(f"📈 API状态: 正常")
        
        # 尝试打开浏览器
        try:
            webbrowser.open("http://localhost:8080")
            print("🌐 已尝试打开浏览器")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问: http://localhost:8080")
        
        print("\n📋 可用的API接口:")
        apis = [
            ("实时数据", "/api/realtime-data"),
            ("智能选股", "/api/stock-selection"),
            ("交易信号", "/api/trading-signals"),
            ("投资组合", "/api/portfolio"),
            ("系统状态", "/api/system/status")
        ]
        
        for name, endpoint in apis:
            print(f"  • {name}: http://localhost:8080{endpoint}")
        
        print("\n🔧 如果浏览器无法访问，请尝试:")
        print("  1. 清除浏览器缓存")
        print("  2. 使用无痕模式")
        print("  3. 尝试不同的浏览器")
        print("  4. 直接访问 http://127.0.0.1:8080")
        
    else:
        print("❌ 系统未运行，正在启动...")
        print("请运行: python web_server.py")
        
        # 尝试启动系统
        try:
            print("🔄 尝试自动启动系统...")
            subprocess.Popen([sys.executable, "web_server.py"], 
                           cwd=os.getcwd(),
                           stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE)
            
            # 等待系统启动
            print("⏳ 等待系统启动...")
            for i in range(30):
                time.sleep(2)
                is_running, _ = check_server_status()
                if is_running:
                    print("✅ 系统启动成功!")
                    webbrowser.open("http://localhost:8080")
                    break
                print(f"   等待中... ({i+1}/30)")
            else:
                print("❌ 系统启动超时，请手动运行: python web_server.py")
                
        except Exception as e:
            print(f"❌ 自动启动失败: {e}")
            print("请手动运行: python web_server.py")

if __name__ == "__main__":
    main()
