"""
主窗口
vn.py风格的专业量化交易主界面
"""

import sys
import logging
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QStatusBar, QToolBar, QAction, QSplitter, QTabWidget,
    QDockWidget, QLabel, QProgressBar, QSystemTrayIcon, QMenu
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QPixmap, QFont

from .widgets.market_monitor import MarketMonitorWidget
from .widgets.factor_manager import FactorManagerWidget
from .widgets.strategy_manager import StrategyManagerWidget
from .widgets.portfolio_manager import PortfolioManagerWidget
from .widgets.backtest_manager import BacktestManagerWidget
from .widgets.trade_manager import TradeManagerWidget
from .widgets.log_viewer import LogViewerWidget
from .widgets.system_monitor import SystemMonitorWidget
from .widgets.chart_widget import ChartWidget
from .utils.style_manager import StyleManager
from .utils.config_manager import UIConfigManager

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """主窗口"""
    
    # 信号定义
    status_update = pyqtSignal(str)
    progress_update = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        
        # 配置管理器
        self.config_manager = UIConfigManager()
        
        # 样式管理器
        self.style_manager = StyleManager()
        
        # 子窗口字典
        self.widgets: Dict[str, QWidget] = {}
        
        # 状态栏组件
        self.status_label = None
        self.progress_bar = None
        
        # 系统托盘
        self.tray_icon = None
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        
        # 初始化界面
        self.init_ui()
        
        # 连接信号
        self.connect_signals()
        
        # 启动定时更新
        self.update_timer.start(1000)  # 每秒更新
        
        logger.info("🖥️ 主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置窗口属性
            self.setWindowTitle("量化交易系统 - Professional Trading Platform")
            self.setMinimumSize(1400, 900)
            self.resize(1600, 1000)
            
            # 设置窗口图标
            self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
            
            # 应用样式
            self.style_manager.apply_vnpy_style(self)
            
            # 创建菜单栏
            self.create_menu_bar()
            
            # 创建工具栏
            self.create_tool_bar()
            
            # 创建状态栏
            self.create_status_bar()
            
            # 创建中央窗口
            self.create_central_widget()
            
            # 创建停靠窗口
            self.create_dock_widgets()
            
            # 创建系统托盘
            self.create_system_tray()
            
            # 恢复窗口状态
            self.restore_window_state()
            
        except Exception as e:
            logger.error(f"❌ 初始化界面失败: {e}")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        try:
            menubar = self.menuBar()
            
            # 文件菜单
            file_menu = menubar.addMenu('文件(&F)')
            
            # 新建策略
            new_strategy_action = QAction('新建策略', self)
            new_strategy_action.setShortcut('Ctrl+N')
            new_strategy_action.triggered.connect(self.new_strategy)
            file_menu.addAction(new_strategy_action)
            
            # 打开策略
            open_strategy_action = QAction('打开策略', self)
            open_strategy_action.setShortcut('Ctrl+O')
            open_strategy_action.triggered.connect(self.open_strategy)
            file_menu.addAction(open_strategy_action)
            
            file_menu.addSeparator()
            
            # 导入数据
            import_data_action = QAction('导入数据', self)
            import_data_action.triggered.connect(self.import_data)
            file_menu.addAction(import_data_action)
            
            # 导出数据
            export_data_action = QAction('导出数据', self)
            export_data_action.triggered.connect(self.export_data)
            file_menu.addAction(export_data_action)
            
            file_menu.addSeparator()
            
            # 退出
            exit_action = QAction('退出', self)
            exit_action.setShortcut('Ctrl+Q')
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)
            
            # 交易菜单
            trade_menu = menubar.addMenu('交易(&T)')
            
            # 启动交易
            start_trading_action = QAction('启动交易', self)
            start_trading_action.triggered.connect(self.start_trading)
            trade_menu.addAction(start_trading_action)
            
            # 停止交易
            stop_trading_action = QAction('停止交易', self)
            stop_trading_action.triggered.connect(self.stop_trading)
            trade_menu.addAction(stop_trading_action)
            
            trade_menu.addSeparator()
            
            # 手动下单
            manual_order_action = QAction('手动下单', self)
            manual_order_action.triggered.connect(self.manual_order)
            trade_menu.addAction(manual_order_action)
            
            # 分析菜单
            analysis_menu = menubar.addMenu('分析(&A)')
            
            # 因子分析
            factor_analysis_action = QAction('因子分析', self)
            factor_analysis_action.triggered.connect(self.show_factor_analysis)
            analysis_menu.addAction(factor_analysis_action)
            
            # 策略回测
            backtest_action = QAction('策略回测', self)
            backtest_action.triggered.connect(self.show_backtest)
            analysis_menu.addAction(backtest_action)
            
            # 绩效分析
            performance_action = QAction('绩效分析', self)
            performance_action.triggered.connect(self.show_performance)
            analysis_menu.addAction(performance_action)
            
            # 工具菜单
            tools_menu = menubar.addMenu('工具(&O)')
            
            # 系统设置
            settings_action = QAction('系统设置', self)
            settings_action.triggered.connect(self.show_settings)
            tools_menu.addAction(settings_action)
            
            # 日志查看
            log_viewer_action = QAction('日志查看', self)
            log_viewer_action.triggered.connect(self.show_log_viewer)
            tools_menu.addAction(log_viewer_action)
            
            # 系统监控
            system_monitor_action = QAction('系统监控', self)
            system_monitor_action.triggered.connect(self.show_system_monitor)
            tools_menu.addAction(system_monitor_action)
            
            # 帮助菜单
            help_menu = menubar.addMenu('帮助(&H)')
            
            # 用户手册
            manual_action = QAction('用户手册', self)
            manual_action.triggered.connect(self.show_manual)
            help_menu.addAction(manual_action)
            
            # 关于
            about_action = QAction('关于', self)
            about_action.triggered.connect(self.show_about)
            help_menu.addAction(about_action)
            
        except Exception as e:
            logger.error(f"❌ 创建菜单栏失败: {e}")
    
    def create_tool_bar(self):
        """创建工具栏"""
        try:
            toolbar = self.addToolBar('主工具栏')
            toolbar.setMovable(False)
            
            # 启动交易
            start_action = QAction(QIcon("resources/icons/start.png"), '启动交易', self)
            start_action.triggered.connect(self.start_trading)
            toolbar.addAction(start_action)
            
            # 停止交易
            stop_action = QAction(QIcon("resources/icons/stop.png"), '停止交易', self)
            stop_action.triggered.connect(self.stop_trading)
            toolbar.addAction(stop_action)
            
            toolbar.addSeparator()
            
            # 策略回测
            backtest_action = QAction(QIcon("resources/icons/backtest.png"), '策略回测', self)
            backtest_action.triggered.connect(self.show_backtest)
            toolbar.addAction(backtest_action)
            
            # 因子分析
            factor_action = QAction(QIcon("resources/icons/factor.png"), '因子分析', self)
            factor_action.triggered.connect(self.show_factor_analysis)
            toolbar.addAction(factor_action)
            
            toolbar.addSeparator()
            
            # 系统设置
            settings_action = QAction(QIcon("resources/icons/settings.png"), '系统设置', self)
            settings_action.triggered.connect(self.show_settings)
            toolbar.addAction(settings_action)
            
        except Exception as e:
            logger.error(f"❌ 创建工具栏失败: {e}")
    
    def create_status_bar(self):
        """创建状态栏"""
        try:
            statusbar = self.statusBar()
            
            # 状态标签
            self.status_label = QLabel("系统就绪")
            statusbar.addWidget(self.status_label)
            
            # 进度条
            self.progress_bar = QProgressBar()
            self.progress_bar.setVisible(False)
            self.progress_bar.setMaximumWidth(200)
            statusbar.addPermanentWidget(self.progress_bar)
            
            # 时间标签
            self.time_label = QLabel()
            statusbar.addPermanentWidget(self.time_label)
            
            # 连接状态标签
            self.connection_label = QLabel("未连接")
            self.connection_label.setStyleSheet("color: red;")
            statusbar.addPermanentWidget(self.connection_label)
            
        except Exception as e:
            logger.error(f"❌ 创建状态栏失败: {e}")
    
    def create_central_widget(self):
        """创建中央窗口"""
        try:
            # 创建中央分割器
            central_splitter = QSplitter(Qt.Horizontal)
            self.setCentralWidget(central_splitter)
            
            # 左侧面板 - 市场监控和图表
            left_widget = QWidget()
            left_layout = QVBoxLayout(left_widget)
            
            # 图表窗口
            self.chart_widget = ChartWidget()
            left_layout.addWidget(self.chart_widget)
            
            central_splitter.addWidget(left_widget)
            
            # 右侧面板 - 功能标签页
            self.tab_widget = QTabWidget()
            
            # 市场监控
            self.market_monitor = MarketMonitorWidget()
            self.tab_widget.addTab(self.market_monitor, "市场监控")
            
            # 因子管理
            self.factor_manager = FactorManagerWidget()
            self.tab_widget.addTab(self.factor_manager, "因子管理")
            
            # 策略管理
            self.strategy_manager = StrategyManagerWidget()
            self.tab_widget.addTab(self.strategy_manager, "策略管理")
            
            # 投资组合
            self.portfolio_manager = PortfolioManagerWidget()
            self.tab_widget.addTab(self.portfolio_manager, "投资组合")
            
            # 回测管理
            self.backtest_manager = BacktestManagerWidget()
            self.tab_widget.addTab(self.backtest_manager, "回测管理")
            
            # 交易管理
            self.trade_manager = TradeManagerWidget()
            self.tab_widget.addTab(self.trade_manager, "交易管理")
            
            central_splitter.addWidget(self.tab_widget)
            
            # 设置分割比例
            central_splitter.setSizes([800, 600])
            
            # 保存组件引用
            self.widgets.update({
                'chart': self.chart_widget,
                'market_monitor': self.market_monitor,
                'factor_manager': self.factor_manager,
                'strategy_manager': self.strategy_manager,
                'portfolio_manager': self.portfolio_manager,
                'backtest_manager': self.backtest_manager,
                'trade_manager': self.trade_manager
            })
            
        except Exception as e:
            logger.error(f"❌ 创建中央窗口失败: {e}")
    
    def create_dock_widgets(self):
        """创建停靠窗口"""
        try:
            # 日志查看器
            log_dock = QDockWidget("系统日志", self)
            self.log_viewer = LogViewerWidget()
            log_dock.setWidget(self.log_viewer)
            self.addDockWidget(Qt.BottomDockWidgetArea, log_dock)
            
            # 系统监控
            monitor_dock = QDockWidget("系统监控", self)
            self.system_monitor = SystemMonitorWidget()
            monitor_dock.setWidget(self.system_monitor)
            self.addDockWidget(Qt.RightDockWidgetArea, monitor_dock)
            
            # 保存组件引用
            self.widgets.update({
                'log_viewer': self.log_viewer,
                'system_monitor': self.system_monitor
            })
            
        except Exception as e:
            logger.error(f"❌ 创建停靠窗口失败: {e}")
    
    def create_system_tray(self):
        """创建系统托盘"""
        try:
            if QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon = QSystemTrayIcon(self)
                self.tray_icon.setIcon(QIcon("resources/icons/app_icon.png"))
                
                # 托盘菜单
                tray_menu = QMenu()
                
                show_action = tray_menu.addAction("显示主窗口")
                show_action.triggered.connect(self.show)
                
                tray_menu.addSeparator()
                
                quit_action = tray_menu.addAction("退出")
                quit_action.triggered.connect(self.close)
                
                self.tray_icon.setContextMenu(tray_menu)
                self.tray_icon.show()
                
                # 双击托盘图标显示窗口
                self.tray_icon.activated.connect(self.tray_icon_activated)
                
        except Exception as e:
            logger.error(f"❌ 创建系统托盘失败: {e}")
    
    def connect_signals(self):
        """连接信号"""
        try:
            # 状态更新信号
            self.status_update.connect(self.update_status_label)
            self.progress_update.connect(self.update_progress_bar)
            
        except Exception as e:
            logger.error(f"❌ 连接信号失败: {e}")
    
    def restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 从配置文件恢复窗口状态
            geometry = self.config_manager.get_config('window.geometry')
            if geometry:
                self.restoreGeometry(geometry)
            
            state = self.config_manager.get_config('window.state')
            if state:
                self.restoreState(state)
                
        except Exception as e:
            logger.error(f"❌ 恢复窗口状态失败: {e}")
    
    def save_window_state(self):
        """保存窗口状态"""
        try:
            self.config_manager.set_config('window.geometry', self.saveGeometry())
            self.config_manager.set_config('window.state', self.saveState())
            
        except Exception as e:
            logger.error(f"❌ 保存窗口状态失败: {e}")
    
    # 菜单和工具栏事件处理
    def new_strategy(self):
        """新建策略"""
        self.strategy_manager.new_strategy()
    
    def open_strategy(self):
        """打开策略"""
        self.strategy_manager.open_strategy()
    
    def import_data(self):
        """导入数据"""
        # TODO: 实现数据导入功能
        self.status_update.emit("导入数据功能开发中...")
    
    def export_data(self):
        """导出数据"""
        # TODO: 实现数据导出功能
        self.status_update.emit("导出数据功能开发中...")
    
    def start_trading(self):
        """启动交易"""
        self.trade_manager.start_trading()
        self.status_update.emit("交易已启动")
    
    def stop_trading(self):
        """停止交易"""
        self.trade_manager.stop_trading()
        self.status_update.emit("交易已停止")
    
    def manual_order(self):
        """手动下单"""
        self.trade_manager.show_manual_order_dialog()
    
    def show_factor_analysis(self):
        """显示因子分析"""
        self.tab_widget.setCurrentWidget(self.factor_manager)
    
    def show_backtest(self):
        """显示回测"""
        self.tab_widget.setCurrentWidget(self.backtest_manager)
    
    def show_performance(self):
        """显示绩效分析"""
        self.portfolio_manager.show_performance_analysis()
    
    def show_settings(self):
        """显示系统设置"""
        # TODO: 实现设置对话框
        self.status_update.emit("系统设置功能开发中...")
    
    def show_log_viewer(self):
        """显示日志查看器"""
        # 确保日志停靠窗口可见
        for dock in self.findChildren(QDockWidget):
            if dock.windowTitle() == "系统日志":
                dock.show()
                dock.raise_()
                break
    
    def show_system_monitor(self):
        """显示系统监控"""
        # 确保监控停靠窗口可见
        for dock in self.findChildren(QDockWidget):
            if dock.windowTitle() == "系统监控":
                dock.show()
                dock.raise_()
                break
    
    def show_manual(self):
        """显示用户手册"""
        # TODO: 实现用户手册
        self.status_update.emit("用户手册功能开发中...")
    
    def show_about(self):
        """显示关于对话框"""
        # TODO: 实现关于对话框
        self.status_update.emit("关于对话框功能开发中...")
    
    # 状态更新
    def update_status(self):
        """更新状态"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)
            
            # 更新连接状态
            # TODO: 实际检查连接状态
            self.connection_label.setText("已连接")
            self.connection_label.setStyleSheet("color: green;")
            
        except Exception as e:
            logger.error(f"❌ 更新状态失败: {e}")
    
    def update_status_label(self, message: str):
        """更新状态标签"""
        if self.status_label:
            self.status_label.setText(message)
    
    def update_progress_bar(self, value: int):
        """更新进度条"""
        if self.progress_bar:
            if value < 0:
                self.progress_bar.setVisible(False)
            else:
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(value)
    
    # 系统托盘事件
    def tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()
    
    # 窗口事件
    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 保存窗口状态
            self.save_window_state()
            
            # 停止定时器
            self.update_timer.stop()
            
            # 清理资源
            for widget in self.widgets.values():
                if hasattr(widget, 'cleanup'):
                    widget.cleanup()
            
            event.accept()
            
        except Exception as e:
            logger.error(f"❌ 关闭窗口失败: {e}")
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("量化交易系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Quantitative Trading")
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
