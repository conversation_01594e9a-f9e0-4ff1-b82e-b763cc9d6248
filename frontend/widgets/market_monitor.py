"""
市场监控组件
显示实时市场数据、股票行情等信息
"""

import logging
from typing import Dict, List, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QLabel, QPushButton, QComboBox, QLineEdit, QSplitter,
    QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

logger = logging.getLogger(__name__)

class MarketMonitorWidget(QWidget):
    """市场监控组件"""
    
    # 信号定义
    symbol_selected = pyqtSignal(str)  # 股票选中信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据缓存
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.selected_symbols: List[str] = []
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_market_data)
        
        # 初始化界面
        self.init_ui()
        
        # 启动数据更新
        self.update_timer.start(1000)  # 每秒更新
        
        logger.info("📊 市场监控组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            layout = QVBoxLayout(self)
            
            # 创建控制面板
            control_panel = self.create_control_panel()
            layout.addWidget(control_panel)
            
            # 创建分割器
            splitter = QSplitter(Qt.Vertical)
            layout.addWidget(splitter)
            
            # 创建市场概览
            market_overview = self.create_market_overview()
            splitter.addWidget(market_overview)
            
            # 创建股票列表
            stock_table = self.create_stock_table()
            splitter.addWidget(stock_table)
            
            # 设置分割比例
            splitter.setSizes([200, 400])
            
        except Exception as e:
            logger.error(f"❌ 初始化市场监控界面失败: {e}")
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        try:
            panel = QWidget()
            layout = QHBoxLayout(panel)
            
            # 市场选择
            market_label = QLabel("市场:")
            self.market_combo = QComboBox()
            self.market_combo.addItems(["沪深A股", "创业板", "科创板", "港股", "美股"])
            self.market_combo.currentTextChanged.connect(self.on_market_changed)
            
            # 搜索框
            search_label = QLabel("搜索:")
            self.search_edit = QLineEdit()
            self.search_edit.setPlaceholderText("输入股票代码或名称")
            self.search_edit.textChanged.connect(self.on_search_changed)
            
            # 刷新按钮
            self.refresh_btn = QPushButton("刷新")
            self.refresh_btn.clicked.connect(self.refresh_data)
            
            # 自动刷新开关
            self.auto_refresh_btn = QPushButton("自动刷新: 开")
            self.auto_refresh_btn.setCheckable(True)
            self.auto_refresh_btn.setChecked(True)
            self.auto_refresh_btn.clicked.connect(self.toggle_auto_refresh)
            
            # 布局
            layout.addWidget(market_label)
            layout.addWidget(self.market_combo)
            layout.addWidget(search_label)
            layout.addWidget(self.search_edit)
            layout.addWidget(self.refresh_btn)
            layout.addWidget(self.auto_refresh_btn)
            layout.addStretch()
            
            return panel
            
        except Exception as e:
            logger.error(f"❌ 创建控制面板失败: {e}")
            return QWidget()
    
    def create_market_overview(self) -> QWidget:
        """创建市场概览"""
        try:
            overview = QGroupBox("市场概览")
            layout = QGridLayout(overview)
            
            # 创建指标标签
            self.index_labels = {}
            
            # 主要指数
            indices = [
                ("上证指数", "000001"),
                ("深证成指", "399001"),
                ("创业板指", "399006"),
                ("科创50", "000688")
            ]
            
            for i, (name, code) in enumerate(indices):
                # 指数名称
                name_label = QLabel(name)
                name_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                layout.addWidget(name_label, i, 0)
                
                # 指数值
                value_label = QLabel("--")
                value_label.setAlignment(Qt.AlignRight)
                layout.addWidget(value_label, i, 1)
                
                # 涨跌幅
                change_label = QLabel("--")
                change_label.setAlignment(Qt.AlignRight)
                layout.addWidget(change_label, i, 2)
                
                self.index_labels[code] = {
                    'value': value_label,
                    'change': change_label
                }
            
            return overview
            
        except Exception as e:
            logger.error(f"❌ 创建市场概览失败: {e}")
            return QGroupBox("市场概览")
    
    def create_stock_table(self) -> QWidget:
        """创建股票列表表格"""
        try:
            # 创建表格
            self.stock_table = QTableWidget()
            
            # 设置列
            columns = [
                "代码", "名称", "最新价", "涨跌幅", "涨跌额", 
                "成交量", "成交额", "换手率", "市盈率", "市净率"
            ]
            
            self.stock_table.setColumnCount(len(columns))
            self.stock_table.setHorizontalHeaderLabels(columns)
            
            # 设置表格属性
            self.stock_table.setAlternatingRowColors(True)
            self.stock_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.stock_table.setSelectionMode(QTableWidget.SingleSelection)
            
            # 设置列宽
            header = self.stock_table.horizontalHeader()
            header.setStretchLastSection(True)
            header.resizeSection(0, 80)   # 代码
            header.resizeSection(1, 100)  # 名称
            header.resizeSection(2, 80)   # 最新价
            header.resizeSection(3, 80)   # 涨跌幅
            header.resizeSection(4, 80)   # 涨跌额
            
            # 连接信号
            self.stock_table.itemSelectionChanged.connect(self.on_stock_selected)
            
            # 初始化数据
            self.init_stock_data()
            
            return self.stock_table
            
        except Exception as e:
            logger.error(f"❌ 创建股票表格失败: {e}")
            return QTableWidget()
    
    def init_stock_data(self):
        """初始化股票数据"""
        try:
            # 模拟股票数据
            sample_stocks = [
                ("000001", "平安银行", 12.50, 2.45, 0.30, 1234567, 15432100, 1.23, 8.5, 0.85),
                ("000002", "万科A", 18.90, -1.25, -0.24, 2345678, 44321000, 2.15, 12.3, 1.45),
                ("600000", "浦发银行", 9.80, 0.51, 0.05, 3456789, 33876500, 0.98, 6.2, 0.72),
                ("600036", "招商银行", 45.60, 1.78, 0.80, 4567890, 208234000, 1.87, 9.8, 1.12),
                ("000858", "五粮液", 168.50, -0.89, -1.51, 5678901, 956789000, 0.65, 28.5, 4.23),
            ]
            
            self.stock_table.setRowCount(len(sample_stocks))
            
            for row, stock_data in enumerate(sample_stocks):
                for col, value in enumerate(stock_data):
                    item = QTableWidgetItem(str(value))
                    
                    # 设置数值对齐
                    if col >= 2:  # 数值列右对齐
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    
                    # 设置涨跌颜色
                    if col == 3:  # 涨跌幅列
                        if value > 0:
                            item.setForeground(Qt.red)
                        elif value < 0:
                            item.setForeground(Qt.green)
                    elif col == 4:  # 涨跌额列
                        if value > 0:
                            item.setForeground(Qt.red)
                        elif value < 0:
                            item.setForeground(Qt.green)
                    
                    self.stock_table.setItem(row, col, item)
            
        except Exception as e:
            logger.error(f"❌ 初始化股票数据失败: {e}")
    
    def update_market_data(self):
        """更新市场数据"""
        try:
            if not self.auto_refresh_btn.isChecked():
                return
            
            # 更新指数数据
            self.update_index_data()
            
            # 更新股票数据
            self.update_stock_data()
            
        except Exception as e:
            logger.error(f"❌ 更新市场数据失败: {e}")
    
    def update_index_data(self):
        """更新指数数据"""
        try:
            # 模拟指数数据更新
            import random
            
            for code, labels in self.index_labels.items():
                # 生成模拟数据
                base_value = {
                    "000001": 3200,
                    "399001": 12000,
                    "399006": 2800,
                    "000688": 1200
                }.get(code, 3000)
                
                # 随机波动
                current_value = base_value + random.uniform(-50, 50)
                change_pct = random.uniform(-3, 3)
                
                # 更新显示
                labels['value'].setText(f"{current_value:.2f}")
                
                change_text = f"{change_pct:+.2f}%"
                labels['change'].setText(change_text)
                
                # 设置颜色
                if change_pct > 0:
                    labels['change'].setStyleSheet("color: red;")
                elif change_pct < 0:
                    labels['change'].setStyleSheet("color: green;")
                else:
                    labels['change'].setStyleSheet("color: white;")
            
        except Exception as e:
            logger.error(f"❌ 更新指数数据失败: {e}")
    
    def update_stock_data(self):
        """更新股票数据"""
        try:
            # 模拟股票数据更新
            import random
            
            for row in range(self.stock_table.rowCount()):
                # 更新价格相关列
                price_item = self.stock_table.item(row, 2)  # 最新价
                change_pct_item = self.stock_table.item(row, 3)  # 涨跌幅
                change_amt_item = self.stock_table.item(row, 4)  # 涨跌额
                
                if price_item and change_pct_item and change_amt_item:
                    # 生成随机变化
                    current_price = float(price_item.text())
                    price_change = random.uniform(-0.5, 0.5)
                    new_price = max(0.01, current_price + price_change)
                    
                    change_pct = (price_change / current_price) * 100
                    
                    # 更新显示
                    price_item.setText(f"{new_price:.2f}")
                    change_pct_item.setText(f"{change_pct:+.2f}%")
                    change_amt_item.setText(f"{price_change:+.2f}")
                    
                    # 设置颜色
                    color = Qt.red if price_change > 0 else Qt.green if price_change < 0 else Qt.white
                    change_pct_item.setForeground(color)
                    change_amt_item.setForeground(color)
            
        except Exception as e:
            logger.error(f"❌ 更新股票数据失败: {e}")
    
    def on_market_changed(self, market: str):
        """市场选择改变事件"""
        try:
            logger.info(f"切换市场: {market}")
            # TODO: 根据市场加载对应股票数据
            
        except Exception as e:
            logger.error(f"❌ 市场切换失败: {e}")
    
    def on_search_changed(self, text: str):
        """搜索文本改变事件"""
        try:
            # 简单的表格过滤
            for row in range(self.stock_table.rowCount()):
                code_item = self.stock_table.item(row, 0)
                name_item = self.stock_table.item(row, 1)
                
                if code_item and name_item:
                    code = code_item.text()
                    name = name_item.text()
                    
                    # 检查是否匹配搜索条件
                    match = (text.lower() in code.lower() or 
                            text.lower() in name.lower() or 
                            text == "")
                    
                    self.stock_table.setRowHidden(row, not match)
            
        except Exception as e:
            logger.error(f"❌ 搜索过滤失败: {e}")
    
    def on_stock_selected(self):
        """股票选中事件"""
        try:
            current_row = self.stock_table.currentRow()
            if current_row >= 0:
                code_item = self.stock_table.item(current_row, 0)
                if code_item:
                    symbol = code_item.text()
                    logger.info(f"选中股票: {symbol}")
                    self.symbol_selected.emit(symbol)
            
        except Exception as e:
            logger.error(f"❌ 股票选中处理失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            logger.info("手动刷新市场数据")
            self.update_market_data()
            
        except Exception as e:
            logger.error(f"❌ 刷新数据失败: {e}")
    
    def toggle_auto_refresh(self, checked: bool):
        """切换自动刷新"""
        try:
            if checked:
                self.auto_refresh_btn.setText("自动刷新: 开")
                self.update_timer.start(1000)
            else:
                self.auto_refresh_btn.setText("自动刷新: 关")
                self.update_timer.stop()
            
            logger.info(f"自动刷新: {'开启' if checked else '关闭'}")
            
        except Exception as e:
            logger.error(f"❌ 切换自动刷新失败: {e}")
    
    def add_symbol(self, symbol: str):
        """添加股票到监控列表"""
        try:
            if symbol not in self.selected_symbols:
                self.selected_symbols.append(symbol)
                logger.info(f"添加股票到监控: {symbol}")
            
        except Exception as e:
            logger.error(f"❌ 添加股票失败: {e}")
    
    def remove_symbol(self, symbol: str):
        """从监控列表移除股票"""
        try:
            if symbol in self.selected_symbols:
                self.selected_symbols.remove(symbol)
                logger.info(f"从监控移除股票: {symbol}")
            
        except Exception as e:
            logger.error(f"❌ 移除股票失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.update_timer.stop()
            logger.info("市场监控组件资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理市场监控资源失败: {e}")
