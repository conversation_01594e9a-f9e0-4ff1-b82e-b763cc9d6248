"""
样式管理器
管理vn.py风格的界面样式
"""

import logging
from typing import Dict, Any
from PyQt5.QtWidgets import QWidget, QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPalette, QColor, QFont

logger = logging.getLogger(__name__)

class StyleManager:
    """样式管理器"""
    
    def __init__(self):
        # vn.py经典配色方案
        self.colors = {
            # 主色调
            'primary': '#1e1e1e',           # 主背景色
            'secondary': '#2d2d2d',         # 次要背景色
            'accent': '#3d3d3d',            # 强调色
            
            # 文本颜色
            'text_primary': '#ffffff',       # 主文本
            'text_secondary': '#cccccc',     # 次要文本
            'text_disabled': '#666666',      # 禁用文本
            
            # 状态颜色
            'success': '#00ff00',           # 成功/上涨
            'danger': '#ff0000',            # 危险/下跌
            'warning': '#ffff00',           # 警告
            'info': '#00ffff',              # 信息
            
            # 边框颜色
            'border': '#555555',            # 边框
            'border_light': '#777777',      # 浅边框
            
            # 选中状态
            'selection': '#0078d4',         # 选中背景
            'hover': '#404040',             # 悬停背景
        }
        
        # 字体配置
        self.fonts = {
            'default': ('Microsoft YaHei', 9),
            'title': ('Microsoft YaHei', 12, True),
            'code': ('Consolas', 9),
            'number': ('Arial', 9)
        }
        
        logger.info("🎨 样式管理器初始化完成")
    
    def apply_vnpy_style(self, widget: QWidget):
        """应用vn.py风格样式"""
        try:
            # 设置全局样式表
            style_sheet = self._generate_global_stylesheet()
            widget.setStyleSheet(style_sheet)
            
            # 设置调色板
            self._set_palette(widget)
            
            # 设置字体
            self._set_fonts(widget)
            
            logger.info("✅ 应用vn.py样式成功")
            
        except Exception as e:
            logger.error(f"❌ 应用样式失败: {e}")
    
    def _generate_global_stylesheet(self) -> str:
        """生成全局样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {self.colors['primary']};
            color: {self.colors['text_primary']};
        }}
        
        /* 菜单栏样式 */
        QMenuBar {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border-bottom: 1px solid {self.colors['border']};
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 4px 8px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {self.colors['hover']};
        }}
        
        QMenu {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
        }}
        
        QMenu::item {{
            padding: 4px 20px;
        }}
        
        QMenu::item:selected {{
            background-color: {self.colors['selection']};
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {self.colors['secondary']};
            border: 1px solid {self.colors['border']};
            spacing: 2px;
        }}
        
        QToolButton {{
            background-color: transparent;
            border: none;
            padding: 4px;
            margin: 1px;
        }}
        
        QToolButton:hover {{
            background-color: {self.colors['hover']};
        }}
        
        QToolButton:pressed {{
            background-color: {self.colors['selection']};
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border-top: 1px solid {self.colors['border']};
        }}
        
        /* 标签页样式 */
        QTabWidget::pane {{
            border: 1px solid {self.colors['border']};
            background-color: {self.colors['primary']};
        }}
        
        QTabBar::tab {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            padding: 6px 12px;
            margin-right: 2px;
            border: 1px solid {self.colors['border']};
            border-bottom: none;
        }}
        
        QTabBar::tab:selected {{
            background-color: {self.colors['primary']};
            border-bottom: 2px solid {self.colors['selection']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {self.colors['hover']};
        }}
        
        /* 停靠窗口样式 */
        QDockWidget {{
            color: {self.colors['text_primary']};
            titlebar-close-icon: url(resources/icons/close.png);
            titlebar-normal-icon: url(resources/icons/float.png);
        }}
        
        QDockWidget::title {{
            background-color: {self.colors['secondary']};
            padding: 4px;
            border: 1px solid {self.colors['border']};
        }}
        
        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {self.colors['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        
        /* 表格样式 */
        QTableWidget {{
            background-color: {self.colors['primary']};
            alternate-background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            gridline-color: {self.colors['border']};
            selection-background-color: {self.colors['selection']};
        }}
        
        QTableWidget::item {{
            padding: 4px;
            border: none;
        }}
        
        QHeaderView::section {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            padding: 4px;
            border: 1px solid {self.colors['border']};
            font-weight: bold;
        }}
        
        /* 树形控件样式 */
        QTreeWidget {{
            background-color: {self.colors['primary']};
            color: {self.colors['text_primary']};
            selection-background-color: {self.colors['selection']};
            outline: none;
        }}
        
        QTreeWidget::item {{
            padding: 2px;
            border: none;
        }}
        
        QTreeWidget::item:hover {{
            background-color: {self.colors['hover']};
        }}
        
        QTreeWidget::item:selected {{
            background-color: {self.colors['selection']};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            padding: 6px 12px;
            border-radius: 2px;
        }}
        
        QPushButton:hover {{
            background-color: {self.colors['hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {self.colors['selection']};
        }}
        
        QPushButton:disabled {{
            color: {self.colors['text_disabled']};
            background-color: {self.colors['accent']};
        }}
        
        /* 输入框样式 */
        QLineEdit {{
            background-color: {self.colors['primary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            padding: 4px;
            border-radius: 2px;
        }}
        
        QLineEdit:focus {{
            border: 2px solid {self.colors['selection']};
        }}
        
        QTextEdit {{
            background-color: {self.colors['primary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            selection-background-color: {self.colors['selection']};
        }}
        
        /* 组合框样式 */
        QComboBox {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            padding: 4px;
            border-radius: 2px;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: url(resources/icons/arrow_down.png);
            width: 12px;
            height: 12px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            selection-background-color: {self.colors['selection']};
            border: 1px solid {self.colors['border']};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {self.colors['secondary']};
            width: 12px;
            border: none;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.colors['border']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.colors['border_light']};
        }}
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        QScrollBar:horizontal {{
            background-color: {self.colors['secondary']};
            height: 12px;
            border: none;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {self.colors['border']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {self.colors['border_light']};
        }}
        
        QScrollBar::add-line:horizontal,
        QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            background-color: {self.colors['secondary']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            border-radius: 2px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {self.colors['selection']};
            border-radius: 2px;
        }}
        
        /* 标签样式 */
        QLabel {{
            color: {self.colors['text_primary']};
        }}
        
        /* 分组框样式 */
        QGroupBox {{
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 4px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }}
        
        /* 复选框和单选框样式 */
        QCheckBox {{
            color: {self.colors['text_primary']};
        }}
        
        QRadioButton {{
            color: {self.colors['text_primary']};
        }}
        
        /* 数值显示样式 */
        .price-up {{
            color: {self.colors['success']};
            font-weight: bold;
        }}
        
        .price-down {{
            color: {self.colors['danger']};
            font-weight: bold;
        }}
        
        .price-neutral {{
            color: {self.colors['text_primary']};
        }}
        """
    
    def _set_palette(self, widget: QWidget):
        """设置调色板"""
        try:
            palette = QPalette()
            
            # 窗口背景
            palette.setColor(QPalette.Window, QColor(self.colors['primary']))
            palette.setColor(QPalette.WindowText, QColor(self.colors['text_primary']))
            
            # 基础背景
            palette.setColor(QPalette.Base, QColor(self.colors['primary']))
            palette.setColor(QPalette.AlternateBase, QColor(self.colors['secondary']))
            
            # 文本
            palette.setColor(QPalette.Text, QColor(self.colors['text_primary']))
            palette.setColor(QPalette.BrightText, QColor(self.colors['text_primary']))
            
            # 按钮
            palette.setColor(QPalette.Button, QColor(self.colors['secondary']))
            palette.setColor(QPalette.ButtonText, QColor(self.colors['text_primary']))
            
            # 高亮
            palette.setColor(QPalette.Highlight, QColor(self.colors['selection']))
            palette.setColor(QPalette.HighlightedText, QColor(self.colors['text_primary']))
            
            widget.setPalette(palette)
            
        except Exception as e:
            logger.error(f"❌ 设置调色板失败: {e}")
    
    def _set_fonts(self, widget: QWidget):
        """设置字体"""
        try:
            # 设置默认字体
            font_family, font_size = self.fonts['default']
            default_font = QFont(font_family, font_size)
            widget.setFont(default_font)
            
            # 设置应用程序默认字体
            QApplication.setFont(default_font)
            
        except Exception as e:
            logger.error(f"❌ 设置字体失败: {e}")
    
    def get_color(self, color_name: str) -> str:
        """获取颜色值"""
        return self.colors.get(color_name, '#ffffff')
    
    def get_font(self, font_name: str) -> QFont:
        """获取字体"""
        try:
            font_config = self.fonts.get(font_name, self.fonts['default'])
            
            if len(font_config) == 2:
                family, size = font_config
                return QFont(family, size)
            elif len(font_config) == 3:
                family, size, bold = font_config
                font = QFont(family, size)
                font.setBold(bold)
                return font
            else:
                return QFont()
                
        except Exception as e:
            logger.error(f"❌ 获取字体失败: {font_name} - {e}")
            return QFont()
    
    def create_price_style(self, value: float, reference: float = 0) -> str:
        """创建价格样式"""
        try:
            if value > reference:
                return f"color: {self.colors['success']}; font-weight: bold;"
            elif value < reference:
                return f"color: {self.colors['danger']}; font-weight: bold;"
            else:
                return f"color: {self.colors['text_primary']};"
                
        except Exception as e:
            logger.error(f"❌ 创建价格样式失败: {e}")
            return ""
    
    def create_status_style(self, status: str) -> str:
        """创建状态样式"""
        try:
            status_colors = {
                'success': self.colors['success'],
                'error': self.colors['danger'],
                'warning': self.colors['warning'],
                'info': self.colors['info'],
                'normal': self.colors['text_primary']
            }
            
            color = status_colors.get(status.lower(), self.colors['text_primary'])
            return f"color: {color};"
            
        except Exception as e:
            logger.error(f"❌ 创建状态样式失败: {e}")
            return ""
