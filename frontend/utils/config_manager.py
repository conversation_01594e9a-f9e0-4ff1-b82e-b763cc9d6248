"""
UI配置管理器
管理前端界面的配置信息
"""

import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path
from PyQt5.QtCore import QSettings

logger = logging.getLogger(__name__)

class UIConfigManager:
    """UI配置管理器"""
    
    def __init__(self, config_file: str = "ui_config.json"):
        self.config_file = Path(config_file)
        
        # Qt设置对象
        self.qt_settings = QSettings("QuantitativeTrading", "TradingSystem")
        
        # 内存配置缓存
        self.config_cache: Dict[str, Any] = {}
        
        # 默认配置
        self.default_configs = {
            # 窗口配置
            'window.width': 1600,
            'window.height': 1000,
            'window.maximized': False,
            'window.remember_position': True,
            
            # 界面配置
            'ui.theme': 'dark',
            'ui.language': 'zh_CN',
            'ui.font_size': 9,
            'ui.show_toolbar': True,
            'ui.show_statusbar': True,
            
            # 图表配置
            'chart.default_period': '1d',
            'chart.show_volume': True,
            'chart.show_indicators': True,
            'chart.max_bars': 1000,
            'chart.auto_scale': True,
            
            # 表格配置
            'table.auto_resize': True,
            'table.show_grid': True,
            'table.alternate_colors': True,
            'table.row_height': 25,
            
            # 数据更新配置
            'data.update_interval': 1000,  # 毫秒
            'data.auto_refresh': True,
            'data.cache_size': 10000,
            
            # 交易配置
            'trading.confirm_orders': True,
            'trading.show_notifications': True,
            'trading.sound_alerts': False,
            
            # 日志配置
            'log.max_lines': 1000,
            'log.auto_scroll': True,
            'log.show_timestamp': True,
            'log.filter_level': 'INFO'
        }
        
        # 加载配置
        self.load_configs()
        
        logger.info("⚙️ UI配置管理器初始化完成")
        logger.info(f"  - 配置文件: {self.config_file}")
    
    def load_configs(self):
        """加载配置"""
        try:
            # 从文件加载
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_configs = json.load(f)
                    self.config_cache.update(file_configs)
            
            # 从Qt设置加载
            for key in self.default_configs:
                qt_value = self.qt_settings.value(key)
                if qt_value is not None:
                    self.config_cache[key] = qt_value
            
            # 填充默认值
            for key, default_value in self.default_configs.items():
                if key not in self.config_cache:
                    self.config_cache[key] = default_value
            
            logger.info(f"✅ 加载UI配置: {len(self.config_cache)}项")
            
        except Exception as e:
            logger.error(f"❌ 加载UI配置失败: {e}")
            # 使用默认配置
            self.config_cache = self.default_configs.copy()
    
    def save_configs(self):
        """保存配置"""
        try:
            # 保存到文件
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_cache, f, indent=2, ensure_ascii=False)
            
            # 保存到Qt设置
            for key, value in self.config_cache.items():
                self.qt_settings.setValue(key, value)
            
            self.qt_settings.sync()
            
            logger.info(f"✅ 保存UI配置: {len(self.config_cache)}项")
            
        except Exception as e:
            logger.error(f"❌ 保存UI配置失败: {e}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            return self.config_cache.get(key, default)
        except Exception as e:
            logger.error(f"❌ 获取配置失败: {key} - {e}")
            return default
    
    def set_config(self, key: str, value: Any, save_immediately: bool = True):
        """设置配置值"""
        try:
            self.config_cache[key] = value
            
            if save_immediately:
                # 保存到Qt设置
                self.qt_settings.setValue(key, value)
                self.qt_settings.sync()
            
            logger.debug(f"✅ 设置UI配置: {key} = {value}")
            
        except Exception as e:
            logger.error(f"❌ 设置UI配置失败: {key} - {e}")
    
    def get_window_config(self) -> Dict[str, Any]:
        """获取窗口配置"""
        return {
            'width': self.get_config('window.width', 1600),
            'height': self.get_config('window.height', 1000),
            'maximized': self.get_config('window.maximized', False),
            'remember_position': self.get_config('window.remember_position', True)
        }
    
    def set_window_config(self, config: Dict[str, Any]):
        """设置窗口配置"""
        for key, value in config.items():
            self.set_config(f'window.{key}', value, False)
        self.save_configs()
    
    def get_chart_config(self) -> Dict[str, Any]:
        """获取图表配置"""
        return {
            'default_period': self.get_config('chart.default_period', '1d'),
            'show_volume': self.get_config('chart.show_volume', True),
            'show_indicators': self.get_config('chart.show_indicators', True),
            'max_bars': self.get_config('chart.max_bars', 1000),
            'auto_scale': self.get_config('chart.auto_scale', True)
        }
    
    def set_chart_config(self, config: Dict[str, Any]):
        """设置图表配置"""
        for key, value in config.items():
            self.set_config(f'chart.{key}', value, False)
        self.save_configs()
    
    def get_table_config(self) -> Dict[str, Any]:
        """获取表格配置"""
        return {
            'auto_resize': self.get_config('table.auto_resize', True),
            'show_grid': self.get_config('table.show_grid', True),
            'alternate_colors': self.get_config('table.alternate_colors', True),
            'row_height': self.get_config('table.row_height', 25)
        }
    
    def set_table_config(self, config: Dict[str, Any]):
        """设置表格配置"""
        for key, value in config.items():
            self.set_config(f'table.{key}', value, False)
        self.save_configs()
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置"""
        return {
            'update_interval': self.get_config('data.update_interval', 1000),
            'auto_refresh': self.get_config('data.auto_refresh', True),
            'cache_size': self.get_config('data.cache_size', 10000)
        }
    
    def set_data_config(self, config: Dict[str, Any]):
        """设置数据配置"""
        for key, value in config.items():
            self.set_config(f'data.{key}', value, False)
        self.save_configs()
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        return {
            'confirm_orders': self.get_config('trading.confirm_orders', True),
            'show_notifications': self.get_config('trading.show_notifications', True),
            'sound_alerts': self.get_config('trading.sound_alerts', False)
        }
    
    def set_trading_config(self, config: Dict[str, Any]):
        """设置交易配置"""
        for key, value in config.items():
            self.set_config(f'trading.{key}', value, False)
        self.save_configs()
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'max_lines': self.get_config('log.max_lines', 1000),
            'auto_scroll': self.get_config('log.auto_scroll', True),
            'show_timestamp': self.get_config('log.show_timestamp', True),
            'filter_level': self.get_config('log.filter_level', 'INFO')
        }
    
    def set_log_config(self, config: Dict[str, Any]):
        """设置日志配置"""
        for key, value in config.items():
            self.set_config(f'log.{key}', value, False)
        self.save_configs()
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        try:
            self.config_cache = self.default_configs.copy()
            self.save_configs()
            
            logger.info("✅ 重置UI配置为默认值")
            
        except Exception as e:
            logger.error(f"❌ 重置UI配置失败: {e}")
    
    def export_configs(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            export_path = Path(file_path)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_cache, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 导出UI配置到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出UI配置失败: {e}")
            return False
    
    def import_configs(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            import_path = Path(file_path)
            
            if not import_path.exists():
                logger.error(f"❌ 配置文件不存在: {file_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_configs = json.load(f)
            
            # 验证配置键
            valid_configs = {}
            for key, value in imported_configs.items():
                if key in self.default_configs:
                    valid_configs[key] = value
                else:
                    logger.warning(f"⚠️ 忽略未知配置: {key}")
            
            self.config_cache.update(valid_configs)
            self.save_configs()
            
            logger.info(f"✅ 导入UI配置: {len(valid_configs)}项")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导入UI配置失败: {e}")
            return False
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_cache.copy()
    
    def validate_config(self, key: str, value: Any) -> bool:
        """验证配置值"""
        try:
            # 基本类型检查
            if key not in self.default_configs:
                return False
            
            default_value = self.default_configs[key]
            expected_type = type(default_value)
            
            if not isinstance(value, expected_type):
                return False
            
            # 特定值范围检查
            if key == 'ui.font_size':
                return 6 <= value <= 20
            elif key == 'data.update_interval':
                return 100 <= value <= 10000
            elif key == 'log.max_lines':
                return 100 <= value <= 10000
            elif key == 'chart.max_bars':
                return 100 <= value <= 10000
            elif key == 'table.row_height':
                return 15 <= value <= 50
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证配置失败: {key} - {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        try:
            # 按类别统计
            categories = {}
            for key in self.config_cache:
                category = key.split('.')[0]
                categories[category] = categories.get(category, 0) + 1
            
            return {
                'total_configs': len(self.config_cache),
                'categories': categories,
                'config_file': str(self.config_file),
                'qt_settings_file': self.qt_settings.fileName()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取配置摘要失败: {e}")
            return {}
