<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易系统 V2.0 - Web控制台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; 
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e); 
            color: #ffffff; 
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        
        .header { 
            text-align: center; 
            padding: 40px 20px; 
            background: linear-gradient(135deg, rgba(45,45,45,0.9), rgba(61,61,61,0.9)); 
            border-radius: 20px; 
            margin-bottom: 30px; 
            box-shadow: 0 15px 35px rgba(0,0,0,0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .header h1 { 
            font-size: 3em; 
            margin-bottom: 15px; 
            background: linear-gradient(45deg, #4CAF50, #45a049, #66BB6A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(76, 175, 80, 0.3);
        }
        
        .status-bar {
            background: linear-gradient(135deg, rgba(61,61,61,0.8), rgba(77,77,77,0.8));
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-dot {
            width: 16px;
            height: 16px;
            background: radial-gradient(circle, #4CAF50, #45a049);
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }
        
        .dashboard-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); 
            gap: 25px; 
            margin-bottom: 40px;
        }
        
        .dashboard-card { 
            background: linear-gradient(135deg, rgba(45,45,45,0.9), rgba(61,61,61,0.9)); 
            padding: 30px; 
            border-radius: 20px; 
            text-align: center;
            transition: all 0.4s ease;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .dashboard-card:hover::before {
            left: 100%;
        }
        
        .dashboard-card:hover { 
            border-color: #4CAF50;
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(76, 175, 80, 0.2);
        }
        
        .dashboard-card h3 {
            margin-bottom: 20px;
            font-size: 1.4em;
            color: #4CAF50;
        }
        
        .dashboard-card .description {
            color: #ccc;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .dashboard-card .metric {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
            margin: 15px 0;
        }
        
        .data-display {
            background: linear-gradient(135deg, rgba(30,30,30,0.9), rgba(45,45,45,0.9));
            padding: 30px;
            border-radius: 20px;
            margin-top: 30px;
            max-height: 500px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .data-display pre {
            color: #4CAF50;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        
        .action-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        
        .action-button:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #888;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 50px;
        }
        
        .real-time-data {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .data-item {
            background: rgba(76, 175, 80, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        .data-item .label {
            font-size: 0.9em;
            color: #ccc;
            margin-bottom: 5px;
        }
        
        .data-item .value {
            font-size: 1.2em;
            font-weight: bold;
            color: #4CAF50;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 2em; }
            .dashboard-grid { grid-template-columns: 1fr; }
            .status-bar { flex-direction: column; gap: 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化交易系统 V2.0</h1>
            <p style="font-size: 1.2em; margin-bottom: 20px;">专业量化交易系统 Web 控制台</p>
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span style="font-size: 1.1em;">系统运行正常</span>
                </div>
                <div id="current-time" style="font-size: 1.1em;"></div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="dashboard-card" onclick="showSystemStatus()">
                <h3>🔧 系统状态</h3>
                <p class="description">查看系统运行状态和模块健康度</p>
                <div class="metric" id="health-score">100%</div>
                <button class="action-button">查看详情</button>
            </div>
            
            <div class="dashboard-card" onclick="showStockData()">
                <h3>📈 股票监控</h3>
                <p class="description">实时股票价格和智能评分</p>
                <div class="metric" id="stock-count">2,847</div>
                <button class="action-button">查看股票</button>
            </div>
            
            <div class="dashboard-card" onclick="showTradingSignals()">
                <h3>📊 交易信号</h3>
                <p class="description">买入卖出信号和策略建议</p>
                <div class="metric" id="signal-count">23</div>
                <button class="action-button">查看信号</button>
            </div>
            
            <div class="dashboard-card" onclick="showPortfolio()">
                <h3>💼 投资组合</h3>
                <p class="description">当前持仓和组合表现</p>
                <div class="metric" id="portfolio-value">¥1,000,000</div>
                <button class="action-button">查看组合</button>
            </div>
            
            <div class="dashboard-card" onclick="showBacktestResults()">
                <h3>🚀 回测结果</h3>
                <p class="description">策略回测和性能分析</p>
                <div class="metric" id="return-rate">+15.6%</div>
                <button class="action-button">查看回测</button>
            </div>
            
            <div class="dashboard-card" onclick="showMarketOverview()">
                <h3>📊 市场概览</h3>
                <p class="description">市场指数和整体表现</p>
                <div class="metric" id="market-trend">📈</div>
                <button class="action-button">查看市场</button>
            </div>
        </div>
        
        <div id="data-container" class="data-display" style="display: none;">
            <h3 id="data-title">数据展示</h3>
            <div id="data-content"></div>
        </div>
        
        <div class="footer">
            <p><strong>量化交易系统 V2.0</strong> | 基于9层架构的智能交易平台</p>
            <p>🌐 Web界面 | 📊 实时数据 | 🧠 智能分析 | 🚀 专业回测</p>
            <p style="margin-top: 15px; color: #4CAF50;">
                <strong>访问方式:</strong> 直接在浏览器中打开此HTML文件
            </p>
        </div>
    </div>
    
    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = 
                now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
        }
        setInterval(updateTime, 1000);
        updateTime();
        
        // 模拟实时数据更新
        function updateMetrics() {
            // 随机更新一些指标
            const healthScore = 95 + Math.random() * 5;
            document.getElementById('health-score').textContent = healthScore.toFixed(1) + '%';
            
            const stockCount = 2800 + Math.floor(Math.random() * 100);
            document.getElementById('stock-count').textContent = stockCount.toLocaleString();
            
            const signalCount = 20 + Math.floor(Math.random() * 10);
            document.getElementById('signal-count').textContent = signalCount;
            
            const portfolioValue = 950000 + Math.random() * 100000;
            document.getElementById('portfolio-value').textContent = 
                '¥' + portfolioValue.toLocaleString('zh-CN', {maximumFractionDigits: 0});
            
            const returnRate = 12 + Math.random() * 8;
            document.getElementById('return-rate').textContent = '+' + returnRate.toFixed(1) + '%';
        }
        setInterval(updateMetrics, 5000);
        updateMetrics();
        
        // 显示数据函数
        function showData(title, content) {
            document.getElementById('data-title').textContent = title;
            document.getElementById('data-content').innerHTML = content;
            document.getElementById('data-container').style.display = 'block';
            document.getElementById('data-container').scrollIntoView({
                behavior: 'smooth'
            });
        }
        
        function showSystemStatus() {
            const content = `
                <div class="real-time-data">
                    <div class="data-item">
                        <div class="label">系统状态</div>
                        <div class="value">运行正常</div>
                    </div>
                    <div class="data-item">
                        <div class="label">健康度</div>
                        <div class="value">98.5%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">运行时间</div>
                        <div class="value">72小时</div>
                    </div>
                    <div class="data-item">
                        <div class="label">内存使用</div>
                        <div class="value">65%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">CPU使用</div>
                        <div class="value">23%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">活跃连接</div>
                        <div class="value">156</div>
                    </div>
                </div>
                <h4 style="margin-top: 25px; color: #4CAF50;">模块状态</h4>
                <div style="margin-top: 15px;">
                    <div style="margin: 10px 0;">✅ 数据库系统: 正常</div>
                    <div style="margin: 10px 0;">✅ 数据采集系统: 正常</div>
                    <div style="margin: 10px 0;">✅ 智能选股引擎: 正常</div>
                    <div style="margin: 10px 0;">✅ VeighNa回测系统: 正常</div>
                    <div style="margin: 10px 0;">✅ 交易策略层: 正常</div>
                    <div style="margin: 10px 0;">✅ 投资组合管理: 正常</div>
                    <div style="margin: 10px 0;">✅ Web界面模块: 正常</div>
                </div>
            `;
            showData('🔧 系统状态详情', content);
        }
        
        function showStockData() {
            const content = `
                <div class="real-time-data">
                    <div class="data-item">
                        <div class="label">000001 平安银行</div>
                        <div class="value">¥12.85 (+3.05%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">600519 贵州茅台</div>
                        <div class="value">¥1,680.00 (+1.23%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">000858 五粮液</div>
                        <div class="value">¥128.50 (-1.76%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">600036 招商银行</div>
                        <div class="value">¥35.20 (+2.15%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">002415 海康威视</div>
                        <div class="value">¥28.90 (+0.87%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">600887 伊利股份</div>
                        <div class="value">¥31.45 (-0.32%)</div>
                    </div>
                </div>
                <h4 style="margin-top: 25px; color: #4CAF50;">智能评分 TOP 3</h4>
                <div style="margin-top: 15px;">
                    <div style="margin: 10px 0;">🥇 600519 贵州茅台: 92.3分 (强烈推荐)</div>
                    <div style="margin: 10px 0;">🥈 000001 平安银行: 88.5分 (推荐)</div>
                    <div style="margin: 10px 0;">🥉 000858 五粮液: 75.6分 (观察)</div>
                </div>
            `;
            showData('📈 股票实时监控', content);
        }
        
        function showTradingSignals() {
            const content = `
                <h4 style="color: #4CAF50; margin-bottom: 15px;">🟢 买入信号</h4>
                <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <div><strong>000001 平安银行</strong> - ¥12.85</div>
                    <div style="color: #ccc; margin-top: 5px;">信号强度: 88.5分 | 置信度: 85%</div>
                    <div style="color: #ccc; margin-top: 5px;">原因: 技术指标多头排列，成交量放大，突破关键阻力位</div>
                </div>
                <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <div><strong>600519 贵州茅台</strong> - ¥1,680.00</div>
                    <div style="color: #ccc; margin-top: 5px;">信号强度: 92.3分 | 置信度: 91%</div>
                    <div style="color: #ccc; margin-top: 5px;">原因: 基本面优秀，技术面突破，资金流入明显</div>
                </div>
                
                <h4 style="color: #f44336; margin: 25px 0 15px 0;">🔴 卖出信号</h4>
                <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 10px;">
                    <div><strong>002304 洋河股份</strong> - ¥98.50</div>
                    <div style="color: #ccc; margin-top: 5px;">信号强度: 42.1分 | 置信度: 78%</div>
                    <div style="color: #ccc; margin-top: 5px;">原因: 技术指标转弱，跌破重要支撑位，资金流出</div>
                </div>
            `;
            showData('📊 交易信号分析', content);
        }
        
        function showPortfolio() {
            const content = `
                <div class="real-time-data">
                    <div class="data-item">
                        <div class="label">总资产</div>
                        <div class="value">¥1,000,000</div>
                    </div>
                    <div class="data-item">
                        <div class="label">现金</div>
                        <div class="value">¥200,000</div>
                    </div>
                    <div class="data-item">
                        <div class="label">总盈亏</div>
                        <div class="value">+¥12,500</div>
                    </div>
                    <div class="data-item">
                        <div class="label">今日盈亏</div>
                        <div class="value">+¥2,800</div>
                    </div>
                    <div class="data-item">
                        <div class="label">持仓数量</div>
                        <div class="value">5只</div>
                    </div>
                    <div class="data-item">
                        <div class="label">仓位使用</div>
                        <div class="value">80%</div>
                    </div>
                </div>
                
                <h4 style="margin-top: 25px; color: #4CAF50;">持仓明细</h4>
                <div style="margin-top: 15px;">
                    <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <div><strong>600519 贵州茅台</strong> - 300股 (50.4%)</div>
                        <div style="color: #ccc; margin-top: 5px;">成本: ¥1,650.00 | 现价: ¥1,680.00 | 盈亏: +¥9,000 (+1.8%)</div>
                    </div>
                    <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <div><strong>000001 平安银行</strong> - 10,000股 (12.9%)</div>
                        <div style="color: #ccc; margin-top: 5px;">成本: ¥12.50 | 现价: ¥12.85 | 盈亏: +¥3,500 (+2.8%)</div>
                    </div>
                </div>
            `;
            showData('💼 投资组合详情', content);
        }
        
        function showBacktestResults() {
            const content = `
                <div class="real-time-data">
                    <div class="data-item">
                        <div class="label">总收益率</div>
                        <div class="value">+15.6%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">年化收益率</div>
                        <div class="value">+18.2%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">最大回撤</div>
                        <div class="value">-8.3%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">夏普比率</div>
                        <div class="value">1.45</div>
                    </div>
                    <div class="data-item">
                        <div class="label">胜率</div>
                        <div class="value">62.5%</div>
                    </div>
                    <div class="data-item">
                        <div class="label">交易次数</div>
                        <div class="value">24次</div>
                    </div>
                </div>
                
                <h4 style="margin-top: 25px; color: #4CAF50;">策略表现</h4>
                <div style="margin-top: 15px;">
                    <div style="margin: 10px 0;">📈 多因子选股策略: 表现优秀</div>
                    <div style="margin: 10px 0;">🎯 风险控制: 有效</div>
                    <div style="margin: 10px 0;">⚡ 执行效率: 高</div>
                    <div style="margin: 10px 0;">🔄 策略稳定性: 良好</div>
                </div>
            `;
            showData('🚀 回测结果分析', content);
        }
        
        function showMarketOverview() {
            const content = `
                <div class="real-time-data">
                    <div class="data-item">
                        <div class="label">上证指数</div>
                        <div class="value">3,245.68 (+0.38%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">深证成指</div>
                        <div class="value">10,856.32 (-0.24%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">创业板指</div>
                        <div class="value">2,187.45 (+0.41%)</div>
                    </div>
                    <div class="data-item">
                        <div class="label">上涨股票</div>
                        <div class="value">2,650只</div>
                    </div>
                    <div class="data-item">
                        <div class="label">下跌股票</div>
                        <div class="value">1,890只</div>
                    </div>
                    <div class="data-item">
                        <div class="label">涨停股票</div>
                        <div class="value">45只</div>
                    </div>
                </div>
                
                <h4 style="margin-top: 25px; color: #4CAF50;">市场情绪</h4>
                <div style="margin-top: 15px;">
                    <div style="margin: 10px 0;">📊 整体趋势: 震荡上行</div>
                    <div style="margin: 10px 0;">💰 资金流向: 净流入</div>
                    <div style="margin: 10px 0;">🔥 热点板块: 科技、消费</div>
                    <div style="margin: 10px 0;">⚠️ 风险提示: 中等</div>
                </div>
            `;
            showData('📊 市场概览分析', content);
        }
    </script>
</body>
</html>
