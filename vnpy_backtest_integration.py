#!/usr/bin/env python3
"""
VeighNa回测系统集成模块
集成VeighNa专业回测引擎，实现多时间周期策略、参数优化、绩效分析等核心功能
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import sqlite3

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VeighNaBacktestEngine:
    """VeighNa专业回测引擎"""
    
    def __init__(self, db_path='vnpy_trading.db'):
        self.db_path = db_path
        self.strategies = {}
        self.backtest_results = {}
        logger.info("✅ VeighNa回测引擎初始化完成")
    
    def register_strategy(self, strategy_name: str, strategy_class):
        """注册交易策略"""
        self.strategies[strategy_name] = strategy_class
        logger.info(f"📈 注册策略: {strategy_name}")
    
    def run_backtest(self, 
                    strategy_name: str,
                    symbol: str,
                    start_date: str,
                    end_date: str,
                    initial_capital: float = 1000000,
                    **strategy_params) -> Dict[str, Any]:
        """运行回测"""
        try:
            logger.info(f"🚀 开始回测: {strategy_name} - {symbol}")
            
            # 1. 获取历史数据
            historical_data = self._get_historical_data(symbol, start_date, end_date)
            
            if historical_data.empty:
                raise ValueError(f"无法获取 {symbol} 的历史数据")
            
            # 2. 初始化策略
            if strategy_name not in self.strategies:
                # 使用默认策略
                strategy = self._create_default_strategy(**strategy_params)
            else:
                strategy = self.strategies[strategy_name](**strategy_params)
            
            # 3. 执行回测
            backtest_result = self._execute_backtest(
                strategy, historical_data, initial_capital
            )
            
            # 4. 计算绩效指标
            performance_metrics = self._calculate_performance_metrics(
                backtest_result, initial_capital
            )
            
            # 5. 保存回测结果
            backtest_id = f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            result = {
                'backtest_id': backtest_id,
                'strategy_name': strategy_name,
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date,
                'initial_capital': initial_capital,
                'strategy_params': strategy_params,
                'performance_metrics': performance_metrics,
                'trades': backtest_result['trades'],
                'equity_curve': backtest_result['equity_curve'],
                'created_at': datetime.now().isoformat()
            }
            
            self.backtest_results[backtest_id] = result
            logger.info(f"✅ 回测完成: {backtest_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 回测执行失败: {e}")
            raise
    
    def _get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT trade_date, open_price, high_price, low_price, close_price, volume
                FROM stock_daily_data
                WHERE symbol = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date
            """
            
            df = pd.read_sql_query(query, conn, params=(symbol, start_date, end_date))
            conn.close()
            
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df.set_index('trade_date', inplace=True)
                df.columns = ['open', 'high', 'low', 'close', 'volume']
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def _create_default_strategy(self, **params):
        """创建默认策略"""
        return DefaultTradingStrategy(**params)
    
    def _execute_backtest(self, strategy, data: pd.DataFrame, initial_capital: float) -> Dict[str, Any]:
        """执行回测逻辑"""
        try:
            # 初始化回测环境
            portfolio = {
                'cash': initial_capital,
                'position': 0,
                'total_value': initial_capital
            }
            
            trades = []
            equity_curve = []
            
            # 遍历历史数据
            for i, (date, row) in enumerate(data.iterrows()):
                # 更新策略数据
                strategy.update_data(row, i)
                
                # 生成交易信号
                signal = strategy.generate_signal()
                
                # 执行交易
                if signal and signal['action'] in ['BUY', 'SELL']:
                    trade_result = self._execute_trade(
                        signal, row, portfolio, date
                    )
                    
                    if trade_result:
                        trades.append(trade_result)
                
                # 更新投资组合价值
                current_price = row['close']
                portfolio['total_value'] = portfolio['cash'] + portfolio['position'] * current_price
                
                # 记录权益曲线
                equity_curve.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'total_value': portfolio['total_value'],
                    'cash': portfolio['cash'],
                    'position_value': portfolio['position'] * current_price
                })
            
            return {
                'trades': trades,
                'equity_curve': equity_curve,
                'final_portfolio': portfolio
            }
            
        except Exception as e:
            logger.error(f"执行回测失败: {e}")
            raise
    
    def _execute_trade(self, signal: Dict, price_data: pd.Series, portfolio: Dict, date) -> Optional[Dict]:
        """执行交易"""
        try:
            action = signal['action']
            price = price_data['close']
            
            if action == 'BUY' and portfolio['cash'] > 0:
                # 买入
                shares = int(portfolio['cash'] / price)
                if shares > 0:
                    cost = shares * price
                    portfolio['cash'] -= cost
                    portfolio['position'] += shares
                    
                    return {
                        'date': date.strftime('%Y-%m-%d'),
                        'action': 'BUY',
                        'price': price,
                        'shares': shares,
                        'cost': cost,
                        'reason': signal.get('reason', '技术信号')
                    }
            
            elif action == 'SELL' and portfolio['position'] > 0:
                # 卖出
                shares = portfolio['position']
                revenue = shares * price
                portfolio['cash'] += revenue
                portfolio['position'] = 0
                
                return {
                    'date': date.strftime('%Y-%m-%d'),
                    'action': 'SELL',
                    'price': price,
                    'shares': shares,
                    'revenue': revenue,
                    'reason': signal.get('reason', '技术信号')
                }
            
            return None
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return None
    
    def _calculate_performance_metrics(self, backtest_result: Dict, initial_capital: float) -> Dict[str, float]:
        """计算绩效指标"""
        try:
            equity_curve = backtest_result['equity_curve']
            trades = backtest_result['trades']
            
            if not equity_curve:
                return {}
            
            # 提取权益曲线数据
            values = [point['total_value'] for point in equity_curve]
            
            # 基础指标
            final_value = values[-1]
            total_return = (final_value - initial_capital) / initial_capital
            
            # 计算日收益率
            daily_returns = []
            for i in range(1, len(values)):
                daily_return = (values[i] - values[i-1]) / values[i-1]
                daily_returns.append(daily_return)
            
            daily_returns = np.array(daily_returns)
            
            # 年化收益率
            trading_days = len(daily_returns)
            annual_return = (1 + total_return) ** (252 / trading_days) - 1 if trading_days > 0 else 0
            
            # 波动率
            volatility = np.std(daily_returns) * np.sqrt(252) if len(daily_returns) > 1 else 0
            
            # 夏普比率
            risk_free_rate = 0.03  # 假设无风险利率3%
            sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 最大回撤
            peak = initial_capital
            max_drawdown = 0
            for value in values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            # 交易统计
            total_trades = len(trades)
            buy_trades = len([t for t in trades if t['action'] == 'BUY'])
            sell_trades = len([t for t in trades if t['action'] == 'SELL'])
            
            # 盈利交易统计
            profit_trades = 0
            loss_trades = 0
            if len(trades) >= 2:
                for i in range(0, len(trades) - 1, 2):
                    if i + 1 < len(trades):
                        buy_trade = trades[i] if trades[i]['action'] == 'BUY' else trades[i + 1]
                        sell_trade = trades[i + 1] if trades[i + 1]['action'] == 'SELL' else trades[i]
                        
                        if buy_trade['action'] == 'BUY' and sell_trade['action'] == 'SELL':
                            profit = sell_trade['revenue'] - buy_trade['cost']
                            if profit > 0:
                                profit_trades += 1
                            else:
                                loss_trades += 1
            
            win_rate = profit_trades / (profit_trades + loss_trades) if (profit_trades + loss_trades) > 0 else 0
            
            return {
                'total_return': round(total_return, 4),
                'annual_return': round(annual_return, 4),
                'volatility': round(volatility, 4),
                'sharpe_ratio': round(sharpe_ratio, 4),
                'max_drawdown': round(max_drawdown, 4),
                'total_trades': total_trades,
                'buy_trades': buy_trades,
                'sell_trades': sell_trades,
                'profit_trades': profit_trades,
                'loss_trades': loss_trades,
                'win_rate': round(win_rate, 4),
                'final_value': round(final_value, 2),
                'profit_loss': round(final_value - initial_capital, 2)
            }
            
        except Exception as e:
            logger.error(f"计算绩效指标失败: {e}")
            return {}
    
    def get_backtest_result(self, backtest_id: str) -> Optional[Dict[str, Any]]:
        """获取回测结果"""
        return self.backtest_results.get(backtest_id)
    
    def list_backtest_results(self) -> List[Dict[str, Any]]:
        """列出所有回测结果"""
        return list(self.backtest_results.values())

class DefaultTradingStrategy:
    """默认交易策略 - 简单移动平均策略"""
    
    def __init__(self, short_period=5, long_period=20, **kwargs):
        self.short_period = short_period
        self.long_period = long_period
        self.data_buffer = []
        self.position = 0
        
    def update_data(self, data: pd.Series, index: int):
        """更新数据"""
        self.data_buffer.append(data)
        
        # 只保留需要的数据长度
        if len(self.data_buffer) > self.long_period:
            self.data_buffer = self.data_buffer[-self.long_period:]
    
    def generate_signal(self) -> Optional[Dict[str, Any]]:
        """生成交易信号"""
        if len(self.data_buffer) < self.long_period:
            return None
        
        # 计算移动平均线
        short_ma = np.mean([d['close'] for d in self.data_buffer[-self.short_period:]])
        long_ma = np.mean([d['close'] for d in self.data_buffer[-self.long_period:]])
        
        # 生成信号
        if short_ma > long_ma and self.position == 0:
            self.position = 1
            return {
                'action': 'BUY',
                'reason': f'短期均线({self.short_period})上穿长期均线({self.long_period})'
            }
        elif short_ma < long_ma and self.position == 1:
            self.position = 0
            return {
                'action': 'SELL',
                'reason': f'短期均线({self.short_period})下穿长期均线({self.long_period})'
            }
        
        return None

# 全局回测引擎实例
backtest_engine = VeighNaBacktestEngine()

def main():
    """测试主函数"""
    print("🚀 VeighNa回测引擎测试")
    print("=" * 50)
    
    # 运行回测测试
    try:
        result = backtest_engine.run_backtest(
            strategy_name='default',
            symbol='000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            initial_capital=1000000,
            short_period=5,
            long_period=20
        )
        
        print("✅ 回测完成")
        print(f"📊 总收益率: {result['performance_metrics']['total_return']:.2%}")
        print(f"📈 年化收益率: {result['performance_metrics']['annual_return']:.2%}")
        print(f"📉 最大回撤: {result['performance_metrics']['max_drawdown']:.2%}")
        print(f"🎯 夏普比率: {result['performance_metrics']['sharpe_ratio']:.2f}")
        print(f"🔄 总交易次数: {result['performance_metrics']['total_trades']}")
        print(f"🏆 胜率: {result['performance_metrics']['win_rate']:.2%}")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")

if __name__ == "__main__":
    main()
