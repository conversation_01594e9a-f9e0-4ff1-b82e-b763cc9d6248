#!/usr/bin/env python3
"""
VeighNa简化版Web服务器 - 使用模拟数据，避免网络问题
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import sqlite3
import logging
import random
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

class SimpleVeighNaServer:
    """简化版VeighNa服务器"""
    
    def __init__(self):
        self.db_path = 'vnpy_trading.db'
        self.setup_routes()
        logger.info("✅ 简化版VeighNa服务器初始化完成")
    
    def setup_routes(self):
        """设置路由"""
        
        @app.route('/')
        def index():
            """主页"""
            return render_template('index.html')
        
        @app.route('/test')
        def test_page():
            """测试页面"""
            return render_template('test.html')
        
        @app.route('/api/realtime-data')
        def get_realtime_data():
            """获取实时数据API"""
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 获取股票基本信息和最新日线数据
                cursor.execute("""
                    SELECT s.symbol, s.name, s.exchange,
                           d.close_price, d.volume, d.amount, d.turnover_rate, d.pe_ratio
                    FROM stock_basic_info s
                    INNER JOIN stock_daily_data d ON s.symbol = d.symbol
                    WHERE d.trade_date = (
                        SELECT MAX(trade_date) FROM stock_daily_data WHERE symbol = s.symbol
                    )
                    ORDER BY s.symbol
                    LIMIT 30
                """)
                
                stocks_data = []
                for row in cursor.fetchall():
                    symbol, name, exchange, close_price, volume, amount, turnover_rate, pe_ratio = row
                    
                    # 模拟实时价格变动
                    current_price = close_price * random.uniform(0.95, 1.05)
                    change = current_price - close_price
                    change_pct = (change / close_price) * 100
                    
                    stocks_data.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': exchange,
                        'price': round(current_price, 2),
                        'change': round(change, 2),
                        'change_pct': round(change_pct, 2),
                        'volume': volume // 10000,  # 转换为万手
                        'turnover': amount / 100000000,  # 转换为亿元
                        'turnover_rate': turnover_rate or 0,
                        'pe_ratio': pe_ratio or 0
                    })
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'stocks': stocks_data,
                        'market_summary': {
                            'total_stocks': len(stocks_data),
                            'up_count': len([s for s in stocks_data if s['change'] > 0]),
                            'down_count': len([s for s in stocks_data if s['change'] < 0]),
                            'unchanged_count': len([s for s in stocks_data if s['change'] == 0])
                        }
                    },
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"获取实时数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/stock-selection')
        def get_stock_selection():
            """获取智能选股结果API"""
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 获取最新选股结果
                cursor.execute("""
                    SELECT sr.symbol, si.name, sr.total_score, sr.technical_score,
                           sr.fundamental_score, sr.market_score, sr.rank,
                           sr.recommendation, sr.reason, sr.selection_date
                    FROM stock_selection_results sr
                    INNER JOIN stock_basic_info si ON sr.symbol = si.symbol
                    ORDER BY sr.rank
                    LIMIT 20
                """)
                
                selected_stocks = []
                for row in cursor.fetchall():
                    symbol, name, total_score, tech_score, fund_score, market_score, rank, recommendation, reason, selection_date = row
                    
                    selected_stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'total_score': total_score,
                        'technical_score': tech_score,
                        'fundamental_score': fund_score,
                        'market_score': market_score,
                        'rank': rank,
                        'recommendation': recommendation,
                        'reason': reason,
                        'selection_date': selection_date
                    })
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'selected_stocks': selected_stocks,
                        'selection_criteria': {
                            'technical_weight': 0.4,
                            'fundamental_weight': 0.4,
                            'market_weight': 0.2,
                            'min_score': 60.0,
                            'max_count': 20
                        },
                        'statistics': {
                            'total_selected': len(selected_stocks),
                            'avg_score': sum(s['total_score'] for s in selected_stocks) / len(selected_stocks) if selected_stocks else 0,
                            'score_distribution': self._get_score_distribution(selected_stocks),
                            'recommendation_distribution': self._get_recommendation_distribution(selected_stocks)
                        },
                        'updated_at': datetime.now().isoformat()
                    },
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"获取选股结果失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/trading-signals')
        def get_trading_signals():
            """获取交易信号API"""
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 获取买入信号
                cursor.execute("""
                    SELECT ts.symbol, si.name, ts.signal_type, ts.signal_time,
                           ts.price, ts.score, ts.confidence, ts.reason, ts.source_indicator
                    FROM trading_signals ts
                    INNER JOIN stock_basic_info si ON ts.symbol = si.symbol
                    WHERE ts.signal_type = 'BUY' AND ts.status = 'active'
                    ORDER BY ts.signal_time DESC
                    LIMIT 10
                """)
                
                buy_signals = []
                for row in cursor.fetchall():
                    symbol, name, signal_type, signal_time, price, score, confidence, reason, source_indicator = row
                    buy_signals.append({
                        'symbol': symbol,
                        'name': name,
                        'signal_type': signal_type,
                        'signal_time': signal_time,
                        'price': price,
                        'score': score,
                        'confidence': confidence,
                        'reason': reason,
                        'source_indicator': source_indicator
                    })
                
                # 获取卖出信号
                cursor.execute("""
                    SELECT ts.symbol, si.name, ts.signal_type, ts.signal_time,
                           ts.price, ts.score, ts.confidence, ts.reason, ts.source_indicator
                    FROM trading_signals ts
                    INNER JOIN stock_basic_info si ON ts.symbol = si.symbol
                    WHERE ts.signal_type = 'SELL' AND ts.status = 'active'
                    ORDER BY ts.signal_time DESC
                    LIMIT 10
                """)
                
                sell_signals = []
                for row in cursor.fetchall():
                    symbol, name, signal_type, signal_time, price, score, confidence, reason, source_indicator = row
                    sell_signals.append({
                        'symbol': symbol,
                        'name': name,
                        'signal_type': signal_type,
                        'signal_time': signal_time,
                        'price': price,
                        'score': score,
                        'confidence': confidence,
                        'reason': reason,
                        'source_indicator': source_indicator
                    })
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'statistics': {
                            'total_signals': len(buy_signals) + len(sell_signals),
                            'buy_signals': len(buy_signals),
                            'sell_signals': len(sell_signals),
                            'success_rate': 0.78,
                            'active_signals': len(buy_signals) + len(sell_signals)
                        },
                        'signal_config': {
                            'confidence_threshold': 0.7,
                            'generation_interval': 60,
                            'max_signals_per_stock': 5
                        },
                        'updated_at': datetime.now().isoformat()
                    },
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"获取交易信号失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/portfolio')
        def get_portfolio():
            """获取投资组合API"""
            try:
                # 模拟投资组合数据
                mock_portfolio = {
                    'positions': [
                        {'symbol': '000001', 'name': '平安银行', 'quantity': 10000, 'avg_price': 12.50, 'current_price': 12.85, 'market_value': 128500, 'pnl': 3500, 'pnl_pct': 2.80, 'weight': 0.128},
                        {'symbol': '600519', 'name': '贵州茅台', 'quantity': 100, 'avg_price': 1650.00, 'current_price': 1680.00, 'market_value': 168000, 'pnl': 3000, 'pnl_pct': 1.82, 'weight': 0.168},
                        {'symbol': '000858', 'name': '五粮液', 'quantity': 800, 'avg_price': 125.00, 'current_price': 128.50, 'market_value': 102800, 'pnl': 2800, 'pnl_pct': 2.80, 'weight': 0.103}
                    ],
                    'risk_metrics': {
                        'portfolio_value': 1000000,
                        'total_pnl': 50000,
                        'total_pnl_pct': 5.26,
                        'sharpe_ratio': 1.85,
                        'max_drawdown': -0.08
                    }
                }
                
                return jsonify({
                    'success': True,
                    'data': mock_portfolio,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"获取投资组合失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/system/status')
        def get_system_status():
            """获取系统状态API"""
            try:
                return jsonify({
                    'success': True,
                    'data': {
                        'business_flow': {
                            'data_collection_status': 'running',
                            'stock_selection_status': 'completed',
                            'signal_tracking_status': 'running',
                            'system_health': 'healthy',
                            'total_stocks_tracked': 30,
                            'active_signals_count': 8,
                            'is_running': True
                        },
                        'web_server': 'running',
                        'database': 'connected',
                        'version': '2.0.0 Professional (Simplified)',
                        'uptime': 3600
                    },
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
    
    def _get_score_distribution(self, stocks):
        """获取评分分布"""
        distribution = {'90-100': 0, '80-89': 0, '70-79': 0, '60-69': 0, '50-59': 0}
        for stock in stocks:
            score = stock['total_score']
            if score >= 90:
                distribution['90-100'] += 1
            elif score >= 80:
                distribution['80-89'] += 1
            elif score >= 70:
                distribution['70-79'] += 1
            elif score >= 60:
                distribution['60-69'] += 1
            else:
                distribution['50-59'] += 1
        return distribution
    
    def _get_recommendation_distribution(self, stocks):
        """获取推荐分布"""
        distribution = {}
        for stock in stocks:
            rec = stock['recommendation']
            distribution[rec] = distribution.get(rec, 0) + 1
        return distribution
    
    def start_server(self, host='0.0.0.0', port=8080, debug=False):
        """启动服务器"""
        logger.info(f"🌐 启动简化版VeighNa服务器: http://{host}:{port}")
        app.run(host=host, port=port, debug=debug)

# 全局服务器实例
server = SimpleVeighNaServer()

def main():
    """主函数"""
    print("🚀 启动VeighNa简化版Web服务器")
    print("=" * 60)
    print("🌐 Web界面: http://localhost:8080")
    print("🧪 测试页面: http://localhost:8080/test")
    print("📊 实时数据API: http://localhost:8080/api/realtime-data")
    print("🧠 智能选股API: http://localhost:8080/api/stock-selection")
    print("📈 交易信号API: http://localhost:8080/api/trading-signals")
    print("💼 投资组合API: http://localhost:8080/api/portfolio")
    print("⚙️ 系统状态API: http://localhost:8080/api/system/status")
    print("=" * 60)
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断，服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
