#!/usr/bin/env python3
"""
分析引擎层测试脚本
测试智能选股和多维度分析功能
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from analysis_engine import (
    MultiFactorSelector, TechnicalIndicators, FinancialAnalyzer, 
    SignalCombiner, FactorCalculator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_technical_indicators():
    """测试技术分析指标"""
    logger.info("📈 测试技术分析指标...")
    
    try:
        technical_indicators = TechnicalIndicators()
        
        # 创建模拟数据
        import pandas as pd
        import numpy as np
        
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        n = len(dates)
        
        # 生成模拟OHLCV数据
        base_price = 10.0
        prices = base_price + np.cumsum(np.random.randn(n) * 0.02)
        
        test_data = pd.DataFrame({
            'trade_date': dates,
            'open_price': prices + np.random.randn(n) * 0.01,
            'high_price': prices + np.abs(np.random.randn(n) * 0.02),
            'low_price': prices - np.abs(np.random.randn(n) * 0.02),
            'close_price': prices,
            'volume': np.random.randint(1000000, 10000000, n)
        })
        
        # 测试计算所有技术指标
        indicators = technical_indicators.calculate_all_indicators(test_data)
        logger.info(f"✅ 计算了 {len(indicators)} 个技术指标")
        
        # 测试趋势信号
        trend_signal = technical_indicators.get_trend_signal(test_data)
        logger.info(f"📊 趋势信号评分: {trend_signal.get('trend_score', 0):.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 技术分析指标测试失败: {e}")
        return False

def test_financial_analyzer():
    """测试基本面分析器"""
    logger.info("💰 测试基本面分析器...")
    
    try:
        financial_analyzer = FinancialAnalyzer()
        
        # 测试计算基本面评分
        test_symbols = ['000001', '000002', '600000']
        
        for symbol in test_symbols:
            fundamental_score = financial_analyzer.calculate_fundamental_score(symbol)
            logger.info(f"📊 {symbol} 基本面评分: {fundamental_score.get('fundamental_score', 0):.2f}")
        
        # 测试批量计算
        batch_results = financial_analyzer.batch_calculate_fundamental_scores(test_symbols)
        logger.info(f"✅ 批量计算了 {len(batch_results)} 只股票的基本面评分")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本面分析器测试失败: {e}")
        return False

def test_factor_calculator():
    """测试因子计算器"""
    logger.info("🧮 测试因子计算器...")
    
    try:
        factor_calculator = FactorCalculator()
        
        # 测试获取因子权重
        factor_weights = factor_calculator.get_factor_weights()
        logger.info(f"📊 加载了 {len(factor_weights)} 个因子权重")
        
        # 测试计算因子
        test_symbols = ['000001', '000002']
        
        for symbol in test_symbols:
            factors = factor_calculator.calculate_all_factors(symbol)
            logger.info(f"🧮 {symbol} 计算了 {len(factors)} 个因子")
        
        # 测试批量计算因子
        batch_factors = factor_calculator.batch_calculate_factors(test_symbols)
        logger.info(f"✅ 批量计算了 {len(batch_factors)} 只股票的因子")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 因子计算器测试失败: {e}")
        return False

def test_multi_factor_selector():
    """测试多因子选股器"""
    logger.info("🎯 测试多因子选股器...")
    
    try:
        selector = MultiFactorSelector()
        
        # 测试获取候选股票
        candidates = selector.get_candidate_stocks()
        logger.info(f"📋 获取到 {len(candidates)} 只候选股票")
        
        # 测试计算综合评分
        if candidates:
            test_symbol = candidates[0]
            comprehensive_score = selector.calculate_comprehensive_score(test_symbol)
            logger.info(f"📊 {test_symbol} 综合评分: {comprehensive_score.get('total_score', 0):.2f}")
            
            # 显示详细评分
            logger.info(f"  - 技术面: {comprehensive_score.get('technical_score', 0):.2f}")
            logger.info(f"  - 基本面: {comprehensive_score.get('fundamental_score', 0):.2f}")
            logger.info(f"  - 市场表现: {comprehensive_score.get('market_score', 0):.2f}")
        
        # 测试智能选股（限制数量避免耗时过长）
        selected_stocks = selector.select_stocks(max_stocks=5)
        logger.info(f"✅ 智能选股完成，选中 {len(selected_stocks)} 只股票")
        
        # 显示选中的股票
        for i, stock in enumerate(selected_stocks[:3], 1):
            logger.info(f"  {i}. {stock['symbol']}: {stock['total_score']:.2f}分")
        
        # 测试获取评分最高的股票
        top_stocks = selector.get_top_stocks(limit=5)
        logger.info(f"📈 获取到 {len(top_stocks)} 只评分最高的股票")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多因子选股器测试失败: {e}")
        return False

def test_signal_combiner():
    """测试信号合成器"""
    logger.info("🎯 测试信号合成器...")
    
    try:
        signal_combiner = SignalCombiner()
        
        # 测试生成交易信号
        test_symbols = ['000001', '000002', '600000']
        
        for symbol in test_symbols:
            signal = signal_combiner.generate_trading_signal(symbol)
            logger.info(f"🎯 {symbol} 交易信号: {signal['signal_type']} ({signal['signal_strength']})")
            logger.info(f"  - 信号评分: {signal['signal_score']:.2f}")
            logger.info(f"  - 置信度: {signal.get('confidence', 0):.2f}")
            logger.info(f"  - 信号原因: {signal.get('signal_reasons', [])[:2]}")
        
        # 测试批量生成信号
        batch_signals = signal_combiner.batch_generate_signals(test_symbols)
        logger.info(f"✅ 批量生成了 {len(batch_signals)} 个交易信号")
        
        # 统计信号类型
        signal_stats = {}
        for signal in batch_signals:
            signal_type = signal['signal_type']
            signal_stats[signal_type] = signal_stats.get(signal_type, 0) + 1
        
        logger.info(f"📊 信号统计: {signal_stats}")
        
        # 测试获取最新信号
        latest_signals = signal_combiner.get_latest_signals(limit=5)
        logger.info(f"📈 获取到 {len(latest_signals)} 个最新信号")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 信号合成器测试失败: {e}")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🧪 开始分析引擎层综合测试...")
    logger.info("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试技术分析指标
        result1 = test_technical_indicators()
        test_results.append(("技术分析指标", result1))
        logger.info("=" * 60)
        
        # 2. 测试基本面分析器
        result2 = test_financial_analyzer()
        test_results.append(("基本面分析器", result2))
        logger.info("=" * 60)
        
        # 3. 测试因子计算器
        result3 = test_factor_calculator()
        test_results.append(("因子计算器", result3))
        logger.info("=" * 60)
        
        # 4. 测试多因子选股器
        result4 = test_multi_factor_selector()
        test_results.append(("多因子选股器", result4))
        logger.info("=" * 60)
        
        # 5. 测试信号合成器
        result5 = test_signal_combiner()
        test_results.append(("信号合成器", result5))
        logger.info("=" * 60)
        
        # 测试结果总结
        logger.info("📊 测试结果总结:")
        success_count = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  - {test_name}: {status}")
            if result:
                success_count += 1
        
        logger.info(f"🎉 分析引擎层测试完成: {success_count}/{len(test_results)} 项通过")
        
        if success_count == len(test_results):
            logger.info("🎊 所有测试均通过！分析引擎层功能正常")
        else:
            logger.warning("⚠️ 部分测试失败，请检查相关功能")
        
    except Exception as e:
        logger.error(f"❌ 综合测试过程中发生异常: {e}")

async def main():
    """主函数"""
    print("🚀 分析引擎层测试")
    print("=" * 60)
    print("智能选股和多维度分析功能测试")
    print("技术面50% + 基本面30% + 市场表现20%")
    print("=" * 60)
    
    await run_comprehensive_test()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
