"""
模拟交易引擎
实现完整的模拟交易功能，支持订单处理、成交回报、风险控制等
"""

import logging
import uuid
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
import time
import queue

from database_models import db_manager

logger = logging.getLogger(__name__)

class OrderType(Enum):
    """订单类型"""
    MARKET = "MARKET"           # 市价单
    LIMIT = "LIMIT"             # 限价单
    STOP = "STOP"               # 止损单
    STOP_LIMIT = "STOP_LIMIT"   # 止损限价单

class OrderSide(Enum):
    """订单方向"""
    BUY = "BUY"                 # 买入
    SELL = "SELL"               # 卖出

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "PENDING"         # 待处理
    SUBMITTED = "SUBMITTED"     # 已提交
    PARTIAL_FILLED = "PARTIAL_FILLED"  # 部分成交
    FILLED = "FILLED"           # 完全成交
    CANCELLED = "CANCELLED"     # 已取消
    REJECTED = "REJECTED"       # 已拒绝
    EXPIRED = "EXPIRED"         # 已过期

@dataclass
class Order:
    """订单对象"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    commission: float = 0.0
    create_time: datetime = field(default_factory=datetime.now)
    update_time: datetime = field(default_factory=datetime.now)
    strategy_id: Optional[str] = None
    client_order_id: Optional[str] = None
    
    @property
    def remaining_quantity(self) -> int:
        """剩余数量"""
        return self.quantity - self.filled_quantity
    
    @property
    def is_active(self) -> bool:
        """是否为活跃订单"""
        return self.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]

@dataclass
class Trade:
    """成交记录"""
    trade_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: int
    price: float
    commission: float
    trade_time: datetime = field(default_factory=datetime.now)
    strategy_id: Optional[str] = None

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int = 0
    avg_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)

class SimulationTradingEngine:
    """模拟交易引擎"""
    
    def __init__(self, initial_capital: float = 1000000.0):
        self.initial_capital = initial_capital
        self.available_cash = initial_capital
        self.total_value = initial_capital
        
        # 订单和成交管理
        self.orders: Dict[str, Order] = {}
        self.trades: List[Trade] = []
        self.positions: Dict[str, Position] = {}
        
        # 交易参数
        self.commission_rate = 0.0003       # 手续费率0.03%
        self.slippage_rate = 0.001          # 滑点0.1%
        self.min_commission = 5.0           # 最小手续费5元
        
        # 风险控制参数
        self.max_position_ratio = 0.1       # 单股最大仓位比例10%
        self.max_total_position = 0.8       # 总仓位限制80%
        self.max_daily_loss = 0.05          # 日最大亏损5%
        
        # 事件处理
        self.order_callbacks: List[Callable] = []
        self.trade_callbacks: List[Callable] = []
        self.position_callbacks: List[Callable] = []
        
        # 处理队列和线程
        self.order_queue = queue.Queue()
        self.processing_thread = None
        self.is_running = False
        
        # 市场数据缓存
        self.market_data_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info("📈 模拟交易引擎初始化完成")
        logger.info(f"  - 初始资金: {self.initial_capital:,.2f}")
        logger.info(f"  - 手续费率: {self.commission_rate:.4f}")
        logger.info(f"  - 滑点率: {self.slippage_rate:.4f}")
    
    def start(self):
        """启动交易引擎"""
        try:
            if self.is_running:
                logger.warning("⚠️ 交易引擎已在运行")
                return
            
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
            self.processing_thread.start()
            
            logger.info("🚀 模拟交易引擎已启动")
            
        except Exception as e:
            logger.error(f"❌ 启动交易引擎失败: {e}")
    
    def stop(self):
        """停止交易引擎"""
        try:
            self.is_running = False
            
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5)
            
            logger.info("⏹️ 模拟交易引擎已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止交易引擎失败: {e}")
    
    def submit_order(self, 
                    symbol: str,
                    side: OrderSide,
                    order_type: OrderType,
                    quantity: int,
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    strategy_id: Optional[str] = None,
                    client_order_id: Optional[str] = None) -> str:
        """
        提交订单
        
        Args:
            symbol: 股票代码
            side: 买卖方向
            order_type: 订单类型
            quantity: 数量
            price: 价格（限价单必填）
            stop_price: 止损价格（止损单必填）
            strategy_id: 策略ID
            client_order_id: 客户端订单ID
            
        Returns:
            订单ID
        """
        try:
            # 生成订单ID
            order_id = str(uuid.uuid4())
            
            # 创建订单对象
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                strategy_id=strategy_id,
                client_order_id=client_order_id
            )
            
            # 订单验证
            validation_result = self._validate_order(order)
            if not validation_result['valid']:
                order.status = OrderStatus.REJECTED
                self.orders[order_id] = order
                logger.error(f"❌ 订单验证失败: {validation_result['reason']}")
                return order_id
            
            # 添加到订单字典
            self.orders[order_id] = order
            
            # 加入处理队列
            self.order_queue.put(order_id)
            
            logger.info(f"📝 提交订单: {symbol} {side.value} {quantity}股 @{price}")
            
            return order_id
            
        except Exception as e:
            logger.error(f"❌ 提交订单失败: {e}")
            return ""
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.orders:
                logger.error(f"❌ 订单不存在: {order_id}")
                return False
            
            order = self.orders[order_id]
            
            if not order.is_active:
                logger.error(f"❌ 订单不可取消: {order_id} 状态{order.status.value}")
                return False
            
            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            order.update_time = datetime.now()
            
            # 触发回调
            self._trigger_order_callbacks(order)
            
            logger.info(f"❌ 取消订单: {order_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 取消订单失败: {order_id} - {e}")
            return False
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单信息"""
        return self.orders.get(order_id)
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取活跃订单"""
        active_orders = [order for order in self.orders.values() if order.is_active]
        
        if symbol:
            active_orders = [order for order in active_orders if order.symbol == symbol]
        
        return active_orders
    
    def get_position(self, symbol: str) -> Position:
        """获取持仓信息"""
        if symbol not in self.positions:
            self.positions[symbol] = Position(symbol=symbol)
        
        return self.positions[symbol]
    
    def get_all_positions(self) -> Dict[str, Position]:
        """获取所有持仓"""
        return self.positions.copy()
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            # 计算总市值
            total_market_value = 0
            total_unrealized_pnl = 0
            
            for position in self.positions.values():
                if position.quantity != 0:
                    # 获取当前市价
                    current_price = self._get_current_price(position.symbol)
                    if current_price > 0:
                        position.market_value = position.quantity * current_price
                        position.unrealized_pnl = (current_price - position.avg_price) * position.quantity
                        
                        total_market_value += position.market_value
                        total_unrealized_pnl += position.unrealized_pnl
            
            self.total_value = self.available_cash + total_market_value
            
            return {
                'initial_capital': self.initial_capital,
                'available_cash': self.available_cash,
                'total_market_value': total_market_value,
                'total_value': self.total_value,
                'unrealized_pnl': total_unrealized_pnl,
                'total_return': (self.total_value - self.initial_capital) / self.initial_capital,
                'position_count': len([p for p in self.positions.values() if p.quantity != 0]),
                'order_count': len(self.orders),
                'trade_count': len(self.trades)
            }
            
        except Exception as e:
            logger.error(f"❌ 获取账户信息失败: {e}")
            return {}
    
    def _processing_loop(self):
        """订单处理循环"""
        logger.info("🔄 订单处理循环开始")
        
        while self.is_running:
            try:
                # 从队列获取订单
                try:
                    order_id = self.order_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 处理订单
                self._process_order(order_id)
                
            except Exception as e:
                logger.error(f"❌ 订单处理循环异常: {e}")
                time.sleep(1)
        
        logger.info("⏹️ 订单处理循环结束")
    
    def _process_order(self, order_id: str):
        """处理单个订单"""
        try:
            if order_id not in self.orders:
                return
            
            order = self.orders[order_id]
            
            if not order.is_active:
                return
            
            # 更新订单状态为已提交
            if order.status == OrderStatus.PENDING:
                order.status = OrderStatus.SUBMITTED
                order.update_time = datetime.now()
                self._trigger_order_callbacks(order)
            
            # 模拟订单执行
            self._simulate_order_execution(order)
            
        except Exception as e:
            logger.error(f"❌ 处理订单失败: {order_id} - {e}")
    
    def _simulate_order_execution(self, order: Order):
        """模拟订单执行"""
        try:
            # 获取当前市价
            current_price = self._get_current_price(order.symbol)
            if current_price <= 0:
                logger.error(f"❌ 无法获取市价: {order.symbol}")
                return
            
            # 判断是否可以成交
            can_fill = False
            fill_price = 0.0
            
            if order.order_type == OrderType.MARKET:
                # 市价单立即成交
                can_fill = True
                fill_price = current_price * (1 + self.slippage_rate if order.side == OrderSide.BUY else 1 - self.slippage_rate)
                
            elif order.order_type == OrderType.LIMIT:
                # 限价单价格检查
                if order.side == OrderSide.BUY and current_price <= order.price:
                    can_fill = True
                    fill_price = order.price
                elif order.side == OrderSide.SELL and current_price >= order.price:
                    can_fill = True
                    fill_price = order.price
            
            # 执行成交
            if can_fill:
                self._execute_trade(order, order.remaining_quantity, fill_price)
            
        except Exception as e:
            logger.error(f"❌ 模拟订单执行失败: {order.order_id} - {e}")
    
    def _execute_trade(self, order: Order, quantity: int, price: float):
        """执行成交"""
        try:
            # 计算手续费
            commission = max(quantity * price * self.commission_rate, self.min_commission)
            
            # 创建成交记录
            trade_id = str(uuid.uuid4())
            trade = Trade(
                trade_id=trade_id,
                order_id=order.order_id,
                symbol=order.symbol,
                side=order.side,
                quantity=quantity,
                price=price,
                commission=commission,
                strategy_id=order.strategy_id
            )
            
            self.trades.append(trade)
            
            # 更新订单状态
            order.filled_quantity += quantity
            order.avg_fill_price = ((order.avg_fill_price * (order.filled_quantity - quantity)) + 
                                  (price * quantity)) / order.filled_quantity
            order.commission += commission
            order.update_time = datetime.now()
            
            if order.filled_quantity >= order.quantity:
                order.status = OrderStatus.FILLED
            else:
                order.status = OrderStatus.PARTIAL_FILLED
            
            # 更新持仓
            self._update_position(trade)
            
            # 更新资金
            if order.side == OrderSide.BUY:
                self.available_cash -= (quantity * price + commission)
            else:
                self.available_cash += (quantity * price - commission)
            
            # 触发回调
            self._trigger_order_callbacks(order)
            self._trigger_trade_callbacks(trade)
            
            logger.info(f"✅ 成交: {order.symbol} {order.side.value} {quantity}股 @{price:.2f} 手续费{commission:.2f}")
            
        except Exception as e:
            logger.error(f"❌ 执行成交失败: {e}")
    
    def _update_position(self, trade: Trade):
        """更新持仓"""
        try:
            position = self.get_position(trade.symbol)
            
            if trade.side == OrderSide.BUY:
                # 买入更新
                if position.quantity >= 0:
                    # 增加多头持仓
                    total_cost = position.quantity * position.avg_price + trade.quantity * trade.price
                    position.quantity += trade.quantity
                    position.avg_price = total_cost / position.quantity if position.quantity > 0 else 0
                else:
                    # 减少空头持仓
                    position.quantity += trade.quantity
                    if position.quantity == 0:
                        position.avg_price = 0
            else:
                # 卖出更新
                if position.quantity > 0:
                    # 减少多头持仓
                    if trade.quantity >= position.quantity:
                        # 完全卖出
                        realized_pnl = (trade.price - position.avg_price) * position.quantity
                        position.realized_pnl += realized_pnl
                        position.quantity = 0
                        position.avg_price = 0
                    else:
                        # 部分卖出
                        realized_pnl = (trade.price - position.avg_price) * trade.quantity
                        position.realized_pnl += realized_pnl
                        position.quantity -= trade.quantity
            
            position.last_update = datetime.now()
            
            # 触发持仓回调
            self._trigger_position_callbacks(position)
            
        except Exception as e:
            logger.error(f"❌ 更新持仓失败: {e}")
    
    def _validate_order(self, order: Order) -> Dict[str, Any]:
        """验证订单"""
        try:
            # 基础验证
            if order.quantity <= 0:
                return {'valid': False, 'reason': '订单数量必须大于0'}
            
            if order.order_type == OrderType.LIMIT and (not order.price or order.price <= 0):
                return {'valid': False, 'reason': '限价单必须指定有效价格'}
            
            # 资金检查（买入订单）
            if order.side == OrderSide.BUY:
                estimated_cost = order.quantity * (order.price or self._get_current_price(order.symbol))
                estimated_commission = max(estimated_cost * self.commission_rate, self.min_commission)
                total_cost = estimated_cost + estimated_commission
                
                if total_cost > self.available_cash:
                    return {'valid': False, 'reason': f'资金不足: 需要{total_cost:.2f}, 可用{self.available_cash:.2f}'}
            
            # 持仓检查（卖出订单）
            if order.side == OrderSide.SELL:
                position = self.get_position(order.symbol)
                if position.quantity < order.quantity:
                    return {'valid': False, 'reason': f'持仓不足: 需要{order.quantity}, 持有{position.quantity}'}
            
            return {'valid': True, 'reason': ''}
            
        except Exception as e:
            logger.error(f"❌ 订单验证失败: {e}")
            return {'valid': False, 'reason': f'验证异常: {e}'}
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        try:
            # 从缓存获取市场数据
            if symbol in self.market_data_cache:
                market_data = self.market_data_cache[symbol]
                return market_data.get('current_price', 0)
            
            # 模拟价格（实际应该从数据源获取）
            import hashlib
            import random
            
            # 基于股票代码生成确定性的模拟价格
            hash_value = int(hashlib.md5(symbol.encode()).hexdigest()[:8], 16)
            random.seed(hash_value + int(datetime.now().timestamp()))
            
            base_price = 10 + (hash_value % 100)  # 10-110的基础价格
            current_price = base_price * (0.9 + random.random() * 0.2)  # ±10%波动
            
            return round(current_price, 2)
            
        except Exception as e:
            logger.error(f"❌ 获取当前价格失败: {symbol} - {e}")
            return 0
    
    def update_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """更新市场数据"""
        self.market_data_cache[symbol] = market_data
    
    def add_order_callback(self, callback: Callable):
        """添加订单回调"""
        self.order_callbacks.append(callback)
    
    def add_trade_callback(self, callback: Callable):
        """添加成交回调"""
        self.trade_callbacks.append(callback)
    
    def add_position_callback(self, callback: Callable):
        """添加持仓回调"""
        self.position_callbacks.append(callback)
    
    def _trigger_order_callbacks(self, order: Order):
        """触发订单回调"""
        for callback in self.order_callbacks:
            try:
                callback(order)
            except Exception as e:
                logger.error(f"❌ 订单回调执行失败: {e}")
    
    def _trigger_trade_callbacks(self, trade: Trade):
        """触发成交回调"""
        for callback in self.trade_callbacks:
            try:
                callback(trade)
            except Exception as e:
                logger.error(f"❌ 成交回调执行失败: {e}")
    
    def _trigger_position_callbacks(self, position: Position):
        """触发持仓回调"""
        for callback in self.position_callbacks:
            try:
                callback(position)
            except Exception as e:
                logger.error(f"❌ 持仓回调执行失败: {e}")
