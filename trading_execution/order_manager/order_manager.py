"""
订单管理器
管理订单生命周期、订单路由、订单监控等功能
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import time

from trading_execution.trading_engine.simulation_engine import (
    SimulationTradingEngine, Order, OrderType, OrderSide, OrderStatus, Trade
)
from database_models import db_manager

logger = logging.getLogger(__name__)

class OrderPriority(Enum):
    """订单优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class OrderRequest:
    """订单请求"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    strategy_id: Optional[str] = None
    priority: OrderPriority = OrderPriority.NORMAL
    time_in_force: str = "DAY"  # DAY, GTC, IOC, FOK
    min_quantity: Optional[int] = None
    max_show_quantity: Optional[int] = None
    
class OrderManager:
    """订单管理器"""
    
    def __init__(self, trading_engine: SimulationTradingEngine):
        self.trading_engine = trading_engine
        
        # 订单队列管理
        self.pending_orders: List[OrderRequest] = []
        self.order_history: Dict[str, Order] = {}
        
        # 订单监控
        self.order_monitors: Dict[str, Dict[str, Any]] = {}
        
        # 风险控制参数
        self.max_order_value = 1000000      # 单笔订单最大金额
        self.max_daily_orders = 1000        # 日最大订单数
        self.max_position_concentration = 0.3  # 最大持仓集中度
        
        # 统计信息
        self.daily_order_count = 0
        self.daily_trade_count = 0
        self.daily_volume = 0.0
        self.last_reset_date = datetime.now().date()
        
        # 回调函数
        self.order_callbacks: List[Callable] = []
        self.risk_callbacks: List[Callable] = []
        
        # 监控线程
        self.monitor_thread = None
        self.is_monitoring = False
        
        # 注册交易引擎回调
        self.trading_engine.add_order_callback(self._on_order_update)
        self.trading_engine.add_trade_callback(self._on_trade_update)
        
        logger.info("📋 订单管理器初始化完成")
        logger.info(f"  - 最大订单金额: {self.max_order_value:,.0f}")
        logger.info(f"  - 日最大订单数: {self.max_daily_orders}")
    
    def start_monitoring(self):
        """启动订单监控"""
        try:
            if self.is_monitoring:
                logger.warning("⚠️ 订单监控已在运行")
                return
            
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info("🔍 订单监控已启动")
            
        except Exception as e:
            logger.error(f"❌ 启动订单监控失败: {e}")
    
    def stop_monitoring(self):
        """停止订单监控"""
        try:
            self.is_monitoring = False
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            logger.info("⏹️ 订单监控已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止订单监控失败: {e}")
    
    def submit_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """
        提交订单
        
        Args:
            order_request: 订单请求
            
        Returns:
            提交结果
        """
        try:
            # 重置日统计
            self._reset_daily_stats_if_needed()
            
            # 订单预检查
            pre_check_result = self._pre_check_order(order_request)
            if not pre_check_result['valid']:
                logger.error(f"❌ 订单预检查失败: {pre_check_result['reason']}")
                return {
                    'success': False,
                    'order_id': None,
                    'reason': pre_check_result['reason']
                }
            
            # 风险检查
            risk_check_result = self._risk_check_order(order_request)
            if not risk_check_result['valid']:
                logger.error(f"❌ 订单风险检查失败: {risk_check_result['reason']}")
                return {
                    'success': False,
                    'order_id': None,
                    'reason': risk_check_result['reason']
                }
            
            # 提交到交易引擎
            order_id = self.trading_engine.submit_order(
                symbol=order_request.symbol,
                side=order_request.side,
                order_type=order_request.order_type,
                quantity=order_request.quantity,
                price=order_request.price,
                stop_price=order_request.stop_price,
                strategy_id=order_request.strategy_id
            )
            
            if not order_id:
                return {
                    'success': False,
                    'order_id': None,
                    'reason': '交易引擎提交失败'
                }
            
            # 添加订单监控
            self._add_order_monitor(order_id, order_request)
            
            # 更新统计
            self.daily_order_count += 1
            
            logger.info(f"✅ 订单提交成功: {order_id}")
            
            return {
                'success': True,
                'order_id': order_id,
                'reason': '订单提交成功'
            }
            
        except Exception as e:
            logger.error(f"❌ 提交订单失败: {e}")
            return {
                'success': False,
                'order_id': None,
                'reason': f'提交异常: {e}'
            }
    
    def cancel_order(self, order_id: str, reason: str = "用户取消") -> bool:
        """取消订单"""
        try:
            success = self.trading_engine.cancel_order(order_id)
            
            if success:
                # 移除监控
                if order_id in self.order_monitors:
                    del self.order_monitors[order_id]
                
                logger.info(f"✅ 取消订单成功: {order_id} - {reason}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 取消订单失败: {order_id} - {e}")
            return False
    
    def cancel_all_orders(self, symbol: Optional[str] = None) -> int:
        """取消所有订单"""
        try:
            active_orders = self.trading_engine.get_active_orders(symbol)
            cancelled_count = 0
            
            for order in active_orders:
                if self.cancel_order(order.order_id, "批量取消"):
                    cancelled_count += 1
            
            logger.info(f"✅ 批量取消订单: {cancelled_count}个")
            
            return cancelled_count
            
        except Exception as e:
            logger.error(f"❌ 批量取消订单失败: {e}")
            return 0
    
    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """获取订单状态"""
        try:
            order = self.trading_engine.get_order(order_id)
            
            if not order:
                return None
            
            return {
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status.value,
                'filled_quantity': order.filled_quantity,
                'remaining_quantity': order.remaining_quantity,
                'avg_fill_price': order.avg_fill_price,
                'commission': order.commission,
                'create_time': order.create_time.isoformat(),
                'update_time': order.update_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取订单状态失败: {order_id} - {e}")
            return None
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取活跃订单"""
        try:
            active_orders = self.trading_engine.get_active_orders(symbol)
            
            result = []
            for order in active_orders:
                order_info = self.get_order_status(order.order_id)
                if order_info:
                    result.append(order_info)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取活跃订单失败: {e}")
            return []
    
    def get_order_history(self, 
                         symbol: Optional[str] = None,
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """获取订单历史"""
        try:
            orders = list(self.trading_engine.orders.values())
            
            # 过滤条件
            if symbol:
                orders = [order for order in orders if order.symbol == symbol]
            
            if start_date:
                orders = [order for order in orders if order.create_time >= start_date]
            
            if end_date:
                orders = [order for order in orders if order.create_time <= end_date]
            
            # 按时间排序
            orders.sort(key=lambda x: x.create_time, reverse=True)
            
            # 限制数量
            orders = orders[:limit]
            
            # 转换为字典
            result = []
            for order in orders:
                order_info = self.get_order_status(order.order_id)
                if order_info:
                    result.append(order_info)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取订单历史失败: {e}")
            return []
    
    def _pre_check_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """订单预检查"""
        try:
            # 基础参数检查
            if order_request.quantity <= 0:
                return {'valid': False, 'reason': '订单数量必须大于0'}
            
            if order_request.order_type == OrderType.LIMIT and (not order_request.price or order_request.price <= 0):
                return {'valid': False, 'reason': '限价单必须指定有效价格'}
            
            # 最小数量检查
            if order_request.min_quantity and order_request.min_quantity > order_request.quantity:
                return {'valid': False, 'reason': '最小数量不能大于订单数量'}
            
            # 显示数量检查
            if order_request.max_show_quantity and order_request.max_show_quantity > order_request.quantity:
                return {'valid': False, 'reason': '最大显示数量不能大于订单数量'}
            
            return {'valid': True, 'reason': ''}
            
        except Exception as e:
            logger.error(f"❌ 订单预检查异常: {e}")
            return {'valid': False, 'reason': f'预检查异常: {e}'}
    
    def _risk_check_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """订单风险检查"""
        try:
            # 日订单数量检查
            if self.daily_order_count >= self.max_daily_orders:
                return {'valid': False, 'reason': f'超过日最大订单数: {self.max_daily_orders}'}
            
            # 订单金额检查
            estimated_value = order_request.quantity * (order_request.price or 100)  # 简化估算
            if estimated_value > self.max_order_value:
                return {'valid': False, 'reason': f'超过最大订单金额: {self.max_order_value:,.0f}'}
            
            # 持仓集中度检查
            if order_request.side == OrderSide.BUY:
                concentration_check = self._check_position_concentration(order_request)
                if not concentration_check['valid']:
                    return concentration_check
            
            return {'valid': True, 'reason': ''}
            
        except Exception as e:
            logger.error(f"❌ 订单风险检查异常: {e}")
            return {'valid': False, 'reason': f'风险检查异常: {e}'}
    
    def _check_position_concentration(self, order_request: OrderRequest) -> Dict[str, Any]:
        """检查持仓集中度"""
        try:
            account_info = self.trading_engine.get_account_info()
            total_value = account_info.get('total_value', 0)
            
            if total_value <= 0:
                return {'valid': True, 'reason': ''}
            
            # 计算新增持仓价值
            estimated_value = order_request.quantity * (order_request.price or 100)
            
            # 获取当前持仓
            current_position = self.trading_engine.get_position(order_request.symbol)
            current_value = current_position.market_value if current_position else 0
            
            # 计算新的持仓比例
            new_position_value = current_value + estimated_value
            concentration_ratio = new_position_value / total_value
            
            if concentration_ratio > self.max_position_concentration:
                return {
                    'valid': False, 
                    'reason': f'超过最大持仓集中度: {concentration_ratio:.2%} > {self.max_position_concentration:.2%}'
                }
            
            return {'valid': True, 'reason': ''}
            
        except Exception as e:
            logger.error(f"❌ 持仓集中度检查异常: {e}")
            return {'valid': True, 'reason': ''}  # 异常时通过检查
    
    def _add_order_monitor(self, order_id: str, order_request: OrderRequest):
        """添加订单监控"""
        try:
            self.order_monitors[order_id] = {
                'order_request': order_request,
                'create_time': datetime.now(),
                'last_check_time': datetime.now(),
                'check_count': 0,
                'alerts': []
            }
            
        except Exception as e:
            logger.error(f"❌ 添加订单监控失败: {order_id} - {e}")
    
    def _monitoring_loop(self):
        """订单监控循环"""
        logger.info("🔍 订单监控循环开始")
        
        while self.is_monitoring:
            try:
                # 检查所有监控中的订单
                for order_id in list(self.order_monitors.keys()):
                    self._check_order_monitor(order_id)
                
                # 清理已完成的订单监控
                self._cleanup_completed_monitors()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 订单监控循环异常: {e}")
                time.sleep(5)
        
        logger.info("⏹️ 订单监控循环结束")
    
    def _check_order_monitor(self, order_id: str):
        """检查单个订单监控"""
        try:
            if order_id not in self.order_monitors:
                return
            
            monitor = self.order_monitors[order_id]
            order = self.trading_engine.get_order(order_id)
            
            if not order:
                return
            
            monitor['last_check_time'] = datetime.now()
            monitor['check_count'] += 1
            
            # 检查订单超时
            if order.is_active:
                elapsed_time = datetime.now() - order.create_time
                
                # 市价单超时检查（30秒）
                if order.order_type == OrderType.MARKET and elapsed_time > timedelta(seconds=30):
                    alert = f"市价单超时未成交: {elapsed_time.total_seconds():.0f}秒"
                    monitor['alerts'].append(alert)
                    logger.warning(f"⚠️ {alert}")
                
                # 限价单超时检查（5分钟）
                elif order.order_type == OrderType.LIMIT and elapsed_time > timedelta(minutes=5):
                    alert = f"限价单长时间未成交: {elapsed_time.total_seconds()/60:.1f}分钟"
                    monitor['alerts'].append(alert)
                    logger.warning(f"⚠️ {alert}")
            
        except Exception as e:
            logger.error(f"❌ 检查订单监控失败: {order_id} - {e}")
    
    def _cleanup_completed_monitors(self):
        """清理已完成的订单监控"""
        try:
            completed_orders = []
            
            for order_id, monitor in self.order_monitors.items():
                order = self.trading_engine.get_order(order_id)
                
                if not order or not order.is_active:
                    completed_orders.append(order_id)
            
            for order_id in completed_orders:
                del self.order_monitors[order_id]
            
            if completed_orders:
                logger.debug(f"🧹 清理已完成订单监控: {len(completed_orders)}个")
            
        except Exception as e:
            logger.error(f"❌ 清理订单监控失败: {e}")
    
    def _reset_daily_stats_if_needed(self):
        """重置日统计（如果需要）"""
        try:
            current_date = datetime.now().date()
            
            if current_date != self.last_reset_date:
                self.daily_order_count = 0
                self.daily_trade_count = 0
                self.daily_volume = 0.0
                self.last_reset_date = current_date
                
                logger.info(f"🔄 重置日统计: {current_date}")
            
        except Exception as e:
            logger.error(f"❌ 重置日统计失败: {e}")
    
    def _on_order_update(self, order: Order):
        """订单更新回调"""
        try:
            # 保存到历史
            self.order_history[order.order_id] = order
            
            # 触发用户回调
            for callback in self.order_callbacks:
                try:
                    callback(order)
                except Exception as e:
                    logger.error(f"❌ 订单回调执行失败: {e}")
            
        except Exception as e:
            logger.error(f"❌ 订单更新回调失败: {e}")
    
    def _on_trade_update(self, trade: Trade):
        """成交更新回调"""
        try:
            # 更新统计
            self.daily_trade_count += 1
            self.daily_volume += trade.quantity * trade.price
            
            logger.info(f"📊 成交统计更新: 日成交{self.daily_trade_count}笔, 日成交额{self.daily_volume:,.0f}")
            
        except Exception as e:
            logger.error(f"❌ 成交更新回调失败: {e}")
    
    def add_order_callback(self, callback: Callable):
        """添加订单回调"""
        self.order_callbacks.append(callback)
    
    def add_risk_callback(self, callback: Callable):
        """添加风险回调"""
        self.risk_callbacks.append(callback)
    
    def get_daily_statistics(self) -> Dict[str, Any]:
        """获取日统计信息"""
        try:
            self._reset_daily_stats_if_needed()
            
            return {
                'date': self.last_reset_date.isoformat(),
                'order_count': self.daily_order_count,
                'trade_count': self.daily_trade_count,
                'volume': self.daily_volume,
                'active_monitors': len(self.order_monitors),
                'max_daily_orders': self.max_daily_orders,
                'remaining_orders': self.max_daily_orders - self.daily_order_count
            }
            
        except Exception as e:
            logger.error(f"❌ 获取日统计失败: {e}")
            return {}
    
    def get_order_monitor_status(self) -> Dict[str, Any]:
        """获取订单监控状态"""
        try:
            total_monitors = len(self.order_monitors)
            alert_count = sum(len(monitor['alerts']) for monitor in self.order_monitors.values())
            
            return {
                'is_monitoring': self.is_monitoring,
                'total_monitors': total_monitors,
                'alert_count': alert_count,
                'monitors': {
                    order_id: {
                        'create_time': monitor['create_time'].isoformat(),
                        'check_count': monitor['check_count'],
                        'alert_count': len(monitor['alerts'])
                    }
                    for order_id, monitor in self.order_monitors.items()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 获取监控状态失败: {e}")
            return {}
