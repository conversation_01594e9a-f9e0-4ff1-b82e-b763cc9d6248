"""
TWAP算法 (Time Weighted Average Price)
时间加权平均价格算法，将大订单分解为多个小订单在指定时间内均匀执行
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import time
import math

from trading_execution.trading_engine.simulation_engine import OrderType, OrderSide
from trading_execution.order_manager.order_manager import OrderManager, OrderRequest, OrderPriority

logger = logging.getLogger(__name__)

class TWAPStatus(Enum):
    """TWAP状态"""
    PENDING = "PENDING"         # 待开始
    RUNNING = "RUNNING"         # 执行中
    PAUSED = "PAUSED"           # 已暂停
    COMPLETED = "COMPLETED"     # 已完成
    CANCELLED = "CANCELLED"     # 已取消
    FAILED = "FAILED"           # 执行失败

@dataclass
class TWAPConfig:
    """TWAP配置"""
    symbol: str
    side: OrderSide
    total_quantity: int
    duration_minutes: int           # 执行时长（分钟）
    slice_interval_seconds: int = 60  # 切片间隔（秒）
    participation_rate: float = 0.1   # 参与率（占成交量比例）
    price_limit: Optional[float] = None  # 价格限制
    min_slice_size: int = 100       # 最小切片大小
    max_slice_size: int = 10000     # 最大切片大小
    strategy_id: Optional[str] = None

@dataclass
class TWAPSlice:
    """TWAP切片"""
    slice_id: str
    target_quantity: int
    target_time: datetime
    order_id: Optional[str] = None
    executed_quantity: int = 0
    avg_price: float = 0.0
    status: str = "PENDING"

class TWAPAlgorithm:
    """TWAP算法执行器"""
    
    def __init__(self, order_manager: OrderManager):
        self.order_manager = order_manager
        
        # TWAP实例管理
        self.twap_instances: Dict[str, Dict[str, Any]] = {}
        
        # 执行线程
        self.execution_threads: Dict[str, threading.Thread] = {}
        
        # 市场数据缓存
        self.market_data_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info("⏰ TWAP算法初始化完成")
    
    def start_twap(self, config: TWAPConfig) -> str:
        """
        启动TWAP执行
        
        Args:
            config: TWAP配置
            
        Returns:
            TWAP实例ID
        """
        try:
            # 生成实例ID
            twap_id = f"TWAP_{config.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 验证配置
            validation_result = self._validate_config(config)
            if not validation_result['valid']:
                logger.error(f"❌ TWAP配置验证失败: {validation_result['reason']}")
                return ""
            
            # 生成执行计划
            execution_plan = self._generate_execution_plan(config)
            
            # 创建TWAP实例
            twap_instance = {
                'twap_id': twap_id,
                'config': config,
                'execution_plan': execution_plan,
                'status': TWAPStatus.PENDING,
                'start_time': None,
                'end_time': None,
                'current_slice_index': 0,
                'total_executed': 0,
                'total_cost': 0.0,
                'avg_execution_price': 0.0,
                'slices': execution_plan,
                'orders': [],
                'statistics': {
                    'slices_completed': 0,
                    'slices_failed': 0,
                    'total_commission': 0.0,
                    'execution_shortfall': 0.0
                }
            }
            
            self.twap_instances[twap_id] = twap_instance
            
            # 启动执行线程
            execution_thread = threading.Thread(
                target=self._execute_twap,
                args=(twap_id,),
                daemon=True
            )
            
            self.execution_threads[twap_id] = execution_thread
            execution_thread.start()
            
            logger.info(f"🚀 启动TWAP执行: {twap_id}")
            logger.info(f"  - 股票: {config.symbol}")
            logger.info(f"  - 方向: {config.side.value}")
            logger.info(f"  - 数量: {config.total_quantity:,}")
            logger.info(f"  - 时长: {config.duration_minutes}分钟")
            logger.info(f"  - 切片数: {len(execution_plan)}")
            
            return twap_id
            
        except Exception as e:
            logger.error(f"❌ 启动TWAP失败: {e}")
            return ""
    
    def pause_twap(self, twap_id: str) -> bool:
        """暂停TWAP执行"""
        try:
            if twap_id not in self.twap_instances:
                logger.error(f"❌ TWAP实例不存在: {twap_id}")
                return False
            
            twap_instance = self.twap_instances[twap_id]
            
            if twap_instance['status'] != TWAPStatus.RUNNING:
                logger.error(f"❌ TWAP状态不允许暂停: {twap_instance['status']}")
                return False
            
            twap_instance['status'] = TWAPStatus.PAUSED
            
            # 取消当前未成交的订单
            self._cancel_pending_orders(twap_id)
            
            logger.info(f"⏸️ TWAP已暂停: {twap_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 暂停TWAP失败: {twap_id} - {e}")
            return False
    
    def resume_twap(self, twap_id: str) -> bool:
        """恢复TWAP执行"""
        try:
            if twap_id not in self.twap_instances:
                logger.error(f"❌ TWAP实例不存在: {twap_id}")
                return False
            
            twap_instance = self.twap_instances[twap_id]
            
            if twap_instance['status'] != TWAPStatus.PAUSED:
                logger.error(f"❌ TWAP状态不允许恢复: {twap_instance['status']}")
                return False
            
            twap_instance['status'] = TWAPStatus.RUNNING
            
            logger.info(f"▶️ TWAP已恢复: {twap_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 恢复TWAP失败: {twap_id} - {e}")
            return False
    
    def cancel_twap(self, twap_id: str) -> bool:
        """取消TWAP执行"""
        try:
            if twap_id not in self.twap_instances:
                logger.error(f"❌ TWAP实例不存在: {twap_id}")
                return False
            
            twap_instance = self.twap_instances[twap_id]
            twap_instance['status'] = TWAPStatus.CANCELLED
            twap_instance['end_time'] = datetime.now()
            
            # 取消所有未成交的订单
            self._cancel_pending_orders(twap_id)
            
            logger.info(f"❌ TWAP已取消: {twap_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 取消TWAP失败: {twap_id} - {e}")
            return False
    
    def get_twap_status(self, twap_id: str) -> Optional[Dict[str, Any]]:
        """获取TWAP状态"""
        try:
            if twap_id not in self.twap_instances:
                return None
            
            twap_instance = self.twap_instances[twap_id]
            config = twap_instance['config']
            
            # 计算执行进度
            progress = twap_instance['total_executed'] / config.total_quantity if config.total_quantity > 0 else 0
            
            # 计算剩余时间
            remaining_time = None
            if twap_instance['start_time'] and twap_instance['status'] == TWAPStatus.RUNNING:
                elapsed = datetime.now() - twap_instance['start_time']
                total_duration = timedelta(minutes=config.duration_minutes)
                remaining_time = max(timedelta(0), total_duration - elapsed).total_seconds()
            
            return {
                'twap_id': twap_id,
                'symbol': config.symbol,
                'side': config.side.value,
                'status': twap_instance['status'].value,
                'total_quantity': config.total_quantity,
                'executed_quantity': twap_instance['total_executed'],
                'remaining_quantity': config.total_quantity - twap_instance['total_executed'],
                'progress': progress,
                'avg_execution_price': twap_instance['avg_execution_price'],
                'total_cost': twap_instance['total_cost'],
                'start_time': twap_instance['start_time'].isoformat() if twap_instance['start_time'] else None,
                'end_time': twap_instance['end_time'].isoformat() if twap_instance['end_time'] else None,
                'remaining_time_seconds': remaining_time,
                'current_slice': twap_instance['current_slice_index'],
                'total_slices': len(twap_instance['slices']),
                'statistics': twap_instance['statistics']
            }
            
        except Exception as e:
            logger.error(f"❌ 获取TWAP状态失败: {twap_id} - {e}")
            return None
    
    def get_all_twap_status(self) -> List[Dict[str, Any]]:
        """获取所有TWAP状态"""
        try:
            result = []
            
            for twap_id in self.twap_instances:
                status = self.get_twap_status(twap_id)
                if status:
                    result.append(status)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取所有TWAP状态失败: {e}")
            return []
    
    def _validate_config(self, config: TWAPConfig) -> Dict[str, Any]:
        """验证TWAP配置"""
        try:
            if config.total_quantity <= 0:
                return {'valid': False, 'reason': '总数量必须大于0'}
            
            if config.duration_minutes <= 0:
                return {'valid': False, 'reason': '执行时长必须大于0'}
            
            if config.slice_interval_seconds <= 0:
                return {'valid': False, 'reason': '切片间隔必须大于0'}
            
            if not (0 < config.participation_rate <= 1):
                return {'valid': False, 'reason': '参与率必须在0-1之间'}
            
            if config.min_slice_size >= config.max_slice_size:
                return {'valid': False, 'reason': '最小切片大小必须小于最大切片大小'}
            
            # 检查执行时间是否合理
            total_slices = (config.duration_minutes * 60) // config.slice_interval_seconds
            if total_slices < 1:
                return {'valid': False, 'reason': '执行时间太短，无法生成有效切片'}
            
            return {'valid': True, 'reason': ''}
            
        except Exception as e:
            logger.error(f"❌ 验证TWAP配置异常: {e}")
            return {'valid': False, 'reason': f'验证异常: {e}'}
    
    def _generate_execution_plan(self, config: TWAPConfig) -> List[TWAPSlice]:
        """生成执行计划"""
        try:
            slices = []
            
            # 计算切片数量
            total_duration_seconds = config.duration_minutes * 60
            slice_count = total_duration_seconds // config.slice_interval_seconds
            
            # 计算每个切片的目标数量
            base_slice_size = config.total_quantity // slice_count
            remaining_quantity = config.total_quantity % slice_count
            
            start_time = datetime.now()
            
            for i in range(slice_count):
                # 计算切片数量
                slice_quantity = base_slice_size
                if i < remaining_quantity:
                    slice_quantity += 1
                
                # 应用大小限制
                slice_quantity = max(config.min_slice_size, 
                                   min(config.max_slice_size, slice_quantity))
                
                # 计算执行时间
                target_time = start_time + timedelta(seconds=i * config.slice_interval_seconds)
                
                # 创建切片
                slice_obj = TWAPSlice(
                    slice_id=f"{config.symbol}_SLICE_{i+1}",
                    target_quantity=slice_quantity,
                    target_time=target_time
                )
                
                slices.append(slice_obj)
            
            logger.info(f"📋 生成TWAP执行计划: {len(slices)}个切片")
            
            return slices
            
        except Exception as e:
            logger.error(f"❌ 生成执行计划失败: {e}")
            return []
    
    def _execute_twap(self, twap_id: str):
        """执行TWAP算法"""
        try:
            twap_instance = self.twap_instances[twap_id]
            config = twap_instance['config']
            
            # 更新状态
            twap_instance['status'] = TWAPStatus.RUNNING
            twap_instance['start_time'] = datetime.now()
            
            logger.info(f"🏃 开始执行TWAP: {twap_id}")
            
            # 执行每个切片
            for i, slice_obj in enumerate(twap_instance['slices']):
                twap_instance['current_slice_index'] = i
                
                # 检查状态
                if twap_instance['status'] == TWAPStatus.CANCELLED:
                    logger.info(f"❌ TWAP已取消，停止执行: {twap_id}")
                    break
                
                # 等待到执行时间
                self._wait_for_slice_time(slice_obj.target_time, twap_instance)
                
                # 检查暂停状态
                while twap_instance['status'] == TWAPStatus.PAUSED:
                    time.sleep(1)
                
                # 检查取消状态
                if twap_instance['status'] == TWAPStatus.CANCELLED:
                    break
                
                # 执行切片
                self._execute_slice(twap_id, slice_obj)
            
            # 完成执行
            if twap_instance['status'] == TWAPStatus.RUNNING:
                twap_instance['status'] = TWAPStatus.COMPLETED
                twap_instance['end_time'] = datetime.now()
                
                logger.info(f"✅ TWAP执行完成: {twap_id}")
                logger.info(f"  - 总执行: {twap_instance['total_executed']:,}")
                logger.info(f"  - 平均价格: {twap_instance['avg_execution_price']:.4f}")
            
        except Exception as e:
            logger.error(f"❌ TWAP执行异常: {twap_id} - {e}")
            twap_instance['status'] = TWAPStatus.FAILED
            twap_instance['end_time'] = datetime.now()
    
    def _wait_for_slice_time(self, target_time: datetime, twap_instance: Dict[str, Any]):
        """等待切片执行时间"""
        try:
            while datetime.now() < target_time:
                # 检查状态
                if twap_instance['status'] in [TWAPStatus.CANCELLED, TWAPStatus.PAUSED]:
                    break
                
                time.sleep(0.1)
                
        except Exception as e:
            logger.error(f"❌ 等待切片时间失败: {e}")
    
    def _execute_slice(self, twap_id: str, slice_obj: TWAPSlice):
        """执行单个切片"""
        try:
            twap_instance = self.twap_instances[twap_id]
            config = twap_instance['config']
            
            # 调整切片大小（基于市场条件）
            adjusted_quantity = self._adjust_slice_size(config, slice_obj.target_quantity)
            
            # 创建订单请求
            order_request = OrderRequest(
                symbol=config.symbol,
                side=config.side,
                order_type=OrderType.LIMIT if config.price_limit else OrderType.MARKET,
                quantity=adjusted_quantity,
                price=config.price_limit,
                strategy_id=config.strategy_id,
                priority=OrderPriority.NORMAL
            )
            
            # 提交订单
            submit_result = self.order_manager.submit_order(order_request)
            
            if submit_result['success']:
                slice_obj.order_id = submit_result['order_id']
                slice_obj.status = "SUBMITTED"
                
                twap_instance['orders'].append(submit_result['order_id'])
                
                logger.info(f"📝 TWAP切片订单提交: {slice_obj.slice_id} -> {slice_obj.order_id}")
                
                # 等待订单完成（简化处理）
                self._wait_for_slice_completion(twap_id, slice_obj)
                
            else:
                slice_obj.status = "FAILED"
                twap_instance['statistics']['slices_failed'] += 1
                
                logger.error(f"❌ TWAP切片订单失败: {slice_obj.slice_id} - {submit_result['reason']}")
            
        except Exception as e:
            logger.error(f"❌ 执行TWAP切片失败: {slice_obj.slice_id} - {e}")
            slice_obj.status = "FAILED"
            twap_instance['statistics']['slices_failed'] += 1
    
    def _adjust_slice_size(self, config: TWAPConfig, target_quantity: int) -> int:
        """调整切片大小"""
        try:
            # 简化处理：基于参与率调整
            # 实际应该基于实时成交量数据
            
            # 获取市场数据
            market_data = self.market_data_cache.get(config.symbol, {})
            recent_volume = market_data.get('volume', 1000000)  # 默认成交量
            
            # 计算基于参与率的最大切片大小
            max_participation_size = int(recent_volume * config.participation_rate)
            
            # 应用限制
            adjusted_size = min(target_quantity, max_participation_size)
            adjusted_size = max(config.min_slice_size, adjusted_size)
            adjusted_size = min(config.max_slice_size, adjusted_size)
            
            return adjusted_size
            
        except Exception as e:
            logger.error(f"❌ 调整切片大小失败: {e}")
            return target_quantity
    
    def _wait_for_slice_completion(self, twap_id: str, slice_obj: TWAPSlice):
        """等待切片完成"""
        try:
            # 简化处理：等待固定时间
            # 实际应该监控订单状态
            time.sleep(5)
            
            # 模拟成交
            slice_obj.executed_quantity = slice_obj.target_quantity
            slice_obj.avg_price = 100.0  # 模拟价格
            slice_obj.status = "COMPLETED"
            
            # 更新TWAP统计
            twap_instance = self.twap_instances[twap_id]
            twap_instance['total_executed'] += slice_obj.executed_quantity
            twap_instance['total_cost'] += slice_obj.executed_quantity * slice_obj.avg_price
            
            if twap_instance['total_executed'] > 0:
                twap_instance['avg_execution_price'] = twap_instance['total_cost'] / twap_instance['total_executed']
            
            twap_instance['statistics']['slices_completed'] += 1
            
        except Exception as e:
            logger.error(f"❌ 等待切片完成失败: {e}")
    
    def _cancel_pending_orders(self, twap_id: str):
        """取消待处理订单"""
        try:
            twap_instance = self.twap_instances[twap_id]
            
            for order_id in twap_instance['orders']:
                self.order_manager.cancel_order(order_id, "TWAP取消")
            
        except Exception as e:
            logger.error(f"❌ 取消待处理订单失败: {twap_id} - {e}")
    
    def update_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """更新市场数据"""
        self.market_data_cache[symbol] = market_data
