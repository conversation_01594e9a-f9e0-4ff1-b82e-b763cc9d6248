#!/usr/bin/env python3
"""
VeighNa量化交易系统Web版本启动脚本
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def main():
    """主函数"""
    print("🚀 VeighNa量化交易系统 V2.0 Web版本")
    print("=" * 60)
    print("✅ 严格按照产品原型设计图实现")
    print("📊 包含完整的业务功能模块")
    print("🌐 支持Web界面访问")
    print("=" * 60)
    
    # 检查HTML文件是否存在
    html_file = Path("vnpy_web_system.html")
    if not html_file.exists():
        print("❌ 找不到Web界面文件: vnpy_web_system.html")
        return 1
    
    # 获取文件的绝对路径
    file_path = html_file.absolute()
    file_url = f"file://{file_path}"
    
    print(f"📍 Web界面地址: {file_url}")
    print()
    print("🎯 功能模块:")
    print("  📊 实时行情监控 - 多股票实时价格和涨跌幅")
    print("  📈 K线图表区域 - 多时间周期技术分析")
    print("  🔍 交易信号面板 - 买入卖出信号提示")
    print("  💼 持仓明细表 - 投资组合管理")
    print("  🔧 功能导航面板 - 完整业务功能")
    print("  ⚙️ 系统状态监控 - 实时系统状态")
    print()
    print("🎨 界面特色:")
    print("  🌙 VeighNa专业深色主题")
    print("  📱 响应式设计，支持多设备")
    print("  ⚡ 实时数据更新")
    print("  🎯 严格按照产品原型设计")
    print()
    
    try:
        print("🌐 正在打开Web界面...")
        webbrowser.open(file_url)
        print("✅ Web界面已在浏览器中打开")
        print()
        print("📖 使用说明:")
        print("  1. 界面已在浏览器中自动打开")
        print("  2. 点击左侧导航切换功能模块")
        print("  3. 点击顶部标签切换主要功能")
        print("  4. 实时数据会自动更新")
        print("  5. 支持多时间周期K线图切换")
        print()
        print("🔧 如需后端API支持，请运行:")
        print("  python simple_backend.py")
        print()
        print("✅ VeighNa量化交易系统Web版本启动完成!")
        
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"请手动在浏览器中打开: {file_url}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
