#!/usr/bin/env python3
"""
VeighNa量化交易系统数据库架构设计
支持全A股4000+股票数据的高性能存储和查询
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean, Index, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()

class StockBasicInfo(Base):
    """股票基本信息表"""
    __tablename__ = 'stock_basic_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), unique=True, nullable=False, comment='股票代码')
    name = Column(String(50), nullable=False, comment='股票名称')
    exchange = Column(String(10), nullable=False, comment='交易所')
    market = Column(String(10), nullable=False, comment='市场类型')
    industry = Column(String(50), comment='所属行业')
    sector = Column(String(50), comment='所属板块')
    list_date = Column(DateTime, comment='上市日期')
    delist_date = Column(DateTime, comment='退市日期')
    is_active = Column(Boolean, default=True, comment='是否活跃')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_symbol', 'symbol'),
        Index('idx_exchange', 'exchange'),
        Index('idx_industry', 'industry'),
        Index('idx_is_active', 'is_active'),
    )

class StockDailyData(Base):
    """股票日线数据表"""
    __tablename__ = 'stock_daily_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, comment='股票代码')
    trade_date = Column(DateTime, nullable=False, comment='交易日期')
    open_price = Column(Float, comment='开盘价')
    high_price = Column(Float, comment='最高价')
    low_price = Column(Float, comment='最低价')
    close_price = Column(Float, comment='收盘价')
    volume = Column(Integer, comment='成交量')
    amount = Column(Float, comment='成交额')
    turnover_rate = Column(Float, comment='换手率')
    pe_ratio = Column(Float, comment='市盈率')
    pb_ratio = Column(Float, comment='市净率')
    total_mv = Column(Float, comment='总市值')
    circ_mv = Column(Float, comment='流通市值')
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_symbol_date', 'symbol', 'trade_date'),
        Index('idx_trade_date', 'trade_date'),
        Index('idx_symbol', 'symbol'),
    )

class StockMinuteData(Base):
    """股票分钟数据表"""
    __tablename__ = 'stock_minute_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, comment='股票代码')
    datetime = Column(DateTime, nullable=False, comment='时间')
    open_price = Column(Float, comment='开盘价')
    high_price = Column(Float, comment='最高价')
    low_price = Column(Float, comment='最低价')
    close_price = Column(Float, comment='收盘价')
    volume = Column(Integer, comment='成交量')
    amount = Column(Float, comment='成交额')
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_symbol_datetime', 'symbol', 'datetime'),
        Index('idx_datetime', 'datetime'),
    )

class StockTechnicalIndicators(Base):
    """股票技术指标表"""
    __tablename__ = 'stock_technical_indicators'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, comment='股票代码')
    trade_date = Column(DateTime, nullable=False, comment='交易日期')
    
    # 趋势指标
    sma_5 = Column(Float, comment='5日简单移动平均')
    sma_10 = Column(Float, comment='10日简单移动平均')
    sma_20 = Column(Float, comment='20日简单移动平均')
    sma_60 = Column(Float, comment='60日简单移动平均')
    ema_12 = Column(Float, comment='12日指数移动平均')
    ema_26 = Column(Float, comment='26日指数移动平均')
    
    # MACD指标
    macd_dif = Column(Float, comment='MACD DIF')
    macd_dea = Column(Float, comment='MACD DEA')
    macd_histogram = Column(Float, comment='MACD柱状图')
    
    # 布林带指标
    boll_upper = Column(Float, comment='布林带上轨')
    boll_middle = Column(Float, comment='布林带中轨')
    boll_lower = Column(Float, comment='布林带下轨')
    
    # 动量指标
    rsi_6 = Column(Float, comment='6日RSI')
    rsi_14 = Column(Float, comment='14日RSI')
    kdj_k = Column(Float, comment='KDJ K值')
    kdj_d = Column(Float, comment='KDJ D值')
    kdj_j = Column(Float, comment='KDJ J值')
    
    # 成交量指标
    obv = Column(Float, comment='能量潮指标')
    vol_ratio = Column(Float, comment='量比')
    
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_symbol_date_tech', 'symbol', 'trade_date'),
        Index('idx_trade_date_tech', 'trade_date'),
    )

class StockFundamentalData(Base):
    """股票基本面数据表"""
    __tablename__ = 'stock_fundamental_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, comment='股票代码')
    report_date = Column(DateTime, nullable=False, comment='报告期')
    
    # 估值指标
    pe_ratio = Column(Float, comment='市盈率')
    pb_ratio = Column(Float, comment='市净率')
    ps_ratio = Column(Float, comment='市销率')
    pcf_ratio = Column(Float, comment='市现率')
    
    # 盈利能力
    roe = Column(Float, comment='净资产收益率')
    roa = Column(Float, comment='总资产收益率')
    roic = Column(Float, comment='投入资本回报率')
    gross_profit_margin = Column(Float, comment='毛利率')
    net_profit_margin = Column(Float, comment='净利率')
    
    # 成长性指标
    revenue_growth = Column(Float, comment='营收增长率')
    profit_growth = Column(Float, comment='净利润增长率')
    eps_growth = Column(Float, comment='每股收益增长率')
    
    # 财务健康度
    debt_to_equity = Column(Float, comment='资产负债率')
    current_ratio = Column(Float, comment='流动比率')
    quick_ratio = Column(Float, comment='速动比率')
    cash_ratio = Column(Float, comment='现金比率')
    
    # 运营效率
    inventory_turnover = Column(Float, comment='存货周转率')
    receivables_turnover = Column(Float, comment='应收账款周转率')
    total_asset_turnover = Column(Float, comment='总资产周转率')
    
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_symbol_report_fund', 'symbol', 'report_date'),
        Index('idx_report_date_fund', 'report_date'),
    )

class StockSelectionResults(Base):
    """股票选股结果表"""
    __tablename__ = 'stock_selection_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    selection_id = Column(String(50), nullable=False, comment='选股批次ID')
    symbol = Column(String(10), nullable=False, comment='股票代码')
    selection_date = Column(DateTime, nullable=False, comment='选股日期')
    
    # 评分详情
    total_score = Column(Float, comment='总评分')
    technical_score = Column(Float, comment='技术面评分')
    fundamental_score = Column(Float, comment='基本面评分')
    market_score = Column(Float, comment='市场表现评分')
    
    # 排名信息
    rank = Column(Integer, comment='排名')
    recommendation = Column(String(20), comment='推荐等级')
    reason = Column(Text, comment='推荐理由')
    
    # 选股参数
    technical_weight = Column(Float, comment='技术面权重')
    fundamental_weight = Column(Float, comment='基本面权重')
    market_weight = Column(Float, comment='市场权重')
    
    is_selected = Column(Boolean, default=True, comment='是否被选中')
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_selection_id', 'selection_id'),
        Index('idx_symbol_selection', 'symbol', 'selection_date'),
        Index('idx_selection_date', 'selection_date'),
        Index('idx_total_score', 'total_score'),
    )

class TradingSignals(Base):
    """交易信号表"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    signal_id = Column(String(50), unique=True, nullable=False, comment='信号ID')
    symbol = Column(String(10), nullable=False, comment='股票代码')
    signal_type = Column(String(10), nullable=False, comment='信号类型')  # BUY/SELL
    signal_time = Column(DateTime, nullable=False, comment='信号时间')
    
    # 信号详情
    price = Column(Float, comment='信号价格')
    score = Column(Float, comment='信号评分')
    confidence = Column(Float, comment='置信度')
    reason = Column(Text, comment='信号原因')
    
    # 信号来源
    source_indicator = Column(String(50), comment='来源指标')
    source_strategy = Column(String(50), comment='来源策略')
    
    # 信号状态
    status = Column(String(20), default='active', comment='信号状态')  # active/executed/expired
    executed_price = Column(Float, comment='执行价格')
    executed_time = Column(DateTime, comment='执行时间')
    
    # 信号效果
    profit_loss = Column(Float, comment='盈亏')
    profit_loss_pct = Column(Float, comment='盈亏百分比')
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_signal_id', 'signal_id'),
        Index('idx_symbol_signal', 'symbol', 'signal_time'),
        Index('idx_signal_type', 'signal_type'),
        Index('idx_signal_status', 'status'),
        Index('idx_signal_time', 'signal_time'),
    )

class PortfolioPositions(Base):
    """投资组合持仓表"""
    __tablename__ = 'portfolio_positions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    portfolio_id = Column(String(50), nullable=False, comment='组合ID')
    symbol = Column(String(10), nullable=False, comment='股票代码')
    
    # 持仓信息
    quantity = Column(Integer, comment='持仓数量')
    avg_cost = Column(Float, comment='平均成本')
    current_price = Column(Float, comment='当前价格')
    market_value = Column(Float, comment='市值')
    
    # 盈亏信息
    unrealized_pnl = Column(Float, comment='未实现盈亏')
    unrealized_pnl_pct = Column(Float, comment='未实现盈亏百分比')
    realized_pnl = Column(Float, comment='已实现盈亏')
    
    # 权重信息
    weight = Column(Float, comment='权重')
    target_weight = Column(Float, comment='目标权重')
    
    # 风险指标
    var_1d = Column(Float, comment='1日VaR')
    beta = Column(Float, comment='贝塔值')
    
    # 时间信息
    first_buy_date = Column(DateTime, comment='首次买入日期')
    last_update_time = Column(DateTime, comment='最后更新时间')
    
    is_active = Column(Boolean, default=True, comment='是否活跃')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_portfolio_symbol', 'portfolio_id', 'symbol'),
        Index('idx_portfolio_id', 'portfolio_id'),
        Index('idx_is_active_pos', 'is_active'),
    )

class SystemLogs(Base):
    """系统日志表"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    log_id = Column(String(50), nullable=False, comment='日志ID')
    log_level = Column(String(10), nullable=False, comment='日志级别')
    module = Column(String(50), comment='模块名称')
    message = Column(Text, comment='日志消息')
    details = Column(Text, comment='详细信息')
    created_at = Column(DateTime, default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_log_level', 'log_level'),
        Index('idx_module', 'module'),
        Index('idx_created_at_log', 'created_at'),
    )

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        if database_url is None:
            # 默认使用SQLite
            database_url = "sqlite:///data/vnpy_system.db"
        
        self.engine = create_engine(
            database_url,
            pool_size=20,
            max_overflow=30,
            pool_timeout=30,
            pool_recycle=3600,
            echo=False
        )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ 数据库表创建完成")
    
    def drop_tables(self):
        """删除所有表"""
        Base.metadata.drop_all(bind=self.engine)
        print("⚠️ 数据库表已删除")
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def get_table_info(self):
        """获取表信息"""
        tables = []
        for table_name, table in Base.metadata.tables.items():
            tables.append({
                "name": table_name,
                "columns": len(table.columns),
                "indexes": len(table.indexes),
                "comment": table.comment or "无注释"
            })
        return tables

def main():
    """主函数 - 创建数据库"""
    print("🗄️ VeighNa量化交易系统数据库初始化")
    print("=" * 50)
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    # 创建表
    db_manager.create_tables()
    
    # 显示表信息
    tables = db_manager.get_table_info()
    print(f"\n📊 数据库表统计 (共{len(tables)}张表):")
    for table in tables:
        print(f"  📋 {table['name']}: {table['columns']}列, {table['indexes']}索引")
    
    print("\n✅ 数据库初始化完成!")

if __name__ == "__main__":
    main()
