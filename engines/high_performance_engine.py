#!/usr/bin/env python3
"""
高性能数据处理引擎
设计支持5000+股票数据处理的高性能引擎，包括数据采集、存储、分析等功能
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import logging
import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
import multiprocessing as mp
from dataclasses import dataclass
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class StockData:
    """股票数据结构"""
    symbol: str
    name: str
    exchange: str
    price: float
    volume: int
    market_cap: float
    pe_ratio: float
    pb_ratio: float
    technical_indicators: Dict[str, float]
    fundamental_data: Dict[str, float]
    timestamp: datetime

class HighPerformanceEngine:
    """高性能数据处理引擎"""
    
    def __init__(self, db_path='vnpy_trading.db', max_workers=8):
        self.db_path = db_path
        self.max_workers = max_workers
        
        # 线程池和进程池
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=mp.cpu_count())
        
        # 数据队列
        self.data_queue = queue.Queue(maxsize=10000)
        self.result_queue = queue.Queue(maxsize=10000)
        
        # 缓存系统
        self.data_cache = {}
        self.cache_lock = threading.RLock()
        
        # 性能监控
        self.performance_metrics = {
            'processed_stocks': 0,
            'processing_speed': 0,
            'cache_hit_rate': 0,
            'memory_usage': 0,
            'start_time': time.time()
        }
        
        # 工作线程
        self.workers = []
        self.is_running = False
        
        logger.info(f"✅ 高性能引擎初始化完成 (工作线程: {max_workers})")
    
    def start_engine(self):
        """启动引擎"""
        if self.is_running:
            logger.warning("⚠️ 引擎已在运行中")
            return
        
        self.is_running = True
        
        # 启动数据处理工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._data_processing_worker,
                name=f"DataWorker-{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        # 启动性能监控线程
        monitor_thread = threading.Thread(
            target=self._performance_monitor,
            name="PerformanceMonitor",
            daemon=True
        )
        monitor_thread.start()
        
        logger.info("🚀 高性能引擎启动完成")
    
    def stop_engine(self):
        """停止引擎"""
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.workers:
            worker.join(timeout=5)
        
        # 关闭线程池和进程池
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)
        
        logger.info("⏹️ 高性能引擎已停止")
    
    def _data_processing_worker(self):
        """数据处理工作线程"""
        while self.is_running:
            try:
                # 从队列获取任务
                task = self.data_queue.get(timeout=1)
                
                if task is None:
                    break
                
                # 处理任务
                result = self._process_task(task)
                
                if result:
                    self.result_queue.put(result)
                    self.performance_metrics['processed_stocks'] += 1
                
                self.data_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"数据处理工作线程异常: {e}")
                continue
    
    def _process_task(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理单个任务"""
        try:
            task_type = task.get('type')
            
            if task_type == 'stock_analysis':
                return self._analyze_stock(task['data'])
            elif task_type == 'batch_calculation':
                return self._batch_calculate(task['data'])
            elif task_type == 'data_validation':
                return self._validate_data(task['data'])
            else:
                logger.warning(f"未知任务类型: {task_type}")
                return None
                
        except Exception as e:
            logger.error(f"处理任务失败: {e}")
            return None
    
    def _analyze_stock(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析单只股票"""
        try:
            symbol = stock_data['symbol']
            
            # 检查缓存
            cache_key = f"analysis_{symbol}_{datetime.now().strftime('%Y%m%d')}"
            
            with self.cache_lock:
                if cache_key in self.data_cache:
                    self.performance_metrics['cache_hit_rate'] += 1
                    return self.data_cache[cache_key]
            
            # 执行分析
            analysis_result = {
                'symbol': symbol,
                'name': stock_data.get('name', ''),
                'technical_score': self._calculate_technical_score(stock_data),
                'fundamental_score': self._calculate_fundamental_score(stock_data),
                'market_score': self._calculate_market_score(stock_data),
                'risk_level': self._assess_risk_level(stock_data),
                'recommendation': '',
                'analysis_time': datetime.now().isoformat()
            }
            
            # 计算综合评分
            total_score = (
                analysis_result['technical_score'] * 0.4 +
                analysis_result['fundamental_score'] * 0.4 +
                analysis_result['market_score'] * 0.2
            )
            
            analysis_result['total_score'] = round(total_score, 2)
            
            # 生成推荐
            if total_score >= 85:
                analysis_result['recommendation'] = '强烈推荐'
            elif total_score >= 75:
                analysis_result['recommendation'] = '推荐'
            elif total_score >= 65:
                analysis_result['recommendation'] = '观察'
            else:
                analysis_result['recommendation'] = '谨慎'
            
            # 缓存结果
            with self.cache_lock:
                self.data_cache[cache_key] = analysis_result
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析股票 {stock_data.get('symbol', 'Unknown')} 失败: {e}")
            return None
    
    def _calculate_technical_score(self, stock_data: Dict[str, Any]) -> float:
        """计算技术面评分"""
        try:
            score = 0
            
            # MACD指标
            macd_dif = stock_data.get('macd_dif', 0)
            macd_dea = stock_data.get('macd_dea', 0)
            
            if macd_dif and macd_dea:
                if macd_dif > macd_dea and macd_dif > 0:
                    score += 25  # MACD金叉且在零轴上方
                elif macd_dif > macd_dea:
                    score += 15  # MACD金叉
            
            # RSI指标
            rsi = stock_data.get('rsi_14', 50)
            if 30 <= rsi <= 70:
                score += 20  # RSI在正常区间
            elif rsi < 30:
                score += 25  # RSI超卖
            
            # 价格与均线关系
            close_price = stock_data.get('close_price', 0)
            sma_20 = stock_data.get('sma_20', 0)
            
            if close_price and sma_20 and close_price > sma_20:
                score += 20  # 价格在20日均线上方
            
            # 成交量
            volume = stock_data.get('volume', 0)
            if volume > 1000000:  # 成交量大于100万手
                score += 15
            
            # 换手率
            turnover_rate = stock_data.get('turnover_rate', 0)
            if 2 <= turnover_rate <= 8:  # 适中的换手率
                score += 15
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算技术面评分失败: {e}")
            return 50
    
    def _calculate_fundamental_score(self, stock_data: Dict[str, Any]) -> float:
        """计算基本面评分"""
        try:
            score = 0
            
            # 市盈率
            pe_ratio = stock_data.get('pe_ratio', 0)
            if 0 < pe_ratio < 30:
                score += 25
            elif 30 <= pe_ratio < 50:
                score += 15
            
            # 市净率
            pb_ratio = stock_data.get('pb_ratio', 0)
            if 0 < pb_ratio < 3:
                score += 20
            elif 3 <= pb_ratio < 5:
                score += 10
            
            # ROE
            roe = stock_data.get('roe', 0)
            if roe > 15:
                score += 25
            elif roe > 10:
                score += 15
            
            # 毛利率
            gross_profit_margin = stock_data.get('gross_profit_margin', 0)
            if gross_profit_margin > 30:
                score += 15
            elif gross_profit_margin > 20:
                score += 10
            
            # 营收增长率
            revenue_growth = stock_data.get('revenue_growth', 0)
            if revenue_growth > 20:
                score += 15
            elif revenue_growth > 10:
                score += 10
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算基本面评分失败: {e}")
            return 50
    
    def _calculate_market_score(self, stock_data: Dict[str, Any]) -> float:
        """计算市场表现评分"""
        try:
            score = 50  # 基础分
            
            # 市值规模
            market_cap = stock_data.get('market_cap', 0)
            if market_cap > 100000000000:  # 大于1000亿
                score += 20
            elif market_cap > 50000000000:  # 大于500亿
                score += 15
            elif market_cap > 10000000000:  # 大于100亿
                score += 10
            
            # 流动性
            volume = stock_data.get('volume', 0)
            turnover_rate = stock_data.get('turnover_rate', 0)
            
            if volume > 5000000 and turnover_rate > 1:  # 高流动性
                score += 20
            elif volume > 1000000 and turnover_rate > 0.5:  # 中等流动性
                score += 10
            
            # 价格稳定性（简化计算）
            close_price = stock_data.get('close_price', 0)
            if 5 <= close_price <= 100:  # 合理价格区间
                score += 10
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算市场表现评分失败: {e}")
            return 50
    
    def _assess_risk_level(self, stock_data: Dict[str, Any]) -> str:
        """评估风险等级"""
        try:
            risk_score = 0
            
            # 市盈率风险
            pe_ratio = stock_data.get('pe_ratio', 0)
            if pe_ratio > 50:
                risk_score += 2
            elif pe_ratio > 30:
                risk_score += 1
            
            # 市净率风险
            pb_ratio = stock_data.get('pb_ratio', 0)
            if pb_ratio > 5:
                risk_score += 2
            elif pb_ratio > 3:
                risk_score += 1
            
            # 换手率风险
            turnover_rate = stock_data.get('turnover_rate', 0)
            if turnover_rate > 10:
                risk_score += 2
            elif turnover_rate > 8:
                risk_score += 1
            
            # 价格风险
            close_price = stock_data.get('close_price', 0)
            if close_price < 2 or close_price > 200:
                risk_score += 2
            
            # 风险等级判断
            if risk_score >= 5:
                return '高风险'
            elif risk_score >= 3:
                return '中风险'
            else:
                return '低风险'
                
        except Exception as e:
            logger.error(f"评估风险等级失败: {e}")
            return '未知风险'
    
    def _batch_calculate(self, data_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量计算"""
        try:
            results = []
            
            for item in data_batch:
                result = self._analyze_stock(item)
                if result:
                    results.append(result)
            
            return {
                'batch_results': results,
                'processed_count': len(results),
                'batch_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"批量计算失败: {e}")
            return {'batch_results': [], 'processed_count': 0}
    
    def _validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据验证"""
        try:
            validation_result = {
                'symbol': data.get('symbol', ''),
                'is_valid': True,
                'errors': [],
                'warnings': []
            }
            
            # 基本字段验证
            required_fields = ['symbol', 'name', 'close_price']
            for field in required_fields:
                if not data.get(field):
                    validation_result['errors'].append(f"缺少必需字段: {field}")
                    validation_result['is_valid'] = False
            
            # 数值范围验证
            close_price = data.get('close_price', 0)
            if close_price <= 0:
                validation_result['errors'].append("收盘价必须大于0")
                validation_result['is_valid'] = False
            
            pe_ratio = data.get('pe_ratio', 0)
            if pe_ratio < 0 or pe_ratio > 1000:
                validation_result['warnings'].append("市盈率数值异常")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return {'is_valid': False, 'errors': [str(e)]}
    
    def _performance_monitor(self):
        """性能监控"""
        while self.is_running:
            try:
                current_time = time.time()
                elapsed_time = current_time - self.performance_metrics['start_time']
                
                if elapsed_time > 0:
                    self.performance_metrics['processing_speed'] = (
                        self.performance_metrics['processed_stocks'] / elapsed_time
                    )
                
                # 清理过期缓存
                self._cleanup_cache()
                
                # 每30秒输出一次性能指标
                if int(elapsed_time) % 30 == 0:
                    logger.info(f"📊 性能指标: "
                              f"处理股票数={self.performance_metrics['processed_stocks']}, "
                              f"处理速度={self.performance_metrics['processing_speed']:.2f}股/秒, "
                              f"缓存命中率={self.performance_metrics['cache_hit_rate']}")
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                time.sleep(5)
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        try:
            with self.cache_lock:
                current_date = datetime.now().strftime('%Y%m%d')
                expired_keys = []
                
                for key in self.data_cache.keys():
                    if current_date not in key:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.data_cache[key]
                
                if expired_keys:
                    logger.info(f"🧹 清理过期缓存: {len(expired_keys)} 项")
                    
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
    
    def submit_task(self, task: Dict[str, Any]) -> bool:
        """提交任务到处理队列"""
        try:
            self.data_queue.put(task, timeout=5)
            return True
        except queue.Full:
            logger.warning("任务队列已满，任务提交失败")
            return False
    
    def get_results(self, timeout: float = 1.0) -> List[Dict[str, Any]]:
        """获取处理结果"""
        results = []
        
        try:
            while True:
                result = self.result_queue.get(timeout=timeout)
                results.append(result)
                self.result_queue.task_done()
        except queue.Empty:
            pass
        
        return results
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def process_stock_batch(self, stock_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理股票数据"""
        try:
            # 提交批量任务
            batch_task = {
                'type': 'batch_calculation',
                'data': stock_data_list
            }
            
            if self.submit_task(batch_task):
                # 等待结果
                time.sleep(0.1)  # 给处理时间
                results = self.get_results(timeout=5.0)
                
                if results:
                    return results[0].get('batch_results', [])
            
            return []
            
        except Exception as e:
            logger.error(f"批量处理股票数据失败: {e}")
            return []

# 全局高性能引擎实例
high_performance_engine = HighPerformanceEngine()

def main():
    """测试主函数"""
    print("🚀 高性能数据处理引擎测试")
    print("=" * 60)
    
    try:
        # 启动引擎
        high_performance_engine.start_engine()
        
        # 模拟股票数据
        test_stocks = []
        for i in range(100):
            test_stocks.append({
                'symbol': f'00000{i:04d}',
                'name': f'测试股票{i}',
                'close_price': np.random.uniform(10, 100),
                'volume': np.random.randint(1000000, 50000000),
                'pe_ratio': np.random.uniform(8, 50),
                'pb_ratio': np.random.uniform(0.5, 8),
                'macd_dif': np.random.uniform(-0.5, 0.5),
                'macd_dea': np.random.uniform(-0.3, 0.3),
                'rsi_14': np.random.uniform(20, 80),
                'sma_20': np.random.uniform(10, 50),
                'roe': np.random.uniform(5, 25),
                'gross_profit_margin': np.random.uniform(10, 60),
                'revenue_growth': np.random.uniform(-20, 50),
                'turnover_rate': np.random.uniform(0.5, 8),
                'market_cap': np.random.uniform(1000000000, 100000000000)
            })
        
        # 批量处理
        start_time = time.time()
        results = high_performance_engine.process_stock_batch(test_stocks)
        end_time = time.time()
        
        print(f"✅ 批量处理完成")
        print(f"📊 处理股票数: {len(results)}")
        print(f"⏱️ 处理时间: {end_time - start_time:.2f}秒")
        print(f"🚀 处理速度: {len(results) / (end_time - start_time):.2f}股/秒")
        
        # 显示前5个结果
        print("\n📋 处理结果样本:")
        for i, result in enumerate(results[:5]):
            print(f"  {i+1}. {result['symbol']} {result['name']} - "
                  f"总分: {result['total_score']}, 推荐: {result['recommendation']}")
        
        # 显示性能指标
        metrics = high_performance_engine.get_performance_metrics()
        print(f"\n📈 性能指标:")
        print(f"  处理股票总数: {metrics['processed_stocks']}")
        print(f"  处理速度: {metrics['processing_speed']:.2f}股/秒")
        
        # 等待一段时间观察性能
        time.sleep(5)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        # 停止引擎
        high_performance_engine.stop_engine()
        print("✅ 引擎已停止")

if __name__ == "__main__":
    main()
