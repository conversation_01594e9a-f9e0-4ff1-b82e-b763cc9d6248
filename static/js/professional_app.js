// VeighNa量化交易系统专业版前端应用 - 2K分辨率优化

class VeighNaProfessionalApp {
    constructor() {
        this.currentModule = 'dashboard';
        this.refreshInterval = null;
        this.autoRefreshEnabled = false;
        this.klineChart = null;
        this.selectedStock = null;
        this.websocket = null;
        
        // 数据缓存
        this.dataCache = {
            stocks: [],
            signals: [],
            portfolio: [],
            selection: [],
            lastUpdate: null
        };
        
        // 配置
        this.config = {
            refreshInterval: 3000,  // 3秒刷新
            chartRefreshInterval: 5000,  // 5秒图表刷新
            maxRetries: 3,
            apiTimeout: 10000
        };
        
        this.init();
    }

    async init() {
        try {
            console.log('🚀 初始化VeighNa专业版系统');
            
            this.initEventListeners();
            this.initTimeDisplay();
            this.initWebSocket();
            await this.loadInitialData();
            this.initCharts();
            this.startAutoRefresh();
            
            console.log('✅ 系统初始化完成');
        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.showNotification('系统初始化失败', 'error');
        }
    }

    initEventListeners() {
        // 菜单切换
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const module = e.currentTarget.dataset.module;
                if (module) {
                    this.switchModule(module);
                }
            });
        });

        // 时间周期切换
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.updateKLineChart(e.target.dataset.period);
            });
        });

        // 市场筛选
        const marketFilter = document.getElementById('market-filter');
        if (marketFilter) {
            marketFilter.addEventListener('change', (e) => {
                this.filterStocks(e.target.value);
            });
        }

        // 股票选择器
        const stockSelect = document.getElementById('chart-stock-select');
        if (stockSelect) {
            stockSelect.addEventListener('change', (e) => {
                this.selectedStock = e.target.value;
                if (this.selectedStock) {
                    this.updateKLineChart();
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    initTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeStr;
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    initWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('✅ WebSocket连接已建立');
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('WebSocket消息解析失败:', error);
                }
            };
            
            this.websocket.onclose = () => {
                console.log('⚠️ WebSocket连接已断开');
                this.updateConnectionStatus(false);
                // 尝试重连
                setTimeout(() => this.initWebSocket(), 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket连接错误:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.updateConnectionStatus(false);
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'stock_update':
                this.handleStockUpdate(data.data);
                break;
            case 'signal_update':
                this.handleSignalUpdate(data.data);
                break;
            case 'portfolio_update':
                this.handlePortfolioUpdate(data.data);
                break;
            default:
                console.log('未知WebSocket消息类型:', data.type);
        }
    }

    updateConnectionStatus(connected) {
        const indicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.connection-status span:last-child');
        
        if (indicator && statusText) {
            if (connected) {
                indicator.classList.add('connected');
                statusText.textContent = '实时数据连接正常';
            } else {
                indicator.classList.remove('connected');
                statusText.textContent = '数据连接中断';
            }
        }
    }

    async loadInitialData() {
        try {
            console.log('📊 加载初始数据...');
            
            // 并行加载所有数据
            const [stocksData, signalsData, portfolioData, selectionData] = await Promise.all([
                this.fetchWithRetry('/api/realtime-data'),
                this.fetchWithRetry('/api/trading-signals'),
                this.fetchWithRetry('/api/portfolio'),
                this.fetchWithRetry('/api/stock-selection')
            ]);

            // 更新缓存
            this.dataCache.stocks = stocksData?.data?.stocks || [];
            this.dataCache.signals = signalsData?.data || {};
            this.dataCache.portfolio = portfolioData?.data || {};
            this.dataCache.selection = selectionData?.data || {};
            this.dataCache.lastUpdate = new Date();

            // 更新界面
            this.updateAllPanels();
            
            console.log('✅ 初始数据加载完成');
            
        } catch (error) {
            console.error('❌ 初始数据加载失败:', error);
            this.showMockData();
        }
    }

    async fetchWithRetry(url, options = {}, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.apiTimeout);
                
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
                
            } catch (error) {
                console.warn(`请求失败 (尝试 ${i + 1}/${retries}):`, error.message);
                
                if (i === retries - 1) {
                    throw error;
                }
                
                // 指数退避
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
            }
        }
    }

    showMockData() {
        console.log('📋 显示模拟数据');
        
        // 模拟股票数据
        const mockStocks = [
            { symbol: '000001', name: '平安银行', price: 12.85, change: 0.35, change_pct: 2.80, volume: 125000, turnover: 1606.25, turnover_rate: 0.85, pe_ratio: 5.2 },
            { symbol: '600519', name: '贵州茅台', price: 1680.00, change: -15.00, change_pct: -0.88, volume: 8500, turnover: 1428.0, turnover_rate: 0.68, pe_ratio: 28.5 },
            { symbol: '000858', name: '五粮液', price: 128.50, change: -2.25, change_pct: -1.72, volume: 68000, turnover: 873.8, turnover_rate: 1.25, pe_ratio: 22.1 },
            { symbol: '600036', name: '招商银行', price: 45.20, change: 1.18, change_pct: 2.68, volume: 95000, turnover: 429.4, turnover_rate: 0.92, pe_ratio: 6.8 },
            { symbol: '002415', name: '海康威视', price: 28.90, change: 0.48, change_pct: 1.69, volume: 115000, turnover: 332.35, turnover_rate: 1.15, pe_ratio: 18.5 }
        ];

        // 模拟交易信号
        const mockSignals = {
            buy_signals: [
                { symbol: '000001', name: '平安银行', signal_type: 'BUY', reason: 'MACD金叉形成，成交量放大1.2倍', score: 88.0, confidence: 0.9, signal_time: new Date().toISOString() },
                { symbol: '600036', name: '招商银行', signal_type: 'BUY', reason: 'RSI超卖反弹，价格反弹2.68%', score: 85.0, confidence: 0.8, signal_time: new Date().toISOString() }
            ],
            sell_signals: [
                { symbol: '600519', name: '贵州茅台', signal_type: 'SELL', reason: 'RSI超买回调，价格下跌0.88%', score: 75.0, confidence: 0.8, signal_time: new Date().toISOString() }
            ],
            statistics: {
                total_signals: 156,
                buy_signals: 89,
                sell_signals: 67,
                success_rate: 0.784
            }
        };

        // 模拟投资组合
        const mockPortfolio = {
            positions: [
                { symbol: '000001', name: '平安银行', quantity: 10000, avg_price: 12.50, current_price: 12.85, market_value: 128500, pnl: 3500, pnl_pct: 2.80, weight: 0.128 },
                { symbol: '600519', name: '贵州茅台', quantity: 100, avg_price: 1650.00, current_price: 1680.00, market_value: 168000, pnl: 3000, pnl_pct: 1.82, weight: 0.168 },
                { symbol: '000858', name: '五粮液', quantity: 800, avg_price: 125.00, current_price: 128.50, market_value: 102800, pnl: 2800, pnl_pct: 2.80, weight: 0.103 }
            ],
            risk_metrics: {
                portfolio_value: 1000000,
                total_pnl: 50000,
                total_pnl_pct: 5.26,
                sharpe_ratio: 1.85,
                max_drawdown: -0.08
            }
        };

        // 更新缓存
        this.dataCache.stocks = mockStocks;
        this.dataCache.signals = mockSignals;
        this.dataCache.portfolio = mockPortfolio;
        this.dataCache.lastUpdate = new Date();

        // 更新界面
        this.updateAllPanels();
    }

    updateAllPanels() {
        this.updateStockTable();
        this.updateSignalsPanel();
        this.updatePortfolioPanel();
        this.updateHeaderStats();
        this.updateStockSelector();
    }

    updateStockTable() {
        const tbody = document.getElementById('stock-table-body');
        if (!tbody || !this.dataCache.stocks.length) return;

        tbody.innerHTML = this.dataCache.stocks.map(stock => {
            const changeClass = stock.change > 0 ? 'price-up' : stock.change < 0 ? 'price-down' : 'price-neutral';
            const changeSign = stock.change > 0 ? '+' : '';
            
            return `
                <tr data-symbol="${stock.symbol}" onclick="app.selectStock('${stock.symbol}')">
                    <td class="stock-code">${stock.symbol}</td>
                    <td class="stock-name">${stock.name}</td>
                    <td class="${changeClass}">¥${stock.price.toFixed(2)}</td>
                    <td class="${changeClass}">${changeSign}${stock.change.toFixed(2)}</td>
                    <td class="${changeClass}">${changeSign}${stock.change_pct.toFixed(2)}%</td>
                    <td>${(stock.volume / 10000).toFixed(1)}</td>
                    <td>${(stock.turnover / 100000000).toFixed(2)}</td>
                    <td>${stock.turnover_rate?.toFixed(2) || '--'}%</td>
                    <td>${stock.pe_ratio?.toFixed(1) || '--'}</td>
                    <td>
                        <button class="action-btn" onclick="app.showStockDetail('${stock.symbol}')" title="详情">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="action-btn" onclick="app.addToWatchlist('${stock.symbol}')" title="加入自选">
                            <i class="fas fa-star"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    updateSignalsPanel() {
        const container = document.getElementById('signals-container');
        const buyCount = document.getElementById('buy-signals-count');
        const sellCount = document.getElementById('sell-signals-count');
        
        if (!container || !this.dataCache.signals) return;

        const buySignals = this.dataCache.signals.buy_signals || [];
        const sellSignals = this.dataCache.signals.sell_signals || [];

        if (buyCount) buyCount.textContent = buySignals.length;
        if (sellCount) sellCount.textContent = sellSignals.length;

        const allSignals = [...buySignals, ...sellSignals].sort((a, b) => 
            new Date(b.signal_time) - new Date(a.signal_time)
        );

        container.innerHTML = allSignals.map(signal => {
            const signalClass = signal.signal_type === 'BUY' ? 'buy-signal' : 'sell-signal';
            const typeClass = signal.signal_type.toLowerCase();
            const timeAgo = this.getTimeAgo(signal.signal_time);
            
            return `
                <div class="signal-item ${signalClass}">
                    <div class="signal-header">
                        <span class="signal-symbol">${signal.symbol} ${signal.name}</span>
                        <span class="signal-type ${typeClass}">${signal.signal_type}</span>
                    </div>
                    <div class="signal-reason">${signal.reason}</div>
                    <div class="signal-meta">
                        <span>评分: ${signal.score}</span>
                        <span>置信度: ${(signal.confidence * 100).toFixed(0)}%</span>
                        <span>${timeAgo}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    updatePortfolioPanel() {
        const tbody = document.getElementById('portfolio-table-body');
        const totalValue = document.getElementById('total-market-value');
        const totalPnl = document.getElementById('total-pnl');
        
        if (!tbody || !this.dataCache.portfolio.positions) return;

        const positions = this.dataCache.portfolio.positions;
        const totalMarketValue = positions.reduce((sum, pos) => sum + pos.market_value, 0);
        const totalPnlValue = positions.reduce((sum, pos) => sum + pos.pnl, 0);

        if (totalValue) totalValue.textContent = `¥${totalMarketValue.toLocaleString()}`;
        if (totalPnl) {
            const pnlClass = totalPnlValue >= 0 ? 'price-up' : 'price-down';
            totalPnl.innerHTML = `<span class="${pnlClass}">¥${totalPnlValue.toLocaleString()}</span>`;
        }

        tbody.innerHTML = positions.map(pos => {
            const pnlClass = pos.pnl >= 0 ? 'price-up' : 'price-down';
            const pnlSign = pos.pnl >= 0 ? '+' : '';
            
            return `
                <tr onclick="app.selectStock('${pos.symbol}')">
                    <td>${pos.symbol}</td>
                    <td>${pos.name}</td>
                    <td>${pos.quantity.toLocaleString()}</td>
                    <td>¥${pos.avg_price.toFixed(2)}</td>
                    <td>¥${pos.current_price.toFixed(2)}</td>
                    <td>¥${pos.market_value.toLocaleString()}</td>
                    <td class="${pnlClass}">${pnlSign}¥${pos.pnl.toLocaleString()}</td>
                    <td class="${pnlClass}">${pnlSign}${pos.pnl_pct.toFixed(2)}%</td>
                    <td>${(pos.weight * 100).toFixed(1)}%</td>
                </tr>
            `;
        }).join('');
    }

    updateHeaderStats() {
        const totalStocks = document.getElementById('total-stocks');
        const activeSignals = document.getElementById('active-signals');
        
        if (totalStocks) {
            totalStocks.textContent = this.dataCache.stocks.length;
        }
        
        if (activeSignals && this.dataCache.signals.statistics) {
            activeSignals.textContent = this.dataCache.signals.statistics.total_signals || 0;
        }
    }

    updateStockSelector() {
        const stockSelect = document.getElementById('chart-stock-select');
        if (!stockSelect || !this.dataCache.stocks.length) return;

        stockSelect.innerHTML = '<option value="">选择股票</option>' + 
            this.dataCache.stocks.map(stock => 
                `<option value="${stock.symbol}">${stock.symbol} ${stock.name}</option>`
            ).join('');
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours}小时前`;
        
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}天前`;
    }

    switchModule(moduleName) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-module="${moduleName}"]`)?.classList.add('active');

        // 切换内容模块
        document.querySelectorAll('.content-module').forEach(module => {
            module.classList.remove('active');
        });

        const targetModule = document.getElementById(`${moduleName}-module`);
        if (targetModule) {
            targetModule.classList.add('active');
        }

        this.currentModule = moduleName;

        // 根据模块加载相应数据
        this.loadModuleData(moduleName);
    }

    async loadModuleData(moduleName) {
        try {
            switch (moduleName) {
                case 'dashboard':
                    await this.loadInitialData();
                    break;
                case 'stock-selection':
                    await this.loadStockSelectionData();
                    break;
                case 'signal-tracking':
                    await this.loadSignalTrackingData();
                    break;
                case 'portfolio':
                    await this.loadPortfolioData();
                    break;
                default:
                    console.log(`模块 ${moduleName} 暂未实现`);
            }
        } catch (error) {
            console.error(`加载模块 ${moduleName} 数据失败:`, error);
        }
    }

    async loadStockSelectionData() {
        try {
            const data = await this.fetchWithRetry('/api/stock-selection');
            this.dataCache.selection = data.data || {};
            this.updateSelectionResults();
        } catch (error) {
            console.error('加载选股数据失败:', error);
            this.showMockSelectionResults();
        }
    }

    async loadSignalTrackingData() {
        try {
            const data = await this.fetchWithRetry('/api/trading-signals');
            this.dataCache.signals = data.data || {};
            this.updateSignalTrackingPanel();
        } catch (error) {
            console.error('加载信号跟踪数据失败:', error);
        }
    }

    async loadPortfolioData() {
        try {
            const data = await this.fetchWithRetry('/api/portfolio');
            this.dataCache.portfolio = data.data || {};
            this.updatePortfolioManagementPanel();
        } catch (error) {
            console.error('加载投资组合数据失败:', error);
        }
    }

    initCharts() {
        this.initKLineChart();
    }

    initKLineChart() {
        const chartContainer = document.getElementById('kline-chart');
        if (!chartContainer) return;

        this.klineChart = echarts.init(chartContainer, 'dark');
        this.updateKLineChart('1d');

        // 图表自适应
        window.addEventListener('resize', () => {
            if (this.klineChart) {
                this.klineChart.resize();
            }
        });
    }

    updateKLineChart(period = '1d') {
        if (!this.klineChart) return;

        const loading = document.getElementById('chart-loading');
        if (loading) loading.style.display = 'flex';

        // 模拟K线数据
        const dates = [];
        const data = [];
        const volumes = [];
        const basePrice = this.selectedStock ? this.getStockPrice(this.selectedStock) : 12.50;

        for (let i = 0; i < 60; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (59 - i));
            dates.push(date.toISOString().split('T')[0]);

            const open = basePrice + (Math.random() - 0.5) * 4;
            const close = open + (Math.random() - 0.5) * 2;
            const high = Math.max(open, close) + Math.random() * 1;
            const low = Math.min(open, close) - Math.random() * 1;
            const volume = Math.floor(Math.random() * 1000000 + 500000);

            data.push([open, close, low, high]);
            volumes.push(volume);
        }

        const option = {
            backgroundColor: 'transparent',
            animation: true,
            animationDuration: 1000,
            grid: [
                {
                    left: '8%',
                    right: '8%',
                    height: '65%'
                },
                {
                    left: '8%',
                    right: '8%',
                    top: '75%',
                    height: '15%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false, lineStyle: { color: '#3a4158' } },
                    splitLine: { show: false },
                    splitNumber: 20,
                    min: 'dataMin',
                    max: 'dataMax',
                    axisLabel: { color: '#8892a6' }
                },
                {
                    type: 'category',
                    gridIndex: 1,
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false, lineStyle: { color: '#3a4158' } },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false },
                    splitNumber: 20,
                    min: 'dataMin',
                    max: 'dataMax'
                }
            ],
            yAxis: [
                {
                    scale: true,
                    axisLine: { lineStyle: { color: '#3a4158' } },
                    splitLine: { lineStyle: { color: '#252b3d' } },
                    axisLabel: { color: '#8892a6' }
                },
                {
                    scale: true,
                    gridIndex: 1,
                    splitNumber: 2,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false }
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 70,
                    end: 100
                },
                {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    top: '90%',
                    start: 70,
                    end: 100,
                    backgroundColor: '#252b3d',
                    borderColor: '#3a4158',
                    fillerColor: 'rgba(0, 212, 255, 0.2)',
                    handleStyle: { color: '#00d4ff' }
                }
            ],
            series: [
                {
                    name: 'K线',
                    type: 'candlestick',
                    data: data,
                    itemStyle: {
                        color: '#00ff88',
                        color0: '#ff4757',
                        borderColor: '#00ff88',
                        borderColor0: '#ff4757'
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#00ff88',
                            color0: '#ff4757',
                            borderColor: '#00ff88',
                            borderColor0: '#ff4757'
                        }
                    }
                },
                {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: volumes,
                    itemStyle: {
                        color: function(params) {
                            const dataIndex = params.dataIndex;
                            const klineData = data[dataIndex];
                            return klineData[1] > klineData[0] ? '#00ff88' : '#ff4757';
                        }
                    }
                }
            ],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                backgroundColor: '#1a1f2e',
                borderColor: '#3a4158',
                textStyle: { color: '#ffffff' },
                formatter: function (params) {
                    const klineData = params[0];
                    const volumeData = params[1];
                    if (!klineData) return '';

                    const values = klineData.data;
                    return `
                        <div style="padding: 8px;">
                            <div style="margin-bottom: 4px; font-weight: bold;">${klineData.axisValue}</div>
                            <div>开盘: ${values[0].toFixed(2)}</div>
                            <div>收盘: ${values[1].toFixed(2)}</div>
                            <div>最低: ${values[2].toFixed(2)}</div>
                            <div>最高: ${values[3].toFixed(2)}</div>
                            ${volumeData ? `<div>成交量: ${volumeData.data.toLocaleString()}</div>` : ''}
                        </div>
                    `;
                }
            }
        };

        this.klineChart.setOption(option, true);

        if (loading) {
            setTimeout(() => {
                loading.style.display = 'none';
            }, 1000);
        }
    }

    getStockPrice(symbol) {
        const stock = this.dataCache.stocks.find(s => s.symbol === symbol);
        return stock ? stock.price : 12.50;
    }

    selectStock(symbol) {
        this.selectedStock = symbol;

        // 更新图表选择器
        const stockSelect = document.getElementById('chart-stock-select');
        if (stockSelect) {
            stockSelect.value = symbol;
        }

        // 更新K线图
        this.updateKLineChart();

        // 高亮选中的股票行
        document.querySelectorAll('.stock-table tr').forEach(row => {
            row.classList.remove('selected');
        });

        const selectedRow = document.querySelector(`tr[data-symbol="${symbol}"]`);
        if (selectedRow) {
            selectedRow.classList.add('selected');
        }

        console.log(`选中股票: ${symbol}`);
    }

    filterStocks(market) {
        console.log('筛选市场:', market);
        // 实现股票筛选逻辑
        // 这里可以根据市场类型过滤股票数据
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(async () => {
            if (this.autoRefreshEnabled && this.currentModule === 'dashboard') {
                try {
                    await this.loadInitialData();
                } catch (error) {
                    console.error('自动刷新失败:', error);
                }
            }
        }, this.config.refreshInterval);
    }

    handleKeyboardShortcuts(e) {
        // F5 - 刷新数据
        if (e.key === 'F5') {
            e.preventDefault();
            this.refreshData();
        }

        // Ctrl+1-9 - 切换模块
        if (e.ctrlKey && e.key >= '1' && e.key <= '9') {
            e.preventDefault();
            const modules = ['dashboard', 'stock-selection', 'signal-tracking', 'portfolio'];
            const index = parseInt(e.key) - 1;
            if (modules[index]) {
                this.switchModule(modules[index]);
            }
        }
    }

    handleResize() {
        if (this.klineChart) {
            this.klineChart.resize();
        }
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 全局方法
    refreshData() {
        this.loadInitialData();
    }

    toggleAutoRefresh() {
        this.autoRefreshEnabled = !this.autoRefreshEnabled;
        const icon = document.getElementById('auto-refresh-icon');
        if (icon) {
            icon.className = this.autoRefreshEnabled ? 'fas fa-pause' : 'fas fa-play';
        }
        console.log('自动刷新:', this.autoRefreshEnabled ? '开启' : '关闭');
    }

    exportData() {
        const data = {
            stocks: this.dataCache.stocks,
            signals: this.dataCache.signals,
            portfolio: this.dataCache.portfolio,
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `vnpy_data_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    showStockDetail(symbol) {
        console.log('显示股票详情:', symbol);
        // 实现股票详情弹窗
    }

    addToWatchlist(symbol) {
        console.log('添加到自选股:', symbol);
        this.showNotification(`已添加 ${symbol} 到自选股`, 'success');
    }

    toggleIndicators() {
        console.log('切换技术指标显示');
        // 实现技术指标显示切换
    }

    fullscreenChart() {
        const chartContainer = document.getElementById('kline-chart');
        if (chartContainer.requestFullscreen) {
            chartContainer.requestFullscreen();
        }
    }
}

// 全局实例
let app;

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new VeighNaProfessionalApp();

    // 暴露全局方法
    window.refreshData = () => app.refreshData();
    window.toggleAutoRefresh = () => app.toggleAutoRefresh();
    window.exportData = () => app.exportData();
    window.runStockSelection = () => app.loadStockSelectionData();
});
