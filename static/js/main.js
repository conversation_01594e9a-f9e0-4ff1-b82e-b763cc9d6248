// VeighNa量化交易系统前端JavaScript - 严格按照产品设计图实现

class VeighNaApp {
    constructor() {
        this.currentModule = 'dashboard';
        this.refreshInterval = null;
        this.klineChart = null;
        this.init();
    }

    init() {
        this.initEventListeners();
        this.initTimeDisplay();
        this.loadDashboardData();
        this.initKLineChart();
        this.startAutoRefresh();
    }

    initEventListeners() {
        // 菜单切换
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const module = e.currentTarget.dataset.module;
                if (module) {
                    this.switchModule(module);
                }
            });
        });

        // 时间周期切换
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.period-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.updateKLineChart(e.target.dataset.period);
            });
        });

        // 权重滑块
        ['technical', 'fundamental', 'market'].forEach(type => {
            const slider = document.getElementById(`${type}-weight`);
            const valueSpan = document.getElementById(`${type}-weight-value`);
            if (slider && valueSpan) {
                slider.addEventListener('input', (e) => {
                    valueSpan.textContent = e.target.value + '%';
                });
            }
        });

        // 市场筛选
        const marketFilter = document.getElementById('market-filter');
        if (marketFilter) {
            marketFilter.addEventListener('change', (e) => {
                this.filterStocks(e.target.value);
            });
        }
    }

    initTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace(/\//g, '/');
            
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeStr;
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    switchModule(moduleName) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-module="${moduleName}"]`).classList.add('active');

        // 切换内容模块
        document.querySelectorAll('.content-module').forEach(module => {
            module.classList.remove('active');
        });
        document.getElementById(`${moduleName}-module`).classList.add('active');

        this.currentModule = moduleName;

        // 根据模块加载相应数据
        switch (moduleName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'stock-selection':
                this.loadStockSelectionData();
                break;
            default:
                break;
        }
    }

    async loadDashboardData() {
        try {
            // 加载实时股票数据
            const response = await fetch('/api/realtime-data');
            const data = await response.json();
            
            this.updateStockTable(data.stocks || []);
            this.updateSignalsPanel(data.signals || []);
            this.updatePortfolioTable(data.positions || []);
            
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            this.showMockData();
        }
    }

    showMockData() {
        // 显示模拟数据
        const mockStocks = [
            { symbol: '000001', name: '平安银行', price: 12.85, change: 0.35, change_pct: 2.80, volume: 1250000, turnover: 16062500 },
            { symbol: '600519', name: '贵州茅台', price: 1680.00, change: -15.00, change_pct: -0.88, volume: 85000, turnover: 142800000 },
            { symbol: '000858', name: '五粮液', price: 128.50, change: -2.25, change_pct: -1.72, volume: 680000, turnover: 87380000 },
            { symbol: '600036', name: '招商银行', price: 45.20, change: 1.18, change_pct: 2.68, volume: 950000, turnover: 42940000 },
            { symbol: '002415', name: '海康威视', price: 28.90, change: 0.48, change_pct: 1.69, volume: 1150000, turnover: 33235000 }
        ];

        const mockSignals = [
            { symbol: '000001', name: '平安银行', signal_type: 'BUY', reason: 'RSI超卖反弹，价格反弹2.80%', score: 85.0, confidence: 0.8 },
            { symbol: '600036', name: '招商银行', signal_type: 'BUY', reason: 'MACD金叉形成，成交量放大1.2倍', score: 88.0, confidence: 0.9 },
            { symbol: '600519', name: '贵州茅台', signal_type: 'SELL', reason: 'RSI超买回调，价格回调0.88%', score: 75.0, confidence: 0.8 }
        ];

        const mockPositions = [
            { symbol: '000001', name: '平安银行', quantity: 10000, avg_price: 12.50, current_price: 12.85, market_value: 128500, pnl: 3500, pnl_pct: 2.80, weight: 0.128 },
            { symbol: '600519', name: '贵州茅台', quantity: 100, avg_price: 1650.00, current_price: 1680.00, market_value: 168000, pnl: 3000, pnl_pct: 1.82, weight: 0.168 },
            { symbol: '000858', name: '五粮液', quantity: 800, avg_price: 125.00, current_price: 128.50, market_value: 102800, pnl: 2800, pnl_pct: 2.80, weight: 0.103 }
        ];

        this.updateStockTable(mockStocks);
        this.updateSignalsPanel(mockSignals);
        this.updatePortfolioTable(mockPositions);
    }

    updateStockTable(stocks) {
        const tbody = document.getElementById('stock-table-body');
        if (!tbody) return;

        tbody.innerHTML = stocks.map(stock => {
            const changeClass = stock.change > 0 ? 'price-up' : stock.change < 0 ? 'price-down' : 'price-neutral';
            const changeSign = stock.change > 0 ? '+' : '';
            
            return `
                <tr>
                    <td>${stock.symbol}</td>
                    <td>${stock.name}</td>
                    <td class="${changeClass}">¥${stock.price.toFixed(2)}</td>
                    <td class="${changeClass}">${changeSign}${stock.change.toFixed(2)}</td>
                    <td class="${changeClass}">${changeSign}${stock.change_pct.toFixed(2)}%</td>
                    <td>${(stock.volume / 10000).toFixed(1)}万</td>
                    <td>${(stock.turnover / 100000000).toFixed(2)}亿</td>
                    <td>
                        <button class="btn primary-btn" onclick="app.showStockDetail('${stock.symbol}')">详情</button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    updateSignalsPanel(signals) {
        const container = document.getElementById('signals-container');
        const buyCount = document.getElementById('buy-signals-count');
        const sellCount = document.getElementById('sell-signals-count');
        
        if (!container) return;

        const buySignals = signals.filter(s => s.signal_type === 'BUY');
        const sellSignals = signals.filter(s => s.signal_type === 'SELL');

        if (buyCount) buyCount.textContent = buySignals.length;
        if (sellCount) sellCount.textContent = sellSignals.length;

        container.innerHTML = signals.map(signal => {
            const signalClass = signal.signal_type === 'BUY' ? 'buy-signal' : 'sell-signal';
            const typeClass = signal.signal_type.toLowerCase();
            
            return `
                <div class="signal-item ${signalClass}">
                    <div class="signal-header">
                        <span class="signal-symbol">${signal.symbol} ${signal.name}</span>
                        <span class="signal-type ${typeClass}">${signal.signal_type}</span>
                    </div>
                    <div class="signal-reason">${signal.reason}</div>
                    <div class="signal-meta">
                        <small>评分: ${signal.score} | 置信度: ${(signal.confidence * 100).toFixed(0)}%</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    updatePortfolioTable(positions) {
        const tbody = document.getElementById('portfolio-table-body');
        const totalValue = document.getElementById('total-market-value');
        const totalPnl = document.getElementById('total-pnl');
        
        if (!tbody) return;

        const totalMarketValue = positions.reduce((sum, pos) => sum + pos.market_value, 0);
        const totalPnlValue = positions.reduce((sum, pos) => sum + pos.pnl, 0);

        if (totalValue) totalValue.textContent = `¥${totalMarketValue.toLocaleString()}`;
        if (totalPnl) {
            const pnlClass = totalPnlValue >= 0 ? 'price-up' : 'price-down';
            totalPnl.innerHTML = `<span class="${pnlClass}">¥${totalPnlValue.toLocaleString()}</span>`;
        }

        tbody.innerHTML = positions.map(pos => {
            const pnlClass = pos.pnl >= 0 ? 'price-up' : 'price-down';
            const pnlSign = pos.pnl >= 0 ? '+' : '';
            
            return `
                <tr>
                    <td>${pos.symbol}</td>
                    <td>${pos.name}</td>
                    <td>${pos.quantity.toLocaleString()}</td>
                    <td>¥${pos.avg_price.toFixed(2)}</td>
                    <td>¥${pos.current_price.toFixed(2)}</td>
                    <td>¥${pos.market_value.toLocaleString()}</td>
                    <td class="${pnlClass}">${pnlSign}¥${pos.pnl.toLocaleString()}</td>
                    <td class="${pnlClass}">${pnlSign}${pos.pnl_pct.toFixed(2)}%</td>
                    <td>${(pos.weight * 100).toFixed(1)}%</td>
                </tr>
            `;
        }).join('');
    }

    initKLineChart() {
        const chartContainer = document.getElementById('kline-chart');
        if (!chartContainer) return;

        this.klineChart = echarts.init(chartContainer, 'dark');
        this.updateKLineChart('1d');
    }

    updateKLineChart(period) {
        if (!this.klineChart) return;

        // 模拟K线数据
        const dates = [];
        const data = [];
        const basePrice = 12.50;
        
        for (let i = 0; i < 30; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (29 - i));
            dates.push(date.toISOString().split('T')[0]);
            
            const open = basePrice + (Math.random() - 0.5) * 2;
            const close = open + (Math.random() - 0.5) * 1;
            const high = Math.max(open, close) + Math.random() * 0.5;
            const low = Math.min(open, close) - Math.random() * 0.5;
            
            data.push([open, close, low, high]);
        }

        const option = {
            backgroundColor: '#1a1a1a',
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%'
            },
            xAxis: {
                type: 'category',
                data: dates,
                axisLine: { lineStyle: { color: '#34495e' } },
                axisLabel: { color: '#bdc3c7' }
            },
            yAxis: {
                type: 'value',
                scale: true,
                axisLine: { lineStyle: { color: '#34495e' } },
                axisLabel: { color: '#bdc3c7' },
                splitLine: { lineStyle: { color: '#34495e' } }
            },
            series: [{
                type: 'candlestick',
                data: data,
                itemStyle: {
                    color: '#e74c3c',
                    color0: '#27ae60',
                    borderColor: '#e74c3c',
                    borderColor0: '#27ae60'
                }
            }]
        };

        this.klineChart.setOption(option);
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            if (this.currentModule === 'dashboard') {
                this.loadDashboardData();
            }
        }, 5000); // 每5秒刷新一次
    }

    filterStocks(market) {
        // 实现股票筛选逻辑
        console.log('筛选市场:', market);
    }

    showStockDetail(symbol) {
        this.showModal('股票详情', `正在加载 ${symbol} 的详细信息...`);
    }

    showModal(title, content) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        
        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('show');
        }
    }

    async loadStockSelectionData() {
        try {
            const response = await fetch('/api/stock-selection');
            const data = await response.json();
            this.updateSelectionResults(data.selected_stocks || []);
        } catch (error) {
            console.error('加载选股数据失败:', error);
            this.showMockSelectionResults();
        }
    }

    showMockSelectionResults() {
        const mockResults = [
            { rank: 1, symbol: '000001', name: '平安银行', total_score: 88.5, recommendation: '强烈推荐' },
            { rank: 2, symbol: '600036', name: '招商银行', total_score: 85.2, recommendation: '推荐' },
            { rank: 3, symbol: '002415', name: '海康威视', total_score: 82.8, recommendation: '推荐' }
        ];
        this.updateSelectionResults(mockResults);
    }

    updateSelectionResults(results) {
        const container = document.getElementById('selection-results');
        if (!container) return;

        container.innerHTML = `
            <h3>选股结果</h3>
            <div class="selection-table">
                <table class="stock-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>代码</th>
                            <th>名称</th>
                            <th>综合评分</th>
                            <th>推荐等级</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(stock => `
                            <tr>
                                <td>${stock.rank}</td>
                                <td>${stock.symbol}</td>
                                <td>${stock.name}</td>
                                <td>${stock.total_score.toFixed(1)}</td>
                                <td>${stock.recommendation}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }
}

// 全局函数
function refreshData() {
    if (window.app) {
        window.app.loadDashboardData();
    }
}

function runStockSelection() {
    if (window.app) {
        window.app.loadStockSelectionData();
    }
}

function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.classList.remove('show');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new VeighNaApp();
});
