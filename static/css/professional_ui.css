/* VeighNa量化交易系统专业界面 - 2K分辨率优化 */

/* 全局重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 2K分辨率优化的颜色系统 */
    --primary-bg: #0a0e1a;
    --secondary-bg: #1a1f2e;
    --tertiary-bg: #252b3d;
    --accent-bg: #2d3548;
    --border-color: #3a4158;
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #8892a6;
    --accent-blue: #00d4ff;
    --accent-green: #00ff88;
    --accent-red: #ff4757;
    --accent-orange: #ffa726;
    --accent-purple: #9c27b0;
    --success: #4caf50;
    --warning: #ff9800;
    --error: #f44336;
    --info: #2196f3;
    
    /* 2K分辨率字体系统 */
    --font-xs: 11px;
    --font-sm: 12px;
    --font-base: 14px;
    --font-lg: 16px;
    --font-xl: 18px;
    --font-2xl: 20px;
    --font-3xl: 24px;
    
    /* 间距系统 */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-base: 12px;
    --space-lg: 16px;
    --space-xl: 20px;
    --space-2xl: 24px;
    --space-3xl: 32px;
    
    /* 阴影系统 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-base: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.6);
    
    /* 边框半径 */
    --radius-sm: 4px;
    --radius-base: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

body {
    font-family: 'SF Pro Display', 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    font-size: var(--font-base);
    line-height: 1.5;
    overflow: hidden;
    min-width: 1920px;
    min-height: 1280px;
}

/* 顶部导航栏 - 2K优化 */
.top-header {
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--tertiary-bg) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-2xl);
    border-bottom: 2px solid var(--border-color);
    box-shadow: var(--shadow-base);
    position: relative;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.header-left i {
    color: var(--accent-blue);
    font-size: 24px;
    filter: drop-shadow(0 0 8px var(--accent-blue));
}

.system-title {
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.header-center {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid var(--accent-green);
    border-radius: var(--radius-lg);
    font-size: var(--font-sm);
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--accent-green);
    box-shadow: 0 0 12px var(--accent-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

.time-display {
    font-size: var(--font-base);
    color: var(--text-secondary);
    font-family: 'SF Mono', 'Consolas', monospace;
    background: rgba(255, 255, 255, 0.05);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-base);
    border: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    font-size: var(--font-base);
}

.logout-btn {
    background: linear-gradient(135deg, var(--accent-blue), #0099cc);
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-base);
    cursor: pointer;
    font-size: var(--font-sm);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #0099cc, var(--accent-blue));
    box-shadow: var(--shadow-base);
    transform: translateY(-1px);
}

/* 主容器 - 2K分辨率布局 */
.main-container {
    display: flex;
    height: calc(100vh - 90px);
    position: relative;
}

/* 左侧边栏 - 专业版 */
.sidebar {
    width: 280px;
    background: var(--secondary-bg);
    border-right: 2px solid var(--border-color);
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 900;
}

.nav-menu {
    padding: var(--space-lg) 0;
}

.menu-section {
    margin-bottom: var(--space-2xl);
}

.section-title {
    padding: var(--space-base) var(--space-xl);
    font-size: var(--font-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 700;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--space-base);
    background: rgba(255, 255, 255, 0.02);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg) var(--space-xl);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-base);
    color: var(--text-secondary);
    position: relative;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-blue);
    border-left-color: var(--accent-blue);
    transform: translateX(4px);
}

.menu-item.active {
    background: linear-gradient(90deg, rgba(0, 212, 255, 0.2), rgba(0, 212, 255, 0.05));
    color: var(--accent-blue);
    border-left-color: var(--accent-blue);
    font-weight: 600;
    box-shadow: inset 0 0 20px rgba(0, 212, 255, 0.1);
}

.menu-item i {
    width: 20px;
    text-align: center;
    font-size: var(--font-lg);
}

/* 主内容区 - 2K分辨率优化 */
.main-content {
    flex: 1;
    background: var(--primary-bg);
    overflow: hidden;
    position: relative;
}

.content-module {
    display: none;
    height: 100%;
    padding: var(--space-2xl);
    overflow: hidden;
}

.content-module.active {
    display: flex;
    flex-direction: column;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2xl);
    padding-bottom: var(--space-lg);
    border-bottom: 2px solid var(--border-color);
}

.module-header h2 {
    color: var(--accent-blue);
    font-size: var(--font-2xl);
    font-weight: 700;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.header-controls {
    display: flex;
    gap: var(--space-lg);
}

.refresh-btn, .primary-btn {
    background: linear-gradient(135deg, var(--accent-blue), #0099cc);
    color: white;
    border: none;
    padding: var(--space-base) var(--space-xl);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: var(--font-base);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-base);
}

.refresh-btn:hover, .primary-btn:hover {
    background: linear-gradient(135deg, #0099cc, var(--accent-blue));
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* 仪表板网格 - 2K分辨率专业布局 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: var(--space-2xl);
    height: calc(100% - 100px);
    min-height: 800px;
}

/* 专业面板样式 */
.panel {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);
    position: relative;
    backdrop-filter: blur(10px);
}

.panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-green), var(--accent-blue));
    opacity: 0.6;
}

.panel-header {
    background: var(--tertiary-bg);
    padding: var(--space-lg) var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.panel-header h3 {
    color: var(--text-primary);
    font-size: var(--font-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.panel-header h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--accent-blue);
    border-radius: 2px;
}

.panel-content {
    flex: 1;
    padding: var(--space-xl);
    overflow: auto;
    position: relative;
}

/* 专业数据表格 */
.stock-table-container, .portfolio-table-container {
    height: 100%;
    overflow: auto;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.02);
}

.stock-table, .portfolio-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-sm);
    background: transparent;
}

.stock-table th, .portfolio-table th {
    background: var(--accent-bg);
    color: var(--text-primary);
    padding: var(--space-lg) var(--space-base);
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    font-weight: 600;
    font-size: var(--font-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
}

.stock-table td, .portfolio-table td {
    padding: var(--space-base);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-family: 'SF Mono', 'Consolas', monospace;
    transition: all 0.2s ease;
}

.stock-table tr:hover, .portfolio-table tr:hover {
    background: rgba(0, 212, 255, 0.05);
    transform: scale(1.01);
}

/* 价格颜色系统 */
.price-up {
    color: var(--accent-green) !important;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.price-down {
    color: var(--accent-red) !important;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.price-neutral {
    color: var(--text-secondary) !important;
}

/* 专业图表容器 */
.chart-container {
    height: 400px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.time-period-selector {
    display: flex;
    gap: 2px;
    background: var(--accent-bg);
    border-radius: var(--radius-base);
    padding: 2px;
}

.period-btn {
    background: transparent;
    color: var(--text-secondary);
    border: none;
    padding: var(--space-sm) var(--space-base);
    font-size: var(--font-xs);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
    font-weight: 500;
}

.period-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
}

.period-btn.active {
    background: var(--accent-blue);
    color: white;
    box-shadow: 0 0 12px rgba(0, 212, 255, 0.4);
}

/* 交易信号面板 */
.signal-stats {
    display: flex;
    gap: var(--space-2xl);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-sm);
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-base);
    border: 1px solid var(--border-color);
}

.stat-label {
    color: var(--text-muted);
    font-weight: 500;
}

.buy-signals {
    color: var(--accent-green);
    font-weight: 700;
    font-size: var(--font-lg);
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.sell-signals {
    color: var(--accent-red);
    font-weight: 700;
    font-size: var(--font-lg);
    text-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.signals-container {
    height: 100%;
    overflow-y: auto;
    padding-right: var(--space-sm);
}

.signal-item {
    background: var(--tertiary-bg);
    margin-bottom: var(--space-base);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-blue);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.signal-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), transparent);
    pointer-events: none;
}

.signal-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-base);
}

.signal-item.buy-signal {
    border-left-color: var(--accent-green);
}

.signal-item.sell-signal {
    border-left-color: var(--accent-red);
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.signal-symbol {
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--font-base);
}

.signal-type {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.signal-type.buy {
    background: var(--accent-green);
    color: white;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.signal-type.sell {
    background: var(--accent-red);
    color: white;
    box-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.signal-reason {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-sm);
}

.signal-meta {
    font-size: var(--font-xs);
    color: var(--text-muted);
    display: flex;
    gap: var(--space-lg);
}

/* 投资组合摘要 */
.portfolio-summary {
    display: flex;
    gap: var(--space-2xl);
    margin-bottom: var(--space-lg);
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    flex: 1;
}

.summary-label {
    color: var(--text-muted);
    font-size: var(--font-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.summary-value {
    font-weight: 700;
    color: var(--text-primary);
    font-size: var(--font-xl);
    font-family: 'SF Mono', 'Consolas', monospace;
}

/* 状态栏 - 2K优化 */
.status-bar {
    height: 30px;
    background: var(--tertiary-bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-2xl);
    font-size: var(--font-xs);
    color: var(--text-secondary);
    border-top: 1px solid var(--border-color);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.status-left, .status-center, .status-right {
    display: flex;
    gap: var(--space-xl);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-family: 'SF Mono', 'Consolas', monospace;
}

.status-item i {
    color: var(--accent-blue);
    font-size: var(--font-sm);
}

/* 响应式设计 - 2K及以上分辨率优化 */
@media (min-width: 2560px) {
    :root {
        --font-xs: 12px;
        --font-sm: 14px;
        --font-base: 16px;
        --font-lg: 18px;
        --font-xl: 20px;
        --font-2xl: 24px;
        --font-3xl: 28px;
    }
    
    .sidebar {
        width: 320px;
    }
    
    .dashboard-grid {
        gap: var(--space-3xl);
    }
    
    .panel-content {
        padding: var(--space-2xl);
    }
}

@media (min-width: 3840px) {
    :root {
        --font-xs: 14px;
        --font-sm: 16px;
        --font-base: 18px;
        --font-lg: 20px;
        --font-xl: 24px;
        --font-2xl: 28px;
        --font-3xl: 32px;
    }
    
    .sidebar {
        width: 360px;
    }
    
    .chart-container {
        height: 500px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-blue);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-blue);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--tertiary-bg);
    color: var(--text-primary);
    padding: var(--space-sm) var(--space-base);
    border-radius: var(--radius-base);
    font-size: var(--font-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-base);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
