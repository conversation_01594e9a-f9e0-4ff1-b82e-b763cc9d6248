/* VeighNa量化交易系统样式 - 严格按照产品设计图实现 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

/* 顶部导航栏 */
.top-header {
    height: 50px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #34495e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-left i {
    color: #3498db;
    font-size: 18px;
}

.system-title {
    font-size: 16px;
    font-weight: bold;
    color: #ecf0f1;
}

.header-center {
    display: flex;
    align-items: center;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #e74c3c;
}

.status-indicator.connected {
    background: #27ae60;
    box-shadow: 0 0 6px #27ae60;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.time-display {
    font-size: 14px;
    color: #bdc3c7;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.logout-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.logout-btn:hover {
    background: #2980b9;
}

/* 主容器 */
.main-container {
    display: flex;
    height: calc(100vh - 80px);
}

/* 左侧边栏 */
.sidebar {
    width: 200px;
    background: #2c3e50;
    border-right: 1px solid #34495e;
    overflow-y: auto;
}

.nav-menu {
    padding: 10px 0;
}

.menu-section {
    margin-bottom: 20px;
}

.section-title {
    padding: 8px 20px;
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid #34495e;
    margin-bottom: 5px;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #ecf0f1;
}

.menu-item:hover {
    background: #34495e;
    color: #3498db;
}

.menu-item.active {
    background: #3498db;
    color: white;
    border-right: 3px solid #2980b9;
}

.menu-item i {
    width: 16px;
    text-align: center;
}

/* 主内容区 */
.main-content {
    flex: 1;
    background: #1a1a1a;
    overflow: hidden;
}

.content-module {
    display: none;
    height: 100%;
    padding: 20px;
}

.content-module.active {
    display: block;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #34495e;
}

.module-header h2 {
    color: #3498db;
    font-size: 20px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.refresh-btn, .primary-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.refresh-btn:hover, .primary-btn:hover {
    background: #2980b9;
}

/* 仪表板网格布局 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    height: calc(100% - 80px);
}

/* 面板样式 */
.panel {
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #34495e;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    background: #34495e;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #4a5f7a;
}

.panel-header h3 {
    color: #ecf0f1;
    font-size: 16px;
}

.panel-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
}

/* 股票表格 */
.stock-table-container {
    height: 100%;
    overflow: auto;
}

.stock-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.stock-table th {
    background: #34495e;
    color: #ecf0f1;
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #4a5f7a;
    position: sticky;
    top: 0;
}

.stock-table td {
    padding: 6px 8px;
    border-bottom: 1px solid #34495e;
    color: #bdc3c7;
}

.stock-table tr:hover {
    background: #34495e;
}

.price-up {
    color: #e74c3c !important;
}

.price-down {
    color: #27ae60 !important;
}

.price-neutral {
    color: #95a5a6 !important;
}

/* 图表容器 */
.chart-container {
    height: 300px;
    background: #1a1a1a;
    border-radius: 4px;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.time-period-selector {
    display: flex;
    gap: 2px;
}

.period-btn {
    background: #2c3e50;
    color: #bdc3c7;
    border: 1px solid #34495e;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    border-radius: 3px;
}

.period-btn:hover {
    background: #34495e;
}

.period-btn.active {
    background: #3498db;
    color: white;
    border-color: #2980b9;
}

/* 交易信号面板 */
.signal-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.stat-label {
    color: #7f8c8d;
}

.buy-signals {
    color: #e74c3c;
    font-weight: bold;
}

.sell-signals {
    color: #27ae60;
    font-weight: bold;
}

.signals-container {
    height: 100%;
    overflow-y: auto;
}

.signal-item {
    background: #34495e;
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #3498db;
}

.signal-item.buy-signal {
    border-left-color: #e74c3c;
}

.signal-item.sell-signal {
    border-left-color: #27ae60;
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.signal-symbol {
    font-weight: bold;
    color: #ecf0f1;
}

.signal-type {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

.signal-type.buy {
    background: #e74c3c;
    color: white;
}

.signal-type.sell {
    background: #27ae60;
    color: white;
}

.signal-reason {
    font-size: 11px;
    color: #bdc3c7;
    line-height: 1.4;
}

/* 投资组合面板 */
.portfolio-summary {
    display: flex;
    gap: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.summary-label {
    color: #7f8c8d;
}

.summary-value {
    font-weight: bold;
    color: #ecf0f1;
}

.portfolio-table-container {
    height: 100%;
    overflow: auto;
}

.portfolio-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.portfolio-table th {
    background: #34495e;
    color: #ecf0f1;
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #4a5f7a;
    position: sticky;
    top: 0;
}

.portfolio-table td {
    padding: 6px 8px;
    border-bottom: 1px solid #34495e;
    color: #bdc3c7;
}

.portfolio-table tr:hover {
    background: #34495e;
}

/* 智能选股模块 */
.selection-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    height: calc(100% - 80px);
}

.selection-config {
    background: #2c3e50;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #34495e;
}

.selection-config h3 {
    color: #3498db;
    margin-bottom: 20px;
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #ecf0f1;
    font-size: 14px;
}

.form-group input[type="range"] {
    width: 100%;
}

.selection-results {
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #34495e;
    padding: 20px;
    overflow: auto;
}

/* 状态栏 */
.status-bar {
    height: 30px;
    background: #34495e;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    font-size: 12px;
    color: #bdc3c7;
    border-top: 1px solid #4a5f7a;
}

.status-left, .status-center, .status-right {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-item i {
    color: #3498db;
}

/* 模态框 */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-overlay.show {
    display: flex;
}

.modal-content {
    background: #2c3e50;
    border-radius: 8px;
    border: 1px solid #34495e;
    min-width: 400px;
    max-width: 80%;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    background: #34495e;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #4a5f7a;
}

.modal-header h3 {
    color: #ecf0f1;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #e74c3c;
}

.modal-body {
    padding: 20px;
    color: #ecf0f1;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #34495e;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.secondary-btn {
    background: #7f8c8d;
    color: white;
}

.secondary-btn:hover {
    background: #95a5a6;
}

/* 开发中提示 */
.coming-soon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #7f8c8d;
}

.coming-soon i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #34495e;
}

.coming-soon p {
    font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }
    
    .selection-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    
    .menu-item span {
        display: none;
    }
    
    .section-title {
        display: none;
    }
}
