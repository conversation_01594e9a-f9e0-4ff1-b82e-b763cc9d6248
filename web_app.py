#!/usr/bin/env python3
"""
量化交易系统 Web 应用启动文件
基于FastAPI的Web界面，提供实时监控和交易管理功能
"""

import sys
import os
import logging
import uvicorn
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, Depends, Request, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 项目模块导入
try:
    from database_models import db_manager
    from system_management.config.config_manager import config_manager
    from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector
    from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
    from strategy_layer.trading_strategies.sell_strategy import SellStrategy
    from vnpy_integration.backtesting_engine import VnpyBacktestingEngine
    from portfolio_management.portfolio_builder.portfolio_builder import PortfolioBuilder
except ImportError as e:
    print(f"⚠️ 模块导入警告: {e}")
    print("🔄 使用模拟数据模式")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="量化交易系统 Web API",
    description="基于9层架构的智能量化交易系统Web界面",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件和模板
templates = Jinja2Templates(directory="templates")

# 全局组件实例
stock_selector = None
buy_strategy = None
sell_strategy = None
backtesting_engine = None
portfolio_builder = None

# 数据模型
class StockScore(BaseModel):
    symbol: str
    name: str
    total_score: float
    technical_score: float
    fundamental_score: float
    market_score: float
    signal_type: str
    recommendation: str

class BacktestConfig(BaseModel):
    strategy_name: str
    symbol: str
    start_date: str
    end_date: str
    initial_capital: float
    parameters: Dict[str, Any]

class SystemStatus(BaseModel):
    status: str
    timestamp: str
    modules: Dict[str, bool]
    health_score: float

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化组件"""
    global stock_selector, buy_strategy, sell_strategy, backtesting_engine, portfolio_builder
    
    try:
        logger.info("🚀 初始化Web应用组件...")
        
        # 初始化数据库
        if not db_manager.test_connection():
            logger.error("❌ 数据库连接失败")
            return
        
        # 初始化核心组件
        stock_selector = MultiFactorSelector()
        buy_strategy = BuyStrategy()
        sell_strategy = SellStrategy()
        backtesting_engine = VnpyBacktestingEngine()
        portfolio_builder = PortfolioBuilder()
        
        logger.info("✅ Web应用组件初始化完成")
        
    except Exception as e:
        logger.error(f"❌ Web应用初始化失败: {e}")

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "量化交易系统",
        "system_name": "量化交易系统 V2.0",
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.get("/api/system/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查各模块状态
        modules = {
            "database": db_manager.test_connection(),
            "stock_selector": stock_selector is not None,
            "buy_strategy": buy_strategy is not None,
            "sell_strategy": sell_strategy is not None,
            "backtesting_engine": backtesting_engine is not None,
            "portfolio_builder": portfolio_builder is not None
        }
        
        # 计算健康度
        health_score = sum(modules.values()) / len(modules) * 100
        
        return SystemStatus(
            status="running" if health_score > 80 else "warning",
            timestamp=datetime.now().isoformat(),
            modules=modules,
            health_score=health_score
        )
        
    except Exception as e:
        logger.error(f"❌ 获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stocks/scores")
async def get_stock_scores(limit: int = 20):
    """获取股票评分"""
    try:
        if not stock_selector:
            raise HTTPException(status_code=503, detail="股票选择器未初始化")
        
        # 模拟股票评分数据
        mock_scores = []
        symbols = ['000001', '000002', '600519', '000858', '600036', '002415', '600887', '000063', '600276', '002304']
        
        for i, symbol in enumerate(symbols[:limit]):
            mock_scores.append(StockScore(
                symbol=symbol,
                name=f"股票{symbol}",
                total_score=85.5 - i * 2.1,
                technical_score=80.0 - i * 1.5,
                fundamental_score=75.0 - i * 1.8,
                market_score=70.0 - i * 2.0,
                signal_type="BUY" if i < 3 else "OBSERVE" if i < 7 else "SELL",
                recommendation="强烈推荐" if i < 3 else "观察" if i < 7 else "谨慎"
            ))
        
        return mock_scores
        
    except Exception as e:
        logger.error(f"❌ 获取股票评分失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/signals/buy")
async def get_buy_signals():
    """获取买入信号"""
    try:
        if not buy_strategy:
            raise HTTPException(status_code=503, detail="买入策略未初始化")
        
        # 模拟买入信号
        signals = [
            {
                "symbol": "000001",
                "name": "平安银行",
                "signal_time": datetime.now().isoformat(),
                "price": 12.85,
                "score": 88.5,
                "reason": "技术指标多头排列，成交量放大"
            },
            {
                "symbol": "600519",
                "name": "贵州茅台",
                "signal_time": datetime.now().isoformat(),
                "price": 1680.00,
                "score": 92.3,
                "reason": "基本面优秀，技术面突破关键阻力"
            }
        ]
        
        return signals
        
    except Exception as e:
        logger.error(f"❌ 获取买入信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/signals/sell")
async def get_sell_signals():
    """获取卖出信号"""
    try:
        if not sell_strategy:
            raise HTTPException(status_code=503, detail="卖出策略未初始化")
        
        # 模拟卖出信号
        signals = [
            {
                "symbol": "000858",
                "name": "五粮液",
                "signal_time": datetime.now().isoformat(),
                "price": 128.50,
                "score": 45.2,
                "reason": "技术指标转弱，跌破重要支撑"
            }
        ]
        
        return signals
        
    except Exception as e:
        logger.error(f"❌ 获取卖出信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/backtest/run")
async def run_backtest(config: BacktestConfig):
    """运行回测"""
    try:
        if not backtesting_engine:
            raise HTTPException(status_code=503, detail="回测引擎未初始化")
        
        # 模拟回测结果
        result = {
            "strategy_name": config.strategy_name,
            "symbol": config.symbol,
            "period": f"{config.start_date} 至 {config.end_date}",
            "initial_capital": config.initial_capital,
            "final_capital": config.initial_capital * 1.156,
            "total_return": 15.6,
            "annual_return": 18.2,
            "max_drawdown": -8.3,
            "sharpe_ratio": 1.45,
            "win_rate": 62.5,
            "total_trades": 24,
            "status": "completed",
            "timestamp": datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 运行回测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/portfolio/current")
async def get_current_portfolio():
    """获取当前投资组合"""
    try:
        if not portfolio_builder:
            raise HTTPException(status_code=503, detail="投资组合构建器未初始化")
        
        # 模拟投资组合数据
        portfolio = {
            "total_value": 1000000.0,
            "cash": 200000.0,
            "positions": [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "quantity": 10000,
                    "avg_price": 12.50,
                    "current_price": 12.85,
                    "market_value": 128500.0,
                    "pnl": 3500.0,
                    "pnl_pct": 2.8,
                    "weight": 12.85
                },
                {
                    "symbol": "600519",
                    "name": "贵州茅台",
                    "quantity": 300,
                    "avg_price": 1650.00,
                    "current_price": 1680.00,
                    "market_value": 504000.0,
                    "pnl": 9000.0,
                    "pnl_pct": 1.8,
                    "weight": 50.4
                }
            ],
            "performance": {
                "total_pnl": 12500.0,
                "total_pnl_pct": 1.25,
                "today_pnl": 2800.0,
                "today_pnl_pct": 0.28
            },
            "updated_at": datetime.now().isoformat()
        }
        
        return portfolio
        
    except Exception as e:
        logger.error(f"❌ 获取投资组合失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market/summary")
async def get_market_summary():
    """获取市场概览"""
    try:
        # 模拟市场数据
        summary = {
            "indices": {
                "上证指数": {"value": 3245.68, "change": 12.45, "change_pct": 0.38},
                "深证成指": {"value": 10856.32, "change": -25.67, "change_pct": -0.24},
                "创业板指": {"value": 2187.45, "change": 8.92, "change_pct": 0.41}
            },
            "market_stats": {
                "total_stocks": 4800,
                "rising_stocks": 2650,
                "falling_stocks": 1890,
                "unchanged_stocks": 260,
                "limit_up": 45,
                "limit_down": 12
            },
            "signals": {
                "buy_signals": 23,
                "sell_signals": 8,
                "observe_signals": 156
            },
            "updated_at": datetime.now().isoformat()
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ 获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def create_templates_directory():
    """创建模板目录和基础HTML文件"""
    templates_dir = Path("templates")
    templates_dir.mkdir(exist_ok=True)
    
    # 创建基础HTML模板
    index_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #1a1a1a; 
            color: #ffffff; 
        }
        .header { 
            text-align: center; 
            padding: 20px; 
            background: #2d2d2d; 
            border-radius: 8px; 
            margin-bottom: 20px; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .card { 
            background: #2d2d2d; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 20px; 
        }
        .api-links { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 15px; 
        }
        .api-link { 
            display: block; 
            padding: 15px; 
            background: #3d3d3d; 
            color: #4CAF50; 
            text-decoration: none; 
            border-radius: 5px; 
            transition: background 0.3s; 
        }
        .api-link:hover { 
            background: #4d4d4d; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 {{ system_name }}</h1>
            <p>专业量化交易系统 Web 界面</p>
            <p>当前时间: {{ current_time }}</p>
        </div>
        
        <div class="card">
            <h2>📊 API 接口</h2>
            <div class="api-links">
                <a href="/api/system/status" class="api-link">🔧 系统状态</a>
                <a href="/api/stocks/scores" class="api-link">📈 股票评分</a>
                <a href="/api/signals/buy" class="api-link">🟢 买入信号</a>
                <a href="/api/signals/sell" class="api-link">🔴 卖出信号</a>
                <a href="/api/portfolio/current" class="api-link">💼 当前组合</a>
                <a href="/api/market/summary" class="api-link">📊 市场概览</a>
                <a href="/docs" class="api-link">📚 API 文档</a>
                <a href="/redoc" class="api-link">📖 ReDoc 文档</a>
            </div>
        </div>
        
        <div class="card">
            <h2>🎯 系统功能</h2>
            <ul>
                <li>📊 实时股票数据采集与分析</li>
                <li>🧠 多维度智能选股算法</li>
                <li>🚀 VeighNa专业回测引擎</li>
                <li>📈 买入/卖出策略信号</li>
                <li>💼 智能投资组合管理</li>
                <li>📊 实时市场监控大屏</li>
            </ul>
        </div>
    </div>
</body>
</html>
    """
    
    with open(templates_dir / "index.html", "w", encoding="utf-8") as f:
        f.write(index_html)

def main():
    """主函数"""
    try:
        # 创建模板目录
        create_templates_directory()
        
        # 获取配置
        host = config_manager.get('api_host', '0.0.0.0')
        port = config_manager.get('api_port', 8000)
        
        logger.info("🌐 启动量化交易系统 Web 应用")
        logger.info(f"📍 访问地址: http://{host}:{port}")
        logger.info(f"📚 API文档: http://{host}:{port}/docs")
        logger.info(f"📖 ReDoc文档: http://{host}:{port}/redoc")
        
        # 启动Web服务器
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断Web应用")
    except Exception as e:
        logger.error(f"❌ Web应用启动失败: {e}")
        return 1

if __name__ == "__main__":
    main()
