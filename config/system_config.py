#!/usr/bin/env python3
"""
VeighNa量化交易系统全局配置
支持全A股数据处理、2K分辨率界面、完整业务流程
"""

import os
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

@dataclass
class DatabaseConfig:
    """数据库配置"""
    # 主数据库配置
    DB_TYPE: str = "postgresql"  # 使用PostgreSQL支持大数据量
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "vnpy_trading_system"
    DB_USER: str = "vnpy_user"
    DB_PASSWORD: str = "vnpy_password"
    
    # SQLite备用配置（开发环境）
    SQLITE_PATH: str = str(PROJECT_ROOT / "data" / "vnpy_system.db")
    
    # 连接池配置
    POOL_SIZE: int = 20
    MAX_OVERFLOW: int = 30
    POOL_TIMEOUT: int = 30
    POOL_RECYCLE: int = 3600

@dataclass
class DataSourceConfig:
    """数据源配置"""
    # 主要数据源
    PRIMARY_SOURCE: str = "akshare"  # akshare作为主要数据源
    BACKUP_SOURCES: List[str] = None
    
    # API配置
    AKSHARE_TIMEOUT: int = 30
    TUSHARE_TOKEN: str = ""  # 需要配置tushare token
    
    # 数据更新频率（秒）
    REALTIME_UPDATE_INTERVAL: int = 3  # 实时数据3秒更新
    DAILY_UPDATE_TIME: str = "15:30"   # 日线数据更新时间
    
    # 数据范围
    STOCK_MARKETS: List[str] = None  # 默认全市场
    START_DATE: str = "2020-01-01"   # 历史数据起始日期
    
    def __post_init__(self):
        if self.BACKUP_SOURCES is None:
            self.BACKUP_SOURCES = ["tushare", "yfinance"]
        if self.STOCK_MARKETS is None:
            self.STOCK_MARKETS = ["SH", "SZ", "BJ"]  # 沪深北交易所

@dataclass
class TradingConfig:
    """交易配置"""
    # 选股配置
    MAX_SELECTED_STOCKS: int = 50      # 最大选股数量
    MIN_STOCK_SCORE: float = 70.0      # 最低股票评分
    SELECTION_INTERVAL: int = 3600     # 选股间隔（秒）
    
    # 因子权重
    TECHNICAL_WEIGHT: float = 0.4      # 技术面权重40%
    FUNDAMENTAL_WEIGHT: float = 0.35   # 基本面权重35%
    MARKET_WEIGHT: float = 0.25        # 市场表现权重25%
    
    # 信号配置
    SIGNAL_GENERATION_INTERVAL: int = 60  # 信号生成间隔（秒）
    SIGNAL_CONFIDENCE_THRESHOLD: float = 0.7  # 信号置信度阈值
    MAX_SIGNALS_PER_STOCK: int = 5     # 每只股票最大信号数
    
    # 风险控制
    MAX_POSITION_WEIGHT: float = 0.1   # 单股最大权重10%
    STOP_LOSS_PCT: float = -0.08       # 止损线-8%
    TAKE_PROFIT_PCT: float = 0.15      # 止盈线+15%
    MAX_DRAWDOWN_PCT: float = -0.15    # 最大回撤-15%

@dataclass
class UIConfig:
    """界面配置"""
    # 分辨率配置
    MIN_RESOLUTION: tuple = (1920, 1280)  # 最小分辨率
    OPTIMAL_RESOLUTION: tuple = (2560, 1440)  # 最佳分辨率
    
    # 界面主题
    THEME: str = "dark"  # dark/light
    PRIMARY_COLOR: str = "#3498db"
    SUCCESS_COLOR: str = "#27ae60"
    DANGER_COLOR: str = "#e74c3c"
    WARNING_COLOR: str = "#f39c12"
    
    # 图表配置
    CHART_THEME: str = "dark"
    KLINE_PERIODS: List[str] = None
    DEFAULT_PERIOD: str = "1d"
    
    # 数据刷新频率
    REALTIME_REFRESH_INTERVAL: int = 3000   # 3秒
    CHART_REFRESH_INTERVAL: int = 5000      # 5秒
    SIGNAL_REFRESH_INTERVAL: int = 10000    # 10秒
    
    def __post_init__(self):
        if self.KLINE_PERIODS is None:
            self.KLINE_PERIODS = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M"]

@dataclass
class SystemConfig:
    """系统配置"""
    # 服务配置
    WEB_HOST: str = "0.0.0.0"
    WEB_PORT: int = 8080
    DEBUG_MODE: bool = False
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = str(PROJECT_ROOT / "logs" / "vnpy_system.log")
    LOG_MAX_SIZE: int = 100 * 1024 * 1024  # 100MB
    LOG_BACKUP_COUNT: int = 5
    
    # 性能配置
    MAX_WORKERS: int = 8               # 最大工作线程数
    ASYNC_POOL_SIZE: int = 100         # 异步连接池大小
    CACHE_SIZE: int = 1000             # 缓存大小
    
    # 数据目录
    DATA_DIR: str = str(PROJECT_ROOT / "data")
    CACHE_DIR: str = str(PROJECT_ROOT / "cache")
    LOG_DIR: str = str(PROJECT_ROOT / "logs")
    
    def __post_init__(self):
        # 创建必要目录
        for directory in [self.DATA_DIR, self.CACHE_DIR, self.LOG_DIR]:
            Path(directory).mkdir(parents=True, exist_ok=True)

# 全局配置实例
db_config = DatabaseConfig()
data_config = DataSourceConfig()
trading_config = TradingConfig()
ui_config = UIConfig()
system_config = SystemConfig()

# 股票市场配置
STOCK_EXCHANGES = {
    "SH": {
        "name": "上海证券交易所",
        "code_prefix": ["60", "68", "90"],
        "market_code": "sh"
    },
    "SZ": {
        "name": "深圳证券交易所", 
        "code_prefix": ["00", "30", "20"],
        "market_code": "sz"
    },
    "BJ": {
        "name": "北京证券交易所",
        "code_prefix": ["43", "83", "87"],
        "market_code": "bj"
    }
}

# 技术指标配置
TECHNICAL_INDICATORS = {
    "trend": ["SMA", "EMA", "MACD", "BOLL"],
    "momentum": ["RSI", "KDJ", "CCI", "WR"],
    "volume": ["OBV", "VOL", "VRSI"],
    "volatility": ["ATR", "BBANDS"]
}

# 基本面指标配置
FUNDAMENTAL_INDICATORS = {
    "valuation": ["PE", "PB", "PS", "PCF"],
    "profitability": ["ROE", "ROA", "ROIC", "GPM"],
    "growth": ["REVENUE_GROWTH", "PROFIT_GROWTH", "EPS_GROWTH"],
    "financial": ["DEBT_RATIO", "CURRENT_RATIO", "QUICK_RATIO"]
}

# 行业分类配置
INDUSTRY_CLASSIFICATION = {
    "申万一级": ["银行", "非银金融", "房地产", "建筑装饰", "钢铁", "有色金属", 
                "电子", "家用电器", "食品饮料", "纺织服装", "轻工制造",
                "医药生物", "公用事业", "交通运输", "化工", "建筑材料",
                "机械设备", "电气设备", "国防军工", "计算机", "传媒",
                "通信", "汽车", "商业贸易", "休闲服务", "综合", "农林牧渔", "采掘"],
    "中证行业": ["能源", "材料", "工业", "可选消费", "必需消费", "医疗保健",
                "金融地产", "信息技术", "电信服务", "公用事业"]
}

def get_config_summary() -> Dict:
    """获取配置摘要"""
    return {
        "database": {
            "type": db_config.DB_TYPE,
            "name": db_config.DB_NAME,
            "pool_size": db_config.POOL_SIZE
        },
        "data_source": {
            "primary": data_config.PRIMARY_SOURCE,
            "markets": data_config.STOCK_MARKETS,
            "update_interval": data_config.REALTIME_UPDATE_INTERVAL
        },
        "trading": {
            "max_stocks": trading_config.MAX_SELECTED_STOCKS,
            "min_score": trading_config.MIN_STOCK_SCORE,
            "weights": {
                "technical": trading_config.TECHNICAL_WEIGHT,
                "fundamental": trading_config.FUNDAMENTAL_WEIGHT,
                "market": trading_config.MARKET_WEIGHT
            }
        },
        "ui": {
            "resolution": ui_config.OPTIMAL_RESOLUTION,
            "theme": ui_config.THEME,
            "refresh_intervals": {
                "realtime": ui_config.REALTIME_REFRESH_INTERVAL,
                "chart": ui_config.CHART_REFRESH_INTERVAL,
                "signal": ui_config.SIGNAL_REFRESH_INTERVAL
            }
        },
        "system": {
            "host": system_config.WEB_HOST,
            "port": system_config.WEB_PORT,
            "workers": system_config.MAX_WORKERS,
            "debug": system_config.DEBUG_MODE
        }
    }

def validate_config() -> List[str]:
    """验证配置有效性"""
    errors = []
    
    # 检查必要目录
    for directory in [system_config.DATA_DIR, system_config.CACHE_DIR, system_config.LOG_DIR]:
        if not Path(directory).exists():
            errors.append(f"目录不存在: {directory}")
    
    # 检查权重配置
    total_weight = (trading_config.TECHNICAL_WEIGHT + 
                   trading_config.FUNDAMENTAL_WEIGHT + 
                   trading_config.MARKET_WEIGHT)
    if abs(total_weight - 1.0) > 0.01:
        errors.append(f"因子权重总和不等于1.0: {total_weight}")
    
    # 检查分辨率配置
    min_width, min_height = ui_config.MIN_RESOLUTION
    if min_width < 1920 or min_height < 1280:
        errors.append(f"最小分辨率配置过低: {ui_config.MIN_RESOLUTION}")
    
    return errors

if __name__ == "__main__":
    # 配置验证和摘要输出
    print("🔧 VeighNa量化交易系统配置")
    print("=" * 50)
    
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
    
    # 输出配置摘要
    print("\n📋 配置摘要:")
    import json
    print(json.dumps(get_config_summary(), indent=2, ensure_ascii=False))
