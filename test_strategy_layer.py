#!/usr/bin/env python3
"""
策略层测试脚本
测试完整的策略层功能：买入策略、卖出策略、风险管理、持仓管理、信号处理
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import date, datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from strategy_layer import (
    BuyStrategy, SellStrategy, RiskManager, 
    PositionManager, SignalProcessor
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_buy_strategy():
    """测试买入策略"""
    logger.info("📈 测试买入策略...")
    
    try:
        buy_strategy = BuyStrategy()
        
        # 测试股票列表
        test_symbols = ['000001', '000002', '600000', '000858', '002415']
        
        # 生成买入信号
        buy_signals = buy_strategy.generate_buy_signals(test_symbols)
        
        logger.info(f"✅ 买入策略测试完成")
        logger.info(f"  - 候选股票: {len(test_symbols)}只")
        logger.info(f"  - 生成信号: {len(buy_signals)}个")
        
        # 显示前3个信号详情
        for i, signal in enumerate(buy_signals[:3], 1):
            logger.info(f"  信号{i}: {signal.symbol} 强度{signal.signal_strength:.1f} @{signal.trigger_price:.2f}")
            logger.info(f"    原因: {', '.join(signal.signal_reasons[:2])}")
        
        return len(buy_signals) > 0
        
    except Exception as e:
        logger.error(f"❌ 买入策略测试失败: {e}")
        return False

def test_sell_strategy():
    """测试卖出策略"""
    logger.info("📉 测试卖出策略...")
    
    try:
        sell_strategy = SellStrategy()
        
        # 模拟持仓
        mock_positions = [
            {
                'symbol': '000001',
                'entry_price': 10.50,
                'entry_date': date(2024, 1, 15),
                'shares': 1000
            },
            {
                'symbol': '600000',
                'entry_price': 28.80,
                'entry_date': date(2024, 2, 10),
                'shares': 500
            }
        ]
        
        # 生成卖出信号
        sell_signals = sell_strategy.generate_sell_signals(mock_positions)
        
        logger.info(f"✅ 卖出策略测试完成")
        logger.info(f"  - 持仓数量: {len(mock_positions)}个")
        logger.info(f"  - 生成信号: {len(sell_signals)}个")
        
        # 显示信号详情
        for signal in sell_signals:
            logger.info(f"  信号: {signal.symbol} {signal.signal_type} 紧急度{signal.urgency}")
            logger.info(f"    盈亏: {signal.profit_loss_pct:.2%} 原因: {signal.signal_reasons[0] if signal.signal_reasons else '无'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 卖出策略测试失败: {e}")
        return False

def test_risk_manager():
    """测试风险管理器"""
    logger.info("⚠️ 测试风险管理器...")
    
    try:
        risk_manager = RiskManager()
        
        # 模拟当前持仓
        mock_positions = {
            '000001': {'market_value': 50000, 'shares': 5000},
            '600000': {'market_value': 80000, 'shares': 2500},
            '002415': {'market_value': 30000, 'shares': 1500}
        }
        
        total_portfolio_value = 200000
        
        # 测试仓位风险检查
        risk_result = risk_manager.check_position_risk(
            symbol='000858',
            target_position_value=25000,
            total_portfolio_value=total_portfolio_value,
            current_positions=mock_positions
        )
        
        logger.info(f"✅ 风险管理器测试完成")
        logger.info(f"  - 仓位检查通过: {risk_result['approved']}")
        logger.info(f"  - 调整后价值: ¥{risk_result['adjusted_value']:,.0f}")
        logger.info(f"  - 风险预警: {len(risk_result['risk_alerts'])}个")
        
        # 测试组合风险监控
        mock_history = [
            {'nav': 1.0, 'date': '2024-01-01'},
            {'nav': 1.05, 'date': '2024-01-15'},
            {'nav': 0.98, 'date': '2024-02-01'},
            {'nav': 1.02, 'date': '2024-02-15'}
        ]
        
        risk_monitoring = risk_manager.monitor_portfolio_risk(mock_positions, mock_history)
        
        logger.info(f"  - 组合风险等级: {risk_monitoring['overall_risk_level'].value}")
        logger.info(f"  - 风险预警: {len(risk_monitoring['risk_alerts'])}个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 风险管理器测试失败: {e}")
        return False

def test_position_manager():
    """测试持仓管理器"""
    logger.info("💼 测试持仓管理器...")
    
    try:
        position_manager = PositionManager()
        
        # 测试仓位计算
        target_symbols = ['000001', '000002', '600000']
        current_prices = {
            '000001': 11.20,
            '000002': 15.80,
            '600000': 32.50
        }
        
        position_sizes = position_manager.calculate_position_sizes(
            target_symbols=target_symbols,
            current_prices=current_prices
        )
        
        logger.info(f"✅ 持仓管理器测试完成")
        logger.info(f"  - 目标股票: {len(target_symbols)}只")
        logger.info(f"  - 计算仓位: {len(position_sizes)}个")
        
        for symbol, pos_info in position_sizes.items():
            logger.info(f"  {symbol}: {pos_info['shares']}股 价值¥{pos_info['target_value']:,.0f}")
        
        # 测试持仓更新
        success = position_manager.update_position(
            symbol='000001',
            action='buy',
            shares=1000,
            price=11.20,
            commission=33.6
        )
        
        logger.info(f"  - 持仓更新: {'成功' if success else '失败'}")
        
        # 获取组合摘要
        portfolio_summary = position_manager.get_portfolio_summary(current_prices)
        logger.info(f"  - 组合价值: ¥{portfolio_summary.get('total_portfolio_value', 0):,.0f}")
        logger.info(f"  - 持仓数量: {portfolio_summary.get('position_count', 0)}个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 持仓管理器测试失败: {e}")
        return False

def test_signal_processor():
    """测试信号处理器"""
    logger.info("🎯 测试信号处理器...")
    
    try:
        signal_processor = SignalProcessor()
        
        # 测试信号处理
        candidate_symbols = ['000001', '000002', '600000', '000858']
        current_prices = {
            '000001': 11.20,
            '000002': 15.80,
            '600000': 32.50,
            '000858': 18.90
        }
        
        trading_decisions = signal_processor.process_trading_signals(
            candidate_symbols=candidate_symbols,
            current_prices=current_prices
        )
        
        logger.info(f"✅ 信号处理器测试完成")
        logger.info(f"  - 候选股票: {len(candidate_symbols)}只")
        logger.info(f"  - 交易决策: {len(trading_decisions)}个")
        
        # 显示交易决策
        for decision in trading_decisions:
            logger.info(f"  决策: {decision.symbol} {decision.action} {decision.shares}股")
            logger.info(f"    置信度: {decision.confidence:.2f} 优先级: {decision.priority}")
            logger.info(f"    原因: {decision.reasons[0] if decision.reasons else '无'}")
        
        # 获取处理器状态
        status = signal_processor.get_signal_processor_status()
        logger.info(f"  - 今日交易: {status['daily_trade_count']}/{status['max_daily_trades']}")
        logger.info(f"  - 剩余额度: {status['remaining_trades']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 信号处理器测试失败: {e}")
        return False

def test_integrated_workflow():
    """测试集成工作流"""
    logger.info("🔄 测试集成工作流...")
    
    try:
        # 初始化所有组件
        signal_processor = SignalProcessor()
        
        # 模拟完整的交易流程
        candidate_symbols = ['000001', '000002', '600000']
        current_prices = {
            '000001': 11.20,
            '000002': 15.80,
            '600000': 32.50
        }
        
        # 1. 处理交易信号
        trading_decisions = signal_processor.process_trading_signals(
            candidate_symbols, current_prices
        )
        
        # 2. 执行交易决策
        executed_count = 0
        for decision in trading_decisions[:2]:  # 只执行前2个决策
            if signal_processor.execute_trading_decision(decision):
                executed_count += 1
        
        logger.info(f"✅ 集成工作流测试完成")
        logger.info(f"  - 生成决策: {len(trading_decisions)}个")
        logger.info(f"  - 执行成功: {executed_count}个")
        
        # 获取最终状态
        portfolio_summary = signal_processor.position_manager.get_portfolio_summary(current_prices)
        logger.info(f"  - 最终持仓: {portfolio_summary.get('position_count', 0)}个")
        logger.info(f"  - 组合价值: ¥{portfolio_summary.get('total_portfolio_value', 0):,.0f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成工作流测试失败: {e}")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🧪 开始策略层综合测试...")
    logger.info("=" * 80)
    
    test_results = []
    
    try:
        # 1. 测试买入策略
        buy_result = test_buy_strategy()
        test_results.append(("买入策略", buy_result))
        logger.info("=" * 80)
        
        # 2. 测试卖出策略
        sell_result = test_sell_strategy()
        test_results.append(("卖出策略", sell_result))
        logger.info("=" * 80)
        
        # 3. 测试风险管理器
        risk_result = test_risk_manager()
        test_results.append(("风险管理器", risk_result))
        logger.info("=" * 80)
        
        # 4. 测试持仓管理器
        position_result = test_position_manager()
        test_results.append(("持仓管理器", position_result))
        logger.info("=" * 80)
        
        # 5. 测试信号处理器
        signal_result = test_signal_processor()
        test_results.append(("信号处理器", signal_result))
        logger.info("=" * 80)
        
        # 6. 测试集成工作流
        workflow_result = test_integrated_workflow()
        test_results.append(("集成工作流", workflow_result))
        logger.info("=" * 80)
        
        # 测试结果总结
        logger.info("📊 测试结果总结:")
        success_count = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  - {test_name}: {status}")
            if result:
                success_count += 1
        
        logger.info(f"🎉 策略层测试完成: {success_count}/{len(test_results)} 项通过")
        
        if success_count == len(test_results):
            logger.info("🎊 所有测试均通过！策略层功能正常")
        else:
            logger.warning("⚠️ 部分测试失败，请检查相关功能")
        
        # 系统功能总结
        logger.info("\n🚀 策略层功能总结:")
        logger.info("  ✅ 买入策略 - 多因子综合评分，技术指标确认")
        logger.info("  ✅ 卖出策略 - 止盈止损，技术转弱，趋势反转")
        logger.info("  ✅ 风险管理器 - 仓位控制，回撤监控，集中度管理")
        logger.info("  ✅ 持仓管理器 - 动态仓位调整，资金管理")
        logger.info("  ✅ 信号处理器 - 信号整合，风险检查，交易执行")
        logger.info("  ✅ 集成工作流 - 完整的交易决策流程")
        
    except Exception as e:
        logger.error(f"❌ 综合测试过程中发生异常: {e}")

async def main():
    """主函数"""
    print("🚀 策略层测试")
    print("=" * 80)
    print("完整的交易策略实现")
    print("包括买入策略、卖出策略、风险管理、持仓管理、信号处理")
    print("=" * 80)
    
    await run_comprehensive_test()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
