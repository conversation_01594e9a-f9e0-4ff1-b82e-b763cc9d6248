"""
因子计算器
根据因子配置计算各种因子值
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, date, timedelta

from factor_management.factor_config.factor_config_manager import FactorConfig, FactorCategory
from database_models import db_manager, DailyMarket, FinancialData
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class FactorCalculator:
    """因子计算器"""
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        
        # 注册计算方法
        self.calculation_methods: Dict[str, Callable] = {
            # 技术面因子
            'rsi': self._calculate_rsi,
            'macd': self._calculate_macd,
            'bollinger_bands': self._calculate_bollinger_bands,
            'kdj': self._calculate_kdj,
            'ma_trend': self._calculate_ma_trend,
            
            # 基本面因子
            'roe': self._calculate_roe,
            'pe_ratio': self._calculate_pe_ratio,
            'pb_ratio': self._calculate_pb_ratio,
            'revenue_growth': self._calculate_revenue_growth,
            
            # 市场表现因子
            'price_momentum': self._calculate_price_momentum,
        }
        
        logger.info("🧮 因子计算器初始化完成")
        logger.info(f"  - 支持计算方法: {len(self.calculation_methods)}种")
    
    def calculate_factor(self,
                        symbol: str,
                        factor_config: FactorConfig,
                        end_date: date = None) -> Optional[float]:
        """
        计算单个因子值
        
        Args:
            symbol: 股票代码
            factor_config: 因子配置
            end_date: 计算截止日期
            
        Returns:
            因子值
        """
        try:
            if not factor_config.is_enabled:
                return None
            
            calculation_method = factor_config.calculation_method
            
            if calculation_method not in self.calculation_methods:
                logger.error(f"❌ 不支持的计算方法: {calculation_method}")
                return None
            
            # 调用对应的计算方法
            factor_value = self.calculation_methods[calculation_method](
                symbol, factor_config.parameters, end_date
            )
            
            # 应用数值范围限制
            if factor_value is not None:
                if factor_config.min_value is not None:
                    factor_value = max(factor_value, factor_config.min_value)
                if factor_config.max_value is not None:
                    factor_value = min(factor_value, factor_config.max_value)
            
            return factor_value
            
        except Exception as e:
            logger.error(f"❌ 计算因子失败: {symbol} {factor_config.factor_id} - {e}")
            return None
    
    def calculate_all_factors(self,
                             symbol: str,
                             factor_configs: Dict[str, FactorConfig],
                             end_date: date = None) -> Dict[str, float]:
        """
        计算所有因子值
        
        Args:
            symbol: 股票代码
            factor_configs: 因子配置字典
            end_date: 计算截止日期
            
        Returns:
            因子值字典
        """
        try:
            factor_values = {}
            
            for factor_id, factor_config in factor_configs.items():
                if not factor_config.is_enabled:
                    continue
                
                factor_value = self.calculate_factor(symbol, factor_config, end_date)
                
                if factor_value is not None:
                    factor_values[factor_id] = factor_value
            
            logger.debug(f"📊 计算因子完成: {symbol} {len(factor_values)}个")
            
            return factor_values
            
        except Exception as e:
            logger.error(f"❌ 计算所有因子失败: {symbol} - {e}")
            return {}
    
    def batch_calculate_factors(self,
                               symbols: List[str],
                               factor_configs: Dict[str, FactorConfig],
                               end_date: date = None) -> Dict[str, Dict[str, float]]:
        """
        批量计算因子值
        
        Args:
            symbols: 股票代码列表
            factor_configs: 因子配置字典
            end_date: 计算截止日期
            
        Returns:
            {symbol: {factor_id: factor_value}}
        """
        try:
            logger.info(f"🧮 开始批量计算因子: {len(symbols)}只股票, {len(factor_configs)}个因子")
            
            results = {}
            
            for symbol in symbols:
                try:
                    factor_values = self.calculate_all_factors(symbol, factor_configs, end_date)
                    if factor_values:
                        results[symbol] = factor_values
                        
                except Exception as e:
                    logger.error(f"❌ 计算股票因子失败: {symbol} - {e}")
                    continue
            
            logger.info(f"✅ 批量因子计算完成: {len(results)}只股票")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量计算因子失败: {e}")
            return {}
    
    # 技术面因子计算方法
    
    def _calculate_rsi(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算RSI因子"""
        try:
            period = parameters.get('period', 14)
            market_data = self._get_market_data(symbol, days=period*2, end_date=end_date)
            
            if market_data is None or len(market_data) < period:
                return None
            
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if 'rsi' in indicators and len(indicators['rsi']) > 0:
                rsi_value = indicators['rsi'].iloc[-1]
                
                # RSI标准化：转换为0-1范围，中性值0.5
                normalized_rsi = rsi_value / 100.0
                
                return normalized_rsi
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 计算RSI失败: {symbol} - {e}")
            return None
    
    def _calculate_macd(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算MACD因子"""
        try:
            fast_period = parameters.get('fast_period', 12)
            slow_period = parameters.get('slow_period', 26)
            signal_period = parameters.get('signal_period', 9)
            
            required_days = slow_period + signal_period + 10
            market_data = self._get_market_data(symbol, days=required_days, end_date=end_date)
            
            if market_data is None or len(market_data) < required_days:
                return None
            
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if all(k in indicators for k in ['macd', 'signal']) and len(indicators['macd']) > 0:
                macd_value = indicators['macd'].iloc[-1]
                signal_value = indicators['signal'].iloc[-1]
                
                # MACD信号强度
                macd_signal = macd_value - signal_value
                
                # 标准化处理
                price_std = market_data['close_price'].std()
                if price_std > 0:
                    normalized_macd = np.tanh(macd_signal / price_std)  # 使用tanh函数限制在-1到1
                    return (normalized_macd + 1) / 2  # 转换到0-1范围
                
            return None
            
        except Exception as e:
            logger.error(f"❌ 计算MACD失败: {symbol} - {e}")
            return None
    
    def _calculate_bollinger_bands(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算布林带因子"""
        try:
            period = parameters.get('period', 20)
            std_dev = parameters.get('std_dev', 2)
            
            market_data = self._get_market_data(symbol, days=period+10, end_date=end_date)
            
            if market_data is None or len(market_data) < period:
                return None
            
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if all(k in indicators for k in ['bb_upper', 'bb_middle', 'bb_lower']):
                current_price = market_data['close_price'].iloc[-1]
                bb_upper = indicators['bb_upper'].iloc[-1]
                bb_middle = indicators['bb_middle'].iloc[-1]
                bb_lower = indicators['bb_lower'].iloc[-1]
                
                # 计算价格在布林带中的相对位置
                if bb_upper != bb_lower:
                    bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                    return np.clip(bb_position, 0, 1)
                
            return None
            
        except Exception as e:
            logger.error(f"❌ 计算布林带失败: {symbol} - {e}")
            return None
    
    def _calculate_kdj(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算KDJ因子"""
        try:
            k_period = parameters.get('k_period', 9)
            
            market_data = self._get_market_data(symbol, days=k_period*2, end_date=end_date)
            
            if market_data is None or len(market_data) < k_period:
                return None
            
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if 'stoch_k' in indicators and len(indicators['stoch_k']) > 0:
                k_value = indicators['stoch_k'].iloc[-1]
                return k_value / 100.0  # 标准化到0-1
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 计算KDJ失败: {symbol} - {e}")
            return None
    
    def _calculate_ma_trend(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算均线趋势因子"""
        try:
            periods = parameters.get('periods', [5, 10, 20, 60])
            max_period = max(periods)
            
            market_data = self._get_market_data(symbol, days=max_period+10, end_date=end_date)
            
            if market_data is None or len(market_data) < max_period:
                return None
            
            # 计算各周期均线
            mas = {}
            for period in periods:
                ma_values = market_data['close_price'].rolling(window=period).mean()
                mas[period] = ma_values.iloc[-1]
            
            current_price = market_data['close_price'].iloc[-1]
            
            # 检查多头排列
            ma_values = [current_price] + [mas[p] for p in sorted(periods)]
            
            # 计算趋势强度
            trend_score = 0
            for i in range(len(ma_values) - 1):
                if ma_values[i] > ma_values[i + 1]:
                    trend_score += 1
            
            # 标准化
            max_score = len(ma_values) - 1
            return trend_score / max_score if max_score > 0 else 0
            
        except Exception as e:
            logger.error(f"❌ 计算均线趋势失败: {symbol} - {e}")
            return None
    
    # 基本面因子计算方法
    
    def _calculate_roe(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算ROE因子"""
        try:
            # 简化处理：基于股票代码生成模拟ROE
            # 实际应该从财务数据表获取
            import hashlib
            hash_value = int(hashlib.md5(symbol.encode()).hexdigest()[:8], 16)
            
            # 生成0.05-0.25范围的ROE
            roe = 0.05 + (hash_value % 1000) / 1000 * 0.20
            
            # 标准化到0-1范围
            min_roe = parameters.get('min_roe', 0.05)
            max_roe = 0.30  # 假设最大ROE为30%
            
            normalized_roe = (roe - min_roe) / (max_roe - min_roe)
            return np.clip(normalized_roe, 0, 1)
            
        except Exception as e:
            logger.error(f"❌ 计算ROE失败: {symbol} - {e}")
            return None
    
    def _calculate_pe_ratio(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算PE比率因子"""
        try:
            # 简化处理：基于股票代码生成模拟PE
            import hashlib
            hash_value = int(hashlib.md5((symbol + 'pe').encode()).hexdigest()[:8], 16)
            
            # 生成5-50范围的PE
            pe_ratio = 5 + (hash_value % 1000) / 1000 * 45
            
            # PE因子：低PE高分，使用倒数关系
            max_pe = parameters.get('max_pe', 100)
            
            # 标准化：PE越低分数越高
            normalized_pe = 1 - (pe_ratio / max_pe)
            return np.clip(normalized_pe, 0, 1)
            
        except Exception as e:
            logger.error(f"❌ 计算PE比率失败: {symbol} - {e}")
            return None
    
    def _calculate_pb_ratio(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算PB比率因子"""
        try:
            # 简化处理：基于股票代码生成模拟PB
            import hashlib
            hash_value = int(hashlib.md5((symbol + 'pb').encode()).hexdigest()[:8], 16)
            
            # 生成0.5-10范围的PB
            pb_ratio = 0.5 + (hash_value % 1000) / 1000 * 9.5
            
            # PB因子：低PB高分
            max_pb = parameters.get('max_pb', 20)
            
            normalized_pb = 1 - (pb_ratio / max_pb)
            return np.clip(normalized_pb, 0, 1)
            
        except Exception as e:
            logger.error(f"❌ 计算PB比率失败: {symbol} - {e}")
            return None
    
    def _calculate_revenue_growth(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算营收增长率因子"""
        try:
            # 简化处理：基于股票代码生成模拟增长率
            import hashlib
            hash_value = int(hashlib.md5((symbol + 'growth').encode()).hexdigest()[:8], 16)
            
            # 生成-20%到50%范围的增长率
            growth_rate = -0.2 + (hash_value % 1000) / 1000 * 0.7
            
            # 标准化
            min_growth = parameters.get('min_growth', -0.5)
            max_growth = parameters.get('max_growth', 2.0)
            
            normalized_growth = (growth_rate - min_growth) / (max_growth - min_growth)
            return np.clip(normalized_growth, 0, 1)
            
        except Exception as e:
            logger.error(f"❌ 计算营收增长率失败: {symbol} - {e}")
            return None
    
    # 市场表现因子计算方法
    
    def _calculate_price_momentum(self, symbol: str, parameters: Dict[str, Any], end_date: date = None) -> Optional[float]:
        """计算价格动量因子"""
        try:
            periods = parameters.get('periods', [5, 10, 20])
            weights = parameters.get('weights', [0.5, 0.3, 0.2])
            
            max_period = max(periods)
            market_data = self._get_market_data(symbol, days=max_period+5, end_date=end_date)
            
            if market_data is None or len(market_data) < max_period:
                return None
            
            momentum_score = 0
            total_weight = 0
            
            for period, weight in zip(periods, weights):
                if len(market_data) >= period:
                    # 计算期间收益率
                    start_price = market_data['close_price'].iloc[-period]
                    end_price = market_data['close_price'].iloc[-1]
                    
                    if start_price > 0:
                        period_return = (end_price - start_price) / start_price
                        momentum_score += period_return * weight
                        total_weight += weight
            
            if total_weight > 0:
                momentum_score = momentum_score / total_weight
                
                # 标准化到0-1范围
                # 假设动量范围在-50%到50%
                normalized_momentum = (momentum_score + 0.5) / 1.0
                return np.clip(normalized_momentum, 0, 1)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 计算价格动量失败: {symbol} - {e}")
            return None
    
    def _get_market_data(self, symbol: str, days: int = 60, end_date: date = None) -> Optional[pd.DataFrame]:
        """获取市场数据"""
        try:
            if end_date is None:
                end_date = datetime.now().date()
            
            start_date = end_date - timedelta(days=days)
            
            with db_manager.get_session() as session:
                records = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if not records:
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in records:
                    data_dict = {
                        'trade_date': record.trade_date,
                        'open_price': float(record.open_price or 0),
                        'high_price': float(record.high_price or 0),
                        'low_price': float(record.low_price or 0),
                        'close_price': float(record.close_price or 0),
                        'volume': float(record.volume or 0),
                        'amount': float(record.amount or 0)
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def normalize_factor_value(self, value: float, method: str = "z_score") -> float:
        """标准化因子值"""
        try:
            if method == "z_score":
                # Z-score标准化（需要历史数据）
                # 简化处理：直接返回原值
                return value
            elif method == "min_max":
                # Min-Max标准化
                return np.clip(value, 0, 1)
            elif method == "rank":
                # 排名标准化（需要同期其他股票数据）
                return value
            else:
                return value
                
        except Exception as e:
            logger.error(f"❌ 标准化因子值失败: {e}")
            return value
    
    def get_supported_methods(self) -> List[str]:
        """获取支持的计算方法"""
        return list(self.calculation_methods.keys())
    
    def register_calculation_method(self, method_name: str, method_func: Callable) -> bool:
        """注册新的计算方法"""
        try:
            self.calculation_methods[method_name] = method_func
            logger.info(f"✅ 注册计算方法: {method_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 注册计算方法失败: {method_name} - {e}")
            return False
