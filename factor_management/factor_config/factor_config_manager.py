"""
因子配置管理器
管理所有因子的配置信息、参数设置、启用状态等
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, date
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class FactorCategory(Enum):
    """因子分类"""
    TECHNICAL = "TECHNICAL"         # 技术面因子
    FUNDAMENTAL = "FUNDAMENTAL"     # 基本面因子
    MARKET = "MARKET"              # 市场表现因子
    CUSTOM = "CUSTOM"              # 自定义因子

class FactorType(Enum):
    """因子类型"""
    MOMENTUM = "MOMENTUM"           # 动量因子
    REVERSAL = "REVERSAL"          # 反转因子
    VALUE = "VALUE"                # 价值因子
    GROWTH = "GROWTH"              # 成长因子
    QUALITY = "QUALITY"            # 质量因子
    VOLATILITY = "VOLATILITY"      # 波动率因子
    LIQUIDITY = "LIQUIDITY"        # 流动性因子
    SIZE = "SIZE"                  # 规模因子

@dataclass
class FactorConfig:
    """因子配置"""
    factor_id: str
    factor_name: str
    factor_name_cn: str
    category: FactorCategory
    factor_type: FactorType
    description: str
    calculation_method: str
    parameters: Dict[str, Any]
    weight: float
    is_enabled: bool
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    normalization_method: str = "z_score"
    update_frequency: str = "daily"
    data_requirements: List[str] = None
    created_time: datetime = None
    updated_time: datetime = None

class FactorConfigManager:
    """因子配置管理器"""
    
    def __init__(self, config_file: str = "factor_configs.json"):
        self.config_file = Path(config_file)
        self.factor_configs: Dict[str, FactorConfig] = {}
        
        # 默认因子配置
        self.default_factors = self._get_default_factors()
        
        # 加载配置
        self.load_configs()
        
        logger.info("🔧 因子配置管理器初始化完成")
        logger.info(f"  - 配置文件: {self.config_file}")
        logger.info(f"  - 已加载因子: {len(self.factor_configs)}个")
    
    def _get_default_factors(self) -> List[FactorConfig]:
        """获取默认因子配置"""
        return [
            # 技术面因子
            FactorConfig(
                factor_id="tech_rsi",
                factor_name="RSI",
                factor_name_cn="相对强弱指标",
                category=FactorCategory.TECHNICAL,
                factor_type=FactorType.MOMENTUM,
                description="相对强弱指标，衡量价格动量",
                calculation_method="rsi",
                parameters={"period": 14, "overbought": 70, "oversold": 30},
                weight=0.15,
                is_enabled=True,
                min_value=0,
                max_value=100,
                data_requirements=["close_price"]
            ),
            FactorConfig(
                factor_id="tech_macd",
                factor_name="MACD",
                factor_name_cn="指数平滑移动平均线",
                category=FactorCategory.TECHNICAL,
                factor_type=FactorType.MOMENTUM,
                description="MACD指标，衡量趋势变化",
                calculation_method="macd",
                parameters={"fast_period": 12, "slow_period": 26, "signal_period": 9},
                weight=0.12,
                is_enabled=True,
                data_requirements=["close_price"]
            ),
            FactorConfig(
                factor_id="tech_bollinger",
                factor_name="Bollinger_Bands",
                factor_name_cn="布林带",
                category=FactorCategory.TECHNICAL,
                factor_type=FactorType.REVERSAL,
                description="布林带指标，衡量价格相对位置",
                calculation_method="bollinger_bands",
                parameters={"period": 20, "std_dev": 2},
                weight=0.10,
                is_enabled=True,
                data_requirements=["close_price"]
            ),
            FactorConfig(
                factor_id="tech_kdj",
                factor_name="KDJ",
                factor_name_cn="随机指标",
                category=FactorCategory.TECHNICAL,
                factor_type=FactorType.MOMENTUM,
                description="KDJ随机指标，衡量超买超卖",
                calculation_method="kdj",
                parameters={"k_period": 9, "d_period": 3, "j_period": 3},
                weight=0.08,
                is_enabled=True,
                data_requirements=["high_price", "low_price", "close_price"]
            ),
            FactorConfig(
                factor_id="tech_ma_trend",
                factor_name="MA_Trend",
                factor_name_cn="均线趋势",
                category=FactorCategory.TECHNICAL,
                factor_type=FactorType.MOMENTUM,
                description="多周期均线趋势强度",
                calculation_method="ma_trend",
                parameters={"periods": [5, 10, 20, 60], "trend_threshold": 0.02},
                weight=0.05,
                is_enabled=True,
                data_requirements=["close_price"]
            ),
            
            # 基本面因子
            FactorConfig(
                factor_id="fund_roe",
                factor_name="ROE",
                factor_name_cn="净资产收益率",
                category=FactorCategory.FUNDAMENTAL,
                factor_type=FactorType.QUALITY,
                description="净资产收益率，衡量盈利能力",
                calculation_method="roe",
                parameters={"period": "ttm", "min_roe": 0.05},
                weight=0.12,
                is_enabled=True,
                min_value=-1,
                max_value=1,
                data_requirements=["net_profit", "shareholders_equity"]
            ),
            FactorConfig(
                factor_id="fund_pe_ratio",
                factor_name="PE_Ratio",
                factor_name_cn="市盈率",
                category=FactorCategory.FUNDAMENTAL,
                factor_type=FactorType.VALUE,
                description="市盈率，衡量估值水平",
                calculation_method="pe_ratio",
                parameters={"period": "ttm", "max_pe": 100},
                weight=0.10,
                is_enabled=True,
                min_value=0,
                max_value=100,
                data_requirements=["market_cap", "net_profit"]
            ),
            FactorConfig(
                factor_id="fund_pb_ratio",
                factor_name="PB_Ratio",
                factor_name_cn="市净率",
                category=FactorCategory.FUNDAMENTAL,
                factor_type=FactorType.VALUE,
                description="市净率，衡量估值水平",
                calculation_method="pb_ratio",
                parameters={"period": "latest", "max_pb": 20},
                weight=0.08,
                is_enabled=True,
                min_value=0,
                max_value=20,
                data_requirements=["market_cap", "shareholders_equity"]
            ),
            FactorConfig(
                factor_id="fund_revenue_growth",
                factor_name="Revenue_Growth",
                factor_name_cn="营收增长率",
                category=FactorCategory.FUNDAMENTAL,
                factor_type=FactorType.GROWTH,
                description="营业收入增长率，衡量成长性",
                calculation_method="revenue_growth",
                parameters={"period": "yoy", "min_growth": -0.5, "max_growth": 2.0},
                weight=0.10,
                is_enabled=True,
                min_value=-0.5,
                max_value=2.0,
                data_requirements=["revenue"]
            ),
            
            # 市场表现因子
            FactorConfig(
                factor_id="market_momentum",
                factor_name="Price_Momentum",
                factor_name_cn="价格动量",
                category=FactorCategory.MARKET,
                factor_type=FactorType.MOMENTUM,
                description="价格动量因子，衡量价格趋势",
                calculation_method="price_momentum",
                parameters={"periods": [5, 10, 20], "weights": [0.5, 0.3, 0.2]},
                weight=0.10,
                is_enabled=True,
                data_requirements=["close_price"]
            )
        ]
    
    def load_configs(self) -> bool:
        """加载因子配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                self.factor_configs = {}
                for factor_id, config_dict in config_data.items():
                    # 转换枚举类型
                    config_dict['category'] = FactorCategory(config_dict['category'])
                    config_dict['factor_type'] = FactorType(config_dict['factor_type'])
                    
                    # 转换时间字段
                    if config_dict.get('created_time'):
                        config_dict['created_time'] = datetime.fromisoformat(config_dict['created_time'])
                    if config_dict.get('updated_time'):
                        config_dict['updated_time'] = datetime.fromisoformat(config_dict['updated_time'])
                    
                    self.factor_configs[factor_id] = FactorConfig(**config_dict)
                
                logger.info(f"✅ 从文件加载因子配置: {len(self.factor_configs)}个")
            else:
                # 使用默认配置
                self._initialize_default_configs()
                logger.info("📝 使用默认因子配置")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载因子配置失败: {e}")
            # 使用默认配置
            self._initialize_default_configs()
            return False
    
    def _initialize_default_configs(self) -> None:
        """初始化默认配置"""
        self.factor_configs = {}
        for factor_config in self.default_factors:
            factor_config.created_time = datetime.now()
            factor_config.updated_time = datetime.now()
            self.factor_configs[factor_config.factor_id] = factor_config
        
        # 保存默认配置
        self.save_configs()
    
    def save_configs(self) -> bool:
        """保存因子配置"""
        try:
            config_data = {}
            for factor_id, factor_config in self.factor_configs.items():
                config_dict = asdict(factor_config)
                
                # 转换枚举为字符串
                config_dict['category'] = factor_config.category.value
                config_dict['factor_type'] = factor_config.factor_type.value
                
                # 转换时间为字符串
                if config_dict.get('created_time'):
                    config_dict['created_time'] = factor_config.created_time.isoformat()
                if config_dict.get('updated_time'):
                    config_dict['updated_time'] = factor_config.updated_time.isoformat()
                
                config_data[factor_id] = config_dict
            
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 因子配置已保存: {len(config_data)}个")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存因子配置失败: {e}")
            return False
    
    def get_factor_config(self, factor_id: str) -> Optional[FactorConfig]:
        """获取因子配置"""
        return self.factor_configs.get(factor_id)
    
    def get_all_configs(self) -> Dict[str, FactorConfig]:
        """获取所有因子配置"""
        return self.factor_configs.copy()
    
    def get_enabled_configs(self) -> Dict[str, FactorConfig]:
        """获取启用的因子配置"""
        return {
            factor_id: config 
            for factor_id, config in self.factor_configs.items() 
            if config.is_enabled
        }
    
    def get_configs_by_category(self, category: FactorCategory) -> Dict[str, FactorConfig]:
        """按分类获取因子配置"""
        return {
            factor_id: config 
            for factor_id, config in self.factor_configs.items() 
            if config.category == category
        }
    
    def add_factor_config(self, factor_config: FactorConfig) -> bool:
        """添加因子配置"""
        try:
            if factor_config.factor_id in self.factor_configs:
                logger.warning(f"⚠️ 因子配置已存在: {factor_config.factor_id}")
                return False
            
            factor_config.created_time = datetime.now()
            factor_config.updated_time = datetime.now()
            
            self.factor_configs[factor_config.factor_id] = factor_config
            
            # 保存配置
            self.save_configs()
            
            logger.info(f"✅ 添加因子配置: {factor_config.factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加因子配置失败: {factor_config.factor_id} - {e}")
            return False
    
    def update_factor_config(self, factor_id: str, updates: Dict[str, Any]) -> bool:
        """更新因子配置"""
        try:
            if factor_id not in self.factor_configs:
                logger.error(f"❌ 因子配置不存在: {factor_id}")
                return False
            
            factor_config = self.factor_configs[factor_id]
            
            # 更新字段
            for key, value in updates.items():
                if hasattr(factor_config, key):
                    setattr(factor_config, key, value)
            
            factor_config.updated_time = datetime.now()
            
            # 保存配置
            self.save_configs()
            
            logger.info(f"✅ 更新因子配置: {factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新因子配置失败: {factor_id} - {e}")
            return False
    
    def delete_factor_config(self, factor_id: str) -> bool:
        """删除因子配置"""
        try:
            if factor_id not in self.factor_configs:
                logger.error(f"❌ 因子配置不存在: {factor_id}")
                return False
            
            del self.factor_configs[factor_id]
            
            # 保存配置
            self.save_configs()
            
            logger.info(f"✅ 删除因子配置: {factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除因子配置失败: {factor_id} - {e}")
            return False
    
    def enable_factor(self, factor_id: str) -> bool:
        """启用因子"""
        return self.update_factor_config(factor_id, {'is_enabled': True})
    
    def disable_factor(self, factor_id: str) -> bool:
        """禁用因子"""
        return self.update_factor_config(factor_id, {'is_enabled': False})
    
    def update_factor_weight(self, factor_id: str, weight: float) -> bool:
        """更新因子权重"""
        if not 0 <= weight <= 1:
            logger.error(f"❌ 权重值无效: {weight}")
            return False
        
        return self.update_factor_config(factor_id, {'weight': weight})
    
    def update_factor_parameters(self, factor_id: str, parameters: Dict[str, Any]) -> bool:
        """更新因子参数"""
        return self.update_factor_config(factor_id, {'parameters': parameters})
    
    def get_factor_summary(self) -> Dict[str, Any]:
        """获取因子配置摘要"""
        try:
            total_factors = len(self.factor_configs)
            enabled_factors = len(self.get_enabled_configs())
            
            # 按分类统计
            category_stats = {}
            for category in FactorCategory:
                category_configs = self.get_configs_by_category(category)
                category_stats[category.value] = {
                    'total': len(category_configs),
                    'enabled': len([c for c in category_configs.values() if c.is_enabled])
                }
            
            # 权重统计
            total_weight = sum(config.weight for config in self.factor_configs.values() if config.is_enabled)
            
            return {
                'total_factors': total_factors,
                'enabled_factors': enabled_factors,
                'disabled_factors': total_factors - enabled_factors,
                'category_stats': category_stats,
                'total_weight': total_weight,
                'config_file': str(self.config_file),
                'last_updated': max(
                    (config.updated_time for config in self.factor_configs.values() if config.updated_time),
                    default=datetime.now()
                ).isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取因子摘要失败: {e}")
            return {}
    
    def validate_config(self, factor_config: FactorConfig) -> List[str]:
        """验证因子配置"""
        errors = []
        
        try:
            # 基本字段验证
            if not factor_config.factor_id:
                errors.append("因子ID不能为空")
            
            if not factor_config.factor_name:
                errors.append("因子名称不能为空")
            
            if not factor_config.calculation_method:
                errors.append("计算方法不能为空")
            
            # 权重验证
            if not 0 <= factor_config.weight <= 1:
                errors.append(f"权重值无效: {factor_config.weight}")
            
            # 参数验证
            if not isinstance(factor_config.parameters, dict):
                errors.append("参数必须是字典类型")
            
            # 数值范围验证
            if factor_config.min_value is not None and factor_config.max_value is not None:
                if factor_config.min_value >= factor_config.max_value:
                    errors.append("最小值必须小于最大值")
            
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
        
        return errors
