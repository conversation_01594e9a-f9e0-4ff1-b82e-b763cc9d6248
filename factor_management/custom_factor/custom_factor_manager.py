"""
自定义因子管理器
支持用户创建和管理自定义因子
"""

import logging
import json
import ast
import inspect
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, date
from dataclasses import dataclass
from pathlib import Path

from factor_management.factor_config.factor_config_manager import FactorConfig, FactorCategory, FactorType

logger = logging.getLogger(__name__)

@dataclass
class CustomFactorDefinition:
    """自定义因子定义"""
    factor_id: str
    factor_name: str
    factor_name_cn: str
    description: str
    formula: str                    # 因子计算公式
    code: str                      # Python代码
    input_variables: List[str]     # 输入变量
    output_type: str              # 输出类型
    validation_rules: Dict[str, Any]  # 验证规则
    created_by: str
    created_time: datetime
    updated_time: datetime
    is_validated: bool = False
    test_results: Dict[str, Any] = None

class CustomFactorManager:
    """自定义因子管理器"""
    
    def __init__(self, custom_factors_dir: str = "custom_factors"):
        self.custom_factors_dir = Path(custom_factors_dir)
        self.custom_factors_dir.mkdir(exist_ok=True)
        
        # 自定义因子定义
        self.custom_factor_definitions: Dict[str, CustomFactorDefinition] = {}
        
        # 编译后的因子函数
        self.compiled_factors: Dict[str, Callable] = {}
        
        # 安全的内置函数白名单
        self.safe_builtins = {
            'abs', 'max', 'min', 'sum', 'len', 'round', 'int', 'float',
            'pow', 'sqrt', 'log', 'exp', 'sin', 'cos', 'tan'
        }
        
        # 允许的模块
        self.allowed_modules = {
            'numpy': 'np',
            'pandas': 'pd',
            'math': 'math'
        }
        
        # 加载自定义因子
        self.load_custom_factors()
        
        logger.info("🔧 自定义因子管理器初始化完成")
        logger.info(f"  - 自定义因子目录: {self.custom_factors_dir}")
        logger.info(f"  - 已加载因子: {len(self.custom_factor_definitions)}个")
    
    def create_custom_factor(self,
                           factor_definition: CustomFactorDefinition) -> bool:
        """
        创建自定义因子
        
        Args:
            factor_definition: 因子定义
            
        Returns:
            是否创建成功
        """
        try:
            # 验证因子定义
            validation_errors = self._validate_factor_definition(factor_definition)
            if validation_errors:
                logger.error(f"❌ 因子定义验证失败: {validation_errors}")
                return False
            
            # 编译因子代码
            compiled_function = self._compile_factor_code(factor_definition)
            if compiled_function is None:
                logger.error(f"❌ 因子代码编译失败: {factor_definition.factor_id}")
                return False
            
            # 测试因子函数
            test_result = self._test_factor_function(compiled_function, factor_definition)
            factor_definition.test_results = test_result
            factor_definition.is_validated = test_result.get('success', False)
            
            if not factor_definition.is_validated:
                logger.error(f"❌ 因子函数测试失败: {factor_definition.factor_id}")
                return False
            
            # 保存因子定义
            factor_definition.created_time = datetime.now()
            factor_definition.updated_time = datetime.now()
            
            self.custom_factor_definitions[factor_definition.factor_id] = factor_definition
            self.compiled_factors[factor_definition.factor_id] = compiled_function
            
            # 保存到文件
            self._save_factor_definition(factor_definition)
            
            logger.info(f"✅ 创建自定义因子成功: {factor_definition.factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建自定义因子失败: {factor_definition.factor_id} - {e}")
            return False
    
    def update_custom_factor(self,
                           factor_id: str,
                           updates: Dict[str, Any]) -> bool:
        """
        更新自定义因子
        
        Args:
            factor_id: 因子ID
            updates: 更新内容
            
        Returns:
            是否更新成功
        """
        try:
            if factor_id not in self.custom_factor_definitions:
                logger.error(f"❌ 自定义因子不存在: {factor_id}")
                return False
            
            factor_definition = self.custom_factor_definitions[factor_id]
            
            # 更新字段
            for key, value in updates.items():
                if hasattr(factor_definition, key):
                    setattr(factor_definition, key, value)
            
            factor_definition.updated_time = datetime.now()
            
            # 如果更新了代码，需要重新编译和测试
            if 'code' in updates or 'formula' in updates:
                compiled_function = self._compile_factor_code(factor_definition)
                if compiled_function is None:
                    logger.error(f"❌ 更新后代码编译失败: {factor_id}")
                    return False
                
                test_result = self._test_factor_function(compiled_function, factor_definition)
                factor_definition.test_results = test_result
                factor_definition.is_validated = test_result.get('success', False)
                
                if factor_definition.is_validated:
                    self.compiled_factors[factor_id] = compiled_function
                else:
                    logger.error(f"❌ 更新后因子测试失败: {factor_id}")
                    return False
            
            # 保存更新
            self._save_factor_definition(factor_definition)
            
            logger.info(f"✅ 更新自定义因子成功: {factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新自定义因子失败: {factor_id} - {e}")
            return False
    
    def delete_custom_factor(self, factor_id: str) -> bool:
        """
        删除自定义因子
        
        Args:
            factor_id: 因子ID
            
        Returns:
            是否删除成功
        """
        try:
            if factor_id not in self.custom_factor_definitions:
                logger.error(f"❌ 自定义因子不存在: {factor_id}")
                return False
            
            # 删除内存中的定义
            del self.custom_factor_definitions[factor_id]
            
            if factor_id in self.compiled_factors:
                del self.compiled_factors[factor_id]
            
            # 删除文件
            factor_file = self.custom_factors_dir / f"{factor_id}.json"
            if factor_file.exists():
                factor_file.unlink()
            
            logger.info(f"✅ 删除自定义因子成功: {factor_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除自定义因子失败: {factor_id} - {e}")
            return False
    
    def calculate_custom_factor(self,
                              factor_id: str,
                              input_data: Dict[str, Any]) -> Optional[float]:
        """
        计算自定义因子值
        
        Args:
            factor_id: 因子ID
            input_data: 输入数据
            
        Returns:
            因子值
        """
        try:
            if factor_id not in self.compiled_factors:
                logger.error(f"❌ 自定义因子未编译: {factor_id}")
                return None
            
            factor_function = self.compiled_factors[factor_id]
            factor_definition = self.custom_factor_definitions[factor_id]
            
            # 检查输入变量
            for var in factor_definition.input_variables:
                if var not in input_data:
                    logger.error(f"❌ 缺少输入变量: {var}")
                    return None
            
            # 调用因子函数
            result = factor_function(input_data)
            
            # 验证输出
            if not isinstance(result, (int, float)):
                logger.error(f"❌ 因子输出类型错误: {type(result)}")
                return None
            
            return float(result)
            
        except Exception as e:
            logger.error(f"❌ 计算自定义因子失败: {factor_id} - {e}")
            return None
    
    def _validate_factor_definition(self, factor_definition: CustomFactorDefinition) -> List[str]:
        """验证因子定义"""
        errors = []
        
        try:
            # 基本字段验证
            if not factor_definition.factor_id:
                errors.append("因子ID不能为空")
            
            if not factor_definition.factor_name:
                errors.append("因子名称不能为空")
            
            if not factor_definition.code:
                errors.append("因子代码不能为空")
            
            # 检查因子ID是否已存在
            if factor_definition.factor_id in self.custom_factor_definitions:
                errors.append(f"因子ID已存在: {factor_definition.factor_id}")
            
            # 验证代码语法
            try:
                ast.parse(factor_definition.code)
            except SyntaxError as e:
                errors.append(f"代码语法错误: {e}")
            
            # 验证输入变量
            if not factor_definition.input_variables:
                errors.append("必须指定输入变量")
            
        except Exception as e:
            errors.append(f"验证过程异常: {e}")
        
        return errors
    
    def _compile_factor_code(self, factor_definition: CustomFactorDefinition) -> Optional[Callable]:
        """编译因子代码"""
        try:
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {name: getattr(__builtins__, name) for name in self.safe_builtins if hasattr(__builtins__, name)}
            }
            
            # 添加允许的模块
            import numpy as np
            import pandas as pd
            import math
            
            safe_globals.update({
                'np': np,
                'pd': pd,
                'math': math
            })
            
            # 编译代码
            code_obj = compile(factor_definition.code, f"<factor_{factor_definition.factor_id}>", "exec")
            
            # 执行代码
            local_vars = {}
            exec(code_obj, safe_globals, local_vars)
            
            # 查找因子函数
            factor_function = None
            for name, obj in local_vars.items():
                if callable(obj) and not name.startswith('_'):
                    factor_function = obj
                    break
            
            if factor_function is None:
                logger.error(f"❌ 未找到因子函数: {factor_definition.factor_id}")
                return None
            
            return factor_function
            
        except Exception as e:
            logger.error(f"❌ 编译因子代码失败: {factor_definition.factor_id} - {e}")
            return None
    
    def _test_factor_function(self,
                            factor_function: Callable,
                            factor_definition: CustomFactorDefinition) -> Dict[str, Any]:
        """测试因子函数"""
        try:
            test_result = {
                'success': False,
                'error_message': None,
                'test_cases': []
            }
            
            # 生成测试数据
            test_cases = self._generate_test_data(factor_definition)
            
            for i, test_data in enumerate(test_cases):
                try:
                    result = factor_function(test_data)
                    
                    test_case_result = {
                        'case_id': i + 1,
                        'input': test_data,
                        'output': result,
                        'success': True,
                        'error': None
                    }
                    
                    # 验证输出类型
                    if not isinstance(result, (int, float)):
                        test_case_result['success'] = False
                        test_case_result['error'] = f"输出类型错误: {type(result)}"
                    
                    test_result['test_cases'].append(test_case_result)
                    
                except Exception as e:
                    test_case_result = {
                        'case_id': i + 1,
                        'input': test_data,
                        'output': None,
                        'success': False,
                        'error': str(e)
                    }
                    test_result['test_cases'].append(test_case_result)
            
            # 判断整体测试结果
            successful_cases = sum(1 for case in test_result['test_cases'] if case['success'])
            test_result['success'] = successful_cases == len(test_cases)
            
            if not test_result['success']:
                failed_cases = [case for case in test_result['test_cases'] if not case['success']]
                test_result['error_message'] = f"{len(failed_cases)}个测试用例失败"
            
            return test_result
            
        except Exception as e:
            return {
                'success': False,
                'error_message': f"测试过程异常: {e}",
                'test_cases': []
            }
    
    def _generate_test_data(self, factor_definition: CustomFactorDefinition) -> List[Dict[str, Any]]:
        """生成测试数据"""
        try:
            test_cases = []
            
            # 生成3个测试用例
            for i in range(3):
                test_data = {}
                
                for var in factor_definition.input_variables:
                    # 根据变量名生成合适的测试数据
                    if 'price' in var.lower():
                        test_data[var] = 10.0 + i * 2.0
                    elif 'volume' in var.lower():
                        test_data[var] = 1000000 + i * 500000
                    elif 'ratio' in var.lower() or 'rate' in var.lower():
                        test_data[var] = 0.1 + i * 0.05
                    elif 'return' in var.lower():
                        test_data[var] = 0.02 + i * 0.01
                    else:
                        test_data[var] = 1.0 + i * 0.5
                
                test_cases.append(test_data)
            
            return test_cases
            
        except Exception as e:
            logger.error(f"❌ 生成测试数据失败: {e}")
            return []
    
    def _save_factor_definition(self, factor_definition: CustomFactorDefinition) -> bool:
        """保存因子定义到文件"""
        try:
            factor_file = self.custom_factors_dir / f"{factor_definition.factor_id}.json"
            
            # 转换为字典
            factor_dict = {
                'factor_id': factor_definition.factor_id,
                'factor_name': factor_definition.factor_name,
                'factor_name_cn': factor_definition.factor_name_cn,
                'description': factor_definition.description,
                'formula': factor_definition.formula,
                'code': factor_definition.code,
                'input_variables': factor_definition.input_variables,
                'output_type': factor_definition.output_type,
                'validation_rules': factor_definition.validation_rules,
                'created_by': factor_definition.created_by,
                'created_time': factor_definition.created_time.isoformat(),
                'updated_time': factor_definition.updated_time.isoformat(),
                'is_validated': factor_definition.is_validated,
                'test_results': factor_definition.test_results
            }
            
            with open(factor_file, 'w', encoding='utf-8') as f:
                json.dump(factor_dict, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存因子定义失败: {factor_definition.factor_id} - {e}")
            return False
    
    def load_custom_factors(self) -> bool:
        """加载自定义因子"""
        try:
            loaded_count = 0
            
            for factor_file in self.custom_factors_dir.glob("*.json"):
                try:
                    with open(factor_file, 'r', encoding='utf-8') as f:
                        factor_dict = json.load(f)
                    
                    # 转换时间字段
                    factor_dict['created_time'] = datetime.fromisoformat(factor_dict['created_time'])
                    factor_dict['updated_time'] = datetime.fromisoformat(factor_dict['updated_time'])
                    
                    factor_definition = CustomFactorDefinition(**factor_dict)
                    
                    # 编译因子代码
                    if factor_definition.is_validated:
                        compiled_function = self._compile_factor_code(factor_definition)
                        if compiled_function:
                            self.compiled_factors[factor_definition.factor_id] = compiled_function
                    
                    self.custom_factor_definitions[factor_definition.factor_id] = factor_definition
                    loaded_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 加载因子文件失败: {factor_file} - {e}")
                    continue
            
            logger.info(f"✅ 加载自定义因子完成: {loaded_count}个")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载自定义因子失败: {e}")
            return False
    
    def get_custom_factor_definition(self, factor_id: str) -> Optional[CustomFactorDefinition]:
        """获取自定义因子定义"""
        return self.custom_factor_definitions.get(factor_id)
    
    def get_all_custom_factors(self) -> Dict[str, CustomFactorDefinition]:
        """获取所有自定义因子"""
        return self.custom_factor_definitions.copy()
    
    def get_validated_factors(self) -> Dict[str, CustomFactorDefinition]:
        """获取已验证的自定义因子"""
        return {
            fid: definition 
            for fid, definition in self.custom_factor_definitions.items() 
            if definition.is_validated
        }
    
    def convert_to_factor_config(self, factor_id: str, weight: float = 0.05) -> Optional[FactorConfig]:
        """将自定义因子转换为因子配置"""
        try:
            if factor_id not in self.custom_factor_definitions:
                return None
            
            definition = self.custom_factor_definitions[factor_id]
            
            if not definition.is_validated:
                logger.error(f"❌ 自定义因子未验证: {factor_id}")
                return None
            
            factor_config = FactorConfig(
                factor_id=definition.factor_id,
                factor_name=definition.factor_name,
                factor_name_cn=definition.factor_name_cn,
                category=FactorCategory.CUSTOM,
                factor_type=FactorType.CUSTOM,
                description=definition.description,
                calculation_method=f"custom_{factor_id}",
                parameters={'input_variables': definition.input_variables},
                weight=weight,
                is_enabled=True,
                data_requirements=definition.input_variables
            )
            
            return factor_config
            
        except Exception as e:
            logger.error(f"❌ 转换因子配置失败: {factor_id} - {e}")
            return None
    
    def get_factor_template(self) -> str:
        """获取因子代码模板"""
        return '''
def calculate_factor(data):
    """
    自定义因子计算函数
    
    Args:
        data: 输入数据字典，包含所需的变量
        
    Returns:
        float: 因子值
    """
    # 获取输入变量
    price = data.get('price', 0)
    volume = data.get('volume', 0)
    
    # 计算因子值
    # 示例：价格动量因子
    if price > 0:
        factor_value = price * 0.1
    else:
        factor_value = 0
    
    return factor_value
'''
    
    def get_custom_factor_summary(self) -> Dict[str, Any]:
        """获取自定义因子摘要"""
        try:
            total_factors = len(self.custom_factor_definitions)
            validated_factors = len(self.get_validated_factors())
            
            return {
                'total_factors': total_factors,
                'validated_factors': validated_factors,
                'unvalidated_factors': total_factors - validated_factors,
                'compiled_factors': len(self.compiled_factors),
                'factors_dir': str(self.custom_factors_dir),
                'last_updated': max(
                    (definition.updated_time for definition in self.custom_factor_definitions.values()),
                    default=datetime.now()
                ).isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取自定义因子摘要失败: {e}")
            return {}
