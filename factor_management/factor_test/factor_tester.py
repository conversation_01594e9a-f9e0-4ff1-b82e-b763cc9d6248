"""
因子测试器
测试因子的有效性、稳定性和预测能力
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from scipy import stats

from factor_management.factor_config.factor_config_manager import FactorConfig
from factor_management.factor_calculator.factor_calculator import FactorCalculator

logger = logging.getLogger(__name__)

@dataclass
class FactorTestResult:
    """因子测试结果"""
    factor_id: str
    test_type: str
    test_period: Tuple[date, date]
    sample_size: int
    
    # 统计特性
    mean_value: float
    std_value: float
    skewness: float
    kurtosis: float
    
    # 有效性测试
    ic_mean: Optional[float] = None          # 信息系数均值
    ic_std: Optional[float] = None           # 信息系数标准差
    ic_ir: Optional[float] = None            # 信息比率
    rank_ic: Optional[float] = None          # 排序信息系数
    
    # 稳定性测试
    stability_score: Optional[float] = None  # 稳定性评分
    decay_rate: Optional[float] = None       # 衰减率
    
    # 单调性测试
    monotonicity_score: Optional[float] = None  # 单调性评分
    
    # 分层回测
    layered_returns: Optional[Dict[str, float]] = None  # 分层收益
    
    # 综合评分
    overall_score: Optional[float] = None
    test_conclusion: str = ""

class FactorTester:
    """因子测试器"""
    
    def __init__(self):
        self.factor_calculator = FactorCalculator()
        
        # 测试参数
        self.test_period_days = 252         # 测试周期1年
        self.min_sample_size = 100          # 最小样本数量
        self.ic_window = 20                 # IC计算窗口
        self.layer_count = 5                # 分层数量
        
        logger.info("🧪 因子测试器初始化完成")
        logger.info(f"  - 测试周期: {self.test_period_days}天")
        logger.info(f"  - 最小样本: {self.min_sample_size}")
    
    def test_factor_effectiveness(self,
                                factor_config: FactorConfig,
                                symbols: List[str],
                                end_date: date = None) -> FactorTestResult:
        """
        测试因子有效性
        
        Args:
            factor_config: 因子配置
            symbols: 测试股票列表
            end_date: 测试结束日期
            
        Returns:
            因子测试结果
        """
        try:
            logger.info(f"🧪 开始因子有效性测试: {factor_config.factor_id}")
            
            if end_date is None:
                end_date = datetime.now().date()
            
            start_date = end_date - timedelta(days=self.test_period_days)
            
            # 收集因子数据
            factor_data = self._collect_factor_data(factor_config, symbols, start_date, end_date)
            
            if len(factor_data) < self.min_sample_size:
                logger.error(f"❌ 样本数量不足: {len(factor_data)} < {self.min_sample_size}")
                return self._create_failed_result(factor_config.factor_id, "样本数量不足")
            
            # 创建测试结果对象
            test_result = FactorTestResult(
                factor_id=factor_config.factor_id,
                test_type="effectiveness",
                test_period=(start_date, end_date),
                sample_size=len(factor_data),
                mean_value=0,
                std_value=0,
                skewness=0,
                kurtosis=0
            )
            
            # 1. 统计特性测试
            self._test_statistical_properties(factor_data, test_result)
            
            # 2. 信息系数测试
            self._test_information_coefficient(factor_data, test_result)
            
            # 3. 稳定性测试
            self._test_stability(factor_data, test_result)
            
            # 4. 单调性测试
            self._test_monotonicity(factor_data, test_result)
            
            # 5. 分层回测
            self._test_layered_backtest(factor_data, test_result)
            
            # 6. 综合评分
            self._calculate_overall_score(test_result)
            
            logger.info(f"✅ 因子测试完成: {factor_config.factor_id} 评分{test_result.overall_score:.2f}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ 因子有效性测试失败: {factor_config.factor_id} - {e}")
            return self._create_failed_result(factor_config.factor_id, str(e))
    
    def batch_test_factors(self,
                          factor_configs: Dict[str, FactorConfig],
                          symbols: List[str],
                          end_date: date = None) -> Dict[str, FactorTestResult]:
        """
        批量测试因子
        
        Args:
            factor_configs: 因子配置字典
            symbols: 测试股票列表
            end_date: 测试结束日期
            
        Returns:
            测试结果字典
        """
        try:
            logger.info(f"🧪 开始批量因子测试: {len(factor_configs)}个因子")
            
            test_results = {}
            
            for factor_id, factor_config in factor_configs.items():
                if not factor_config.is_enabled:
                    continue
                
                try:
                    test_result = self.test_factor_effectiveness(factor_config, symbols, end_date)
                    test_results[factor_id] = test_result
                    
                except Exception as e:
                    logger.error(f"❌ 测试因子失败: {factor_id} - {e}")
                    test_results[factor_id] = self._create_failed_result(factor_id, str(e))
            
            logger.info(f"✅ 批量因子测试完成: {len(test_results)}个结果")
            
            return test_results
            
        except Exception as e:
            logger.error(f"❌ 批量因子测试失败: {e}")
            return {}
    
    def _collect_factor_data(self,
                           factor_config: FactorConfig,
                           symbols: List[str],
                           start_date: date,
                           end_date: date) -> pd.DataFrame:
        """收集因子数据"""
        try:
            data_list = []
            
            # 按时间序列收集数据
            current_date = start_date
            while current_date <= end_date:
                try:
                    # 计算当日所有股票的因子值
                    daily_factors = {}
                    for symbol in symbols:
                        factor_value = self.factor_calculator.calculate_factor(
                            symbol, factor_config, current_date
                        )
                        if factor_value is not None:
                            daily_factors[symbol] = factor_value
                    
                    # 模拟收益率数据（实际应该从数据库获取）
                    daily_returns = self._get_mock_returns(symbols, current_date)
                    
                    # 合并数据
                    for symbol in daily_factors:
                        if symbol in daily_returns:
                            data_list.append({
                                'date': current_date,
                                'symbol': symbol,
                                'factor_value': daily_factors[symbol],
                                'return': daily_returns[symbol]
                            })
                    
                    current_date += timedelta(days=1)
                    
                except Exception as e:
                    logger.debug(f"❌ 收集日期数据失败: {current_date} - {e}")
                    current_date += timedelta(days=1)
                    continue
            
            if not data_list:
                return pd.DataFrame()
            
            df = pd.DataFrame(data_list)
            
            # 数据清洗
            df = df.dropna()
            df = df[df['factor_value'].notna()]
            df = df[df['return'].notna()]
            
            logger.debug(f"📊 收集因子数据: {len(df)}条记录")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 收集因子数据失败: {e}")
            return pd.DataFrame()
    
    def _get_mock_returns(self, symbols: List[str], date: date) -> Dict[str, float]:
        """获取模拟收益率数据"""
        try:
            returns = {}
            
            # 简化处理：生成模拟收益率
            import hashlib
            
            for symbol in symbols:
                # 基于股票代码和日期生成确定性的随机收益率
                hash_input = f"{symbol}_{date.isoformat()}"
                hash_value = int(hashlib.md5(hash_input.encode()).hexdigest()[:8], 16)
                
                # 生成-10%到10%的收益率
                return_rate = -0.1 + (hash_value % 10000) / 10000 * 0.2
                returns[symbol] = return_rate
            
            return returns
            
        except Exception as e:
            logger.error(f"❌ 获取模拟收益率失败: {e}")
            return {}
    
    def _test_statistical_properties(self, factor_data: pd.DataFrame, test_result: FactorTestResult) -> None:
        """测试统计特性"""
        try:
            factor_values = factor_data['factor_value'].values
            
            test_result.mean_value = np.mean(factor_values)
            test_result.std_value = np.std(factor_values)
            test_result.skewness = stats.skew(factor_values)
            test_result.kurtosis = stats.kurtosis(factor_values)
            
            logger.debug(f"📊 统计特性: 均值{test_result.mean_value:.4f}, 标准差{test_result.std_value:.4f}")
            
        except Exception as e:
            logger.error(f"❌ 统计特性测试失败: {e}")
    
    def _test_information_coefficient(self, factor_data: pd.DataFrame, test_result: FactorTestResult) -> None:
        """测试信息系数"""
        try:
            # 按日期分组计算IC
            ic_values = []
            
            for date in factor_data['date'].unique():
                daily_data = factor_data[factor_data['date'] == date]
                
                if len(daily_data) >= 10:  # 至少10只股票
                    # 计算Pearson相关系数
                    ic, p_value = stats.pearsonr(daily_data['factor_value'], daily_data['return'])
                    
                    if not np.isnan(ic):
                        ic_values.append(ic)
            
            if ic_values:
                test_result.ic_mean = np.mean(ic_values)
                test_result.ic_std = np.std(ic_values)
                test_result.ic_ir = test_result.ic_mean / test_result.ic_std if test_result.ic_std > 0 else 0
                
                # 计算排序IC
                rank_ic_values = []
                for date in factor_data['date'].unique():
                    daily_data = factor_data[factor_data['date'] == date]
                    
                    if len(daily_data) >= 10:
                        # 排序相关系数
                        rank_ic, _ = stats.spearmanr(daily_data['factor_value'], daily_data['return'])
                        
                        if not np.isnan(rank_ic):
                            rank_ic_values.append(rank_ic)
                
                if rank_ic_values:
                    test_result.rank_ic = np.mean(rank_ic_values)
            
            logger.debug(f"📊 IC测试: IC均值{test_result.ic_mean:.4f}, IR{test_result.ic_ir:.4f}")
            
        except Exception as e:
            logger.error(f"❌ IC测试失败: {e}")
    
    def _test_stability(self, factor_data: pd.DataFrame, test_result: FactorTestResult) -> None:
        """测试稳定性"""
        try:
            # 按时间窗口计算IC的稳定性
            dates = sorted(factor_data['date'].unique())
            
            if len(dates) < self.ic_window:
                return
            
            window_ics = []
            
            for i in range(len(dates) - self.ic_window + 1):
                window_dates = dates[i:i + self.ic_window]
                window_data = factor_data[factor_data['date'].isin(window_dates)]
                
                # 计算窗口内的平均IC
                daily_ics = []
                for date in window_dates:
                    daily_data = window_data[window_data['date'] == date]
                    
                    if len(daily_data) >= 10:
                        ic, _ = stats.pearsonr(daily_data['factor_value'], daily_data['return'])
                        if not np.isnan(ic):
                            daily_ics.append(ic)
                
                if daily_ics:
                    window_ics.append(np.mean(daily_ics))
            
            if len(window_ics) >= 2:
                # 稳定性评分：IC标准差的倒数
                ic_stability = np.std(window_ics)
                test_result.stability_score = 1.0 / (1.0 + ic_stability) if ic_stability > 0 else 1.0
                
                # 衰减率：IC随时间的变化趋势
                if len(window_ics) >= 3:
                    x = np.arange(len(window_ics))
                    slope, _, _, _, _ = stats.linregress(x, window_ics)
                    test_result.decay_rate = slope
            
            logger.debug(f"📊 稳定性测试: 稳定性{test_result.stability_score:.4f}")
            
        except Exception as e:
            logger.error(f"❌ 稳定性测试失败: {e}")
    
    def _test_monotonicity(self, factor_data: pd.DataFrame, test_result: FactorTestResult) -> None:
        """测试单调性"""
        try:
            # 按因子值分层，检查收益率的单调性
            factor_data_sorted = factor_data.sort_values('factor_value')
            
            # 分成5层
            layer_size = len(factor_data_sorted) // self.layer_count
            layer_returns = []
            
            for i in range(self.layer_count):
                start_idx = i * layer_size
                end_idx = (i + 1) * layer_size if i < self.layer_count - 1 else len(factor_data_sorted)
                
                layer_data = factor_data_sorted.iloc[start_idx:end_idx]
                layer_return = layer_data['return'].mean()
                layer_returns.append(layer_return)
            
            # 计算单调性评分
            if len(layer_returns) >= 2:
                # 检查是否单调递增
                monotonic_increases = sum(
                    1 for i in range(len(layer_returns) - 1) 
                    if layer_returns[i + 1] > layer_returns[i]
                )
                
                test_result.monotonicity_score = monotonic_increases / (len(layer_returns) - 1)
            
            logger.debug(f"📊 单调性测试: 单调性{test_result.monotonicity_score:.4f}")
            
        except Exception as e:
            logger.error(f"❌ 单调性测试失败: {e}")
    
    def _test_layered_backtest(self, factor_data: pd.DataFrame, test_result: FactorTestResult) -> None:
        """分层回测"""
        try:
            # 按因子值分层
            factor_data['factor_quantile'] = pd.qcut(
                factor_data['factor_value'], 
                q=self.layer_count, 
                labels=[f'Q{i+1}' for i in range(self.layer_count)]
            )
            
            # 计算各层收益
            layered_returns = {}
            
            for quantile in factor_data['factor_quantile'].unique():
                if pd.isna(quantile):
                    continue
                
                quantile_data = factor_data[factor_data['factor_quantile'] == quantile]
                avg_return = quantile_data['return'].mean()
                layered_returns[str(quantile)] = avg_return
            
            test_result.layered_returns = layered_returns
            
            logger.debug(f"📊 分层回测: {len(layered_returns)}层")
            
        except Exception as e:
            logger.error(f"❌ 分层回测失败: {e}")
    
    def _calculate_overall_score(self, test_result: FactorTestResult) -> None:
        """计算综合评分"""
        try:
            score_components = []
            
            # IC评分 (40%)
            if test_result.ic_mean is not None:
                ic_score = min(100, max(0, (abs(test_result.ic_mean) * 100)))
                score_components.append(('IC', ic_score, 0.4))
            
            # IR评分 (20%)
            if test_result.ic_ir is not None:
                ir_score = min(100, max(0, (abs(test_result.ic_ir) * 50)))
                score_components.append(('IR', ir_score, 0.2))
            
            # 稳定性评分 (20%)
            if test_result.stability_score is not None:
                stability_score = test_result.stability_score * 100
                score_components.append(('稳定性', stability_score, 0.2))
            
            # 单调性评分 (20%)
            if test_result.monotonicity_score is not None:
                monotonicity_score = test_result.monotonicity_score * 100
                score_components.append(('单调性', monotonicity_score, 0.2))
            
            # 计算加权平均分
            if score_components:
                total_score = sum(score * weight for _, score, weight in score_components)
                total_weight = sum(weight for _, _, weight in score_components)
                
                test_result.overall_score = total_score / total_weight if total_weight > 0 else 0
                
                # 生成测试结论
                if test_result.overall_score >= 80:
                    test_result.test_conclusion = "优秀 - 因子表现出色，建议使用"
                elif test_result.overall_score >= 60:
                    test_result.test_conclusion = "良好 - 因子表现较好，可以使用"
                elif test_result.overall_score >= 40:
                    test_result.test_conclusion = "一般 - 因子表现平平，谨慎使用"
                else:
                    test_result.test_conclusion = "较差 - 因子表现不佳，不建议使用"
            else:
                test_result.overall_score = 0
                test_result.test_conclusion = "无法评估 - 缺少足够的测试数据"
            
        except Exception as e:
            logger.error(f"❌ 计算综合评分失败: {e}")
            test_result.overall_score = 0
            test_result.test_conclusion = "评分失败"
    
    def _create_failed_result(self, factor_id: str, error_message: str) -> FactorTestResult:
        """创建失败的测试结果"""
        return FactorTestResult(
            factor_id=factor_id,
            test_type="effectiveness",
            test_period=(datetime.now().date(), datetime.now().date()),
            sample_size=0,
            mean_value=0,
            std_value=0,
            skewness=0,
            kurtosis=0,
            overall_score=0,
            test_conclusion=f"测试失败: {error_message}"
        )
    
    def generate_test_report(self, test_results: Dict[str, FactorTestResult]) -> str:
        """生成测试报告"""
        try:
            report = []
            report.append("=" * 80)
            report.append("🧪 因子测试报告")
            report.append("=" * 80)
            
            # 测试概览
            total_factors = len(test_results)
            excellent_factors = sum(1 for r in test_results.values() if r.overall_score >= 80)
            good_factors = sum(1 for r in test_results.values() if 60 <= r.overall_score < 80)
            
            report.append(f"\n📊 测试概览:")
            report.append(f"  总因子数: {total_factors}")
            report.append(f"  优秀因子: {excellent_factors} ({excellent_factors/total_factors:.1%})")
            report.append(f"  良好因子: {good_factors} ({good_factors/total_factors:.1%})")
            
            # 详细结果
            report.append(f"\n📈 详细测试结果:")
            
            # 按评分排序
            sorted_results = sorted(
                test_results.items(), 
                key=lambda x: x[1].overall_score, 
                reverse=True
            )
            
            for factor_id, result in sorted_results:
                report.append(f"\n  {factor_id}:")
                report.append(f"    综合评分: {result.overall_score:.1f}")
                report.append(f"    测试结论: {result.test_conclusion}")
                
                if result.ic_mean is not None:
                    report.append(f"    IC均值: {result.ic_mean:.4f}")
                
                if result.ic_ir is not None:
                    report.append(f"    信息比率: {result.ic_ir:.4f}")
                
                if result.stability_score is not None:
                    report.append(f"    稳定性: {result.stability_score:.4f}")
            
            report.append("\n" + "=" * 80)
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"❌ 生成测试报告失败: {e}")
            return "测试报告生成失败"
    
    def get_test_summary(self, test_results: Dict[str, FactorTestResult]) -> Dict[str, Any]:
        """获取测试摘要"""
        try:
            if not test_results:
                return {}
            
            scores = [r.overall_score for r in test_results.values() if r.overall_score is not None]
            
            return {
                'total_factors': len(test_results),
                'tested_factors': len([r for r in test_results.values() if r.overall_score > 0]),
                'average_score': np.mean(scores) if scores else 0,
                'max_score': max(scores) if scores else 0,
                'min_score': min(scores) if scores else 0,
                'excellent_count': sum(1 for s in scores if s >= 80),
                'good_count': sum(1 for s in scores if 60 <= s < 80),
                'poor_count': sum(1 for s in scores if s < 40),
                'test_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取测试摘要失败: {e}")
            return {}
