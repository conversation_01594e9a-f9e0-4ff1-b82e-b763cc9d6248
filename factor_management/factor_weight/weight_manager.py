"""
权重管理器
管理因子权重的动态调整和优化
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from factor_management.factor_config.factor_config_manager import FactorConfig, FactorCategory

logger = logging.getLogger(__name__)

class WeightAdjustmentMethod(Enum):
    """权重调整方法"""
    MANUAL = "MANUAL"                   # 手动调整
    PERFORMANCE_BASED = "PERFORMANCE_BASED"  # 基于绩效调整
    CORRELATION_BASED = "CORRELATION_BASED"  # 基于相关性调整
    VOLATILITY_BASED = "VOLATILITY_BASED"    # 基于波动率调整
    EQUAL_WEIGHT = "EQUAL_WEIGHT"       # 等权重
    CATEGORY_BALANCED = "CATEGORY_BALANCED"  # 分类平衡

@dataclass
class WeightAdjustmentRecord:
    """权重调整记录"""
    adjustment_id: str
    factor_id: str
    old_weight: float
    new_weight: float
    adjustment_method: WeightAdjustmentMethod
    adjustment_reason: str
    adjustment_time: datetime
    performance_impact: Optional[float] = None

class WeightManager:
    """权重管理器"""
    
    def __init__(self):
        # 权重约束
        self.min_weight = 0.01              # 最小权重1%
        self.max_weight = 0.30              # 最大权重30%
        self.weight_precision = 0.001       # 权重精度0.1%
        
        # 调整参数
        self.max_adjustment_per_time = 0.05  # 单次最大调整幅度5%
        self.rebalance_threshold = 0.02      # 再平衡阈值2%
        
        # 调整历史
        self.adjustment_history: List[WeightAdjustmentRecord] = []
        
        logger.info("⚖️ 权重管理器初始化完成")
        logger.info(f"  - 权重范围: {self.min_weight:.1%} - {self.max_weight:.1%}")
        logger.info(f"  - 调整精度: {self.weight_precision:.1%}")
    
    def adjust_weights(self,
                      factor_configs: Dict[str, FactorConfig],
                      method: WeightAdjustmentMethod,
                      adjustment_params: Dict[str, Any] = None) -> Dict[str, float]:
        """
        调整因子权重
        
        Args:
            factor_configs: 因子配置字典
            method: 调整方法
            adjustment_params: 调整参数
            
        Returns:
            新的权重字典
        """
        try:
            logger.info(f"⚖️ 开始权重调整: {method.value}")
            
            if method == WeightAdjustmentMethod.MANUAL:
                new_weights = self._manual_adjustment(factor_configs, adjustment_params or {})
            elif method == WeightAdjustmentMethod.PERFORMANCE_BASED:
                new_weights = self._performance_based_adjustment(factor_configs, adjustment_params or {})
            elif method == WeightAdjustmentMethod.CORRELATION_BASED:
                new_weights = self._correlation_based_adjustment(factor_configs, adjustment_params or {})
            elif method == WeightAdjustmentMethod.VOLATILITY_BASED:
                new_weights = self._volatility_based_adjustment(factor_configs, adjustment_params or {})
            elif method == WeightAdjustmentMethod.EQUAL_WEIGHT:
                new_weights = self._equal_weight_adjustment(factor_configs)
            elif method == WeightAdjustmentMethod.CATEGORY_BALANCED:
                new_weights = self._category_balanced_adjustment(factor_configs, adjustment_params or {})
            else:
                logger.error(f"❌ 不支持的权重调整方法: {method}")
                return {}
            
            # 应用权重约束
            constrained_weights = self._apply_weight_constraints(new_weights)
            
            # 权重归一化
            normalized_weights = self._normalize_weights(constrained_weights)
            
            # 记录调整历史
            self._record_weight_adjustments(factor_configs, normalized_weights, method)
            
            logger.info(f"✅ 权重调整完成: {len(normalized_weights)}个因子")
            
            return normalized_weights
            
        except Exception as e:
            logger.error(f"❌ 权重调整失败: {e}")
            return {}
    
    def _manual_adjustment(self,
                          factor_configs: Dict[str, FactorConfig],
                          adjustment_params: Dict[str, Any]) -> Dict[str, float]:
        """手动权重调整"""
        try:
            new_weights = {}
            manual_weights = adjustment_params.get('weights', {})
            
            for factor_id, factor_config in factor_configs.items():
                if factor_id in manual_weights:
                    new_weights[factor_id] = manual_weights[factor_id]
                else:
                    new_weights[factor_id] = factor_config.weight
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 手动权重调整失败: {e}")
            return {}
    
    def _performance_based_adjustment(self,
                                    factor_configs: Dict[str, FactorConfig],
                                    adjustment_params: Dict[str, Any]) -> Dict[str, float]:
        """基于绩效的权重调整"""
        try:
            # 获取因子绩效数据
            factor_performances = adjustment_params.get('performances', {})
            
            if not factor_performances:
                logger.warning("⚠️ 缺少绩效数据，使用当前权重")
                return {fid: fc.weight for fid, fc in factor_configs.items()}
            
            new_weights = {}
            
            # 计算绩效权重
            total_performance = sum(max(0, perf) for perf in factor_performances.values())
            
            if total_performance == 0:
                # 如果所有绩效都不好，使用等权重
                return self._equal_weight_adjustment(factor_configs)
            
            for factor_id, factor_config in factor_configs.items():
                performance = factor_performances.get(factor_id, 0)
                
                # 基于绩效计算新权重
                if performance > 0:
                    performance_weight = performance / total_performance
                    
                    # 与当前权重加权平均，避免过度调整
                    adjustment_rate = adjustment_params.get('adjustment_rate', 0.3)
                    new_weight = (1 - adjustment_rate) * factor_config.weight + adjustment_rate * performance_weight
                else:
                    # 绩效不好的因子降低权重
                    new_weight = factor_config.weight * 0.8
                
                new_weights[factor_id] = new_weight
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 基于绩效的权重调整失败: {e}")
            return {}
    
    def _correlation_based_adjustment(self,
                                    factor_configs: Dict[str, FactorConfig],
                                    adjustment_params: Dict[str, Any]) -> Dict[str, float]:
        """基于相关性的权重调整"""
        try:
            # 获取因子相关性矩阵
            correlation_matrix = adjustment_params.get('correlation_matrix', {})
            
            if not correlation_matrix:
                logger.warning("⚠️ 缺少相关性数据，使用当前权重")
                return {fid: fc.weight for fid, fc in factor_configs.items()}
            
            new_weights = {}
            correlation_threshold = adjustment_params.get('correlation_threshold', 0.7)
            
            # 识别高相关性因子组
            high_corr_groups = self._identify_correlation_groups(
                correlation_matrix, correlation_threshold
            )
            
            # 对高相关性因子组进行权重调整
            for factor_id, factor_config in factor_configs.items():
                current_weight = factor_config.weight
                
                # 检查是否在高相关性组中
                in_high_corr_group = any(factor_id in group for group in high_corr_groups)
                
                if in_high_corr_group:
                    # 降低高相关性因子的权重
                    new_weight = current_weight * 0.8
                else:
                    # 增加独立因子的权重
                    new_weight = current_weight * 1.1
                
                new_weights[factor_id] = new_weight
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 基于相关性的权重调整失败: {e}")
            return {}
    
    def _volatility_based_adjustment(self,
                                   factor_configs: Dict[str, FactorConfig],
                                   adjustment_params: Dict[str, Any]) -> Dict[str, float]:
        """基于波动率的权重调整"""
        try:
            # 获取因子波动率数据
            factor_volatilities = adjustment_params.get('volatilities', {})
            
            if not factor_volatilities:
                logger.warning("⚠️ 缺少波动率数据，使用当前权重")
                return {fid: fc.weight for fid, fc in factor_configs.items()}
            
            new_weights = {}
            
            # 计算波动率倒数权重（低波动率高权重）
            inv_volatilities = {
                fid: 1.0 / max(vol, 0.01) 
                for fid, vol in factor_volatilities.items()
            }
            
            total_inv_vol = sum(inv_volatilities.values())
            
            for factor_id, factor_config in factor_configs.items():
                if factor_id in inv_volatilities:
                    vol_weight = inv_volatilities[factor_id] / total_inv_vol
                    
                    # 与当前权重加权平均
                    adjustment_rate = adjustment_params.get('adjustment_rate', 0.2)
                    new_weight = (1 - adjustment_rate) * factor_config.weight + adjustment_rate * vol_weight
                else:
                    new_weight = factor_config.weight
                
                new_weights[factor_id] = new_weight
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 基于波动率的权重调整失败: {e}")
            return {}
    
    def _equal_weight_adjustment(self, factor_configs: Dict[str, FactorConfig]) -> Dict[str, float]:
        """等权重调整"""
        try:
            enabled_factors = [fid for fid, fc in factor_configs.items() if fc.is_enabled]
            
            if not enabled_factors:
                return {}
            
            equal_weight = 1.0 / len(enabled_factors)
            
            new_weights = {}
            for factor_id, factor_config in factor_configs.items():
                if factor_config.is_enabled:
                    new_weights[factor_id] = equal_weight
                else:
                    new_weights[factor_id] = 0.0
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 等权重调整失败: {e}")
            return {}
    
    def _category_balanced_adjustment(self,
                                    factor_configs: Dict[str, FactorConfig],
                                    adjustment_params: Dict[str, Any]) -> Dict[str, float]:
        """分类平衡权重调整"""
        try:
            # 获取分类权重配置
            category_weights = adjustment_params.get('category_weights', {
                FactorCategory.TECHNICAL.value: 0.5,
                FactorCategory.FUNDAMENTAL.value: 0.3,
                FactorCategory.MARKET.value: 0.2
            })
            
            # 按分类分组
            category_factors = {}
            for factor_id, factor_config in factor_configs.items():
                if not factor_config.is_enabled:
                    continue
                
                category = factor_config.category.value
                if category not in category_factors:
                    category_factors[category] = []
                category_factors[category].append(factor_id)
            
            new_weights = {}
            
            # 为每个分类分配权重
            for category, factor_ids in category_factors.items():
                category_weight = category_weights.get(category, 0.1)
                factor_weight = category_weight / len(factor_ids)
                
                for factor_id in factor_ids:
                    new_weights[factor_id] = factor_weight
            
            # 处理未启用的因子
            for factor_id, factor_config in factor_configs.items():
                if not factor_config.is_enabled:
                    new_weights[factor_id] = 0.0
            
            return new_weights
            
        except Exception as e:
            logger.error(f"❌ 分类平衡权重调整失败: {e}")
            return {}
    
    def _identify_correlation_groups(self,
                                   correlation_matrix: Dict[str, Dict[str, float]],
                                   threshold: float) -> List[List[str]]:
        """识别高相关性因子组"""
        try:
            groups = []
            processed = set()
            
            for factor1 in correlation_matrix:
                if factor1 in processed:
                    continue
                
                group = [factor1]
                processed.add(factor1)
                
                for factor2 in correlation_matrix:
                    if factor2 in processed:
                        continue
                    
                    correlation = correlation_matrix.get(factor1, {}).get(factor2, 0)
                    
                    if abs(correlation) >= threshold:
                        group.append(factor2)
                        processed.add(factor2)
                
                if len(group) > 1:
                    groups.append(group)
            
            return groups
            
        except Exception as e:
            logger.error(f"❌ 识别相关性组失败: {e}")
            return []
    
    def _apply_weight_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """应用权重约束"""
        try:
            constrained_weights = {}
            
            for factor_id, weight in weights.items():
                # 应用最小最大权重约束
                constrained_weight = max(self.min_weight, min(self.max_weight, weight))
                
                # 应用精度约束
                constrained_weight = round(constrained_weight / self.weight_precision) * self.weight_precision
                
                constrained_weights[factor_id] = constrained_weight
            
            return constrained_weights
            
        except Exception as e:
            logger.error(f"❌ 应用权重约束失败: {e}")
            return weights
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """权重归一化"""
        try:
            total_weight = sum(weights.values())
            
            if total_weight == 0:
                logger.error("❌ 总权重为0，无法归一化")
                return weights
            
            normalized_weights = {
                factor_id: weight / total_weight 
                for factor_id, weight in weights.items()
            }
            
            return normalized_weights
            
        except Exception as e:
            logger.error(f"❌ 权重归一化失败: {e}")
            return weights
    
    def _record_weight_adjustments(self,
                                  old_configs: Dict[str, FactorConfig],
                                  new_weights: Dict[str, float],
                                  method: WeightAdjustmentMethod) -> None:
        """记录权重调整历史"""
        try:
            adjustment_time = datetime.now()
            
            for factor_id, new_weight in new_weights.items():
                old_weight = old_configs.get(factor_id, FactorConfig("", "", "", None, None, "", "", {}, 0, False)).weight
                
                if abs(new_weight - old_weight) > self.weight_precision:
                    adjustment_id = f"{factor_id}_{adjustment_time.strftime('%Y%m%d_%H%M%S')}"
                    
                    record = WeightAdjustmentRecord(
                        adjustment_id=adjustment_id,
                        factor_id=factor_id,
                        old_weight=old_weight,
                        new_weight=new_weight,
                        adjustment_method=method,
                        adjustment_reason=f"{method.value}调整",
                        adjustment_time=adjustment_time
                    )
                    
                    self.adjustment_history.append(record)
            
            logger.debug(f"📝 记录权重调整: {len(new_weights)}个因子")
            
        except Exception as e:
            logger.error(f"❌ 记录权重调整失败: {e}")
    
    def get_weight_adjustment_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取权重调整历史"""
        try:
            history = []
            
            for record in self.adjustment_history[-limit:]:
                history.append({
                    'adjustment_id': record.adjustment_id,
                    'factor_id': record.factor_id,
                    'old_weight': record.old_weight,
                    'new_weight': record.new_weight,
                    'weight_change': record.new_weight - record.old_weight,
                    'adjustment_method': record.adjustment_method.value,
                    'adjustment_reason': record.adjustment_reason,
                    'adjustment_time': record.adjustment_time.isoformat(),
                    'performance_impact': record.performance_impact
                })
            
            return history
            
        except Exception as e:
            logger.error(f"❌ 获取权重调整历史失败: {e}")
            return []
    
    def calculate_weight_entropy(self, weights: Dict[str, float]) -> float:
        """计算权重熵（衡量权重分散程度）"""
        try:
            weight_values = list(weights.values())
            weight_values = [w for w in weight_values if w > 0]
            
            if not weight_values:
                return 0
            
            # 计算熵
            entropy = -sum(w * np.log(w) for w in weight_values if w > 0)
            
            # 标准化熵
            max_entropy = np.log(len(weight_values))
            normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
            
            return normalized_entropy
            
        except Exception as e:
            logger.error(f"❌ 计算权重熵失败: {e}")
            return 0
    
    def suggest_weight_adjustments(self,
                                  factor_configs: Dict[str, FactorConfig],
                                  performance_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """建议权重调整"""
        try:
            suggestions = []
            
            # 检查权重分布
            current_weights = {fid: fc.weight for fid, fc in factor_configs.items() if fc.is_enabled}
            
            # 1. 检查权重过于集中
            max_weight = max(current_weights.values()) if current_weights else 0
            if max_weight > 0.4:
                suggestions.append({
                    'type': 'concentration_risk',
                    'message': f'权重过于集中，最大权重{max_weight:.1%}',
                    'suggestion': '建议使用分类平衡调整',
                    'priority': 'high'
                })
            
            # 2. 检查权重熵
            entropy = self.calculate_weight_entropy(current_weights)
            if entropy < 0.5:
                suggestions.append({
                    'type': 'low_diversity',
                    'message': f'权重分散度较低，熵值{entropy:.2f}',
                    'suggestion': '建议增加权重多样性',
                    'priority': 'medium'
                })
            
            # 3. 基于绩效的建议
            if performance_data:
                poor_performers = [
                    fid for fid, perf in performance_data.items() 
                    if perf < -0.05  # 绩效低于-5%
                ]
                
                if poor_performers:
                    suggestions.append({
                        'type': 'poor_performance',
                        'message': f'{len(poor_performers)}个因子绩效较差',
                        'suggestion': '建议基于绩效调整权重',
                        'priority': 'high',
                        'factors': poor_performers
                    })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"❌ 生成权重调整建议失败: {e}")
            return []
    
    def get_weight_statistics(self, weights: Dict[str, float]) -> Dict[str, Any]:
        """获取权重统计信息"""
        try:
            weight_values = [w for w in weights.values() if w > 0]
            
            if not weight_values:
                return {}
            
            return {
                'total_factors': len(weights),
                'active_factors': len(weight_values),
                'max_weight': max(weight_values),
                'min_weight': min(weight_values),
                'mean_weight': np.mean(weight_values),
                'std_weight': np.std(weight_values),
                'weight_entropy': self.calculate_weight_entropy(weights),
                'concentration_ratio': max(weight_values) / sum(weight_values) if sum(weight_values) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 获取权重统计失败: {e}")
            return {}
