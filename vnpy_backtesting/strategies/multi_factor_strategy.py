"""
多因子选股策略
基于智能选股引擎的多因子交易策略
"""

import logging
from typing import Dict, Set
from datetime import datetime

from vnpy_backtesting.multi_timeframe_strategy import MultiTimeframeStrategy, BarData, ArrayManager
from vnpy_backtesting.backtesting_engine import Direction

logger = logging.getLogger(__name__)

class MultiFactorStrategy(MultiTimeframeStrategy):
    """多因子选股策略"""
    
    def __init__(self, engine, **params):
        super().__init__(engine, **params)
        
        # 策略特定参数
        self.rebalance_days = params.get('rebalance_days', 20)  # 调仓周期20天
        self.max_stocks = params.get('max_stocks', 10)          # 最大持股数量
        self.min_score = params.get('min_score', 65)            # 最小评分要求
        
        # 策略状态
        self.last_rebalance_date = None
        self.current_stocks: Set[str] = set()
        self.target_stocks: Set[str] = set()
        self.days_since_rebalance = 0
        
        logger.info(f"📊 多因子选股策略初始化完成")
        logger.info(f"  - 调仓周期: {self.rebalance_days}天")
        logger.info(f"  - 最大持股: {self.max_stocks}只")
        logger.info(f"  - 最小评分: {self.min_score}分")
    
    def on_strategy_bar(self, symbol: str, bar: BarData, am: ArrayManager) -> None:
        """策略主逻辑"""
        try:
            # 检查是否需要调仓
            if self._should_rebalance(bar.datetime):
                self._rebalance()
            
            # 检查当前持仓的止损止盈
            self._check_risk_control(symbol, bar, am)
            
        except Exception as e:
            logger.error(f"❌ 策略执行失败: {symbol} - {e}")
    
    def _should_rebalance(self, current_datetime: datetime) -> bool:
        """判断是否需要调仓"""
        if self.last_rebalance_date is None:
            return True
        
        # 计算距离上次调仓的天数
        days_diff = (current_datetime.date() - self.last_rebalance_date).days
        return days_diff >= self.rebalance_days
    
    def _rebalance(self) -> None:
        """执行调仓"""
        try:
            logger.info("🔄 开始执行调仓...")
            
            # 获取当前所有股票的评分
            candidate_symbols = list(self.engine.bars.keys())
            
            # 计算综合评分
            stock_scores = []
            for symbol in candidate_symbols:
                try:
                    # 获取技术面评分
                    am = self.array_managers.get(symbol)
                    if am and am.inited:
                        technical_score = self.get_technical_score(symbol, am)
                        
                        # 获取基本面评分（简化处理）
                        fundamental_score = self._get_fundamental_score(symbol)
                        
                        # 综合评分 (技术面70% + 基本面30%)
                        total_score = technical_score * 0.7 + fundamental_score * 0.3
                        
                        if total_score >= self.min_score:
                            stock_scores.append((symbol, total_score))
                            
                except Exception as e:
                    logger.error(f"❌ 计算股票评分失败: {symbol} - {e}")
            
            # 按评分排序，选择前N只股票
            stock_scores.sort(key=lambda x: x[1], reverse=True)
            self.target_stocks = set([symbol for symbol, score in stock_scores[:self.max_stocks]])
            
            logger.info(f"📊 选中目标股票: {len(self.target_stocks)}只")
            for symbol, score in stock_scores[:self.max_stocks]:
                logger.info(f"  - {symbol}: {score:.2f}分")
            
            # 执行调仓操作
            self._execute_rebalance()
            
            # 更新调仓日期
            self.last_rebalance_date = self.engine.current_datetime.date()
            
        except Exception as e:
            logger.error(f"❌ 调仓执行失败: {e}")
    
    def _get_fundamental_score(self, symbol: str) -> float:
        """获取基本面评分（简化处理）"""
        try:
            # 这里应该调用基本面分析器，简化处理返回固定值
            return 60.0
        except Exception as e:
            logger.error(f"❌ 获取基本面评分失败: {symbol} - {e}")
            return 50.0
    
    def _execute_rebalance(self) -> None:
        """执行具体的调仓操作"""
        try:
            # 卖出不在目标股票中的持仓
            stocks_to_sell = self.current_stocks - self.target_stocks
            for symbol in stocks_to_sell:
                position = self.get_position(symbol)
                if position > 0:
                    logger.info(f"📉 卖出: {symbol} {position}股")
                    self.sell(symbol, position)
            
            # 买入目标股票
            if self.target_stocks:
                # 计算每只股票的目标仓位
                target_weight = 1.0 / len(self.target_stocks)
                total_value = self.engine.get_total_value()
                target_value_per_stock = total_value * target_weight
                
                for symbol in self.target_stocks:
                    current_position = self.get_position(symbol)
                    
                    # 获取当前价格
                    current_bar = self.engine._get_current_bar(symbol)
                    if not current_bar:
                        continue
                    
                    current_price = current_bar.close_price
                    current_value = current_position * current_price
                    
                    # 计算需要调整的数量
                    value_diff = target_value_per_stock - current_value
                    volume_diff = int(value_diff / current_price / 100) * 100  # 按手数调整
                    
                    if volume_diff > 100:  # 需要买入
                        logger.info(f"📈 买入: {symbol} {volume_diff}股 @{current_price:.2f}")
                        self.buy(symbol, volume_diff)
                    elif volume_diff < -100:  # 需要卖出
                        volume_to_sell = min(abs(volume_diff), current_position)
                        if volume_to_sell > 0:
                            logger.info(f"📉 减仓: {symbol} {volume_to_sell}股 @{current_price:.2f}")
                            self.sell(symbol, volume_to_sell)
            
            # 更新当前持股
            self.current_stocks = self.target_stocks.copy()
            
        except Exception as e:
            logger.error(f"❌ 执行调仓操作失败: {e}")
    
    def _check_risk_control(self, symbol: str, bar: BarData, am: ArrayManager) -> None:
        """检查风险控制"""
        try:
            position = self.get_position(symbol)
            if position <= 0:
                return
            
            current_price = bar.close_price
            
            # 检查止损
            if self.check_stop_loss(symbol, current_price):
                logger.warning(f"⚠️ 触发止损: {symbol} @{current_price:.2f}")
                self.sell(symbol, position)
                self.current_stocks.discard(symbol)
                return
            
            # 检查止盈
            if self.check_take_profit(symbol, current_price):
                logger.info(f"💰 触发止盈: {symbol} @{current_price:.2f}")
                self.sell(symbol, position)
                self.current_stocks.discard(symbol)
                return
            
            # 检查技术面恶化
            technical_score = self.get_technical_score(symbol, am)
            if technical_score < self.sell_score_threshold:
                logger.warning(f"📉 技术面恶化: {symbol} 评分{technical_score:.1f}")
                self.sell(symbol, position)
                self.current_stocks.discard(symbol)
                return
                
        except Exception as e:
            logger.error(f"❌ 风险控制检查失败: {symbol} - {e}")
    
    def on_window_strategy_bar(self, symbol: str, bar: BarData) -> None:
        """5日K线策略逻辑"""
        try:
            # 在5日K线上进行趋势确认
            am = self.array_managers.get(symbol)
            if not am or not am.inited:
                return
            
            # 检查5日趋势
            ma5 = am.sma(5)
            ma20 = am.sma(20)
            current_price = bar.close_price
            
            # 如果持有该股票且趋势转弱，考虑减仓
            if symbol in self.current_stocks:
                position = self.get_position(symbol)
                if position > 0 and current_price < ma5 < ma20:
                    # 趋势转弱，减仓50%
                    sell_volume = position // 2
                    if sell_volume >= 100:
                        logger.info(f"📉 趋势转弱减仓: {symbol} {sell_volume}股")
                        self.sell(symbol, sell_volume)
                        
        except Exception as e:
            logger.error(f"❌ 5日K线策略失败: {symbol} - {e}")
    
    def get_strategy_status(self) -> Dict[str, any]:
        """获取策略状态"""
        return {
            'current_stocks': list(self.current_stocks),
            'target_stocks': list(self.target_stocks),
            'last_rebalance_date': self.last_rebalance_date.isoformat() if self.last_rebalance_date else None,
            'days_since_rebalance': self.days_since_rebalance,
            'max_stocks': self.max_stocks,
            'min_score': self.min_score,
            'rebalance_days': self.rebalance_days
        }
