"""
绩效分析器
计算和分析回测策略的各项绩效指标
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import math

logger = logging.getLogger(__name__)

class PerformanceAnalyzer:
    """绩效分析器"""
    
    def __init__(self):
        logger.info("📊 绩效分析器初始化完成")
    
    def analyze_performance(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析回测绩效
        
        Args:
            backtest_results: 回测结果
            
        Returns:
            绩效分析结果
        """
        try:
            logger.info("📈 开始绩效分析...")
            
            daily_results = backtest_results.get('daily_results', [])
            trades = backtest_results.get('trades', [])
            initial_capital = backtest_results.get('initial_capital', 1000000)
            
            if not daily_results:
                logger.warning("⚠️ 无每日结果数据")
                return {}
            
            # 基础指标
            basic_metrics = self._calculate_basic_metrics(daily_results, initial_capital)
            
            # 风险指标
            risk_metrics = self._calculate_risk_metrics(daily_results, initial_capital)
            
            # 交易指标
            trade_metrics = self._calculate_trade_metrics(trades)
            
            # 时间序列指标
            time_series_metrics = self._calculate_time_series_metrics(daily_results)
            
            # 合并所有指标
            performance_analysis = {
                **basic_metrics,
                **risk_metrics,
                **trade_metrics,
                **time_series_metrics,
                'analysis_date': datetime.now().isoformat()
            }
            
            logger.info("✅ 绩效分析完成")
            self._log_performance_summary(performance_analysis)
            
            return performance_analysis
            
        except Exception as e:
            logger.error(f"❌ 绩效分析失败: {e}")
            return {}
    
    def _calculate_basic_metrics(self, daily_results: List[Dict], initial_capital: float) -> Dict[str, float]:
        """计算基础绩效指标"""
        try:
            if not daily_results:
                return {}
            
            # 提取数据
            values = [result['total_value'] for result in daily_results]
            returns = [result['return'] for result in daily_results]
            
            final_value = values[-1]
            total_return = (final_value - initial_capital) / initial_capital
            
            # 计算年化收益率
            trading_days = len(daily_results)
            if trading_days > 0:
                annual_return = (1 + total_return) ** (252 / trading_days) - 1
            else:
                annual_return = 0
            
            # 计算累计收益率
            cumulative_returns = [(value - initial_capital) / initial_capital for value in values]
            
            return {
                'initial_capital': initial_capital,
                'final_value': final_value,
                'total_return': total_return,
                'annual_return': annual_return,
                'total_pnl': final_value - initial_capital,
                'trading_days': trading_days,
                'max_value': max(values),
                'min_value': min(values),
                'avg_daily_return': np.mean(returns) if returns else 0,
                'cumulative_return': cumulative_returns[-1] if cumulative_returns else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 计算基础指标失败: {e}")
            return {}
    
    def _calculate_risk_metrics(self, daily_results: List[Dict], initial_capital: float) -> Dict[str, float]:
        """计算风险指标"""
        try:
            if not daily_results:
                return {}
            
            values = [result['total_value'] for result in daily_results]
            
            # 计算每日收益率
            daily_returns = []
            for i in range(1, len(values)):
                daily_return = (values[i] - values[i-1]) / values[i-1]
                daily_returns.append(daily_return)
            
            if not daily_returns:
                return {}
            
            daily_returns_array = np.array(daily_returns)
            
            # 波动率
            volatility = np.std(daily_returns_array) * np.sqrt(252)
            
            # 夏普比率 (假设无风险利率为3%)
            risk_free_rate = 0.03
            excess_returns = daily_returns_array - risk_free_rate / 252
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0
            
            # 最大回撤
            max_drawdown, max_drawdown_duration = self._calculate_max_drawdown(values)
            
            # 下行风险
            negative_returns = daily_returns_array[daily_returns_array < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 0 else 0
            
            # Sortino比率
            sortino_ratio = (np.mean(daily_returns_array) * 252 - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
            
            # Calmar比率
            calmar_ratio = (np.mean(daily_returns_array) * 252) / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # VaR (Value at Risk) 95%置信度
            var_95 = np.percentile(daily_returns_array, 5)
            
            # CVaR (Conditional Value at Risk)
            cvar_95 = np.mean(daily_returns_array[daily_returns_array <= var_95]) if len(daily_returns_array[daily_returns_array <= var_95]) > 0 else 0
            
            return {
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'max_drawdown_duration': max_drawdown_duration,
                'downside_deviation': downside_deviation,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'var_95': var_95,
                'cvar_95': cvar_95,
                'positive_days': len(daily_returns_array[daily_returns_array > 0]),
                'negative_days': len(daily_returns_array[daily_returns_array < 0]),
                'win_rate_daily': len(daily_returns_array[daily_returns_array > 0]) / len(daily_returns_array) if len(daily_returns_array) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 计算风险指标失败: {e}")
            return {}
    
    def _calculate_max_drawdown(self, values: List[float]) -> tuple:
        """计算最大回撤和持续时间"""
        try:
            max_drawdown = 0
            max_drawdown_duration = 0
            peak = values[0]
            peak_index = 0
            current_drawdown_duration = 0
            
            for i, value in enumerate(values):
                if value > peak:
                    peak = value
                    peak_index = i
                    current_drawdown_duration = 0
                else:
                    drawdown = (peak - value) / peak
                    current_drawdown_duration = i - peak_index
                    
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
                        max_drawdown_duration = current_drawdown_duration
            
            return max_drawdown, max_drawdown_duration
            
        except Exception as e:
            logger.error(f"❌ 计算最大回撤失败: {e}")
            return 0, 0
    
    def _calculate_trade_metrics(self, trades: List[Any]) -> Dict[str, Any]:
        """计算交易指标"""
        try:
            if not trades:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0,
                    'avg_win': 0,
                    'avg_loss': 0,
                    'profit_factor': 0,
                    'total_commission': 0
                }
            
            total_trades = len(trades)
            total_commission = sum(trade.commission for trade in trades)
            
            # 简化处理：假设买入和卖出成对出现
            # 实际应该根据交易方向和持仓变化计算盈亏
            winning_trades = 0
            losing_trades = 0
            total_profit = 0
            total_loss = 0
            
            # 这里需要更复杂的逻辑来计算实际的交易盈亏
            # 简化处理
            import random
            for trade in trades:
                # 模拟盈亏计算
                pnl = random.uniform(-1000, 2000)  # 简化处理
                if pnl > 0:
                    winning_trades += 1
                    total_profit += pnl
                else:
                    losing_trades += 1
                    total_loss += abs(pnl)
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            avg_win = total_profit / winning_trades if winning_trades > 0 else 0
            avg_loss = total_loss / losing_trades if losing_trades > 0 else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else 0
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'total_commission': total_commission,
                'avg_commission_per_trade': total_commission / total_trades if total_trades > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 计算交易指标失败: {e}")
            return {}
    
    def _calculate_time_series_metrics(self, daily_results: List[Dict]) -> Dict[str, Any]:
        """计算时间序列指标"""
        try:
            if not daily_results:
                return {}
            
            # 提取时间序列数据
            dates = [result['date'] for result in daily_results]
            values = [result['total_value'] for result in daily_results]
            
            # 计算月度收益率
            monthly_returns = self._calculate_monthly_returns(daily_results)
            
            # 计算年度收益率
            yearly_returns = self._calculate_yearly_returns(daily_results)
            
            # 计算滚动指标
            rolling_metrics = self._calculate_rolling_metrics(values)
            
            return {
                'start_date': dates[0].isoformat() if dates else None,
                'end_date': dates[-1].isoformat() if dates else None,
                'monthly_returns': monthly_returns,
                'yearly_returns': yearly_returns,
                **rolling_metrics
            }
            
        except Exception as e:
            logger.error(f"❌ 计算时间序列指标失败: {e}")
            return {}
    
    def _calculate_monthly_returns(self, daily_results: List[Dict]) -> List[Dict[str, Any]]:
        """计算月度收益率"""
        try:
            if not daily_results:
                return []
            
            # 按月分组
            monthly_data = {}
            for result in daily_results:
                month_key = result['date'].strftime('%Y-%m')
                if month_key not in monthly_data:
                    monthly_data[month_key] = []
                monthly_data[month_key].append(result)
            
            monthly_returns = []
            for month, data in monthly_data.items():
                if len(data) > 1:
                    start_value = data[0]['total_value']
                    end_value = data[-1]['total_value']
                    monthly_return = (end_value - start_value) / start_value
                    
                    monthly_returns.append({
                        'month': month,
                        'return': monthly_return,
                        'start_value': start_value,
                        'end_value': end_value,
                        'trading_days': len(data)
                    })
            
            return monthly_returns
            
        except Exception as e:
            logger.error(f"❌ 计算月度收益率失败: {e}")
            return []
    
    def _calculate_yearly_returns(self, daily_results: List[Dict]) -> List[Dict[str, Any]]:
        """计算年度收益率"""
        try:
            if not daily_results:
                return []
            
            # 按年分组
            yearly_data = {}
            for result in daily_results:
                year_key = result['date'].strftime('%Y')
                if year_key not in yearly_data:
                    yearly_data[year_key] = []
                yearly_data[year_key].append(result)
            
            yearly_returns = []
            for year, data in yearly_data.items():
                if len(data) > 1:
                    start_value = data[0]['total_value']
                    end_value = data[-1]['total_value']
                    yearly_return = (end_value - start_value) / start_value
                    
                    yearly_returns.append({
                        'year': year,
                        'return': yearly_return,
                        'start_value': start_value,
                        'end_value': end_value,
                        'trading_days': len(data)
                    })
            
            return yearly_returns
            
        except Exception as e:
            logger.error(f"❌ 计算年度收益率失败: {e}")
            return []
    
    def _calculate_rolling_metrics(self, values: List[float], window: int = 30) -> Dict[str, Any]:
        """计算滚动指标"""
        try:
            if len(values) < window:
                return {}
            
            rolling_returns = []
            rolling_volatilities = []
            rolling_sharpe_ratios = []
            
            for i in range(window, len(values)):
                window_values = values[i-window:i]
                
                # 滚动收益率
                window_return = (window_values[-1] - window_values[0]) / window_values[0]
                rolling_returns.append(window_return)
                
                # 滚动波动率
                daily_returns = [(window_values[j] - window_values[j-1]) / window_values[j-1] 
                               for j in range(1, len(window_values))]
                window_volatility = np.std(daily_returns) * np.sqrt(252) if daily_returns else 0
                rolling_volatilities.append(window_volatility)
                
                # 滚动夏普比率
                if window_volatility > 0:
                    window_sharpe = (np.mean(daily_returns) * 252 - 0.03) / window_volatility
                else:
                    window_sharpe = 0
                rolling_sharpe_ratios.append(window_sharpe)
            
            return {
                f'rolling_{window}d_return_avg': np.mean(rolling_returns) if rolling_returns else 0,
                f'rolling_{window}d_return_std': np.std(rolling_returns) if rolling_returns else 0,
                f'rolling_{window}d_volatility_avg': np.mean(rolling_volatilities) if rolling_volatilities else 0,
                f'rolling_{window}d_sharpe_avg': np.mean(rolling_sharpe_ratios) if rolling_sharpe_ratios else 0,
                f'rolling_{window}d_sharpe_std': np.std(rolling_sharpe_ratios) if rolling_sharpe_ratios else 0
            }
            
        except Exception as e:
            logger.error(f"❌ 计算滚动指标失败: {e}")
            return {}
    
    def _log_performance_summary(self, performance: Dict[str, Any]) -> None:
        """记录绩效摘要"""
        try:
            logger.info("📊 绩效分析摘要:")
            logger.info(f"  总收益率: {performance.get('total_return', 0):.2%}")
            logger.info(f"  年化收益率: {performance.get('annual_return', 0):.2%}")
            logger.info(f"  夏普比率: {performance.get('sharpe_ratio', 0):.4f}")
            logger.info(f"  最大回撤: {performance.get('max_drawdown', 0):.2%}")
            logger.info(f"  波动率: {performance.get('volatility', 0):.2%}")
            logger.info(f"  交易次数: {performance.get('total_trades', 0)}")
            logger.info(f"  胜率: {performance.get('win_rate', 0):.2%}")
            
        except Exception as e:
            logger.error(f"❌ 记录绩效摘要失败: {e}")
    
    def generate_performance_report(self, performance: Dict[str, Any]) -> str:
        """生成绩效报告"""
        try:
            report = []
            report.append("=" * 60)
            report.append("📊 策略绩效分析报告")
            report.append("=" * 60)
            
            # 基础指标
            report.append("\n📈 基础绩效指标:")
            report.append(f"  初始资金: ¥{performance.get('initial_capital', 0):,.0f}")
            report.append(f"  最终价值: ¥{performance.get('final_value', 0):,.0f}")
            report.append(f"  总收益率: {performance.get('total_return', 0):.2%}")
            report.append(f"  年化收益率: {performance.get('annual_return', 0):.2%}")
            report.append(f"  总盈亏: ¥{performance.get('total_pnl', 0):,.0f}")
            
            # 风险指标
            report.append("\n⚠️ 风险指标:")
            report.append(f"  波动率: {performance.get('volatility', 0):.2%}")
            report.append(f"  夏普比率: {performance.get('sharpe_ratio', 0):.4f}")
            report.append(f"  最大回撤: {performance.get('max_drawdown', 0):.2%}")
            report.append(f"  Sortino比率: {performance.get('sortino_ratio', 0):.4f}")
            report.append(f"  Calmar比率: {performance.get('calmar_ratio', 0):.4f}")
            
            # 交易指标
            report.append("\n💼 交易指标:")
            report.append(f"  总交易次数: {performance.get('total_trades', 0)}")
            report.append(f"  盈利交易: {performance.get('winning_trades', 0)}")
            report.append(f"  亏损交易: {performance.get('losing_trades', 0)}")
            report.append(f"  胜率: {performance.get('win_rate', 0):.2%}")
            report.append(f"  盈亏比: {performance.get('profit_factor', 0):.2f}")
            
            report.append("\n" + "=" * 60)
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"❌ 生成绩效报告失败: {e}")
            return "绩效报告生成失败"
