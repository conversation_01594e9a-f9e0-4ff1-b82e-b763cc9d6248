"""
风险分析器
分析策略的各种风险指标和风险暴露
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import math

logger = logging.getLogger(__name__)

class RiskAnalyzer:
    """风险分析器"""
    
    def __init__(self):
        # 风险阈值配置
        self.risk_thresholds = {
            'max_drawdown_warning': 0.10,      # 最大回撤预警10%
            'max_drawdown_danger': 0.20,       # 最大回撤危险20%
            'volatility_warning': 0.25,        # 波动率预警25%
            'volatility_danger': 0.40,         # 波动率危险40%
            'var_warning': -0.05,              # VaR预警-5%
            'var_danger': -0.10,               # VaR危险-10%
            'concentration_warning': 0.20,     # 集中度预警20%
            'concentration_danger': 0.30       # 集中度危险30%
        }
        
        logger.info("⚠️ 风险分析器初始化完成")
    
    def analyze_risk(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合风险分析
        
        Args:
            backtest_results: 回测结果
            
        Returns:
            风险分析结果
        """
        try:
            logger.info("⚠️ 开始风险分析...")
            
            daily_results = backtest_results.get('daily_results', [])
            trades = backtest_results.get('trades', [])
            positions = backtest_results.get('positions', {})
            
            if not daily_results:
                logger.warning("⚠️ 无每日结果数据")
                return {}
            
            # 市场风险分析
            market_risk = self._analyze_market_risk(daily_results)
            
            # 流动性风险分析
            liquidity_risk = self._analyze_liquidity_risk(trades, positions)
            
            # 集中度风险分析
            concentration_risk = self._analyze_concentration_risk(positions)
            
            # 操作风险分析
            operational_risk = self._analyze_operational_risk(trades)
            
            # 风险预警
            risk_warnings = self._generate_risk_warnings(
                market_risk, liquidity_risk, concentration_risk, operational_risk
            )
            
            # 风险评级
            risk_rating = self._calculate_risk_rating(
                market_risk, liquidity_risk, concentration_risk, operational_risk
            )
            
            risk_analysis = {
                'market_risk': market_risk,
                'liquidity_risk': liquidity_risk,
                'concentration_risk': concentration_risk,
                'operational_risk': operational_risk,
                'risk_warnings': risk_warnings,
                'risk_rating': risk_rating,
                'analysis_date': datetime.now().isoformat()
            }
            
            logger.info("✅ 风险分析完成")
            self._log_risk_summary(risk_analysis)
            
            return risk_analysis
            
        except Exception as e:
            logger.error(f"❌ 风险分析失败: {e}")
            return {}
    
    def _analyze_market_risk(self, daily_results: List[Dict]) -> Dict[str, Any]:
        """分析市场风险"""
        try:
            if not daily_results:
                return {}
            
            values = [result['total_value'] for result in daily_results]
            
            # 计算每日收益率
            daily_returns = []
            for i in range(1, len(values)):
                daily_return = (values[i] - values[i-1]) / values[i-1]
                daily_returns.append(daily_return)
            
            if not daily_returns:
                return {}
            
            daily_returns_array = np.array(daily_returns)
            
            # VaR计算 (Value at Risk)
            var_95 = np.percentile(daily_returns_array, 5)  # 95%置信度
            var_99 = np.percentile(daily_returns_array, 1)  # 99%置信度
            
            # CVaR计算 (Conditional Value at Risk)
            cvar_95 = np.mean(daily_returns_array[daily_returns_array <= var_95])
            cvar_99 = np.mean(daily_returns_array[daily_returns_array <= var_99])
            
            # 最大回撤
            max_drawdown = self._calculate_max_drawdown_simple(values)
            
            # 波动率
            volatility_daily = np.std(daily_returns_array)
            volatility_annual = volatility_daily * np.sqrt(252)
            
            # 下行风险
            negative_returns = daily_returns_array[daily_returns_array < 0]
            downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0
            
            # Beta计算（简化处理，假设市场收益率）
            market_returns = np.random.normal(0.0008, 0.02, len(daily_returns_array))  # 模拟市场收益
            beta = np.cov(daily_returns_array, market_returns)[0, 1] / np.var(market_returns)
            
            return {
                'var_95': var_95,
                'var_99': var_99,
                'cvar_95': cvar_95,
                'cvar_99': cvar_99,
                'max_drawdown': max_drawdown,
                'volatility_daily': volatility_daily,
                'volatility_annual': volatility_annual,
                'downside_deviation': downside_deviation,
                'beta': beta,
                'tracking_error': volatility_annual,  # 简化处理
                'information_ratio': 0.0  # 简化处理
            }
            
        except Exception as e:
            logger.error(f"❌ 市场风险分析失败: {e}")
            return {}
    
    def _analyze_liquidity_risk(self, trades: List[Any], positions: Dict[str, Any]) -> Dict[str, Any]:
        """分析流动性风险"""
        try:
            if not trades:
                return {
                    'avg_trade_size': 0,
                    'large_trade_ratio': 0,
                    'position_turnover': 0,
                    'liquidity_score': 100  # 无交易时流动性评分为满分
                }
            
            # 计算平均交易规模
            trade_sizes = [trade.volume * trade.price for trade in trades]
            avg_trade_size = np.mean(trade_sizes)
            
            # 大额交易比例（超过平均规模2倍的交易）
            large_trades = [size for size in trade_sizes if size > avg_trade_size * 2]
            large_trade_ratio = len(large_trades) / len(trade_sizes)
            
            # 持仓周转率
            total_trade_value = sum(trade_sizes)
            avg_position_value = sum(pos.volume * pos.price for pos in positions.values()) if positions else 1
            position_turnover = total_trade_value / avg_position_value if avg_position_value > 0 else 0
            
            # 流动性评分 (0-100)
            liquidity_score = 100
            if large_trade_ratio > 0.3:
                liquidity_score -= 20
            if position_turnover > 5:
                liquidity_score -= 15
            if avg_trade_size > 1000000:  # 100万以上
                liquidity_score -= 10
            
            liquidity_score = max(0, liquidity_score)
            
            return {
                'avg_trade_size': avg_trade_size,
                'large_trade_ratio': large_trade_ratio,
                'position_turnover': position_turnover,
                'liquidity_score': liquidity_score,
                'total_trades': len(trades),
                'total_trade_value': total_trade_value
            }
            
        except Exception as e:
            logger.error(f"❌ 流动性风险分析失败: {e}")
            return {}
    
    def _analyze_concentration_risk(self, positions: Dict[str, Any]) -> Dict[str, Any]:
        """分析集中度风险"""
        try:
            if not positions:
                return {
                    'position_count': 0,
                    'max_position_weight': 0,
                    'top3_concentration': 0,
                    'top5_concentration': 0,
                    'herfindahl_index': 0,
                    'concentration_score': 100
                }
            
            # 计算各持仓权重
            total_value = sum(abs(pos.volume * pos.price) for pos in positions.values())
            
            if total_value == 0:
                return {
                    'position_count': len(positions),
                    'max_position_weight': 0,
                    'top3_concentration': 0,
                    'top5_concentration': 0,
                    'herfindahl_index': 0,
                    'concentration_score': 100
                }
            
            position_weights = []
            for pos in positions.values():
                weight = abs(pos.volume * pos.price) / total_value
                position_weights.append(weight)
            
            position_weights.sort(reverse=True)
            
            # 最大单一持仓权重
            max_position_weight = position_weights[0] if position_weights else 0
            
            # 前3大持仓集中度
            top3_concentration = sum(position_weights[:3])
            
            # 前5大持仓集中度
            top5_concentration = sum(position_weights[:5])
            
            # Herfindahl指数（集中度指数）
            herfindahl_index = sum(w**2 for w in position_weights)
            
            # 集中度评分 (0-100)
            concentration_score = 100
            if max_position_weight > 0.3:
                concentration_score -= 30
            elif max_position_weight > 0.2:
                concentration_score -= 15
            
            if top3_concentration > 0.6:
                concentration_score -= 20
            elif top3_concentration > 0.5:
                concentration_score -= 10
            
            if herfindahl_index > 0.2:
                concentration_score -= 15
            
            concentration_score = max(0, concentration_score)
            
            return {
                'position_count': len(positions),
                'max_position_weight': max_position_weight,
                'top3_concentration': top3_concentration,
                'top5_concentration': top5_concentration,
                'herfindahl_index': herfindahl_index,
                'concentration_score': concentration_score
            }
            
        except Exception as e:
            logger.error(f"❌ 集中度风险分析失败: {e}")
            return {}
    
    def _analyze_operational_risk(self, trades: List[Any]) -> Dict[str, Any]:
        """分析操作风险"""
        try:
            if not trades:
                return {
                    'trade_frequency': 0,
                    'avg_holding_period': 0,
                    'error_rate': 0,
                    'operational_score': 100
                }
            
            # 交易频率
            if len(trades) > 1:
                first_trade_time = trades[0].datetime
                last_trade_time = trades[-1].datetime
                time_span = (last_trade_time - first_trade_time).days
                trade_frequency = len(trades) / max(time_span, 1)
            else:
                trade_frequency = 0
            
            # 平均持仓周期（简化计算）
            avg_holding_period = 30  # 简化处理，假设平均持仓30天
            
            # 错误率（简化处理）
            error_rate = 0.01  # 假设1%的错误率
            
            # 操作风险评分
            operational_score = 100
            if trade_frequency > 10:  # 每天超过10次交易
                operational_score -= 20
            if avg_holding_period < 1:  # 持仓时间过短
                operational_score -= 15
            if error_rate > 0.05:  # 错误率超过5%
                operational_score -= 25
            
            operational_score = max(0, operational_score)
            
            return {
                'trade_frequency': trade_frequency,
                'avg_holding_period': avg_holding_period,
                'error_rate': error_rate,
                'operational_score': operational_score,
                'total_trades': len(trades)
            }
            
        except Exception as e:
            logger.error(f"❌ 操作风险分析失败: {e}")
            return {}
    
    def _calculate_max_drawdown_simple(self, values: List[float]) -> float:
        """简单计算最大回撤"""
        try:
            max_drawdown = 0
            peak = values[0]
            
            for value in values:
                if value > peak:
                    peak = value
                else:
                    drawdown = (peak - value) / peak
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
            
            return max_drawdown
            
        except Exception as e:
            logger.error(f"❌ 计算最大回撤失败: {e}")
            return 0
    
    def _generate_risk_warnings(self, 
                              market_risk: Dict[str, Any],
                              liquidity_risk: Dict[str, Any],
                              concentration_risk: Dict[str, Any],
                              operational_risk: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成风险预警"""
        warnings = []
        
        try:
            # 市场风险预警
            max_drawdown = market_risk.get('max_drawdown', 0)
            if max_drawdown > self.risk_thresholds['max_drawdown_danger']:
                warnings.append({
                    'type': 'market_risk',
                    'level': 'danger',
                    'message': f'最大回撤过高: {max_drawdown:.2%}',
                    'suggestion': '考虑降低仓位或调整策略参数'
                })
            elif max_drawdown > self.risk_thresholds['max_drawdown_warning']:
                warnings.append({
                    'type': 'market_risk',
                    'level': 'warning',
                    'message': f'最大回撤较高: {max_drawdown:.2%}',
                    'suggestion': '密切关注回撤控制'
                })
            
            # 波动率预警
            volatility = market_risk.get('volatility_annual', 0)
            if volatility > self.risk_thresholds['volatility_danger']:
                warnings.append({
                    'type': 'market_risk',
                    'level': 'danger',
                    'message': f'波动率过高: {volatility:.2%}',
                    'suggestion': '考虑降低风险暴露'
                })
            elif volatility > self.risk_thresholds['volatility_warning']:
                warnings.append({
                    'type': 'market_risk',
                    'level': 'warning',
                    'message': f'波动率较高: {volatility:.2%}',
                    'suggestion': '注意风险控制'
                })
            
            # 集中度风险预警
            max_position_weight = concentration_risk.get('max_position_weight', 0)
            if max_position_weight > self.risk_thresholds['concentration_danger']:
                warnings.append({
                    'type': 'concentration_risk',
                    'level': 'danger',
                    'message': f'单一持仓过于集中: {max_position_weight:.2%}',
                    'suggestion': '分散投资，降低单一持仓权重'
                })
            elif max_position_weight > self.risk_thresholds['concentration_warning']:
                warnings.append({
                    'type': 'concentration_risk',
                    'level': 'warning',
                    'message': f'单一持仓集中度较高: {max_position_weight:.2%}',
                    'suggestion': '考虑适当分散投资'
                })
            
            # 流动性风险预警
            liquidity_score = liquidity_risk.get('liquidity_score', 100)
            if liquidity_score < 60:
                warnings.append({
                    'type': 'liquidity_risk',
                    'level': 'warning',
                    'message': f'流动性评分较低: {liquidity_score}',
                    'suggestion': '注意交易规模和频率'
                })
            
        except Exception as e:
            logger.error(f"❌ 生成风险预警失败: {e}")
        
        return warnings
    
    def _calculate_risk_rating(self,
                             market_risk: Dict[str, Any],
                             liquidity_risk: Dict[str, Any],
                             concentration_risk: Dict[str, Any],
                             operational_risk: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合风险评级"""
        try:
            # 各项风险评分 (0-100, 100为最低风险)
            market_score = 100
            max_drawdown = market_risk.get('max_drawdown', 0)
            volatility = market_risk.get('volatility_annual', 0)
            
            if max_drawdown > 0.2:
                market_score -= 40
            elif max_drawdown > 0.1:
                market_score -= 20
            
            if volatility > 0.4:
                market_score -= 30
            elif volatility > 0.25:
                market_score -= 15
            
            market_score = max(0, market_score)
            
            # 流动性评分
            liquidity_score = liquidity_risk.get('liquidity_score', 100)
            
            # 集中度评分
            concentration_score = concentration_risk.get('concentration_score', 100)
            
            # 操作风险评分
            operational_score = operational_risk.get('operational_score', 100)
            
            # 综合评分 (加权平均)
            overall_score = (
                market_score * 0.4 +
                liquidity_score * 0.2 +
                concentration_score * 0.3 +
                operational_score * 0.1
            )
            
            # 风险等级
            if overall_score >= 80:
                risk_level = 'LOW'
                risk_description = '低风险'
            elif overall_score >= 60:
                risk_level = 'MEDIUM'
                risk_description = '中等风险'
            elif overall_score >= 40:
                risk_level = 'HIGH'
                risk_description = '高风险'
            else:
                risk_level = 'VERY_HIGH'
                risk_description = '极高风险'
            
            return {
                'overall_score': overall_score,
                'risk_level': risk_level,
                'risk_description': risk_description,
                'market_score': market_score,
                'liquidity_score': liquidity_score,
                'concentration_score': concentration_score,
                'operational_score': operational_score
            }
            
        except Exception as e:
            logger.error(f"❌ 计算风险评级失败: {e}")
            return {
                'overall_score': 50,
                'risk_level': 'MEDIUM',
                'risk_description': '中等风险'
            }
    
    def _log_risk_summary(self, risk_analysis: Dict[str, Any]) -> None:
        """记录风险分析摘要"""
        try:
            risk_rating = risk_analysis.get('risk_rating', {})
            warnings = risk_analysis.get('risk_warnings', [])
            
            logger.info("⚠️ 风险分析摘要:")
            logger.info(f"  综合风险评级: {risk_rating.get('risk_description', '未知')}")
            logger.info(f"  风险评分: {risk_rating.get('overall_score', 0):.1f}/100")
            logger.info(f"  风险预警数量: {len(warnings)}")
            
            for warning in warnings[:3]:  # 只显示前3个预警
                logger.warning(f"  ⚠️ {warning.get('message', '')}")
                
        except Exception as e:
            logger.error(f"❌ 记录风险摘要失败: {e}")
