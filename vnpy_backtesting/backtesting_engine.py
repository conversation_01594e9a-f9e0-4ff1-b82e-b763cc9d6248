"""
VeighNa回测引擎
基于VeighNa的专业回测引擎实现
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Callable
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, DailyMarket, Minute1Market, Minute5Market
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector

logger = logging.getLogger(__name__)

class OrderType(Enum):
    """订单类型"""
    MARKET = "MARKET"  # 市价单
    LIMIT = "LIMIT"    # 限价单

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "PENDING"      # 待成交
    FILLED = "FILLED"        # 已成交
    CANCELLED = "CANCELLED"  # 已取消
    REJECTED = "REJECTED"    # 已拒绝

class Direction(Enum):
    """交易方向"""
    LONG = "LONG"   # 买入
    SHORT = "SHORT" # 卖出

@dataclass
class BarData:
    """K线数据"""
    symbol: str
    datetime: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    amount: float = 0.0

@dataclass
class OrderData:
    """订单数据"""
    order_id: str
    symbol: str
    direction: Direction
    order_type: OrderType
    volume: int
    price: float
    status: OrderStatus
    datetime: datetime
    filled_volume: int = 0
    filled_price: float = 0.0

@dataclass
class TradeData:
    """成交数据"""
    trade_id: str
    order_id: str
    symbol: str
    direction: Direction
    volume: int
    price: float
    datetime: datetime
    commission: float = 0.0

@dataclass
class PositionData:
    """持仓数据"""
    symbol: str
    volume: int
    price: float
    datetime: datetime
    pnl: float = 0.0

class BacktestingEngine:
    """VeighNa回测引擎"""
    
    def __init__(self):
        # 回测配置
        self.start_date: Optional[date] = None
        self.end_date: Optional[date] = None
        self.initial_capital: float = 1000000.0  # 初始资金100万
        self.commission_rate: float = 0.0003     # 手续费率0.03%
        self.slippage: float = 0.001             # 滑点0.1%
        
        # 回测状态
        self.current_datetime: Optional[datetime] = None
        self.current_capital: float = 0.0
        self.total_commission: float = 0.0
        
        # 数据存储
        self.bars: Dict[str, List[BarData]] = {}
        self.orders: Dict[str, OrderData] = {}
        self.trades: List[TradeData] = []
        self.positions: Dict[str, PositionData] = {}
        self.daily_results: List[Dict[str, Any]] = []
        
        # 策略相关
        self.strategy: Optional[Any] = None
        self.strategy_params: Dict[str, Any] = {}
        
        # 选股器
        self.stock_selector = MultiFactorSelector()
        
        logger.info("🚀 VeighNa回测引擎初始化完成")
    
    def set_parameters(self,
                      start_date: date,
                      end_date: date,
                      initial_capital: float = 1000000.0,
                      commission_rate: float = 0.0003,
                      slippage: float = 0.001) -> None:
        """
        设置回测参数
        
        Args:
            start_date: 回测开始日期
            end_date: 回测结束日期
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点
        """
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.current_capital = initial_capital
        
        logger.info(f"📊 回测参数设置: {start_date} - {end_date}, 初始资金: ¥{initial_capital:,.0f}")
    
    def add_strategy(self, strategy_class: type, strategy_params: Dict[str, Any] = None) -> None:
        """
        添加策略
        
        Args:
            strategy_class: 策略类
            strategy_params: 策略参数
        """
        try:
            self.strategy_params = strategy_params or {}
            self.strategy = strategy_class(self, **self.strategy_params)
            
            logger.info(f"✅ 策略添加成功: {strategy_class.__name__}")
            
        except Exception as e:
            logger.error(f"❌ 添加策略失败: {e}")
            raise
    
    def load_data(self, symbols: List[str], timeframe: str = "1d") -> bool:
        """
        加载回测数据

        Args:
            symbols: 股票代码列表
            timeframe: 时间周期 (1d, 1h, 5m, 1m)

        Returns:
            是否加载成功
        """
        try:
            logger.info(f"📊 开始加载回测数据: {len(symbols)}只股票, 周期: {timeframe}")

            # 先尝试从数据库加载
            success = self._load_data_from_database(symbols, timeframe)

            # 如果数据库没有数据，使用模拟数据
            if not success or len(self.bars) == 0:
                logger.info("📊 数据库无数据，使用模拟数据进行测试...")
                success = self._load_simulated_data(symbols)

            logger.info(f"✅ 数据加载完成: {len(self.bars)}只股票")
            return success

        except Exception as e:
            logger.error(f"❌ 加载回测数据失败: {e}")
            return False

    def _load_data_from_database(self, symbols: List[str], timeframe: str) -> bool:
        """从数据库加载数据"""
        try:
            # 根据时间周期选择数据表
            model_map = {
                "1d": DailyMarket,
                "1h": DailyMarket,  # 暂时使用日线数据
                "5m": Minute5Market,
                "1m": Minute1Market
            }

            model_class = model_map.get(timeframe, DailyMarket)

            with db_manager.get_session() as session:
                for symbol in symbols:
                    # 查询数据
                    query = session.query(model_class).filter(
                        model_class.symbol == symbol,
                        model_class.trade_date >= self.start_date,
                        model_class.trade_date <= self.end_date
                    ).order_by(model_class.trade_date.asc())

                    records = query.all()

                    if not records:
                        logger.debug(f"⚠️ 数据库无数据: {symbol}")
                        continue

                    # 转换为BarData
                    bars = []
                    for record in records:
                        bar = BarData(
                            symbol=record.symbol,
                            datetime=datetime.combine(record.trade_date, datetime.min.time()),
                            open_price=float(record.open_price or 0),
                            high_price=float(record.high_price or 0),
                            low_price=float(record.low_price or 0),
                            close_price=float(record.close_price or 0),
                            volume=float(record.volume or 0),
                            amount=float(record.amount or 0)
                        )
                        bars.append(bar)

                    self.bars[symbol] = bars
                    logger.debug(f"✅ 加载数据: {symbol} {len(bars)}条")

            return len(self.bars) > 0

        except Exception as e:
            logger.error(f"❌ 从数据库加载数据失败: {e}")
            return False

    def _load_simulated_data(self, symbols: List[str]) -> bool:
        """加载模拟数据用于测试"""
        try:
            import pandas as pd
            import numpy as np

            # 生成日期序列
            date_range = pd.date_range(start=self.start_date, end=self.end_date, freq='D')
            # 只保留工作日
            trading_days = [d for d in date_range if d.weekday() < 5]

            for symbol in symbols:
                bars = []
                base_price = 10.0 + hash(symbol) % 20  # 基于股票代码生成基础价格

                for i, trade_date in enumerate(trading_days):
                    # 生成随机价格走势
                    np.random.seed(hash(symbol + str(i)) % 2**32)  # 确保可重复

                    # 价格随机游走
                    price_change = np.random.normal(0, 0.02)  # 2%的日波动
                    current_price = base_price * (1 + price_change)

                    # 生成OHLC数据
                    open_price = base_price
                    high_price = current_price * (1 + abs(np.random.normal(0, 0.01)))
                    low_price = current_price * (1 - abs(np.random.normal(0, 0.01)))
                    close_price = current_price

                    # 确保价格逻辑正确
                    high_price = max(high_price, open_price, close_price)
                    low_price = min(low_price, open_price, close_price)

                    # 生成成交量
                    volume = np.random.randint(1000000, 10000000)
                    amount = volume * close_price

                    bar = BarData(
                        symbol=symbol,
                        datetime=datetime.combine(trade_date.date(), datetime.min.time()),
                        open_price=open_price,
                        high_price=high_price,
                        low_price=low_price,
                        close_price=close_price,
                        volume=volume,
                        amount=amount
                    )
                    bars.append(bar)

                    # 更新基础价格
                    base_price = close_price

                self.bars[symbol] = bars
                logger.debug(f"✅ 生成模拟数据: {symbol} {len(bars)}条")

            return True

        except Exception as e:
            logger.error(f"❌ 生成模拟数据失败: {e}")
            return False
    
    def send_order(self,
                  symbol: str,
                  direction: Direction,
                  volume: int,
                  order_type: OrderType = OrderType.MARKET,
                  price: float = 0.0) -> str:
        """
        发送订单
        
        Args:
            symbol: 股票代码
            direction: 交易方向
            volume: 交易数量
            order_type: 订单类型
            price: 价格（限价单使用）
            
        Returns:
            订单ID
        """
        try:
            order_id = f"{symbol}_{direction.value}_{self.current_datetime.strftime('%Y%m%d_%H%M%S')}"
            
            # 获取当前价格
            current_bar = self._get_current_bar(symbol)
            if not current_bar:
                logger.warning(f"⚠️ 无法获取当前价格: {symbol}")
                return ""
            
            # 计算成交价格
            if order_type == OrderType.MARKET:
                if direction == Direction.LONG:
                    fill_price = current_bar.close_price * (1 + self.slippage)
                else:
                    fill_price = current_bar.close_price * (1 - self.slippage)
            else:
                fill_price = price
            
            # 创建订单
            order = OrderData(
                order_id=order_id,
                symbol=symbol,
                direction=direction,
                order_type=order_type,
                volume=volume,
                price=fill_price,
                status=OrderStatus.FILLED,  # 简化处理，直接成交
                datetime=self.current_datetime,
                filled_volume=volume,
                filled_price=fill_price
            )
            
            self.orders[order_id] = order
            
            # 生成成交记录
            self._generate_trade(order)
            
            # 更新持仓
            self._update_position(order)
            
            logger.debug(f"📋 订单成交: {symbol} {direction.value} {volume}股 @{fill_price:.2f}")
            
            return order_id
            
        except Exception as e:
            logger.error(f"❌ 发送订单失败: {symbol} - {e}")
            return ""
    
    def _get_current_bar(self, symbol: str) -> Optional[BarData]:
        """获取当前K线数据"""
        if symbol not in self.bars:
            return None
        
        bars = self.bars[symbol]
        for bar in bars:
            if bar.datetime.date() == self.current_datetime.date():
                return bar
        
        return None
    
    def _generate_trade(self, order: OrderData) -> None:
        """生成成交记录"""
        trade_id = f"trade_{len(self.trades) + 1}"
        
        # 计算手续费
        commission = order.filled_volume * order.filled_price * self.commission_rate
        
        trade = TradeData(
            trade_id=trade_id,
            order_id=order.order_id,
            symbol=order.symbol,
            direction=order.direction,
            volume=order.filled_volume,
            price=order.filled_price,
            datetime=order.datetime,
            commission=commission
        )
        
        self.trades.append(trade)
        self.total_commission += commission
        
        # 更新资金
        if order.direction == Direction.LONG:
            self.current_capital -= (order.filled_volume * order.filled_price + commission)
        else:
            self.current_capital += (order.filled_volume * order.filled_price - commission)
    
    def _update_position(self, order: OrderData) -> None:
        """更新持仓"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = PositionData(
                symbol=symbol,
                volume=0,
                price=0.0,
                datetime=order.datetime
            )
        
        position = self.positions[symbol]
        
        if order.direction == Direction.LONG:
            # 买入
            if position.volume >= 0:
                # 增加多头持仓
                total_cost = position.volume * position.price + order.filled_volume * order.filled_price
                total_volume = position.volume + order.filled_volume
                position.price = total_cost / total_volume if total_volume > 0 else 0
                position.volume = total_volume
            else:
                # 减少空头持仓
                position.volume += order.filled_volume
        else:
            # 卖出
            if position.volume > 0:
                # 减少多头持仓
                position.volume -= order.filled_volume
            else:
                # 增加空头持仓
                total_cost = abs(position.volume) * position.price + order.filled_volume * order.filled_price
                total_volume = abs(position.volume) + order.filled_volume
                position.price = total_cost / total_volume if total_volume > 0 else 0
                position.volume = -total_volume
        
        position.datetime = order.datetime
        
        # 如果持仓为0，移除持仓记录
        if position.volume == 0:
            del self.positions[symbol]
    
    def get_position(self, symbol: str) -> Optional[PositionData]:
        """获取持仓信息"""
        return self.positions.get(symbol)
    
    def get_capital(self) -> float:
        """获取当前资金"""
        return self.current_capital
    
    def get_total_value(self) -> float:
        """获取总资产价值"""
        total_value = self.current_capital
        
        # 加上持仓市值
        for symbol, position in self.positions.items():
            current_bar = self._get_current_bar(symbol)
            if current_bar:
                position_value = position.volume * current_bar.close_price
                total_value += position_value
        
        return total_value
    
    def run_backtesting(self) -> bool:
        """
        运行回测
        
        Returns:
            是否运行成功
        """
        try:
            if not self.strategy:
                logger.error("❌ 未设置策略")
                return False
            
            if not self.bars:
                logger.error("❌ 未加载数据")
                return False
            
            logger.info("🚀 开始运行回测...")
            
            # 获取所有交易日期
            all_dates = set()
            for bars in self.bars.values():
                for bar in bars:
                    all_dates.add(bar.datetime.date())
            
            sorted_dates = sorted(all_dates)
            
            # 策略初始化
            if hasattr(self.strategy, 'on_init'):
                self.strategy.on_init()
            
            # 逐日回测
            for trade_date in sorted_dates:
                if trade_date < self.start_date or trade_date > self.end_date:
                    continue
                
                self.current_datetime = datetime.combine(trade_date, datetime.min.time())
                
                # 更新持仓盈亏
                self._update_position_pnl()
                
                # 策略处理
                if hasattr(self.strategy, 'on_bar'):
                    # 为每只股票调用策略
                    for symbol in self.bars.keys():
                        current_bar = self._get_current_bar(symbol)
                        if current_bar:
                            self.strategy.on_bar(current_bar)
                
                # 记录每日结果
                self._record_daily_result()
            
            # 策略结束
            if hasattr(self.strategy, 'on_stop'):
                self.strategy.on_stop()
            
            logger.info("✅ 回测运行完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 回测运行失败: {e}")
            return False
    
    def _update_position_pnl(self) -> None:
        """更新持仓盈亏"""
        for symbol, position in self.positions.items():
            current_bar = self._get_current_bar(symbol)
            if current_bar:
                if position.volume > 0:
                    # 多头盈亏
                    position.pnl = position.volume * (current_bar.close_price - position.price)
                elif position.volume < 0:
                    # 空头盈亏
                    position.pnl = abs(position.volume) * (position.price - current_bar.close_price)
    
    def _record_daily_result(self) -> None:
        """记录每日结果"""
        total_value = self.get_total_value()
        
        daily_result = {
            'date': self.current_datetime.date(),
            'capital': self.current_capital,
            'total_value': total_value,
            'pnl': total_value - self.initial_capital,
            'return': (total_value - self.initial_capital) / self.initial_capital,
            'positions': len(self.positions),
            'trades': len(self.trades)
        }
        
        self.daily_results.append(daily_result)
    
    def get_results(self) -> Dict[str, Any]:
        """
        获取回测结果
        
        Returns:
            回测结果字典
        """
        if not self.daily_results:
            return {}
        
        # 基本统计
        final_value = self.daily_results[-1]['total_value']
        total_return = (final_value - self.initial_capital) / self.initial_capital
        
        # 计算每日收益率
        daily_returns = []
        for i in range(1, len(self.daily_results)):
            prev_value = self.daily_results[i-1]['total_value']
            curr_value = self.daily_results[i]['total_value']
            daily_return = (curr_value - prev_value) / prev_value
            daily_returns.append(daily_return)
        
        # 计算统计指标
        if daily_returns:
            daily_returns_array = np.array(daily_returns)
            
            # 年化收益率
            trading_days = len(daily_returns)
            annual_return = (1 + total_return) ** (252 / trading_days) - 1
            
            # 波动率
            volatility = np.std(daily_returns_array) * np.sqrt(252)
            
            # 夏普比率
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            peak = self.initial_capital
            max_drawdown = 0
            for result in self.daily_results:
                if result['total_value'] > peak:
                    peak = result['total_value']
                drawdown = (peak - result['total_value']) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
        else:
            annual_return = 0
            volatility = 0
            sharpe_ratio = 0
            max_drawdown = 0
        
        # 交易统计
        winning_trades = 0
        losing_trades = 0
        total_profit = 0
        total_loss = 0
        
        for trade in self.trades:
            # 简化处理，假设所有交易都已平仓
            if trade.direction == Direction.LONG:
                # 买入交易，需要找到对应的卖出交易来计算盈亏
                pass
            else:
                # 卖出交易
                pass
        
        # 胜率（简化计算）
        total_trades = len(self.trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        results = {
            'start_date': self.start_date,
            'end_date': self.end_date,
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_commission': self.total_commission,
            'daily_results': self.daily_results,
            'trades': self.trades,
            'positions': self.positions
        }
        
        return results
