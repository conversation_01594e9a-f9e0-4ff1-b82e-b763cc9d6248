"""
参数优化器
支持遗传算法和网格搜索的策略参数优化
"""

import logging
import random
import numpy as np
from typing import Dict, List, Tuple, Any, Callable
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, as_completed
import itertools

from vnpy_backtesting.backtesting_engine import BacktestingEngine

logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """优化结果"""
    parameters: Dict[str, Any]
    target_value: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        self.optimization_results: List[OptimizationResult] = []
        
        logger.info("🔧 参数优化器初始化完成")
    
    def grid_search_optimization(self,
                                strategy_class: type,
                                parameter_ranges: Dict[str, List[Any]],
                                target_function: Callable = None,
                                engine_config: Dict[str, Any] = None,
                                max_workers: int = 4) -> List[OptimizationResult]:
        """
        网格搜索优化
        
        Args:
            strategy_class: 策略类
            parameter_ranges: 参数范围字典
            target_function: 目标函数，默认使用夏普比率
            engine_config: 回测引擎配置
            max_workers: 最大并行工作进程数
            
        Returns:
            优化结果列表
        """
        try:
            logger.info("🔍 开始网格搜索优化...")
            logger.info(f"📊 参数范围: {parameter_ranges}")
            
            # 生成所有参数组合
            param_names = list(parameter_ranges.keys())
            param_values = list(parameter_ranges.values())
            param_combinations = list(itertools.product(*param_values))
            
            logger.info(f"🎯 总共需要测试 {len(param_combinations)} 个参数组合")
            
            # 默认目标函数
            if target_function is None:
                target_function = lambda results: results.get('sharpe_ratio', 0)
            
            # 并行执行回测
            results = []
            
            # 分批处理，避免内存占用过大
            batch_size = min(50, len(param_combinations))
            
            for i in range(0, len(param_combinations), batch_size):
                batch_combinations = param_combinations[i:i + batch_size]
                
                logger.info(f"📈 处理批次 {i//batch_size + 1}/{(len(param_combinations)-1)//batch_size + 1}")
                
                with ProcessPoolExecutor(max_workers=max_workers) as executor:
                    # 提交任务
                    future_to_params = {}
                    for param_values in batch_combinations:
                        param_dict = dict(zip(param_names, param_values))
                        
                        future = executor.submit(
                            self._run_single_backtest,
                            strategy_class,
                            param_dict,
                            engine_config or {}
                        )
                        future_to_params[future] = param_dict
                    
                    # 收集结果
                    for future in as_completed(future_to_params):
                        param_dict = future_to_params[future]
                        try:
                            backtest_results = future.result()
                            if backtest_results:
                                target_value = target_function(backtest_results)
                                
                                result = OptimizationResult(
                                    parameters=param_dict,
                                    target_value=target_value,
                                    total_return=backtest_results.get('total_return', 0),
                                    sharpe_ratio=backtest_results.get('sharpe_ratio', 0),
                                    max_drawdown=backtest_results.get('max_drawdown', 0),
                                    win_rate=backtest_results.get('win_rate', 0)
                                )
                                results.append(result)
                                
                        except Exception as e:
                            logger.error(f"❌ 参数组合失败: {param_dict} - {e}")
            
            # 按目标值排序
            results.sort(key=lambda x: x.target_value, reverse=True)
            
            self.optimization_results = results
            
            logger.info(f"✅ 网格搜索优化完成，共测试 {len(results)} 个有效组合")
            
            # 显示最佳结果
            if results:
                best_result = results[0]
                logger.info(f"🏆 最佳参数组合:")
                logger.info(f"  参数: {best_result.parameters}")
                logger.info(f"  目标值: {best_result.target_value:.4f}")
                logger.info(f"  总收益率: {best_result.total_return:.2%}")
                logger.info(f"  夏普比率: {best_result.sharpe_ratio:.4f}")
                logger.info(f"  最大回撤: {best_result.max_drawdown:.2%}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 网格搜索优化失败: {e}")
            return []
    
    def genetic_algorithm_optimization(self,
                                     strategy_class: type,
                                     parameter_ranges: Dict[str, Tuple[Any, Any]],
                                     target_function: Callable = None,
                                     engine_config: Dict[str, Any] = None,
                                     population_size: int = 50,
                                     generations: int = 20,
                                     mutation_rate: float = 0.1,
                                     crossover_rate: float = 0.8) -> List[OptimizationResult]:
        """
        遗传算法优化
        
        Args:
            strategy_class: 策略类
            parameter_ranges: 参数范围字典 (参数名: (最小值, 最大值))
            target_function: 目标函数
            engine_config: 回测引擎配置
            population_size: 种群大小
            generations: 进化代数
            mutation_rate: 变异率
            crossover_rate: 交叉率
            
        Returns:
            优化结果列表
        """
        try:
            logger.info("🧬 开始遗传算法优化...")
            logger.info(f"📊 种群大小: {population_size}, 进化代数: {generations}")
            
            # 默认目标函数
            if target_function is None:
                target_function = lambda results: results.get('sharpe_ratio', 0)
            
            # 初始化种群
            population = self._initialize_population(parameter_ranges, population_size)
            
            best_results = []
            
            for generation in range(generations):
                logger.info(f"🔄 第 {generation + 1}/{generations} 代进化...")
                
                # 评估种群
                fitness_scores = []
                for individual in population:
                    try:
                        backtest_results = self._run_single_backtest(
                            strategy_class, individual, engine_config or {}
                        )
                        
                        if backtest_results:
                            fitness = target_function(backtest_results)
                            fitness_scores.append((individual, fitness, backtest_results))
                        else:
                            fitness_scores.append((individual, -999, {}))
                            
                    except Exception as e:
                        logger.error(f"❌ 个体评估失败: {individual} - {e}")
                        fitness_scores.append((individual, -999, {}))
                
                # 按适应度排序
                fitness_scores.sort(key=lambda x: x[1], reverse=True)
                
                # 记录最佳结果
                best_individual, best_fitness, best_backtest = fitness_scores[0]
                
                result = OptimizationResult(
                    parameters=best_individual,
                    target_value=best_fitness,
                    total_return=best_backtest.get('total_return', 0),
                    sharpe_ratio=best_backtest.get('sharpe_ratio', 0),
                    max_drawdown=best_backtest.get('max_drawdown', 0),
                    win_rate=best_backtest.get('win_rate', 0)
                )
                best_results.append(result)
                
                logger.info(f"  最佳适应度: {best_fitness:.4f}")
                
                # 选择、交叉、变异
                if generation < generations - 1:
                    population = self._evolve_population(
                        fitness_scores, parameter_ranges, 
                        crossover_rate, mutation_rate
                    )
            
            # 按目标值排序所有结果
            best_results.sort(key=lambda x: x.target_value, reverse=True)
            
            self.optimization_results = best_results
            
            logger.info("✅ 遗传算法优化完成")
            
            # 显示最佳结果
            if best_results:
                best_result = best_results[0]
                logger.info(f"🏆 最佳参数组合:")
                logger.info(f"  参数: {best_result.parameters}")
                logger.info(f"  目标值: {best_result.target_value:.4f}")
                logger.info(f"  总收益率: {best_result.total_return:.2%}")
                logger.info(f"  夏普比率: {best_result.sharpe_ratio:.4f}")
                logger.info(f"  最大回撤: {best_result.max_drawdown:.2%}")
            
            return best_results
            
        except Exception as e:
            logger.error(f"❌ 遗传算法优化失败: {e}")
            return []
    
    def _run_single_backtest(self, 
                           strategy_class: type, 
                           parameters: Dict[str, Any],
                           engine_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行单次回测"""
        try:
            # 创建回测引擎
            engine = BacktestingEngine()
            
            # 设置回测参数
            engine.set_parameters(**engine_config)
            
            # 添加策略
            engine.add_strategy(strategy_class, parameters)
            
            # 加载数据（简化处理）
            symbols = engine_config.get('symbols', ['000001', '000002'])
            if not engine.load_data(symbols):
                return {}
            
            # 运行回测
            if not engine.run_backtesting():
                return {}
            
            # 获取结果
            results = engine.get_results()
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 单次回测失败: {parameters} - {e}")
            return {}
    
    def _initialize_population(self, 
                             parameter_ranges: Dict[str, Tuple[Any, Any]], 
                             population_size: int) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        
        for _ in range(population_size):
            individual = {}
            for param_name, (min_val, max_val) in parameter_ranges.items():
                if isinstance(min_val, int) and isinstance(max_val, int):
                    individual[param_name] = random.randint(min_val, max_val)
                elif isinstance(min_val, float) or isinstance(max_val, float):
                    individual[param_name] = random.uniform(min_val, max_val)
                else:
                    # 对于其他类型，随机选择
                    individual[param_name] = random.choice([min_val, max_val])
            
            population.append(individual)
        
        return population
    
    def _evolve_population(self,
                         fitness_scores: List[Tuple[Dict[str, Any], float, Dict[str, Any]]],
                         parameter_ranges: Dict[str, Tuple[Any, Any]],
                         crossover_rate: float,
                         mutation_rate: float) -> List[Dict[str, Any]]:
        """进化种群"""
        population_size = len(fitness_scores)
        new_population = []
        
        # 保留最优个体（精英策略）
        elite_count = max(1, population_size // 10)
        for i in range(elite_count):
            new_population.append(fitness_scores[i][0].copy())
        
        # 生成新个体
        while len(new_population) < population_size:
            # 选择父母（轮盘赌选择）
            parent1 = self._tournament_selection(fitness_scores)
            parent2 = self._tournament_selection(fitness_scores)
            
            # 交叉
            if random.random() < crossover_rate:
                child1, child2 = self._crossover(parent1, parent2, parameter_ranges)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            if random.random() < mutation_rate:
                child1 = self._mutate(child1, parameter_ranges)
            if random.random() < mutation_rate:
                child2 = self._mutate(child2, parameter_ranges)
            
            new_population.extend([child1, child2])
        
        return new_population[:population_size]
    
    def _tournament_selection(self, 
                            fitness_scores: List[Tuple[Dict[str, Any], float, Dict[str, Any]]],
                            tournament_size: int = 3) -> Dict[str, Any]:
        """锦标赛选择"""
        tournament = random.sample(fitness_scores, min(tournament_size, len(fitness_scores)))
        winner = max(tournament, key=lambda x: x[1])
        return winner[0].copy()
    
    def _crossover(self, 
                  parent1: Dict[str, Any], 
                  parent2: Dict[str, Any],
                  parameter_ranges: Dict[str, Tuple[Any, Any]]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """交叉操作"""
        child1 = parent1.copy()
        child2 = parent2.copy()
        
        # 单点交叉
        param_names = list(parameter_ranges.keys())
        crossover_point = random.randint(1, len(param_names) - 1)
        
        for i in range(crossover_point, len(param_names)):
            param_name = param_names[i]
            child1[param_name], child2[param_name] = child2[param_name], child1[param_name]
        
        return child1, child2
    
    def _mutate(self, 
               individual: Dict[str, Any],
               parameter_ranges: Dict[str, Tuple[Any, Any]]) -> Dict[str, Any]:
        """变异操作"""
        mutated = individual.copy()
        
        # 随机选择一个参数进行变异
        param_name = random.choice(list(parameter_ranges.keys()))
        min_val, max_val = parameter_ranges[param_name]
        
        if isinstance(min_val, int) and isinstance(max_val, int):
            mutated[param_name] = random.randint(min_val, max_val)
        elif isinstance(min_val, float) or isinstance(max_val, float):
            mutated[param_name] = random.uniform(min_val, max_val)
        else:
            mutated[param_name] = random.choice([min_val, max_val])
        
        return mutated
    
    def get_best_parameters(self, top_n: int = 1) -> List[OptimizationResult]:
        """获取最佳参数"""
        return self.optimization_results[:top_n]
    
    def save_results(self, filename: str) -> bool:
        """保存优化结果"""
        try:
            import json
            
            results_data = []
            for result in self.optimization_results:
                results_data.append({
                    'parameters': result.parameters,
                    'target_value': result.target_value,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 优化结果已保存到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存优化结果失败: {e}")
            return False
