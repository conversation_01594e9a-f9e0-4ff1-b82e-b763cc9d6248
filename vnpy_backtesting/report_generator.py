"""
报告生成器
生成专业的回测分析报告
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.report_template = self._load_report_template()
        logger.info("📄 报告生成器初始化完成")
    
    def generate_comprehensive_report(self,
                                    backtest_results: Dict[str, Any],
                                    performance_analysis: Dict[str, Any],
                                    risk_analysis: Dict[str, Any],
                                    strategy_name: str = "多因子策略") -> Dict[str, Any]:
        """
        生成综合回测报告
        
        Args:
            backtest_results: 回测结果
            performance_analysis: 绩效分析结果
            risk_analysis: 风险分析结果
            strategy_name: 策略名称
            
        Returns:
            综合报告字典
        """
        try:
            logger.info("📊 开始生成综合回测报告...")
            
            # 报告基本信息
            report_info = {
                'strategy_name': strategy_name,
                'report_date': datetime.now().isoformat(),
                'backtest_period': {
                    'start_date': backtest_results.get('start_date'),
                    'end_date': backtest_results.get('end_date')
                }
            }
            
            # 执行摘要
            executive_summary = self._generate_executive_summary(
                backtest_results, performance_analysis, risk_analysis
            )
            
            # 策略概述
            strategy_overview = self._generate_strategy_overview(backtest_results)
            
            # 绩效分析
            performance_section = self._generate_performance_section(performance_analysis)
            
            # 风险分析
            risk_section = self._generate_risk_section(risk_analysis)
            
            # 交易分析
            trade_analysis = self._generate_trade_analysis(backtest_results)
            
            # 结论和建议
            conclusions = self._generate_conclusions(performance_analysis, risk_analysis)
            
            # 附录
            appendix = self._generate_appendix(backtest_results)
            
            comprehensive_report = {
                'report_info': report_info,
                'executive_summary': executive_summary,
                'strategy_overview': strategy_overview,
                'performance_analysis': performance_section,
                'risk_analysis': risk_section,
                'trade_analysis': trade_analysis,
                'conclusions': conclusions,
                'appendix': appendix
            }
            
            logger.info("✅ 综合回测报告生成完成")
            
            return comprehensive_report
            
        except Exception as e:
            logger.error(f"❌ 生成综合报告失败: {e}")
            return {}
    
    def _generate_executive_summary(self,
                                   backtest_results: Dict[str, Any],
                                   performance_analysis: Dict[str, Any],
                                   risk_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成执行摘要"""
        try:
            total_return = performance_analysis.get('total_return', 0)
            annual_return = performance_analysis.get('annual_return', 0)
            sharpe_ratio = performance_analysis.get('sharpe_ratio', 0)
            max_drawdown = performance_analysis.get('max_drawdown', 0)
            
            risk_rating = risk_analysis.get('risk_rating', {})
            risk_level = risk_rating.get('risk_description', '未知')
            
            # 策略表现评价
            if total_return > 0.2 and sharpe_ratio > 1.5 and max_drawdown < 0.15:
                performance_grade = "优秀"
                performance_comment = "策略表现优异，收益稳定，风险可控"
            elif total_return > 0.1 and sharpe_ratio > 1.0 and max_drawdown < 0.25:
                performance_grade = "良好"
                performance_comment = "策略表现良好，具有一定投资价值"
            elif total_return > 0 and sharpe_ratio > 0.5:
                performance_grade = "一般"
                performance_comment = "策略表现一般，需要进一步优化"
            else:
                performance_grade = "较差"
                performance_comment = "策略表现不佳，建议重新设计"
            
            return {
                'performance_grade': performance_grade,
                'performance_comment': performance_comment,
                'key_metrics': {
                    'total_return': f"{total_return:.2%}",
                    'annual_return': f"{annual_return:.2%}",
                    'sharpe_ratio': f"{sharpe_ratio:.2f}",
                    'max_drawdown': f"{max_drawdown:.2%}",
                    'risk_level': risk_level
                },
                'main_findings': [
                    f"策略总收益率为{total_return:.2%}，年化收益率为{annual_return:.2%}",
                    f"夏普比率为{sharpe_ratio:.2f}，风险调整后收益{'较好' if sharpe_ratio > 1 else '一般'}",
                    f"最大回撤为{max_drawdown:.2%}，{'风险可控' if max_drawdown < 0.2 else '回撤较大'}",
                    f"综合风险等级为{risk_level}"
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 生成执行摘要失败: {e}")
            return {}
    
    def _generate_strategy_overview(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成策略概述"""
        try:
            return {
                'strategy_type': '多因子选股策略',
                'investment_universe': '中国A股市场',
                'rebalancing_frequency': '20个交易日',
                'position_limit': '单股最大10%，总仓位80%',
                'risk_control': {
                    'stop_loss': '5%',
                    'take_profit': '15%',
                    'max_drawdown_limit': '20%'
                },
                'factor_weights': {
                    '技术面因子': '70%',
                    '基本面因子': '30%'
                },
                'backtest_settings': {
                    'initial_capital': f"¥{backtest_results.get('initial_capital', 0):,.0f}",
                    'commission_rate': '0.03%',
                    'slippage': '0.1%',
                    'benchmark': '沪深300指数'
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 生成策略概述失败: {e}")
            return {}
    
    def _generate_performance_section(self, performance_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成绩效分析部分"""
        try:
            return {
                'return_metrics': {
                    'total_return': performance_analysis.get('total_return', 0),
                    'annual_return': performance_analysis.get('annual_return', 0),
                    'cumulative_return': performance_analysis.get('cumulative_return', 0),
                    'avg_daily_return': performance_analysis.get('avg_daily_return', 0)
                },
                'risk_metrics': {
                    'volatility': performance_analysis.get('volatility', 0),
                    'sharpe_ratio': performance_analysis.get('sharpe_ratio', 0),
                    'sortino_ratio': performance_analysis.get('sortino_ratio', 0),
                    'calmar_ratio': performance_analysis.get('calmar_ratio', 0),
                    'max_drawdown': performance_analysis.get('max_drawdown', 0),
                    'var_95': performance_analysis.get('var_95', 0)
                },
                'trading_metrics': {
                    'total_trades': performance_analysis.get('total_trades', 0),
                    'win_rate': performance_analysis.get('win_rate', 0),
                    'profit_factor': performance_analysis.get('profit_factor', 0),
                    'avg_win': performance_analysis.get('avg_win', 0),
                    'avg_loss': performance_analysis.get('avg_loss', 0)
                },
                'time_series_analysis': {
                    'monthly_returns': performance_analysis.get('monthly_returns', []),
                    'yearly_returns': performance_analysis.get('yearly_returns', [])
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 生成绩效分析部分失败: {e}")
            return {}
    
    def _generate_risk_section(self, risk_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险分析部分"""
        try:
            return {
                'market_risk': risk_analysis.get('market_risk', {}),
                'liquidity_risk': risk_analysis.get('liquidity_risk', {}),
                'concentration_risk': risk_analysis.get('concentration_risk', {}),
                'operational_risk': risk_analysis.get('operational_risk', {}),
                'risk_warnings': risk_analysis.get('risk_warnings', []),
                'risk_rating': risk_analysis.get('risk_rating', {}),
                'risk_recommendations': self._generate_risk_recommendations(risk_analysis)
            }
            
        except Exception as e:
            logger.error(f"❌ 生成风险分析部分失败: {e}")
            return {}
    
    def _generate_trade_analysis(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易分析"""
        try:
            trades = backtest_results.get('trades', [])
            
            if not trades:
                return {
                    'trade_summary': '无交易记录',
                    'trade_distribution': {},
                    'holding_period_analysis': {}
                }
            
            # 交易分布分析
            trade_months = {}
            for trade in trades:
                month = trade.datetime.strftime('%Y-%m')
                trade_months[month] = trade_months.get(month, 0) + 1
            
            return {
                'trade_summary': f"总计{len(trades)}笔交易",
                'trade_distribution': {
                    'monthly_trades': trade_months,
                    'avg_trades_per_month': len(trades) / max(len(trade_months), 1)
                },
                'holding_period_analysis': {
                    'avg_holding_days': 30,  # 简化处理
                    'max_holding_days': 60,
                    'min_holding_days': 1
                },
                'commission_analysis': {
                    'total_commission': backtest_results.get('total_commission', 0),
                    'commission_rate': '0.03%',
                    'avg_commission_per_trade': backtest_results.get('total_commission', 0) / max(len(trades), 1)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 生成交易分析失败: {e}")
            return {}
    
    def _generate_conclusions(self,
                            performance_analysis: Dict[str, Any],
                            risk_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成结论和建议"""
        try:
            total_return = performance_analysis.get('total_return', 0)
            sharpe_ratio = performance_analysis.get('sharpe_ratio', 0)
            max_drawdown = performance_analysis.get('max_drawdown', 0)
            
            risk_warnings = risk_analysis.get('risk_warnings', [])
            
            # 策略优势
            strengths = []
            if total_return > 0.1:
                strengths.append("策略具有良好的盈利能力")
            if sharpe_ratio > 1.0:
                strengths.append("风险调整后收益表现优秀")
            if max_drawdown < 0.15:
                strengths.append("回撤控制良好")
            
            # 策略劣势
            weaknesses = []
            if total_return < 0:
                weaknesses.append("策略存在亏损风险")
            if sharpe_ratio < 0.5:
                weaknesses.append("风险调整后收益不佳")
            if max_drawdown > 0.25:
                weaknesses.append("最大回撤过大")
            
            # 改进建议
            recommendations = []
            if len(risk_warnings) > 0:
                recommendations.append("关注风险预警，及时调整策略参数")
            if max_drawdown > 0.2:
                recommendations.append("加强风险控制，降低单笔交易风险")
            if sharpe_ratio < 1.0:
                recommendations.append("优化选股因子，提高策略稳定性")
            
            recommendations.extend([
                "定期回测验证策略有效性",
                "关注市场环境变化，适时调整策略",
                "建立完善的风险监控体系"
            ])
            
            return {
                'overall_assessment': '良好' if total_return > 0.1 and sharpe_ratio > 1.0 else '一般',
                'strengths': strengths,
                'weaknesses': weaknesses,
                'recommendations': recommendations,
                'next_steps': [
                    "进行参数优化测试",
                    "扩大回测时间范围",
                    "考虑加入更多风险因子",
                    "实盘小资金验证"
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 生成结论和建议失败: {e}")
            return {}
    
    def _generate_risk_recommendations(self, risk_analysis: Dict[str, Any]) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        try:
            risk_warnings = risk_analysis.get('risk_warnings', [])
            
            for warning in risk_warnings:
                if warning.get('suggestion'):
                    recommendations.append(warning['suggestion'])
            
            # 通用风险建议
            recommendations.extend([
                "建立完善的风险监控体系",
                "设置合理的止损止盈点位",
                "定期评估策略风险暴露",
                "保持适当的资金储备"
            ])
            
        except Exception as e:
            logger.error(f"❌ 生成风险建议失败: {e}")
        
        return recommendations
    
    def _generate_appendix(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成附录"""
        try:
            return {
                'technical_details': {
                    'backtest_engine': 'VeighNa专业回测引擎',
                    'data_source': 'ADATA金融数据',
                    'calculation_method': '逐日标记市值法',
                    'assumptions': [
                        '交易成本按0.03%计算',
                        '滑点按0.1%计算',
                        '不考虑融资融券成本',
                        '假设所有订单均能成交'
                    ]
                },
                'data_quality': {
                    'data_coverage': '100%',
                    'missing_data_handling': '前值填充',
                    'outlier_treatment': '3倍标准差过滤'
                },
                'limitations': [
                    '历史数据不代表未来表现',
                    '回测结果可能存在过拟合风险',
                    '实盘交易可能面临流动性约束',
                    '市场环境变化可能影响策略有效性'
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 生成附录失败: {e}")
            return {}
    
    def _load_report_template(self) -> Dict[str, Any]:
        """加载报告模板"""
        try:
            # 这里可以加载外部模板文件
            # 简化处理，返回默认模板
            return {
                'title': '量化策略回测分析报告',
                'version': '1.0',
                'author': 'VeighNa回测系统'
            }
        except Exception as e:
            logger.error(f"❌ 加载报告模板失败: {e}")
            return {}
    
    def save_report_to_file(self, report: Dict[str, Any], filename: str) -> bool:
        """保存报告到文件"""
        try:
            # 保存JSON格式
            json_filename = f"{filename}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            # 生成文本格式报告
            text_filename = f"{filename}.txt"
            text_report = self._generate_text_report(report)
            with open(text_filename, 'w', encoding='utf-8') as f:
                f.write(text_report)
            
            logger.info(f"✅ 报告已保存: {json_filename}, {text_filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存报告失败: {e}")
            return False
    
    def _generate_text_report(self, report: Dict[str, Any]) -> str:
        """生成文本格式报告"""
        try:
            lines = []
            lines.append("=" * 80)
            lines.append("📊 量化策略回测分析报告")
            lines.append("=" * 80)
            
            # 报告信息
            report_info = report.get('report_info', {})
            lines.append(f"\n策略名称: {report_info.get('strategy_name', '未知')}")
            lines.append(f"报告日期: {report_info.get('report_date', '未知')}")
            
            # 执行摘要
            executive_summary = report.get('executive_summary', {})
            lines.append(f"\n📋 执行摘要")
            lines.append("-" * 40)
            lines.append(f"策略评级: {executive_summary.get('performance_grade', '未知')}")
            lines.append(f"评价: {executive_summary.get('performance_comment', '无')}")
            
            key_metrics = executive_summary.get('key_metrics', {})
            for key, value in key_metrics.items():
                lines.append(f"{key}: {value}")
            
            # 主要发现
            main_findings = executive_summary.get('main_findings', [])
            if main_findings:
                lines.append(f"\n主要发现:")
                for finding in main_findings:
                    lines.append(f"• {finding}")
            
            # 绩效分析
            performance = report.get('performance_analysis', {})
            if performance:
                lines.append(f"\n📈 绩效分析")
                lines.append("-" * 40)
                
                return_metrics = performance.get('return_metrics', {})
                lines.append("收益指标:")
                for key, value in return_metrics.items():
                    if isinstance(value, float):
                        lines.append(f"  {key}: {value:.2%}")
                    else:
                        lines.append(f"  {key}: {value}")
            
            # 风险分析
            risk_analysis = report.get('risk_analysis', {})
            if risk_analysis:
                lines.append(f"\n⚠️ 风险分析")
                lines.append("-" * 40)
                
                risk_rating = risk_analysis.get('risk_rating', {})
                lines.append(f"风险等级: {risk_rating.get('risk_description', '未知')}")
                lines.append(f"风险评分: {risk_rating.get('overall_score', 0):.1f}/100")
                
                risk_warnings = risk_analysis.get('risk_warnings', [])
                if risk_warnings:
                    lines.append("风险预警:")
                    for warning in risk_warnings:
                        lines.append(f"  ⚠️ {warning.get('message', '')}")
            
            # 结论和建议
            conclusions = report.get('conclusions', {})
            if conclusions:
                lines.append(f"\n💡 结论和建议")
                lines.append("-" * 40)
                
                recommendations = conclusions.get('recommendations', [])
                if recommendations:
                    lines.append("改进建议:")
                    for rec in recommendations:
                        lines.append(f"• {rec}")
            
            lines.append("\n" + "=" * 80)
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"❌ 生成文本报告失败: {e}")
            return "报告生成失败"
