"""
多时间周期策略框架
基于VeighNa的多时间周期策略基类
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np

from vnpy_backtesting.backtesting_engine import BarData, Direction, OrderType
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector

logger = logging.getLogger(__name__)

class ArrayManager:
    """数组管理器 - 高效存储和计算技术指标"""
    
    def __init__(self, size: int = 100):
        self.size = size
        self.count = 0
        
        # 价格数组
        self.open_array = np.zeros(size)
        self.high_array = np.zeros(size)
        self.low_array = np.zeros(size)
        self.close_array = np.zeros(size)
        self.volume_array = np.zeros(size)
        
        # 技术指标缓存
        self.indicator_cache: Dict[str, Any] = {}
        
    def update_bar(self, bar: BarData) -> None:
        """更新K线数据"""
        self.count += 1
        
        # 滚动数组
        if self.count > self.size:
            self.open_array[:-1] = self.open_array[1:]
            self.high_array[:-1] = self.high_array[1:]
            self.low_array[:-1] = self.low_array[1:]
            self.close_array[:-1] = self.close_array[1:]
            self.volume_array[:-1] = self.volume_array[1:]
            
            index = -1
        else:
            index = self.count - 1
        
        # 更新最新数据
        self.open_array[index] = bar.open_price
        self.high_array[index] = bar.high_price
        self.low_array[index] = bar.low_price
        self.close_array[index] = bar.close_price
        self.volume_array[index] = bar.volume
        
        # 清除指标缓存
        self.indicator_cache.clear()
    
    @property
    def inited(self) -> bool:
        """是否已初始化"""
        return self.count >= self.size
    
    def sma(self, period: int, array: bool = False) -> float:
        """简单移动平均"""
        if self.count < period:
            return 0.0
        
        cache_key = f"sma_{period}"
        if cache_key not in self.indicator_cache:
            result = np.mean(self.close_array[-period:])
            self.indicator_cache[cache_key] = result
        
        return self.indicator_cache[cache_key]
    
    def ema(self, period: int) -> float:
        """指数移动平均"""
        if self.count < period:
            return 0.0
        
        cache_key = f"ema_{period}"
        if cache_key not in self.indicator_cache:
            alpha = 2.0 / (period + 1)
            result = self.close_array[-1]
            
            for i in range(2, min(period + 1, self.count + 1)):
                result = alpha * self.close_array[-i] + (1 - alpha) * result
            
            self.indicator_cache[cache_key] = result
        
        return self.indicator_cache[cache_key]
    
    def rsi(self, period: int = 14) -> float:
        """RSI相对强弱指标"""
        if self.count < period + 1:
            return 50.0
        
        cache_key = f"rsi_{period}"
        if cache_key not in self.indicator_cache:
            deltas = np.diff(self.close_array[-period-1:])
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains)
            avg_loss = np.mean(losses)
            
            if avg_loss == 0:
                result = 100.0
            else:
                rs = avg_gain / avg_loss
                result = 100 - (100 / (1 + rs))
            
            self.indicator_cache[cache_key] = result
        
        return self.indicator_cache[cache_key]
    
    def macd(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> tuple:
        """MACD指标"""
        if self.count < slow_period:
            return 0.0, 0.0, 0.0
        
        cache_key = f"macd_{fast_period}_{slow_period}_{signal_period}"
        if cache_key not in self.indicator_cache:
            ema_fast = self.ema(fast_period)
            ema_slow = self.ema(slow_period)
            
            macd_line = ema_fast - ema_slow
            
            # 简化信号线计算
            signal_line = macd_line * 0.9  # 简化处理
            histogram = macd_line - signal_line
            
            result = (macd_line, signal_line, histogram)
            self.indicator_cache[cache_key] = result
        
        return self.indicator_cache[cache_key]

class BarGenerator:
    """K线生成器 - 处理多时间周期数据"""
    
    def __init__(self, on_bar: callable, window: int = 1, on_window_bar: callable = None):
        self.on_bar = on_bar
        self.on_window_bar = on_window_bar
        self.window = window
        self.window_bar: Optional[BarData] = None
        self.interval = 0
        
    def update_bar(self, bar: BarData) -> None:
        """更新K线数据"""
        # 调用基础周期回调
        self.on_bar(bar)
        
        # 生成更高周期K线
        if self.on_window_bar and self.window > 1:
            self._update_window_bar(bar)
    
    def _update_window_bar(self, bar: BarData) -> None:
        """更新窗口K线"""
        if not self.window_bar:
            self.window_bar = BarData(
                symbol=bar.symbol,
                datetime=bar.datetime,
                open_price=bar.open_price,
                high_price=bar.high_price,
                low_price=bar.low_price,
                close_price=bar.close_price,
                volume=bar.volume,
                amount=bar.amount
            )
        else:
            self.window_bar.high_price = max(self.window_bar.high_price, bar.high_price)
            self.window_bar.low_price = min(self.window_bar.low_price, bar.low_price)
            self.window_bar.close_price = bar.close_price
            self.window_bar.volume += bar.volume
            self.window_bar.amount += bar.amount
        
        self.interval += 1
        
        if self.interval >= self.window:
            self.on_window_bar(self.window_bar)
            self.window_bar = None
            self.interval = 0

class MultiTimeframeStrategy(ABC):
    """多时间周期策略基类"""
    
    def __init__(self, engine, **params):
        self.engine = engine
        self.params = params
        
        # 技术分析工具
        self.technical_indicators = TechnicalIndicators()
        self.stock_selector = MultiFactorSelector()
        
        # 数据管理
        self.array_managers: Dict[str, ArrayManager] = {}
        self.bar_generators: Dict[str, BarGenerator] = {}
        
        # 策略状态
        self.inited = False
        self.trading = False
        
        # 策略参数
        self.buy_score_threshold = params.get('buy_score_threshold', 70)
        self.sell_score_threshold = params.get('sell_score_threshold', 30)
        self.max_position_pct = params.get('max_position_pct', 0.1)  # 单股最大仓位10%
        self.stop_loss_pct = params.get('stop_loss_pct', 0.05)      # 止损5%
        self.take_profit_pct = params.get('take_profit_pct', 0.15)  # 止盈15%
        
        logger.info(f"📈 多时间周期策略初始化: {self.__class__.__name__}")
    
    def on_init(self) -> None:
        """策略初始化"""
        logger.info("🚀 策略初始化开始...")
        
        # 初始化数组管理器
        for symbol in self.engine.bars.keys():
            self.array_managers[symbol] = ArrayManager(size=100)
            
            # 创建K线生成器
            self.bar_generators[symbol] = BarGenerator(
                on_bar=lambda bar, s=symbol: self._on_base_bar(s, bar),
                window=5,  # 5日K线
                on_window_bar=lambda bar, s=symbol: self._on_window_bar(s, bar)
            )
        
        self.inited = True
        self.trading = True
        
        logger.info("✅ 策略初始化完成")
    
    def on_bar(self, bar: BarData) -> None:
        """K线数据回调"""
        if not self.inited:
            return
        
        symbol = bar.symbol
        
        # 更新数组管理器
        if symbol in self.array_managers:
            self.array_managers[symbol].update_bar(bar)
            
            # 更新K线生成器
            if symbol in self.bar_generators:
                self.bar_generators[symbol].update_bar(bar)
    
    def _on_base_bar(self, symbol: str, bar: BarData) -> None:
        """基础周期K线回调"""
        if not self.trading:
            return
        
        # 检查是否有足够数据
        am = self.array_managers.get(symbol)
        if not am or not am.inited:
            return
        
        # 调用策略逻辑
        self.on_strategy_bar(symbol, bar, am)
    
    def _on_window_bar(self, symbol: str, bar: BarData) -> None:
        """窗口周期K线回调"""
        if not self.trading:
            return
        
        # 调用窗口策略逻辑
        self.on_window_strategy_bar(symbol, bar)
    
    @abstractmethod
    def on_strategy_bar(self, symbol: str, bar: BarData, am: ArrayManager) -> None:
        """策略K线处理 - 子类必须实现"""
        pass
    
    def on_window_strategy_bar(self, symbol: str, bar: BarData) -> None:
        """窗口策略K线处理 - 子类可选实现"""
        pass
    
    def on_stop(self) -> None:
        """策略停止"""
        self.trading = False
        logger.info("⏹️ 策略停止")
    
    def buy(self, symbol: str, volume: int, price: float = 0.0) -> str:
        """买入"""
        if not self.trading:
            return ""
        
        return self.engine.send_order(
            symbol=symbol,
            direction=Direction.LONG,
            volume=volume,
            order_type=OrderType.MARKET if price == 0 else OrderType.LIMIT,
            price=price
        )
    
    def sell(self, symbol: str, volume: int, price: float = 0.0) -> str:
        """卖出"""
        if not self.trading:
            return ""
        
        return self.engine.send_order(
            symbol=symbol,
            direction=Direction.SHORT,
            volume=volume,
            order_type=OrderType.MARKET if price == 0 else OrderType.LIMIT,
            price=price
        )
    
    def get_position(self, symbol: str) -> int:
        """获取持仓数量"""
        position = self.engine.get_position(symbol)
        return position.volume if position else 0
    
    def calculate_position_size(self, symbol: str, price: float) -> int:
        """计算仓位大小"""
        total_value = self.engine.get_total_value()
        max_value = total_value * self.max_position_pct
        volume = int(max_value / price / 100) * 100  # 按手数计算
        return max(100, volume)  # 最小1手
    
    def check_stop_loss(self, symbol: str, current_price: float) -> bool:
        """检查止损"""
        position = self.engine.get_position(symbol)
        if not position or position.volume <= 0:
            return False
        
        loss_pct = (position.price - current_price) / position.price
        return loss_pct >= self.stop_loss_pct
    
    def check_take_profit(self, symbol: str, current_price: float) -> bool:
        """检查止盈"""
        position = self.engine.get_position(symbol)
        if not position or position.volume <= 0:
            return False
        
        profit_pct = (current_price - position.price) / position.price
        return profit_pct >= self.take_profit_pct
    
    def get_technical_score(self, symbol: str, am: ArrayManager) -> float:
        """获取技术面评分"""
        try:
            if not am.inited:
                return 50.0
            
            score = 50.0
            
            # MA趋势评分
            ma5 = am.sma(5)
            ma10 = am.sma(10)
            ma20 = am.sma(20)
            current_price = am.close_array[-1]
            
            if current_price > ma5 > ma10 > ma20:
                score += 20  # 多头排列
            elif current_price < ma5 < ma10 < ma20:
                score -= 20  # 空头排列
            
            # RSI评分
            rsi = am.rsi(14)
            if 30 <= rsi <= 70:
                score += 10  # RSI在合理区间
            elif rsi < 30:
                score += 15  # 超卖
            elif rsi > 70:
                score -= 15  # 超买
            
            # MACD评分
            macd, signal, histogram = am.macd()
            if macd > signal and histogram > 0:
                score += 15  # MACD金叉
            elif macd < signal and histogram < 0:
                score -= 15  # MACD死叉
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"❌ 计算技术评分失败: {symbol} - {e}")
            return 50.0
