-- 量化交易系统数据库初始化脚本 V2.0
-- 基于ADATA接口文档和产品设计要求

-- 创建数据库
CREATE DATABASE IF NOT EXISTS quantitative_trading_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE quantitative_trading_system;

-- 创建扩展（PostgreSQL）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- 1. 基础数据表
-- ============================================================================

-- 股票基本信息表
CREATE TABLE IF NOT EXISTS stock_info (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL UNIQUE COMMENT '股票代码',
    name VARCHAR(50) NOT NULL COMMENT '股票名称',
    industry VARCHAR(50) COMMENT '所属行业',
    sector VARCHAR(50) COMMENT '所属板块',
    market VARCHAR(10) COMMENT '交易市场(SH/SZ)',
    list_date DATE COMMENT '上市日期',
    delist_date DATE COMMENT '退市日期',
    market_cap BIGINT COMMENT '总市值',
    total_shares BIGINT COMMENT '总股本',
    float_shares BIGINT COMMENT '流通股本',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_stock_info_symbol (symbol),
    INDEX idx_stock_info_industry (industry),
    INDEX idx_stock_info_market (market),
    INDEX idx_stock_info_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息表';

-- ============================================================================
-- 2. 多时间周期行情数据表
-- ============================================================================

-- 1分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_1_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='1分钟K线数据表';

-- 5分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_5_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='5分钟K线数据表';

-- 15分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_15_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='15分钟K线数据表';

-- 1小时K线数据表
CREATE TABLE IF NOT EXISTS hour_1_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='1小时K线数据表';

-- 4小时K线数据表
CREATE TABLE IF NOT EXISTS hour_4_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='4小时K线数据表';

-- 日线数据表
CREATE TABLE IF NOT EXISTS daily_market (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_date DATE NOT NULL COMMENT '交易日期',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    turnover_rate DECIMAL(8,4) COMMENT '换手率',
    pe_ratio DECIMAL(8,4) COMMENT 'PE市盈率',
    pb_ratio DECIMAL(8,4) COMMENT 'PB市净率',
    market_cap DECIMAL(20,2) COMMENT '总市值',
    circulating_cap DECIMAL(20,2) COMMENT '流通市值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_date (symbol, trade_date),
    INDEX idx_symbol_date (symbol, trade_date),
    INDEX idx_trade_date (trade_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日线数据表';

-- ============================================================================
-- 3. 基本面数据表
-- ============================================================================

-- 财务数据表
CREATE TABLE IF NOT EXISTS financial_data (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    report_date DATE NOT NULL COMMENT '报告期',
    report_type VARCHAR(10) NOT NULL COMMENT '报告类型(Q1/Q2/Q3/Q4)',
    
    -- 收入利润指标
    total_revenue DECIMAL(20,2) COMMENT '总营收',
    net_profit DECIMAL(20,2) COMMENT '净利润',
    gross_profit DECIMAL(20,2) COMMENT '毛利润',
    operating_profit DECIMAL(20,2) COMMENT '营业利润',
    
    -- 资产负债指标
    total_assets DECIMAL(20,2) COMMENT '总资产',
    total_liabilities DECIMAL(20,2) COMMENT '总负债',
    total_equity DECIMAL(20,2) COMMENT '股东权益',
    current_assets DECIMAL(20,2) COMMENT '流动资产',
    current_liabilities DECIMAL(20,2) COMMENT '流动负债',
    
    -- 财务比率
    roe DECIMAL(8,4) COMMENT '净资产收益率',
    roa DECIMAL(8,4) COMMENT '总资产收益率',
    gross_margin DECIMAL(8,4) COMMENT '毛利率',
    net_margin DECIMAL(8,4) COMMENT '净利率',
    debt_ratio DECIMAL(8,4) COMMENT '资产负债率',
    current_ratio DECIMAL(8,4) COMMENT '流动比率',
    
    -- 增长率指标
    revenue_growth DECIMAL(8,4) COMMENT '营收增长率',
    profit_growth DECIMAL(8,4) COMMENT '利润增长率',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_report (symbol, report_date, report_type),
    INDEX idx_symbol_date (symbol, report_date),
    INDEX idx_report_date (report_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务数据表';

-- ============================================================================
-- 4. 因子数据表
-- ============================================================================

-- 因子配置表
CREATE TABLE IF NOT EXISTS factor_config (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    factor_id VARCHAR(50) NOT NULL UNIQUE COMMENT '因子ID',
    factor_name VARCHAR(100) NOT NULL COMMENT '因子名称',
    factor_category ENUM('technical', 'fundamental', 'market') NOT NULL COMMENT '因子分类',
    factor_type VARCHAR(50) NOT NULL COMMENT '因子类型',
    weight DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '权重',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '因子描述',
    calculation_formula TEXT COMMENT '计算公式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_factor_category (factor_category),
    INDEX idx_factor_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='因子配置表';

-- 因子参数表
CREATE TABLE IF NOT EXISTS factor_parameters (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    factor_id VARCHAR(50) NOT NULL COMMENT '因子ID',
    param_name VARCHAR(50) NOT NULL COMMENT '参数名称',
    param_type ENUM('int', 'float', 'string', 'boolean') NOT NULL COMMENT '参数类型',
    param_value TEXT NOT NULL COMMENT '参数值',
    default_value TEXT COMMENT '默认值',
    min_value DECIMAL(20,6) COMMENT '最小值',
    max_value DECIMAL(20,6) COMMENT '最大值',
    description TEXT COMMENT '参数描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_factor_param (factor_id, param_name),
    FOREIGN KEY (factor_id) REFERENCES factor_config(factor_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='因子参数表';

-- ============================================================================
-- 5. 交易信号表
-- ============================================================================

-- 股票评分表
CREATE TABLE IF NOT EXISTS stock_scores (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    calculation_date DATE NOT NULL COMMENT '计算日期',

    -- 分项评分
    technical_score DECIMAL(8,4) COMMENT '技术面评分',
    fundamental_score DECIMAL(8,4) COMMENT '基本面评分',
    market_score DECIMAL(8,4) COMMENT '市场表现评分',

    -- 综合评分
    total_score DECIMAL(8,4) NOT NULL COMMENT '综合评分',
    score_rank INTEGER COMMENT '评分排名',

    -- 详细因子评分
    factor_scores JSON COMMENT '详细因子评分',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_symbol_date (symbol, calculation_date),
    INDEX idx_symbol_score (symbol, total_score),
    INDEX idx_calculation_date (calculation_date),
    INDEX idx_total_score (total_score DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票评分表';

-- 交易信号表
CREATE TABLE IF NOT EXISTS trading_signals (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    signal_time TIMESTAMP NOT NULL COMMENT '信号时间',
    signal_type ENUM('BUY', 'SELL', 'HOLD') NOT NULL COMMENT '信号类型',
    signal_strength ENUM('WEAK', 'MEDIUM', 'STRONG') NOT NULL COMMENT '信号强度',
    signal_score DECIMAL(8,4) NOT NULL COMMENT '信号评分',
    confidence DECIMAL(5,4) COMMENT '置信度',

    -- 价格信息
    trigger_price DECIMAL(10,3) COMMENT '触发价格',
    target_price DECIMAL(10,3) COMMENT '目标价格',
    stop_loss_price DECIMAL(10,3) COMMENT '止损价格',

    -- 信号详情
    signal_reasons JSON COMMENT '信号原因',
    technical_indicators JSON COMMENT '技术指标状态',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_symbol_time (symbol, signal_time),
    INDEX idx_signal_type (signal_type),
    INDEX idx_signal_time (signal_time),
    INDEX idx_signal_score (signal_score DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易信号表';

-- ============================================================================
-- 6. 投资组合管理表
-- ============================================================================

-- 投资组合表
CREATE TABLE IF NOT EXISTS portfolios (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    portfolio_name VARCHAR(100) NOT NULL COMMENT '组合名称',
    portfolio_type ENUM('QUANTITATIVE', 'MANUAL', 'HYBRID') NOT NULL COMMENT '组合类型',
    initial_capital DECIMAL(20,2) NOT NULL COMMENT '初始资金',
    current_value DECIMAL(20,2) COMMENT '当前市值',
    cash_balance DECIMAL(20,2) COMMENT '现金余额',

    -- 配置参数
    max_positions INTEGER DEFAULT 20 COMMENT '最大持仓数',
    max_position_weight DECIMAL(5,4) DEFAULT 0.1 COMMENT '单股最大权重',
    rebalance_frequency ENUM('DAILY', 'WEEKLY', 'MONTHLY') DEFAULT 'MONTHLY',

    -- 绩效指标
    total_return DECIMAL(8,4) COMMENT '总收益率',
    annual_return DECIMAL(8,4) COMMENT '年化收益率',
    max_drawdown DECIMAL(8,4) COMMENT '最大回撤',
    sharpe_ratio DECIMAL(8,4) COMMENT '夏普比率',

    status ENUM('ACTIVE', 'INACTIVE', 'CLOSED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_portfolio_type (portfolio_type),
    INDEX idx_portfolio_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投资组合表';

-- 投资组合持仓表
CREATE TABLE IF NOT EXISTS portfolio_holdings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    portfolio_id VARCHAR(36) NOT NULL COMMENT '组合ID',
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    quantity INTEGER NOT NULL COMMENT '持仓数量',
    avg_cost DECIMAL(10,3) NOT NULL COMMENT '平均成本',
    current_price DECIMAL(10,3) COMMENT '当前价格',
    market_value DECIMAL(20,2) COMMENT '市值',
    weight DECIMAL(5,4) COMMENT '权重',
    unrealized_pnl DECIMAL(20,2) COMMENT '浮动盈亏',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_portfolio_symbol (portfolio_id, symbol),
    INDEX idx_portfolio_id (portfolio_id),
    INDEX idx_symbol (symbol),
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投资组合持仓表';

-- 交易订单表
CREATE TABLE IF NOT EXISTS trading_orders (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    portfolio_id VARCHAR(36) COMMENT '组合ID',
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    order_type ENUM('BUY', 'SELL') NOT NULL COMMENT '订单类型',
    quantity INTEGER NOT NULL COMMENT '数量',
    price DECIMAL(10,3) COMMENT '价格',
    order_status ENUM('PENDING', 'PARTIAL', 'FILLED', 'CANCELLED') DEFAULT 'PENDING',

    -- 执行信息
    filled_quantity INTEGER DEFAULT 0 COMMENT '已成交数量',
    avg_fill_price DECIMAL(10,3) COMMENT '平均成交价',
    commission DECIMAL(10,2) COMMENT '手续费',

    -- 时间信息
    order_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    fill_time TIMESTAMP COMMENT '成交时间',

    -- 订单详情
    order_reason TEXT COMMENT '下单原因',

    INDEX idx_portfolio_id (portfolio_id),
    INDEX idx_symbol (symbol),
    INDEX idx_order_status (order_status),
    INDEX idx_order_time (order_time),
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易订单表';

-- ============================================================================
-- 7. VeighNa回测系统表
-- ============================================================================

-- 回测策略表
CREATE TABLE IF NOT EXISTS backtest_strategies (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    strategy_name VARCHAR(100) NOT NULL COMMENT '策略名称',
    strategy_type VARCHAR(50) NOT NULL COMMENT '策略类型',
    description TEXT COMMENT '策略描述',

    -- 回测配置
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    initial_capital DECIMAL(20,2) NOT NULL COMMENT '初始资金',

    -- 策略参数
    strategy_params JSON COMMENT '策略参数',

    -- 风险控制参数
    max_position_size DECIMAL(5,4) COMMENT '最大仓位',
    stop_loss_ratio DECIMAL(5,4) COMMENT '止损比例',
    take_profit_ratio DECIMAL(5,4) COMMENT '止盈比例',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_strategy_type (strategy_type),
    INDEX idx_strategy_name (strategy_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回测策略表';

-- 回测结果表
CREATE TABLE IF NOT EXISTS backtest_results (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    strategy_id VARCHAR(36) NOT NULL COMMENT '策略ID',
    symbol VARCHAR(10) COMMENT '股票代码',

    -- 绩效指标
    total_return DECIMAL(8,4) COMMENT '总收益率',
    annual_return DECIMAL(8,4) COMMENT '年化收益率',
    max_drawdown DECIMAL(8,4) COMMENT '最大回撤',
    sharpe_ratio DECIMAL(8,4) COMMENT '夏普比率',
    sortino_ratio DECIMAL(8,4) COMMENT '索提诺比率',
    calmar_ratio DECIMAL(8,4) COMMENT '卡尔马比率',
    volatility DECIMAL(8,4) COMMENT '波动率',

    -- 交易统计
    total_trades INTEGER COMMENT '总交易次数',
    winning_trades INTEGER COMMENT '盈利交易次数',
    losing_trades INTEGER COMMENT '亏损交易次数',
    win_rate DECIMAL(5,4) COMMENT '胜率',
    profit_loss_ratio DECIMAL(8,4) COMMENT '盈亏比',

    -- 其他指标
    max_consecutive_wins INTEGER COMMENT '最大连胜次数',
    max_consecutive_losses INTEGER COMMENT '最大连亏次数',
    avg_trade_return DECIMAL(8,4) COMMENT '平均交易收益率',

    -- 详细数据
    daily_returns JSON COMMENT '每日收益率数据',
    trade_details JSON COMMENT '交易明细数据',
    performance_metrics JSON COMMENT '详细绩效指标',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_strategy_id (strategy_id),
    INDEX idx_symbol (symbol),
    INDEX idx_total_return (total_return DESC),
    INDEX idx_sharpe_ratio (sharpe_ratio DESC),
    FOREIGN KEY (strategy_id) REFERENCES backtest_strategies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回测结果表';

-- ============================================================================
-- 8. 系统管理表
-- ============================================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', 'JSON') DEFAULT 'STRING',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_config_key (config_key),
    INDEX idx_config_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    module_name VARCHAR(50) NOT NULL COMMENT '模块名称',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_log_level (log_level),
    INDEX idx_module_name (module_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- ============================================================================
-- 初始化数据
-- ============================================================================

-- 插入默认因子配置
INSERT INTO factor_config (factor_id, factor_name, factor_category, factor_type, weight, description) VALUES
('momentum_5d', '5日动量因子', 'technical', 'momentum', 0.05, '5日价格动量'),
('momentum_20d', '20日动量因子', 'technical', 'momentum', 0.10, '20日价格动量'),
('rsi_14', 'RSI相对强弱指标', 'technical', 'momentum', 0.08, '14日RSI指标'),
('macd', 'MACD指标', 'technical', 'momentum', 0.12, 'MACD金叉死叉'),
('ma_trend', '均线趋势', 'technical', 'trend', 0.15, '多均线趋势判断'),
('pe_ratio', 'PE估值因子', 'fundamental', 'value', 0.10, 'PE市盈率估值'),
('pb_ratio', 'PB估值因子', 'fundamental', 'value', 0.08, 'PB市净率估值'),
('roe', 'ROE盈利因子', 'fundamental', 'quality', 0.12, '净资产收益率'),
('revenue_growth', '营收增长因子', 'fundamental', 'growth', 0.10, '营收增长率'),
('volume_ratio', '成交量比率', 'market', 'liquidity', 0.10, '成交量相对比率');

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('data_collection_interval', '300', 'INTEGER', '数据采集间隔(秒)'),
('max_stock_selection', '50', 'INTEGER', '最大选股数量'),
('default_portfolio_size', '20', 'INTEGER', '默认组合大小'),
('risk_free_rate', '0.03', 'FLOAT', '无风险利率'),
('max_position_weight', '0.1', 'FLOAT', '单股最大权重'),
('rebalance_threshold', '0.05', 'FLOAT', '再平衡阈值'),
('enable_real_trading', 'false', 'BOOLEAN', '是否启用实盘交易'),
('backtest_commission', '0.0003', 'FLOAT', '回测手续费率');

-- 创建用户和权限（根据实际数据库类型调整）
-- CREATE USER 'quant_user'@'%' IDENTIFIED BY 'secure_password';
-- GRANT ALL PRIVILEGES ON quantitative_trading_system.* TO 'quant_user'@'%';
-- FLUSH PRIVILEGES;
