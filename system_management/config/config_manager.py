"""
系统配置管理器
负责系统配置的加载、保存和管理
"""

import os
import json
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
import logging

from database_models import db_manager, SystemConfig

logger = logging.getLogger(__name__)

class ConfigManager:
    """系统配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config_data = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            # 首先尝试从文件加载配置
            config_path = Path(self.config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                        self.config_data = yaml.safe_load(f) or {}
                    else:
                        self.config_data = json.load(f)
                logger.info(f"✅ 从文件加载配置: {config_path}")
            else:
                logger.info("📋 配置文件不存在，使用默认配置")
                self.config_data = self._get_default_config()
            
            # 从数据库加载配置（优先级更高）
            self._load_from_database()
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            self.config_data = self._get_default_config()
    
    def _load_from_database(self):
        """从数据库加载配置"""
        try:
            with db_manager.get_session_context() as session:
                configs = session.query(SystemConfig).filter(
                    SystemConfig.is_active == True
                ).all()
                
                for config in configs:
                    # 根据配置类型转换值
                    value = self._convert_config_value(config.config_value, config.config_type)
                    self.config_data[config.config_key] = value
                
                logger.info(f"✅ 从数据库加载了 {len(configs)} 个配置项")
                
        except Exception as e:
            logger.warning(f"⚠️ 从数据库加载配置失败: {e}")
    
    def _convert_config_value(self, value: str, config_type: str) -> Any:
        """转换配置值类型"""
        try:
            if config_type == 'INTEGER':
                return int(value)
            elif config_type == 'FLOAT':
                return float(value)
            elif config_type == 'BOOLEAN':
                return value.lower() in ('true', '1', 'yes', 'on')
            elif config_type == 'JSON':
                return json.loads(value)
            else:  # STRING
                return value
        except Exception as e:
            logger.warning(f"⚠️ 配置值转换失败: {value} -> {config_type}, 错误: {e}")
            return value
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 数据采集配置
            'data_collection_interval': 300,  # 数据采集间隔(秒)
            'max_stock_selection': 50,        # 最大选股数量
            'enable_realtime_data': True,     # 是否启用实时数据
            
            # 分析引擎配置
            'technical_score_weight': 0.5,    # 技术面评分权重
            'fundamental_score_weight': 0.3,  # 基本面评分权重
            'market_score_weight': 0.2,       # 市场表现评分权重
            
            # 投资组合配置
            'default_portfolio_size': 20,     # 默认组合大小
            'max_position_weight': 0.1,       # 单股最大权重
            'rebalance_threshold': 0.05,      # 再平衡阈值
            
            # 风险管理配置
            'risk_free_rate': 0.03,           # 无风险利率
            'max_drawdown_limit': 0.2,        # 最大回撤限制
            'stop_loss_ratio': 0.1,           # 止损比例
            
            # 回测配置
            'backtest_commission': 0.0003,    # 回测手续费率
            'backtest_slippage': 0.001,       # 回测滑点
            
            # 系统配置
            'enable_real_trading': False,     # 是否启用实盘交易
            'log_level': 'INFO',              # 日志级别
            'max_log_files': 10,              # 最大日志文件数
            
            # 数据库配置
            'database_pool_size': 20,         # 数据库连接池大小
            'database_max_overflow': 30,      # 数据库连接池最大溢出
            
            # API配置
            'api_host': '0.0.0.0',           # API服务器地址
            'api_port': 8000,                # API服务器端口
            'api_workers': 4,                # API工作进程数
            
            # 缓存配置
            'redis_host': 'localhost',       # Redis主机
            'redis_port': 6379,              # Redis端口
            'redis_db': 0,                   # Redis数据库
            
            # 监控配置
            'enable_monitoring': True,       # 是否启用监控
            'monitoring_interval': 60,      # 监控间隔(秒)
            'alert_email': '',               # 预警邮箱
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config_data.get(key, default)
    
    def set(self, key: str, value: Any, save_to_db: bool = True) -> bool:
        """设置配置值"""
        try:
            # 更新内存中的配置
            self.config_data[key] = value
            
            # 保存到数据库
            if save_to_db:
                self._save_to_database(key, value)
            
            logger.info(f"✅ 配置已更新: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置配置失败: {key} = {value}, 错误: {e}")
            return False
    
    def _save_to_database(self, key: str, value: Any):
        """保存配置到数据库"""
        try:
            with db_manager.get_session() as session:
                # 查找现有配置
                config = session.query(SystemConfig).filter(
                    SystemConfig.config_key == key
                ).first()
                
                # 确定配置类型和值
                config_type, config_value = self._determine_config_type_and_value(value)
                
                if config:
                    # 更新现有配置
                    config.config_value = config_value
                    config.config_type = config_type
                else:
                    # 创建新配置
                    config = SystemConfig(
                        config_key=key,
                        config_value=config_value,
                        config_type=config_type,
                        description=f"动态配置: {key}"
                    )
                    session.add(config)
                
                session.commit()
                logger.debug(f"✅ 配置已保存到数据库: {key}")
                
        except Exception as e:
            logger.error(f"❌ 保存配置到数据库失败: {key}, 错误: {e}")
    
    def _determine_config_type_and_value(self, value: Any) -> tuple:
        """确定配置类型和值"""
        if isinstance(value, bool):
            return 'BOOLEAN', str(value).lower()
        elif isinstance(value, int):
            return 'INTEGER', str(value)
        elif isinstance(value, float):
            return 'FLOAT', str(value)
        elif isinstance(value, (dict, list)):
            return 'JSON', json.dumps(value)
        else:
            return 'STRING', str(value)
    
    def save_to_file(self, file_path: Optional[str] = None) -> bool:
        """保存配置到文件"""
        try:
            file_path = file_path or self.config_file
            
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.endswith('.yaml') or file_path.endswith('.yml'):
                    yaml.dump(self.config_data, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 配置已保存到文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存配置到文件失败: {e}")
            return False
    
    def reload(self):
        """重新加载配置"""
        logger.info("🔄 重新加载配置...")
        self._load_config()
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_data.copy()
    
    def update_configs(self, configs: Dict[str, Any], save_to_db: bool = True) -> bool:
        """批量更新配置"""
        try:
            for key, value in configs.items():
                self.set(key, value, save_to_db=save_to_db)
            
            logger.info(f"✅ 批量更新了 {len(configs)} 个配置项")
            return True
            
        except Exception as e:
            logger.error(f"❌ 批量更新配置失败: {e}")
            return False
    
    def delete_config(self, key: str) -> bool:
        """删除配置"""
        try:
            # 从内存中删除
            if key in self.config_data:
                del self.config_data[key]
            
            # 从数据库中删除
            with db_manager.get_session() as session:
                config = session.query(SystemConfig).filter(
                    SystemConfig.config_key == key
                ).first()
                
                if config:
                    session.delete(config)
                    session.commit()
            
            logger.info(f"✅ 配置已删除: {key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除配置失败: {key}, 错误: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()
