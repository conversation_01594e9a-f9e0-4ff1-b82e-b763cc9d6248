"""
性能监控器
监控系统性能指标、资源使用情况、响应时间等
"""

import logging
import psutil
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque
import socket
import os

from database_models import db_manager, PerformanceMetrics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    host_name: str
    process_name: str

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, monitor_interval: int = 60):
        self.monitor_interval = monitor_interval  # 监控间隔(秒)
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 主机信息
        self.host_name = socket.gethostname()
        self.process_name = os.path.basename(os.path.abspath(__file__))
        
        # 内存中的指标缓存
        self.metrics_cache = deque(maxlen=1000)
        
        # 监控的指标类型
        self.metric_collectors = {
            'cpu_usage': self._collect_cpu_metrics,
            'memory_usage': self._collect_memory_metrics,
            'disk_usage': self._collect_disk_metrics,
            'network_io': self._collect_network_metrics,
            'process_metrics': self._collect_process_metrics,
            'database_metrics': self._collect_database_metrics
        }
        
        # 告警阈值
        self.alert_thresholds = {
            'cpu_usage': 80.0,          # CPU使用率 > 80%
            'memory_usage': 85.0,       # 内存使用率 > 85%
            'disk_usage': 90.0,         # 磁盘使用率 > 90%
            'response_time': 5.0,       # 响应时间 > 5秒
            'error_rate': 5.0           # 错误率 > 5%
        }
        
        # 告警回调函数
        self.alert_callbacks: List[Callable] = []
        
        logger.info("📊 性能监控器初始化完成")
        logger.info(f"  - 监控间隔: {self.monitor_interval}秒")
        logger.info(f"  - 主机名: {self.host_name}")
    
    def start_monitoring(self) -> bool:
        """开始监控"""
        try:
            if self.is_monitoring:
                logger.warning("⚠️ 性能监控已在运行")
                return False
            
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info("🚀 性能监控已启动")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动性能监控失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        try:
            if not self.is_monitoring:
                logger.warning("⚠️ 性能监控未在运行")
                return False
            
            self.is_monitoring = False
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            logger.info("⏹️ 性能监控已停止")
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止性能监控失败: {e}")
            return False
    
    def _monitoring_loop(self):
        """监控循环"""
        logger.info("🔄 性能监控循环开始")
        
        while self.is_monitoring:
            try:
                # 收集所有指标
                metrics = self._collect_all_metrics()
                
                # 保存到数据库
                self._save_metrics_to_database(metrics)
                
                # 缓存到内存
                for metric in metrics:
                    self.metrics_cache.append(metric)
                
                # 检查告警
                self._check_alerts(metrics)
                
                # 等待下一次监控
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
                time.sleep(self.monitor_interval)
        
        logger.info("⏹️ 性能监控循环结束")
    
    def _collect_all_metrics(self) -> List[PerformanceMetric]:
        """收集所有性能指标"""
        all_metrics = []
        
        for metric_type, collector in self.metric_collectors.items():
            try:
                metrics = collector()
                all_metrics.extend(metrics)
                
            except Exception as e:
                logger.error(f"❌ 收集{metric_type}指标失败: {e}")
        
        return all_metrics
    
    def _collect_cpu_metrics(self) -> List[PerformanceMetric]:
        """收集CPU指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics.append(PerformanceMetric(
                name='cpu_usage',
                value=cpu_percent,
                unit='percent',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # CPU核心数
            cpu_count = psutil.cpu_count()
            metrics.append(PerformanceMetric(
                name='cpu_count',
                value=cpu_count,
                unit='count',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 负载平均值（Linux/Mac）
            if hasattr(os, 'getloadavg'):
                load_avg = os.getloadavg()[0]  # 1分钟负载
                metrics.append(PerformanceMetric(
                    name='load_average',
                    value=load_avg,
                    unit='ratio',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
            
        except Exception as e:
            logger.error(f"❌ 收集CPU指标失败: {e}")
        
        return metrics
    
    def _collect_memory_metrics(self) -> List[PerformanceMetric]:
        """收集内存指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # 系统内存
            memory = psutil.virtual_memory()
            
            metrics.append(PerformanceMetric(
                name='memory_usage',
                value=memory.percent,
                unit='percent',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='memory_total',
                value=memory.total / (1024**3),  # GB
                unit='GB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='memory_available',
                value=memory.available / (1024**3),  # GB
                unit='GB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 交换内存
            swap = psutil.swap_memory()
            metrics.append(PerformanceMetric(
                name='swap_usage',
                value=swap.percent,
                unit='percent',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
        except Exception as e:
            logger.error(f"❌ 收集内存指标失败: {e}")
        
        return metrics
    
    def _collect_disk_metrics(self) -> List[PerformanceMetric]:
        """收集磁盘指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # 磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            
            metrics.append(PerformanceMetric(
                name='disk_usage',
                value=(disk_usage.used / disk_usage.total) * 100,
                unit='percent',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='disk_total',
                value=disk_usage.total / (1024**3),  # GB
                unit='GB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='disk_free',
                value=disk_usage.free / (1024**3),  # GB
                unit='GB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            if disk_io:
                metrics.append(PerformanceMetric(
                    name='disk_read_bytes',
                    value=disk_io.read_bytes / (1024**2),  # MB
                    unit='MB',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
                
                metrics.append(PerformanceMetric(
                    name='disk_write_bytes',
                    value=disk_io.write_bytes / (1024**2),  # MB
                    unit='MB',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
            
        except Exception as e:
            logger.error(f"❌ 收集磁盘指标失败: {e}")
        
        return metrics
    
    def _collect_network_metrics(self) -> List[PerformanceMetric]:
        """收集网络指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # 网络IO
            net_io = psutil.net_io_counters()
            
            metrics.append(PerformanceMetric(
                name='network_bytes_sent',
                value=net_io.bytes_sent / (1024**2),  # MB
                unit='MB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='network_bytes_recv',
                value=net_io.bytes_recv / (1024**2),  # MB
                unit='MB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='network_packets_sent',
                value=net_io.packets_sent,
                unit='count',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            metrics.append(PerformanceMetric(
                name='network_packets_recv',
                value=net_io.packets_recv,
                unit='count',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
        except Exception as e:
            logger.error(f"❌ 收集网络指标失败: {e}")
        
        return metrics
    
    def _collect_process_metrics(self) -> List[PerformanceMetric]:
        """收集进程指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # 当前进程
            current_process = psutil.Process()
            
            # 进程CPU使用率
            cpu_percent = current_process.cpu_percent()
            metrics.append(PerformanceMetric(
                name='process_cpu_usage',
                value=cpu_percent,
                unit='percent',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 进程内存使用
            memory_info = current_process.memory_info()
            metrics.append(PerformanceMetric(
                name='process_memory_rss',
                value=memory_info.rss / (1024**2),  # MB
                unit='MB',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 进程线程数
            num_threads = current_process.num_threads()
            metrics.append(PerformanceMetric(
                name='process_threads',
                value=num_threads,
                unit='count',
                timestamp=timestamp,
                host_name=self.host_name,
                process_name=self.process_name
            ))
            
            # 进程文件描述符数（Linux/Mac）
            if hasattr(current_process, 'num_fds'):
                num_fds = current_process.num_fds()
                metrics.append(PerformanceMetric(
                    name='process_file_descriptors',
                    value=num_fds,
                    unit='count',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
            
        except Exception as e:
            logger.error(f"❌ 收集进程指标失败: {e}")
        
        return metrics
    
    def _collect_database_metrics(self) -> List[PerformanceMetric]:
        """收集数据库指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            with db_manager.get_session() as session:
                # 数据库连接数（简化处理）
                # 实际应该查询数据库的系统表获取真实连接数
                metrics.append(PerformanceMetric(
                    name='database_connections',
                    value=1,  # 当前会话数
                    unit='count',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
                
                # 数据库响应时间
                start_time = time.time()
                session.execute("SELECT 1")
                response_time = (time.time() - start_time) * 1000  # 毫秒
                
                metrics.append(PerformanceMetric(
                    name='database_response_time',
                    value=response_time,
                    unit='ms',
                    timestamp=timestamp,
                    host_name=self.host_name,
                    process_name=self.process_name
                ))
            
        except Exception as e:
            logger.error(f"❌ 收集数据库指标失败: {e}")
        
        return metrics
    
    def _save_metrics_to_database(self, metrics: List[PerformanceMetric]) -> None:
        """保存指标到数据库"""
        try:
            with db_manager.get_session() as session:
                for metric in metrics:
                    db_metric = PerformanceMetrics(
                        metric_name=metric.name,
                        metric_value=metric.value,
                        metric_unit=metric.unit,
                        host_name=metric.host_name,
                        process_name=metric.process_name,
                        timestamp=metric.timestamp
                    )
                    session.add(db_metric)
                
                session.commit()
                
        except Exception as e:
            logger.error(f"❌ 保存性能指标到数据库失败: {e}")
    
    def _check_alerts(self, metrics: List[PerformanceMetric]) -> None:
        """检查告警"""
        try:
            for metric in metrics:
                threshold = self.alert_thresholds.get(metric.name)
                
                if threshold and metric.value > threshold:
                    alert_info = {
                        'metric_name': metric.name,
                        'current_value': metric.value,
                        'threshold': threshold,
                        'unit': metric.unit,
                        'timestamp': metric.timestamp,
                        'host_name': metric.host_name
                    }
                    
                    # 记录告警日志
                    logger.warning(f"🚨 性能告警: {metric.name}={metric.value}{metric.unit} > {threshold}{metric.unit}")
                    
                    # 调用告警回调
                    for callback in self.alert_callbacks:
                        try:
                            callback(alert_info)
                        except Exception as e:
                            logger.error(f"❌ 告警回调执行失败: {e}")
            
        except Exception as e:
            logger.error(f"❌ 检查告警失败: {e}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        try:
            metrics = self._collect_all_metrics()
            
            result = {}
            for metric in metrics:
                result[metric.name] = {
                    'value': metric.value,
                    'unit': metric.unit,
                    'timestamp': metric.timestamp.isoformat()
                }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取当前性能指标失败: {e}")
            return {}
    
    def get_historical_metrics(self,
                             metric_name: str,
                             hours: int = 24) -> List[Dict[str, Any]]:
        """获取历史性能指标"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            with db_manager.get_session() as session:
                metrics = session.query(PerformanceMetrics).filter(
                    PerformanceMetrics.metric_name == metric_name,
                    PerformanceMetrics.timestamp >= start_time,
                    PerformanceMetrics.timestamp <= end_time
                ).order_by(PerformanceMetrics.timestamp.asc()).all()
                
                result = []
                for metric in metrics:
                    result.append({
                        'value': metric.metric_value,
                        'unit': metric.metric_unit,
                        'timestamp': metric.timestamp.isoformat(),
                        'host_name': metric.host_name
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"❌ 获取历史性能指标失败: {e}")
            return []
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            with db_manager.get_session() as session:
                # 获取各指标的统计信息
                summary = {}
                
                for metric_name in ['cpu_usage', 'memory_usage', 'disk_usage']:
                    metrics = session.query(PerformanceMetrics).filter(
                        PerformanceMetrics.metric_name == metric_name,
                        PerformanceMetrics.timestamp >= start_time
                    ).all()
                    
                    if metrics:
                        values = [m.metric_value for m in metrics]
                        summary[metric_name] = {
                            'current': values[-1] if values else 0,
                            'average': sum(values) / len(values),
                            'max': max(values),
                            'min': min(values),
                            'unit': metrics[0].metric_unit
                        }
                
                return summary
                
        except Exception as e:
            logger.error(f"❌ 获取性能摘要失败: {e}")
            return {}
    
    def add_alert_callback(self, callback: Callable) -> None:
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def set_alert_threshold(self, metric_name: str, threshold: float) -> None:
        """设置告警阈值"""
        self.alert_thresholds[metric_name] = threshold
        logger.info(f"✅ 设置告警阈值: {metric_name} = {threshold}")
    
    def clean_old_metrics(self, days_to_keep: int = 7) -> bool:
        """清理旧的性能指标"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            
            with db_manager.get_session() as session:
                deleted_count = session.query(PerformanceMetrics).filter(
                    PerformanceMetrics.timestamp < cutoff_time
                ).delete()
                
                session.commit()
                
                logger.info(f"✅ 清理旧性能指标: 删除{deleted_count}条记录")
                return True
                
        except Exception as e:
            logger.error(f"❌ 清理旧性能指标失败: {e}")
            return False
