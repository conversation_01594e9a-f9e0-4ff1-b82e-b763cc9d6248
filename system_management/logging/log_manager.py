"""
系统日志管理器
负责系统日志的配置、管理和存储
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json

from database_models import db_manager, SystemLog

class LogManager:
    """系统日志管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建各种日志记录器
        self.loggers = {}
        self._setup_loggers()
    
    def _setup_loggers(self):
        """设置日志记录器"""
        # 主系统日志
        self._create_logger(
            name="system",
            filename="system.log",
            level=logging.INFO,
            format_string="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        
        # 数据采集日志
        self._create_logger(
            name="data_collection",
            filename="data_collection.log",
            level=logging.INFO
        )
        
        # 分析引擎日志
        self._create_logger(
            name="analysis_engine",
            filename="analysis_engine.log",
            level=logging.INFO
        )
        
        # 交易执行日志
        self._create_logger(
            name="trading_execution",
            filename="trading_execution.log",
            level=logging.INFO
        )
        
        # 错误日志
        self._create_logger(
            name="error",
            filename="error.log",
            level=logging.ERROR
        )
        
        # 性能日志
        self._create_logger(
            name="performance",
            filename="performance.log",
            level=logging.INFO
        )
    
    def _create_logger(
        self, 
        name: str, 
        filename: str, 
        level: int = logging.INFO,
        format_string: Optional[str] = None,
        max_bytes: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ) -> logging.Logger:
        """创建日志记录器"""
        
        logger = logging.getLogger(f"quant_system.{name}")
        logger.setLevel(level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 文件处理器（轮转日志）
        log_file = self.log_dir / filename
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        
        # 设置格式
        if format_string is None:
            format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        formatter = logging.Formatter(format_string)
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        # 数据库处理器（用于重要日志）
        if name in ["system", "error", "trading_execution"]:
            db_handler = DatabaseLogHandler()
            db_handler.setLevel(logging.WARNING)  # 只记录警告及以上级别到数据库
            logger.addHandler(db_handler)
        
        self.loggers[name] = logger
        return logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        return self.loggers.get(name, logging.getLogger(f"quant_system.{name}"))
    
    def log_system_event(self, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """记录系统事件"""
        logger = self.get_logger("system")
        
        if level.upper() == "DEBUG":
            logger.debug(message)
        elif level.upper() == "INFO":
            logger.info(message)
        elif level.upper() == "WARNING":
            logger.warning(message)
        elif level.upper() == "ERROR":
            logger.error(message)
        elif level.upper() == "CRITICAL":
            logger.critical(message)
        
        # 如果有详细信息，也记录到数据库
        if details:
            self._save_to_database(level.upper(), "system", message, details)
    
    def log_data_collection(self, message: str, level: str = "INFO"):
        """记录数据采集日志"""
        logger = self.get_logger("data_collection")
        getattr(logger, level.lower())(message)
    
    def log_analysis_engine(self, message: str, level: str = "INFO"):
        """记录分析引擎日志"""
        logger = self.get_logger("analysis_engine")
        getattr(logger, level.lower())(message)
    
    def log_trading_execution(self, message: str, level: str = "INFO", details: Optional[Dict[str, Any]] = None):
        """记录交易执行日志"""
        logger = self.get_logger("trading_execution")
        getattr(logger, level.lower())(message)
        
        # 交易相关日志都保存到数据库
        if details:
            self._save_to_database(level.upper(), "trading_execution", message, details)
    
    def log_error(self, message: str, exception: Optional[Exception] = None, details: Optional[Dict[str, Any]] = None):
        """记录错误日志"""
        logger = self.get_logger("error")
        
        if exception:
            logger.error(f"{message}: {str(exception)}", exc_info=True)
            if details is None:
                details = {}
            details["exception"] = str(exception)
            details["exception_type"] = type(exception).__name__
        else:
            logger.error(message)
        
        # 错误日志保存到数据库
        self._save_to_database("ERROR", "error", message, details)
    
    def log_performance(self, operation: str, duration: float, details: Optional[Dict[str, Any]] = None):
        """记录性能日志"""
        logger = self.get_logger("performance")
        message = f"操作: {operation}, 耗时: {duration:.4f}秒"
        logger.info(message)
        
        # 性能日志也保存到数据库
        perf_details = {"operation": operation, "duration": duration}
        if details:
            perf_details.update(details)
        
        self._save_to_database("INFO", "performance", message, perf_details)
    
    def _save_to_database(self, level: str, module: str, message: str, details: Optional[Dict[str, Any]] = None):
        """保存日志到数据库"""
        try:
            with db_manager.get_session() as session:
                log_entry = SystemLog(
                    log_level=level,
                    module_name=module,
                    message=message,
                    details=details
                )
                session.add(log_entry)
                session.commit()
        except Exception as e:
            # 避免日志记录本身出错导致系统问题
            print(f"保存日志到数据库失败: {e}")
    
    def get_recent_logs(self, module: Optional[str] = None, level: Optional[str] = None, limit: int = 100):
        """获取最近的日志记录"""
        try:
            with db_manager.get_session() as session:
                query = session.query(SystemLog)
                
                if module:
                    query = query.filter(SystemLog.module_name == module)
                
                if level:
                    query = query.filter(SystemLog.log_level == level)
                
                logs = query.order_by(SystemLog.created_at.desc()).limit(limit).all()
                
                return [
                    {
                        "id": log.id,
                        "level": log.log_level,
                        "module": log.module_name,
                        "message": log.message,
                        "details": log.details,
                        "created_at": log.created_at.isoformat()
                    }
                    for log in logs
                ]
        except Exception as e:
            self.log_error("获取日志记录失败", e)
            return []
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with db_manager.get_session() as session:
                deleted_count = session.query(SystemLog).filter(
                    SystemLog.created_at < cutoff_date
                ).delete()
                session.commit()
                
                self.log_system_event("INFO", f"清理了 {deleted_count} 条旧日志记录")
                
        except Exception as e:
            self.log_error("清理旧日志失败", e)

class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def emit(self, record):
        """发送日志记录到数据库"""
        try:
            # 格式化日志消息
            message = self.format(record)
            
            # 提取模块名
            module_name = record.name.split('.')[-1] if '.' in record.name else record.name
            
            # 准备详细信息
            details = {
                "logger_name": record.name,
                "filename": record.filename,
                "lineno": record.lineno,
                "funcName": record.funcName
            }
            
            # 如果有异常信息，添加到详细信息中
            if record.exc_info:
                details["exc_info"] = self.formatException(record.exc_info)
            
            # 保存到数据库
            with db_manager.get_session() as session:
                log_entry = SystemLog(
                    log_level=record.levelname,
                    module_name=module_name,
                    message=message,
                    details=details
                )
                session.add(log_entry)
                session.commit()
                
        except Exception:
            # 避免日志记录本身出错导致系统问题
            self.handleError(record)

# 全局日志管理器实例
log_manager = LogManager()
