"""
用户管理器
管理用户认证、权限、会话等
"""

import logging
import hashlib
import secrets
import jwt
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, UserSessions

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """用户角色"""
    ADMIN = "ADMIN"           # 管理员
    TRADER = "TRADER"         # 交易员
    ANALYST = "ANALYST"       # 分析师
    VIEWER = "VIEWER"         # 查看者

@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: str
    role: UserRole
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

@dataclass
class UserSession:
    """用户会话"""
    session_id: str
    user_id: str
    login_time: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    is_active: bool

class UserManager:
    """用户管理器"""
    
    def __init__(self, jwt_secret: str = None):
        # JWT密钥
        self.jwt_secret = jwt_secret or secrets.token_urlsafe(32)
        self.jwt_algorithm = 'HS256'
        self.token_expire_hours = 24
        
        # 会话配置
        self.session_timeout_hours = 1
        self.max_sessions_per_user = 5
        
        # 内存中的用户缓存（简化处理）
        self.users_cache: Dict[str, User] = {}
        self.sessions_cache: Dict[str, UserSession] = {}
        
        # 权限配置
        self.role_permissions = {
            UserRole.ADMIN: [
                'system.config', 'system.logs', 'system.monitor',
                'user.manage', 'factor.manage', 'strategy.manage',
                'portfolio.manage', 'backtest.run', 'trade.execute'
            ],
            UserRole.TRADER: [
                'factor.view', 'strategy.manage', 'portfolio.manage',
                'backtest.run', 'trade.execute'
            ],
            UserRole.ANALYST: [
                'factor.view', 'factor.test', 'strategy.view',
                'portfolio.view', 'backtest.run'
            ],
            UserRole.VIEWER: [
                'factor.view', 'strategy.view', 'portfolio.view'
            ]
        }
        
        # 初始化默认用户
        self._initialize_default_users()
        
        logger.info("👤 用户管理器初始化完成")
        logger.info(f"  - JWT过期时间: {self.token_expire_hours}小时")
        logger.info(f"  - 会话超时: {self.session_timeout_hours}小时")
    
    def _initialize_default_users(self):
        """初始化默认用户"""
        try:
            # 创建默认管理员用户
            admin_user = User(
                user_id='admin',
                username='admin',
                email='<EMAIL>',
                role=UserRole.ADMIN,
                is_active=True,
                created_at=datetime.now()
            )
            
            self.users_cache['admin'] = admin_user
            
            # 创建默认交易员用户
            trader_user = User(
                user_id='trader',
                username='trader',
                email='<EMAIL>',
                role=UserRole.TRADER,
                is_active=True,
                created_at=datetime.now()
            )
            
            self.users_cache['trader'] = trader_user
            
            logger.info("✅ 初始化默认用户完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化默认用户失败: {e}")
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            JWT token或None
        """
        try:
            # 简化处理：固定密码验证
            # 实际应该从数据库验证加密密码
            valid_passwords = {
                'admin': 'admin123',
                'trader': 'trader123'
            }
            
            if username not in valid_passwords or valid_passwords[username] != password:
                logger.warning(f"⚠️ 用户认证失败: {username}")
                return None
            
            user = self.users_cache.get(username)
            if not user or not user.is_active:
                logger.warning(f"⚠️ 用户不存在或已禁用: {username}")
                return None
            
            # 生成JWT token
            payload = {
                'user_id': user.user_id,
                'username': user.username,
                'role': user.role.value,
                'exp': datetime.utcnow() + timedelta(hours=self.token_expire_hours),
                'iat': datetime.utcnow()
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
            
            # 更新最后登录时间
            user.last_login = datetime.now()
            
            logger.info(f"✅ 用户认证成功: {username}")
            return token
            
        except Exception as e:
            logger.error(f"❌ 用户认证异常: {username} - {e}")
            return None
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证JWT token
        
        Args:
            token: JWT token
            
        Returns:
            用户信息或None
        """
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # 检查用户是否仍然有效
            user = self.users_cache.get(payload['username'])
            if not user or not user.is_active:
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("⚠️ Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"⚠️ Token无效: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Token验证异常: {e}")
            return None
    
    def create_session(self,
                      user_id: str,
                      ip_address: str,
                      user_agent: str) -> Optional[str]:
        """
        创建用户会话
        
        Args:
            user_id: 用户ID
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            会话ID或None
        """
        try:
            # 检查用户是否存在
            user = self.users_cache.get(user_id)
            if not user or not user.is_active:
                return None
            
            # 清理该用户的过期会话
            self._cleanup_user_sessions(user_id)
            
            # 检查会话数量限制
            user_sessions = [
                s for s in self.sessions_cache.values()
                if s.user_id == user_id and s.is_active
            ]
            
            if len(user_sessions) >= self.max_sessions_per_user:
                # 删除最旧的会话
                oldest_session = min(user_sessions, key=lambda s: s.last_activity)
                self._deactivate_session(oldest_session.session_id)
            
            # 创建新会话
            session_id = secrets.token_urlsafe(32)
            session = UserSession(
                session_id=session_id,
                user_id=user_id,
                login_time=datetime.now(),
                last_activity=datetime.now(),
                ip_address=ip_address,
                user_agent=user_agent,
                is_active=True
            )
            
            self.sessions_cache[session_id] = session
            
            # 保存到数据库
            self._save_session_to_database(session)
            
            logger.info(f"✅ 创建用户会话: {user_id} - {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"❌ 创建用户会话失败: {user_id} - {e}")
            return None
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """
        验证用户会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息或None
        """
        try:
            session = self.sessions_cache.get(session_id)
            
            if not session or not session.is_active:
                return None
            
            # 检查会话是否过期
            if self._is_session_expired(session):
                self._deactivate_session(session_id)
                return None
            
            # 更新最后活动时间
            session.last_activity = datetime.now()
            self._update_session_activity(session_id)
            
            return session
            
        except Exception as e:
            logger.error(f"❌ 验证用户会话失败: {session_id} - {e}")
            return None
    
    def logout_session(self, session_id: str) -> bool:
        """
        登出会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功
        """
        try:
            session = self.sessions_cache.get(session_id)
            
            if session:
                self._deactivate_session(session_id)
                logger.info(f"✅ 用户登出: {session.user_id} - {session_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 用户登出失败: {session_id} - {e}")
            return False
    
    def check_permission(self, user_id: str, permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            permission: 权限名称
            
        Returns:
            是否有权限
        """
        try:
            user = self.users_cache.get(user_id)
            
            if not user or not user.is_active:
                return False
            
            user_permissions = self.role_permissions.get(user.role, [])
            
            return permission in user_permissions
            
        except Exception as e:
            logger.error(f"❌ 检查用户权限失败: {user_id} - {e}")
            return False
    
    def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            user = self.users_cache.get(user_id)
            
            if not user:
                return None
            
            return {
                'user_id': user.user_id,
                'username': user.username,
                'email': user.email,
                'role': user.role.value,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
            
        except Exception as e:
            logger.error(f"❌ 获取用户信息失败: {user_id} - {e}")
            return None
    
    def get_active_sessions(self, user_id: str = None) -> List[Dict[str, Any]]:
        """获取活跃会话"""
        try:
            sessions = []
            
            for session in self.sessions_cache.values():
                if not session.is_active:
                    continue
                
                if user_id and session.user_id != user_id:
                    continue
                
                if self._is_session_expired(session):
                    continue
                
                sessions.append({
                    'session_id': session.session_id,
                    'user_id': session.user_id,
                    'login_time': session.login_time.isoformat(),
                    'last_activity': session.last_activity.isoformat(),
                    'ip_address': session.ip_address,
                    'user_agent': session.user_agent
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"❌ 获取活跃会话失败: {e}")
            return []
    
    def _is_session_expired(self, session: UserSession) -> bool:
        """检查会话是否过期"""
        timeout = timedelta(hours=self.session_timeout_hours)
        return datetime.now() - session.last_activity > timeout
    
    def _cleanup_user_sessions(self, user_id: str):
        """清理用户过期会话"""
        try:
            expired_sessions = []
            
            for session_id, session in self.sessions_cache.items():
                if session.user_id == user_id and self._is_session_expired(session):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                self._deactivate_session(session_id)
            
        except Exception as e:
            logger.error(f"❌ 清理用户会话失败: {user_id} - {e}")
    
    def _deactivate_session(self, session_id: str):
        """停用会话"""
        try:
            session = self.sessions_cache.get(session_id)
            
            if session:
                session.is_active = False
                
                # 更新数据库
                with db_manager.get_session() as db_session:
                    db_session_record = db_session.query(UserSessions).filter(
                        UserSessions.session_id == session_id
                    ).first()
                    
                    if db_session_record:
                        db_session_record.is_active = False
                        db_session_record.logout_time = datetime.now()
                        db_session.commit()
            
        except Exception as e:
            logger.error(f"❌ 停用会话失败: {session_id} - {e}")
    
    def _save_session_to_database(self, session: UserSession):
        """保存会话到数据库"""
        try:
            with db_manager.get_session() as db_session:
                db_session_record = UserSessions(
                    session_id=session.session_id,
                    user_id=session.user_id,
                    login_time=session.login_time,
                    last_activity=session.last_activity,
                    ip_address=session.ip_address,
                    user_agent=session.user_agent,
                    is_active=session.is_active
                )
                
                db_session.add(db_session_record)
                db_session.commit()
                
        except Exception as e:
            logger.error(f"❌ 保存会话到数据库失败: {session.session_id} - {e}")
    
    def _update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        try:
            with db_manager.get_session() as db_session:
                db_session_record = db_session.query(UserSessions).filter(
                    UserSessions.session_id == session_id
                ).first()
                
                if db_session_record:
                    db_session_record.last_activity = datetime.now()
                    db_session.commit()
                
        except Exception as e:
            logger.error(f"❌ 更新会话活动时间失败: {session_id} - {e}")
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            total_users = len(self.users_cache)
            active_users = len([u for u in self.users_cache.values() if u.is_active])
            
            # 按角色统计
            role_stats = {}
            for role in UserRole:
                count = len([u for u in self.users_cache.values() if u.role == role])
                role_stats[role.value] = count
            
            # 活跃会话统计
            active_sessions = len([s for s in self.sessions_cache.values() if s.is_active])
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'inactive_users': total_users - active_users,
                'role_stats': role_stats,
                'active_sessions': active_sessions,
                'max_sessions_per_user': self.max_sessions_per_user,
                'session_timeout_hours': self.session_timeout_hours
            }
            
        except Exception as e:
            logger.error(f"❌ 获取用户统计失败: {e}")
            return {}
    
    def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        try:
            expired_count = 0
            expired_sessions = []
            
            for session_id, session in self.sessions_cache.items():
                if session.is_active and self._is_session_expired(session):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                self._deactivate_session(session_id)
                expired_count += 1
            
            logger.info(f"✅ 清理过期会话: {expired_count}个")
            return expired_count
            
        except Exception as e:
            logger.error(f"❌ 清理过期会话失败: {e}")
            return 0
