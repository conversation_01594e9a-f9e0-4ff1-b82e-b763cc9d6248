"""
日志管理器
管理系统日志、操作日志、错误日志等
"""

import logging
import logging.handlers
import os
import json
import traceback
from typing import Dict, List, Optional, Any
from datetime import datetime, date, timedelta
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, SystemLog
from sqlalchemy import func

class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class LogEntry:
    """日志条目"""
    level: LogLevel
    module_name: str
    function_name: str
    message: str
    exception_info: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    timestamp: datetime = None

class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def __init__(self, log_manager):
        super().__init__()
        self.log_manager = log_manager
    
    def emit(self, record):
        """发送日志记录到数据库"""
        try:
            # 获取异常信息
            exception_info = None
            if record.exc_info:
                exception_info = self.format_exception(record.exc_info)
            
            # 创建日志条目
            log_entry = LogEntry(
                level=LogLevel(record.levelname),
                module_name=record.module if hasattr(record, 'module') else record.name,
                function_name=record.funcName,
                message=record.getMessage(),
                exception_info=exception_info,
                user_id=getattr(record, 'user_id', None),
                session_id=getattr(record, 'session_id', None),
                ip_address=getattr(record, 'ip_address', None),
                timestamp=datetime.fromtimestamp(record.created)
            )
            
            # 保存到数据库
            self.log_manager.save_log_to_database(log_entry)
            
        except Exception as e:
            # 避免日志处理器本身出错导致的循环
            print(f"Database log handler error: {e}")
    
    def format_exception(self, exc_info):
        """格式化异常信息"""
        return ''.join(traceback.format_exception(*exc_info))

class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志配置
        self.max_log_files = 30
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.log_format = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        
        # 初始化日志系统
        self._setup_logging()
        
        # 数据库日志处理器
        self.db_handler = DatabaseLogHandler(self)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("📝 日志管理器初始化完成")
        self.logger.info(f"  - 日志目录: {self.log_dir}")
        self.logger.info(f"  - 最大文件数: {self.max_log_files}")
    
    def _setup_logging(self):
        """设置日志系统"""
        try:
            # 创建根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 1. 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
            
            # 2. 文件处理器 - 所有日志
            all_log_file = self.log_dir / "all.log"
            file_handler = logging.handlers.RotatingFileHandler(
                all_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.max_log_files,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(self.log_format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            
            # 3. 错误日志文件处理器
            error_log_file = self.log_dir / "error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.max_log_files,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)
            root_logger.addHandler(error_handler)
            
            # 4. 添加数据库处理器
            self.db_handler.setLevel(logging.WARNING)  # 只记录警告及以上级别到数据库
            root_logger.addHandler(self.db_handler)
            
        except Exception as e:
            print(f"Setup logging failed: {e}")
    
    def save_log_to_database(self, log_entry: LogEntry) -> bool:
        """保存日志到数据库"""
        try:
            with db_manager.get_session() as session:
                system_log = SystemLog(
                    log_level=log_entry.level.value,
                    module_name=log_entry.module_name,
                    function_name=log_entry.function_name,
                    message=log_entry.message,
                    exception_info=log_entry.exception_info,
                    user_id=log_entry.user_id,
                    session_id=log_entry.session_id,
                    ip_address=log_entry.ip_address,
                    created_at=log_entry.timestamp or datetime.now()
                )
                
                session.add(system_log)
                session.commit()
                
                return True
                
        except Exception as e:
            # 避免循环日志错误
            print(f"Save log to database failed: {e}")
            return False
    
    def get_logs(self,
                level: Optional[LogLevel] = None,
                module_name: Optional[str] = None,
                start_date: Optional[date] = None,
                end_date: Optional[date] = None,
                limit: int = 100) -> List[Dict[str, Any]]:
        """获取日志记录"""
        try:
            with db_manager.get_session() as session:
                query = session.query(SystemLog)
                
                # 过滤条件
                if level:
                    query = query.filter(SystemLog.log_level == level.value)

                if module_name:
                    query = query.filter(SystemLog.module_name.like(f'%{module_name}%'))

                if start_date:
                    query = query.filter(SystemLog.created_at >= start_date)

                if end_date:
                    query = query.filter(SystemLog.created_at <= end_date + timedelta(days=1))

                # 排序和限制
                logs = query.order_by(SystemLog.created_at.desc()).limit(limit).all()
                
                # 转换为字典
                result = []
                for log in logs:
                    result.append({
                        'id': log.id,
                        'level': log.log_level,
                        'module_name': log.module_name,
                        'function_name': log.function_name,
                        'message': log.message,
                        'exception_info': log.exception_info,
                        'user_id': log.user_id,
                        'session_id': log.session_id,
                        'ip_address': log.ip_address,
                        'created_at': log.created_at.isoformat()
                    })
                
                return result
                
        except Exception as e:
            self.logger.error(f"❌ 获取日志记录失败: {e}")
            return []
    
    def get_log_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with db_manager.get_session() as session:
                # 总日志数
                total_logs = session.query(SystemLog).filter(
                    SystemLog.created_at >= start_date
                ).count()

                # 按级别统计
                level_stats = {}
                for level in LogLevel:
                    count = session.query(SystemLog).filter(
                        SystemLog.created_at >= start_date,
                        SystemLog.log_level == level.value
                    ).count()
                    level_stats[level.value] = count

                # 按模块统计
                module_stats = {}
                module_results = session.query(
                    SystemLog.module_name,
                    func.count(SystemLog.id).label('count')
                ).filter(
                    SystemLog.created_at >= start_date
                ).group_by(SystemLog.module_name).all()

                for module_name, count in module_results:
                    module_stats[module_name] = count

                # 按日期统计
                daily_stats = {}
                for i in range(days):
                    day = start_date + timedelta(days=i)
                    day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
                    day_end = day_start + timedelta(days=1)

                    count = session.query(SystemLog).filter(
                        SystemLog.created_at >= day_start,
                        SystemLog.created_at < day_end
                    ).count()
                    
                    daily_stats[day.strftime('%Y-%m-%d')] = count
                
                return {
                    'period_days': days,
                    'total_logs': total_logs,
                    'level_stats': level_stats,
                    'module_stats': module_stats,
                    'daily_stats': daily_stats,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"❌ 获取日志统计失败: {e}")
            return {}
    
    def clean_old_logs(self, days_to_keep: int = 30) -> bool:
        """清理旧日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with db_manager.get_session() as session:
                # 删除数据库中的旧日志
                deleted_count = session.query(SystemLog).filter(
                    SystemLog.created_at < cutoff_date
                ).delete()
                
                session.commit()
                
                self.logger.info(f"✅ 清理旧日志: 删除{deleted_count}条记录")
                
                # 清理日志文件
                self._clean_log_files(days_to_keep)
                
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 清理旧日志失败: {e}")
            return False
    
    def _clean_log_files(self, days_to_keep: int):
        """清理日志文件"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in self.log_dir.glob("*.log*"):
                try:
                    # 获取文件修改时间
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    
                    if file_time < cutoff_time:
                        log_file.unlink()
                        self.logger.debug(f"删除旧日志文件: {log_file}")
                        
                except Exception as e:
                    self.logger.error(f"删除日志文件失败: {log_file} - {e}")
                    
        except Exception as e:
            self.logger.error(f"❌ 清理日志文件失败: {e}")
    
    def export_logs(self,
                   file_path: str,
                   level: Optional[LogLevel] = None,
                   module_name: Optional[str] = None,
                   start_date: Optional[date] = None,
                   end_date: Optional[date] = None,
                   limit: int = 10000) -> bool:
        """导出日志到文件"""
        try:
            logs = self.get_logs(level, module_name, start_date, end_date, limit)
            
            export_path = Path(file_path)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(logs, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 导出日志到文件: {file_path}, {len(logs)}条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 导出日志失败: {e}")
            return False
    
    def search_logs(self, keyword: str, limit: int = 100) -> List[Dict[str, Any]]:
        """搜索日志"""
        try:
            with db_manager.get_session() as session:
                logs = session.query(SystemLog).filter(
                    SystemLog.message.like(f'%{keyword}%')
                ).order_by(SystemLog.created_at.desc()).limit(limit).all()
                
                result = []
                for log in logs:
                    result.append({
                        'id': log.id,
                        'level': log.log_level,
                        'module_name': log.module_name,
                        'function_name': log.function_name,
                        'message': log.message,
                        'exception_info': log.exception_info,
                        'user_id': log.user_id,
                        'session_id': log.session_id,
                        'ip_address': log.ip_address,
                        'created_at': log.created_at.isoformat()
                    })
                
                return result
                
        except Exception as e:
            self.logger.error(f"❌ 搜索日志失败: {e}")
            return []
    
    def get_error_summary(self, days: int = 1) -> Dict[str, Any]:
        """获取错误摘要"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with db_manager.get_session() as session:
                # 错误总数
                error_count = session.query(SystemLog).filter(
                    SystemLog.created_at >= start_date,
                    SystemLog.log_level.in_(['ERROR', 'CRITICAL'])
                ).count()

                # 按模块统计错误
                module_errors = {}
                module_results = session.query(
                    SystemLog.module_name,
                    func.count(SystemLog.id).label('count')
                ).filter(
                    SystemLog.created_at >= start_date,
                    SystemLog.log_level.in_(['ERROR', 'CRITICAL'])
                ).group_by(SystemLog.module_name).all()

                for module_name, count in module_results:
                    module_errors[module_name] = count

                # 最近的错误
                recent_errors = session.query(SystemLog).filter(
                    SystemLog.created_at >= start_date,
                    SystemLog.log_level.in_(['ERROR', 'CRITICAL'])
                ).order_by(SystemLog.created_at.desc()).limit(10).all()
                
                recent_error_list = []
                for error in recent_errors:
                    recent_error_list.append({
                        'level': error.log_level,
                        'module_name': error.module_name,
                        'message': error.message,
                        'created_at': error.created_at.isoformat()
                    })
                
                return {
                    'period_days': days,
                    'total_errors': error_count,
                    'module_errors': module_errors,
                    'recent_errors': recent_error_list,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"❌ 获取错误摘要失败: {e}")
            return {}
    
    def set_log_level(self, level: LogLevel) -> bool:
        """设置日志级别"""
        try:
            # 更新根日志记录器级别
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, level.value))
            
            # 更新所有处理器级别
            for handler in root_logger.handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    # 控制台处理器
                    handler.setLevel(getattr(logging, level.value))
            
            self.logger.info(f"✅ 设置日志级别: {level.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 设置日志级别失败: {e}")
            return False
    
    def create_custom_logger(self, name: str, level: LogLevel = LogLevel.INFO) -> logging.Logger:
        """创建自定义日志记录器"""
        try:
            logger = logging.getLogger(name)
            logger.setLevel(getattr(logging, level.value))
            
            # 如果没有处理器，添加文件处理器
            if not logger.handlers:
                log_file = self.log_dir / f"{name}.log"
                file_handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=self.max_file_size,
                    backupCount=5,
                    encoding='utf-8'
                )
                file_handler.setLevel(getattr(logging, level.value))
                
                formatter = logging.Formatter(self.log_format)
                file_handler.setFormatter(formatter)
                
                logger.addHandler(file_handler)
            
            return logger
            
        except Exception as e:
            self.logger.error(f"❌ 创建自定义日志记录器失败: {name} - {e}")
            return logging.getLogger(name)
    
    def get_log_files_info(self) -> List[Dict[str, Any]]:
        """获取日志文件信息"""
        try:
            files_info = []
            
            for log_file in self.log_dir.glob("*.log*"):
                try:
                    stat = log_file.stat()
                    files_info.append({
                        'name': log_file.name,
                        'path': str(log_file),
                        'size': stat.st_size,
                        'size_mb': round(stat.st_size / (1024 * 1024), 2),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat()
                    })
                except Exception as e:
                    self.logger.error(f"获取日志文件信息失败: {log_file} - {e}")
            
            # 按修改时间排序
            files_info.sort(key=lambda x: x['modified_time'], reverse=True)
            
            return files_info
            
        except Exception as e:
            self.logger.error(f"❌ 获取日志文件信息失败: {e}")
            return []
