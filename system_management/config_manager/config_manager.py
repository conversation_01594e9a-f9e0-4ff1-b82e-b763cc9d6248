"""
配置管理器
管理系统配置参数、策略参数、数据源配置等
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
from cryptography.fernet import Fernet
import base64

from database_models import db_manager, SystemConfig

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "system_config.json"):
        self.config_file = Path(config_file)
        
        # 内存缓存
        self.config_cache: Dict[str, Any] = {}
        self.cache_timestamp = None
        self.cache_ttl = 300  # 缓存5分钟
        
        # 加密密钥
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # 默认配置
        self.default_configs = self._get_default_configs()
        
        # 初始化配置
        self._initialize_configs()
        
        logger.info("⚙️ 配置管理器初始化完成")
        logger.info(f"  - 配置文件: {self.config_file}")
        logger.info(f"  - 缓存TTL: {self.cache_ttl}秒")
    
    def _get_or_create_encryption_key(self) -> bytes:
        """获取或创建加密密钥"""
        try:
            key_file = Path("encryption.key")
            
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                # 生成新密钥
                key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(key)
                
                # 设置文件权限（仅所有者可读写）
                os.chmod(key_file, 0o600)
                
                logger.info("🔐 生成新的加密密钥")
                return key
                
        except Exception as e:
            logger.error(f"❌ 获取加密密钥失败: {e}")
            # 使用默认密钥（不安全，仅用于开发）
            return base64.urlsafe_b64encode(b"default_key_1234567890123456")
    
    def _get_default_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取默认配置"""
        return {
            # 数据库配置
            'database.host': {
                'value': 'localhost',
                'type': 'string',
                'description': '数据库主机地址',
                'encrypted': False
            },
            'database.port': {
                'value': '5432',
                'type': 'int',
                'description': '数据库端口',
                'encrypted': False
            },
            'database.name': {
                'value': 'quantitative_trading',
                'type': 'string',
                'description': '数据库名称',
                'encrypted': False
            },
            'database.username': {
                'value': 'postgres',
                'type': 'string',
                'description': '数据库用户名',
                'encrypted': False
            },
            'database.password': {
                'value': 'password',
                'type': 'string',
                'description': '数据库密码',
                'encrypted': True
            },
            
            # ADATA配置
            'adata.token': {
                'value': '',
                'type': 'string',
                'description': 'ADATA API Token',
                'encrypted': True
            },
            'adata.base_url': {
                'value': 'https://adata.api.com',
                'type': 'string',
                'description': 'ADATA API基础URL',
                'encrypted': False
            },
            'adata.timeout': {
                'value': '30',
                'type': 'int',
                'description': 'API请求超时时间(秒)',
                'encrypted': False
            },
            'adata.retry_times': {
                'value': '3',
                'type': 'int',
                'description': 'API请求重试次数',
                'encrypted': False
            },
            
            # 策略配置
            'strategy.max_position_size': {
                'value': '0.1',
                'type': 'float',
                'description': '单股最大仓位比例',
                'encrypted': False
            },
            'strategy.total_position_limit': {
                'value': '0.8',
                'type': 'float',
                'description': '总仓位限制',
                'encrypted': False
            },
            'strategy.max_drawdown_limit': {
                'value': '0.15',
                'type': 'float',
                'description': '最大回撤限制',
                'encrypted': False
            },
            'strategy.stop_loss_pct': {
                'value': '0.05',
                'type': 'float',
                'description': '止损比例',
                'encrypted': False
            },
            'strategy.take_profit_pct': {
                'value': '0.15',
                'type': 'float',
                'description': '止盈比例',
                'encrypted': False
            },
            
            # 系统配置
            'system.log_level': {
                'value': 'INFO',
                'type': 'string',
                'description': '日志级别',
                'encrypted': False
            },
            'system.max_log_files': {
                'value': '30',
                'type': 'int',
                'description': '最大日志文件数',
                'encrypted': False
            },
            'system.performance_monitor_interval': {
                'value': '60',
                'type': 'int',
                'description': '性能监控间隔(秒)',
                'encrypted': False
            },
            'system.session_timeout': {
                'value': '3600',
                'type': 'int',
                'description': '会话超时时间(秒)',
                'encrypted': False
            },
            
            # 因子配置
            'factor.default_weight': {
                'value': '0.05',
                'type': 'float',
                'description': '默认因子权重',
                'encrypted': False
            },
            'factor.min_weight': {
                'value': '0.01',
                'type': 'float',
                'description': '最小因子权重',
                'encrypted': False
            },
            'factor.max_weight': {
                'value': '0.3',
                'type': 'float',
                'description': '最大因子权重',
                'encrypted': False
            },
            'factor.rebalance_threshold': {
                'value': '0.02',
                'type': 'float',
                'description': '权重再平衡阈值',
                'encrypted': False
            },
            
            # 回测配置
            'backtest.initial_capital': {
                'value': '100000',
                'type': 'float',
                'description': '初始资金',
                'encrypted': False
            },
            'backtest.commission_rate': {
                'value': '0.0003',
                'type': 'float',
                'description': '手续费率',
                'encrypted': False
            },
            'backtest.slippage': {
                'value': '0.001',
                'type': 'float',
                'description': '滑点',
                'encrypted': False
            }
        }
    
    def _initialize_configs(self) -> None:
        """初始化配置"""
        try:
            # 从数据库加载配置
            self._load_from_database()
            
            # 如果数据库为空，使用默认配置
            if not self.config_cache:
                self._load_default_configs()
                self._save_to_database()
            
            # 保存到文件
            self._save_to_file()
            
        except Exception as e:
            logger.error(f"❌ 初始化配置失败: {e}")
            # 使用默认配置
            self._load_default_configs()
    
    def _load_from_database(self) -> None:
        """从数据库加载配置"""
        try:
            with db_manager.get_session_context() as session:
                configs = session.query(SystemConfig).all()
                
                for config in configs:
                    value = config.config_value
                    
                    # 解密加密的配置
                    if config.is_encrypted:
                        try:
                            value = self.cipher.decrypt(value.encode()).decode()
                        except Exception as e:
                            logger.error(f"❌ 解密配置失败: {config.config_key} - {e}")
                            continue
                    
                    # 类型转换
                    if config.config_type == 'int':
                        value = int(value)
                    elif config.config_type == 'float':
                        value = float(value)
                    elif config.config_type == 'bool':
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif config.config_type == 'json':
                        value = json.loads(value)
                    
                    self.config_cache[config.config_key] = value
                
                self.cache_timestamp = datetime.now()
                
                logger.info(f"✅ 从数据库加载配置: {len(self.config_cache)}项")
                
        except Exception as e:
            logger.error(f"❌ 从数据库加载配置失败: {e}")
    
    def _load_default_configs(self) -> None:
        """加载默认配置"""
        try:
            for key, config_info in self.default_configs.items():
                value = config_info['value']
                
                # 类型转换
                if config_info['type'] == 'int':
                    value = int(value)
                elif config_info['type'] == 'float':
                    value = float(value)
                elif config_info['type'] == 'bool':
                    value = value.lower() in ('true', '1', 'yes', 'on')
                
                self.config_cache[key] = value
            
            self.cache_timestamp = datetime.now()
            
            logger.info(f"✅ 加载默认配置: {len(self.config_cache)}项")
            
        except Exception as e:
            logger.error(f"❌ 加载默认配置失败: {e}")
    
    def _save_to_database(self) -> None:
        """保存配置到数据库"""
        try:
            with db_manager.get_session() as session:
                for key, value in self.config_cache.items():
                    # 获取配置信息
                    config_info = self.default_configs.get(key, {})
                    config_type = config_info.get('type', 'string')
                    description = config_info.get('description', '')
                    is_encrypted = config_info.get('encrypted', False)
                    
                    # 转换为字符串
                    if isinstance(value, (dict, list)):
                        str_value = json.dumps(value, ensure_ascii=False)
                        config_type = 'json'
                    else:
                        str_value = str(value)
                    
                    # 加密敏感配置
                    if is_encrypted:
                        str_value = self.cipher.encrypt(str_value.encode()).decode()
                    
                    # 更新或插入配置
                    config = session.query(SystemConfig).filter(
                        SystemConfig.config_key == key
                    ).first()
                    
                    if config:
                        config.config_value = str_value
                        config.config_type = config_type
                        config.description = description
                        config.is_encrypted = is_encrypted
                        config.updated_at = datetime.now()
                    else:
                        config = SystemConfig(
                            config_key=key,
                            config_value=str_value,
                            config_type=config_type,
                            description=description,
                            is_encrypted=is_encrypted
                        )
                        session.add(config)
                
                session.commit()
                
                logger.info(f"✅ 保存配置到数据库: {len(self.config_cache)}项")
                
        except Exception as e:
            logger.error(f"❌ 保存配置到数据库失败: {e}")
    
    def _save_to_file(self) -> None:
        """保存配置到文件"""
        try:
            # 只保存非敏感配置到文件
            file_config = {}
            
            for key, value in self.config_cache.items():
                config_info = self.default_configs.get(key, {})
                if not config_info.get('encrypted', False):
                    file_config[key] = value
            
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(file_config, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"✅ 保存配置到文件: {len(file_config)}项")
            
        except Exception as e:
            logger.error(f"❌ 保存配置到文件失败: {e}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            # 检查缓存是否过期
            if self._is_cache_expired():
                self._refresh_cache()
            
            return self.config_cache.get(key, default)
            
        except Exception as e:
            logger.error(f"❌ 获取配置失败: {key} - {e}")
            return default
    
    def set_config(self, key: str, value: Any, save_immediately: bool = True) -> bool:
        """设置配置值"""
        try:
            # 更新缓存
            self.config_cache[key] = value
            self.cache_timestamp = datetime.now()
            
            if save_immediately:
                # 保存到数据库
                self._save_single_config(key, value)
                
                # 保存到文件（如果不是敏感配置）
                config_info = self.default_configs.get(key, {})
                if not config_info.get('encrypted', False):
                    self._save_to_file()
            
            logger.info(f"✅ 设置配置: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置配置失败: {key} - {e}")
            return False
    
    def _save_single_config(self, key: str, value: Any) -> None:
        """保存单个配置到数据库"""
        try:
            with db_manager.get_session() as session:
                # 获取配置信息
                config_info = self.default_configs.get(key, {})
                config_type = config_info.get('type', 'string')
                description = config_info.get('description', '')
                is_encrypted = config_info.get('encrypted', False)
                
                # 转换为字符串
                if isinstance(value, (dict, list)):
                    str_value = json.dumps(value, ensure_ascii=False)
                    config_type = 'json'
                else:
                    str_value = str(value)
                
                # 加密敏感配置
                if is_encrypted:
                    str_value = self.cipher.encrypt(str_value.encode()).decode()
                
                # 更新或插入配置
                config = session.query(SystemConfig).filter(
                    SystemConfig.config_key == key
                ).first()
                
                if config:
                    config.config_value = str_value
                    config.config_type = config_type
                    config.description = description
                    config.is_encrypted = is_encrypted
                    config.updated_at = datetime.now()
                else:
                    config = SystemConfig(
                        config_key=key,
                        config_value=str_value,
                        config_type=config_type,
                        description=description,
                        is_encrypted=is_encrypted
                    )
                    session.add(config)
                
                session.commit()
                
        except Exception as e:
            logger.error(f"❌ 保存单个配置失败: {key} - {e}")
    
    def delete_config(self, key: str) -> bool:
        """删除配置"""
        try:
            # 从缓存删除
            if key in self.config_cache:
                del self.config_cache[key]
            
            # 从数据库删除
            with db_manager.get_session() as session:
                config = session.query(SystemConfig).filter(
                    SystemConfig.config_key == key
                ).first()
                
                if config:
                    session.delete(config)
                    session.commit()
            
            # 更新文件
            self._save_to_file()
            
            logger.info(f"✅ 删除配置: {key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除配置失败: {key} - {e}")
            return False
    
    def get_all_configs(self, include_encrypted: bool = False) -> Dict[str, Any]:
        """获取所有配置"""
        try:
            if self._is_cache_expired():
                self._refresh_cache()
            
            if include_encrypted:
                return self.config_cache.copy()
            else:
                # 过滤掉敏感配置
                filtered_configs = {}
                for key, value in self.config_cache.items():
                    config_info = self.default_configs.get(key, {})
                    if not config_info.get('encrypted', False):
                        filtered_configs[key] = value
                
                return filtered_configs
                
        except Exception as e:
            logger.error(f"❌ 获取所有配置失败: {e}")
            return {}
    
    def _is_cache_expired(self) -> bool:
        """检查缓存是否过期"""
        if self.cache_timestamp is None:
            return True
        
        elapsed = (datetime.now() - self.cache_timestamp).total_seconds()
        return elapsed > self.cache_ttl
    
    def _refresh_cache(self) -> None:
        """刷新缓存"""
        try:
            self._load_from_database()
            logger.debug("🔄 配置缓存已刷新")
            
        except Exception as e:
            logger.error(f"❌ 刷新配置缓存失败: {e}")
    
    def batch_update_configs(self, configs: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            # 更新缓存
            self.config_cache.update(configs)
            self.cache_timestamp = datetime.now()
            
            # 批量保存到数据库
            with db_manager.get_session() as session:
                for key, value in configs.items():
                    # 获取配置信息
                    config_info = self.default_configs.get(key, {})
                    config_type = config_info.get('type', 'string')
                    description = config_info.get('description', '')
                    is_encrypted = config_info.get('encrypted', False)
                    
                    # 转换为字符串
                    if isinstance(value, (dict, list)):
                        str_value = json.dumps(value, ensure_ascii=False)
                        config_type = 'json'
                    else:
                        str_value = str(value)
                    
                    # 加密敏感配置
                    if is_encrypted:
                        str_value = self.cipher.encrypt(str_value.encode()).decode()
                    
                    # 更新或插入配置
                    config = session.query(SystemConfig).filter(
                        SystemConfig.config_key == key
                    ).first()
                    
                    if config:
                        config.config_value = str_value
                        config.config_type = config_type
                        config.description = description
                        config.is_encrypted = is_encrypted
                        config.updated_at = datetime.now()
                    else:
                        config = SystemConfig(
                            config_key=key,
                            config_value=str_value,
                            config_type=config_type,
                            description=description,
                            is_encrypted=is_encrypted
                        )
                        session.add(config)
                
                session.commit()
            
            # 更新文件
            self._save_to_file()
            
            logger.info(f"✅ 批量更新配置: {len(configs)}项")
            return True
            
        except Exception as e:
            logger.error(f"❌ 批量更新配置失败: {e}")
            return False
    
    def export_configs(self, file_path: str, include_encrypted: bool = False) -> bool:
        """导出配置到文件"""
        try:
            configs = self.get_all_configs(include_encrypted)
            
            export_path = Path(file_path)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(configs, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 导出配置到文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出配置失败: {e}")
            return False
    
    def import_configs(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            import_path = Path(file_path)
            
            if not import_path.exists():
                logger.error(f"❌ 配置文件不存在: {file_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                configs = json.load(f)
            
            return self.batch_update_configs(configs)
            
        except Exception as e:
            logger.error(f"❌ 导入配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        try:
            all_configs = self.get_all_configs(include_encrypted=True)
            
            # 按类别统计
            category_stats = {}
            for key in all_configs.keys():
                category = key.split('.')[0] if '.' in key else 'other'
                category_stats[category] = category_stats.get(category, 0) + 1
            
            # 敏感配置统计
            encrypted_count = sum(
                1 for key in all_configs.keys()
                if self.default_configs.get(key, {}).get('encrypted', False)
            )
            
            return {
                'total_configs': len(all_configs),
                'encrypted_configs': encrypted_count,
                'category_stats': category_stats,
                'cache_timestamp': self.cache_timestamp.isoformat() if self.cache_timestamp else None,
                'cache_ttl': self.cache_ttl
            }
            
        except Exception as e:
            logger.error(f"❌ 获取配置摘要失败: {e}")
            return {}
