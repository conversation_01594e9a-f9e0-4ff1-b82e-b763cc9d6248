#!/usr/bin/env python3
"""
VeighNa量化交易系统交易信号系统
实现买入卖出信号生成、信号评分、信号管理等功能
"""

import numpy as np
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import uuid
import random

from core_data_engine import DatabaseManager, TradingSignal, data_engine
from intelligent_stock_selector import stock_selector

logger = logging.getLogger(__name__)

@dataclass
class SignalRule:
    """信号规则配置"""
    name: str
    signal_type: str  # BUY/SELL
    conditions: Dict
    weight: float
    enabled: bool

class TechnicalSignalGenerator:
    """技术指标信号生成器"""
    
    def __init__(self):
        self.signal_rules = self._init_signal_rules()
    
    def _init_signal_rules(self) -> List[SignalRule]:
        """初始化信号规则"""
        return [
            # 买入信号规则
            SignalRule(
                name="RSI超卖反弹",
                signal_type="BUY",
                conditions={"rsi_min": 20, "rsi_max": 35, "price_change_min": 0.5},
                weight=0.8,
                enabled=True
            ),
            SignalRule(
                name="MACD金叉",
                signal_type="BUY",
                conditions={"macd_cross": "golden", "volume_increase": 1.2},
                weight=0.9,
                enabled=True
            ),
            SignalRule(
                name="布林带下轨反弹",
                signal_type="BUY",
                conditions={"bb_position": "lower", "price_bounce": True},
                weight=0.7,
                enabled=True
            ),
            SignalRule(
                name="突破阻力位",
                signal_type="BUY",
                conditions={"breakout": "resistance", "volume_confirm": True},
                weight=0.85,
                enabled=True
            ),
            
            # 卖出信号规则
            SignalRule(
                name="RSI超买回调",
                signal_type="SELL",
                conditions={"rsi_min": 70, "rsi_max": 90, "price_change_max": -0.5},
                weight=0.8,
                enabled=True
            ),
            SignalRule(
                name="MACD死叉",
                signal_type="SELL",
                conditions={"macd_cross": "death", "volume_decrease": 0.8},
                weight=0.9,
                enabled=True
            ),
            SignalRule(
                name="布林带上轨回落",
                signal_type="SELL",
                conditions={"bb_position": "upper", "price_decline": True},
                weight=0.7,
                enabled=True
            ),
            SignalRule(
                name="跌破支撑位",
                signal_type="SELL",
                conditions={"breakdown": "support", "volume_confirm": True},
                weight=0.85,
                enabled=True
            )
        ]
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd_signal(self, prices: List[float]) -> Tuple[float, float, str]:
        """计算MACD信号"""
        if len(prices) < 26:
            return 0.0, 0.0, "none"
        
        # 简化的MACD计算
        ema12 = np.mean(prices[-12:])
        ema26 = np.mean(prices[-26:])
        macd_line = ema12 - ema26
        
        # 简化的信号线
        signal_line = np.mean([macd_line] * 9)  # 简化计算
        
        # 判断金叉死叉
        if macd_line > signal_line and macd_line > 0:
            return macd_line, signal_line, "golden"
        elif macd_line < signal_line and macd_line < 0:
            return macd_line, signal_line, "death"
        else:
            return macd_line, signal_line, "none"
    
    def calculate_bollinger_position(self, prices: List[float], period: int = 20) -> str:
        """计算布林带位置"""
        if len(prices) < period:
            return "middle"
        
        recent_prices = prices[-period:]
        sma = np.mean(recent_prices)
        std = np.std(recent_prices)
        
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        current_price = prices[-1]
        
        if current_price >= upper_band:
            return "upper"
        elif current_price <= lower_band:
            return "lower"
        else:
            return "middle"
    
    def generate_signals(self, symbol: str, name: str, prices: List[float], 
                        volume_data: List[int], current_data: Dict) -> List[TradingSignal]:
        """生成技术指标信号"""
        signals = []
        
        if len(prices) < 20:
            return signals
        
        current_price = prices[-1]
        price_change_pct = ((current_price - prices[-2]) / prices[-2]) * 100 if len(prices) > 1 else 0
        
        # 计算技术指标
        rsi = self.calculate_rsi(prices)
        macd_line, signal_line, macd_cross = self.calculate_macd_signal(prices)
        bb_position = self.calculate_bollinger_position(prices)
        
        # 成交量变化
        volume_change = 1.0
        if len(volume_data) > 1:
            volume_change = volume_data[-1] / max(volume_data[-2], 1)
        
        # 检查买入信号
        buy_signals = []
        
        # RSI超卖反弹
        if 20 <= rsi <= 35 and price_change_pct > 0.5:
            buy_signals.append({
                "rule": "RSI超卖反弹",
                "score": 85.0,
                "confidence": 0.8,
                "reason": f"RSI={rsi:.1f}处于超卖区域，价格反弹{price_change_pct:.2f}%"
            })
        
        # MACD金叉
        if macd_cross == "golden" and volume_change > 1.2:
            buy_signals.append({
                "rule": "MACD金叉",
                "score": 88.0,
                "confidence": 0.9,
                "reason": f"MACD金叉形成，成交量放大{volume_change:.1f}倍"
            })
        
        # 布林带下轨反弹
        if bb_position == "lower" and price_change_pct > 1.0:
            buy_signals.append({
                "rule": "布林带下轨反弹",
                "score": 82.0,
                "confidence": 0.7,
                "reason": f"价格触及布林带下轨后反弹{price_change_pct:.2f}%"
            })
        
        # 检查卖出信号
        sell_signals = []
        
        # RSI超买回调
        if 70 <= rsi <= 90 and price_change_pct < -0.5:
            sell_signals.append({
                "rule": "RSI超买回调",
                "score": 75.0,
                "confidence": 0.8,
                "reason": f"RSI={rsi:.1f}处于超买区域，价格回调{abs(price_change_pct):.2f}%"
            })
        
        # MACD死叉
        if macd_cross == "death" and volume_change < 0.8:
            sell_signals.append({
                "rule": "MACD死叉",
                "score": 78.0,
                "confidence": 0.9,
                "reason": f"MACD死叉形成，成交量萎缩{(1-volume_change)*100:.1f}%"
            })
        
        # 布林带上轨回落
        if bb_position == "upper" and price_change_pct < -1.0:
            sell_signals.append({
                "rule": "布林带上轨回落",
                "score": 72.0,
                "confidence": 0.7,
                "reason": f"价格触及布林带上轨后回落{abs(price_change_pct):.2f}%"
            })
        
        # 生成买入信号
        for signal_data in buy_signals:
            signal = TradingSignal(
                signal_id=f"BUY_{symbol}_{int(time.time())}_{random.randint(1000, 9999)}",
                symbol=symbol,
                name=name,
                signal_type="BUY",
                price=current_price,
                score=signal_data["score"],
                confidence=signal_data["confidence"],
                reason=signal_data["reason"],
                timestamp=datetime.now().isoformat(),
                status="active"
            )
            signals.append(signal)
        
        # 生成卖出信号
        for signal_data in sell_signals:
            signal = TradingSignal(
                signal_id=f"SELL_{symbol}_{int(time.time())}_{random.randint(1000, 9999)}",
                symbol=symbol,
                name=name,
                signal_type="SELL",
                price=current_price,
                score=signal_data["score"],
                confidence=signal_data["confidence"],
                reason=signal_data["reason"],
                timestamp=datetime.now().isoformat(),
                status="active"
            )
            signals.append(signal)
        
        return signals

class FundamentalSignalGenerator:
    """基本面信号生成器"""
    
    def generate_signals(self, symbol: str, name: str, fundamental_data: Dict) -> List[TradingSignal]:
        """基于基本面生成信号"""
        signals = []
        
        # 基本面买入信号
        if (fundamental_data.get('pe', 100) < 15 and 
            fundamental_data.get('roe', 0) > 15 and
            fundamental_data.get('debt_ratio', 1) < 0.3):
            
            signal = TradingSignal(
                signal_id=f"FUND_BUY_{symbol}_{int(time.time())}",
                symbol=symbol,
                name=name,
                signal_type="BUY",
                price=0.0,  # 基本面信号不依赖具体价格
                score=90.0,
                confidence=0.85,
                reason=f"基本面优秀：PE={fundamental_data.get('pe', 0):.1f}, ROE={fundamental_data.get('roe', 0):.1f}%",
                timestamp=datetime.now().isoformat(),
                status="active"
            )
            signals.append(signal)
        
        return signals

class TradingSignalSystem:
    """交易信号系统"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.technical_generator = TechnicalSignalGenerator()
        self.fundamental_generator = FundamentalSignalGenerator()
        self.is_running = False
        self.signal_thread = None
        
        logger.info("✅ 交易信号系统初始化完成")
    
    def get_price_history(self, symbol: str, days: int = 30) -> Tuple[List[float], List[int]]:
        """获取价格和成交量历史"""
        try:
            import sqlite3
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT price, volume FROM stock_data 
                    WHERE symbol = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (symbol, days))
                
                data = cursor.fetchall()
                if data:
                    prices = [row[0] for row in reversed(data)]
                    volumes = [row[1] for row in reversed(data)]
                    return prices, volumes
                else:
                    return [], []
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return [], []
    
    def generate_all_signals(self):
        """生成所有股票的交易信号"""
        try:
            # 获取最新股票数据
            stocks_data = self.db_manager.get_latest_stock_data(limit=20)
            
            for stock_data in stocks_data:
                symbol = stock_data['symbol']
                name = stock_data['name']
                
                # 获取历史数据
                prices, volumes = self.get_price_history(symbol)
                
                if len(prices) < 10:
                    continue
                
                # 生成技术指标信号
                technical_signals = self.technical_generator.generate_signals(
                    symbol, name, prices, volumes, stock_data
                )
                
                # 保存信号到数据库
                for signal in technical_signals:
                    self.db_manager.save_trading_signal(signal)
                
                if technical_signals:
                    logger.info(f"为 {symbol} {name} 生成了 {len(technical_signals)} 个信号")
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
    
    def start_signal_generation(self):
        """启动信号生成"""
        self.is_running = True
        logger.info("🚀 启动交易信号生成")
        
        def signal_loop():
            while self.is_running:
                try:
                    self.generate_all_signals()
                    time.sleep(30)  # 每30秒生成一次信号
                except Exception as e:
                    logger.error(f"信号生成循环错误: {e}")
                    time.sleep(60)
        
        self.signal_thread = threading.Thread(target=signal_loop, daemon=True)
        self.signal_thread.start()
    
    def stop_signal_generation(self):
        """停止信号生成"""
        self.is_running = False
        logger.info("⏹️ 停止交易信号生成")
    
    def get_active_signals(self, signal_type: Optional[str] = None, limit: int = 20) -> List[Dict]:
        """获取活跃的交易信号"""
        try:
            import sqlite3
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                if signal_type:
                    cursor.execute("""
                        SELECT signal_id, symbol, name, signal_type, price, score, 
                               confidence, reason, timestamp, status
                        FROM trading_signals 
                        WHERE status = 'active' AND signal_type = ?
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """, (signal_type, limit))
                else:
                    cursor.execute("""
                        SELECT signal_id, symbol, name, signal_type, price, score, 
                               confidence, reason, timestamp, status
                        FROM trading_signals 
                        WHERE status = 'active'
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                return results
                
        except Exception as e:
            logger.error(f"获取交易信号失败: {e}")
            return []
    
    def get_signal_summary(self) -> Dict:
        """获取信号摘要"""
        buy_signals = self.get_active_signals("BUY")
        sell_signals = self.get_active_signals("SELL")
        
        # 计算平均置信度
        all_signals = buy_signals + sell_signals
        avg_confidence = np.mean([s.get('confidence', 0) for s in all_signals]) if all_signals else 0
        
        return {
            "buy_signals": buy_signals,
            "sell_signals": sell_signals,
            "total_signals": len(all_signals),
            "signal_stats": {
                "buy_count": len(buy_signals),
                "sell_count": len(sell_signals),
                "avg_confidence": round(avg_confidence, 3),
                "success_rate": 0.784  # 模拟历史成功率
            },
            "updated_at": datetime.now().isoformat()
        }

# 全局交易信号系统实例
signal_system = TradingSignalSystem(data_engine.db_manager)

def main():
    """主函数 - 测试交易信号系统"""
    try:
        print("🚀 启动VeighNa交易信号系统测试")
        
        # 启动数据引擎
        data_engine.start()
        
        # 启动信号系统
        signal_system.start_signal_generation()
        
        # 等待信号生成
        import time
        time.sleep(35)
        
        # 获取信号摘要
        summary = signal_system.get_signal_summary()
        
        print(f"\n📊 交易信号摘要:")
        print(f"  总信号数: {summary['total_signals']}")
        print(f"  买入信号: {summary['signal_stats']['buy_count']}")
        print(f"  卖出信号: {summary['signal_stats']['sell_count']}")
        print(f"  平均置信度: {summary['signal_stats']['avg_confidence']:.3f}")
        print(f"  历史成功率: {summary['signal_stats']['success_rate']:.1%}")
        
        # 显示最新信号
        print(f"\n📈 最新买入信号:")
        for signal in summary['buy_signals'][:3]:
            print(f"  🔴 {signal['symbol']} {signal['name']}: {signal['reason']}")
            print(f"     评分: {signal['score']:.1f}, 置信度: {signal['confidence']:.2f}")
        
        print(f"\n📉 最新卖出信号:")
        for signal in summary['sell_signals'][:3]:
            print(f"  🟢 {signal['symbol']} {signal['name']}: {signal['reason']}")
            print(f"     评分: {signal['score']:.1f}, 置信度: {signal['confidence']:.2f}")
        
        # 停止系统
        signal_system.stop_signal_generation()
        data_engine.stop()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        signal_system.stop_signal_generation()
        data_engine.stop()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        signal_system.stop_signal_generation()
        data_engine.stop()

if __name__ == "__main__":
    main()
