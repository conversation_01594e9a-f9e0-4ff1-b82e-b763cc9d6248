#!/usr/bin/env python3
"""
VeighNa量化交易系统智能选股算法引擎
基于全A股真实数据的多因子选股算法
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.system_config import trading_config, TECHNICAL_INDICATORS, FUNDAMENTAL_INDICATORS
from database.database_schema import (
    DatabaseManager, StockBasicInfo, StockDailyData, 
    StockTechnicalIndicators, StockFundamentalData, StockSelectionResults
)

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """股票评分结果"""
    symbol: str
    name: str
    exchange: str
    total_score: float
    technical_score: float
    fundamental_score: float
    market_score: float
    rank: int
    recommendation: str
    reason: str
    details: Dict
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'name': self.name,
            'exchange': self.exchange,
            'total_score': self.total_score,
            'technical_score': self.technical_score,
            'fundamental_score': self.fundamental_score,
            'market_score': self.market_score,
            'rank': self.rank,
            'recommendation': self.recommendation,
            'reason': self.reason,
            'details': self.details
        }

class TechnicalAnalyzer:
    """技术分析器"""
    
    def __init__(self):
        self.indicators_weights = {
            'trend': 0.4,      # 趋势指标权重40%
            'momentum': 0.35,  # 动量指标权重35%
            'volume': 0.25     # 成交量指标权重25%
        }
    
    def analyze_trend_indicators(self, tech_data: Dict) -> float:
        """分析趋势指标"""
        try:
            score = 0.0
            
            # MACD分析 (30分)
            macd_dif = tech_data.get('macd_dif', 0)
            macd_dea = tech_data.get('macd_dea', 0)
            macd_histogram = tech_data.get('macd_histogram', 0)
            
            if macd_dif > macd_dea and macd_histogram > 0:
                score += 30  # MACD金叉且柱状图为正
            elif macd_dif > macd_dea:
                score += 20  # 仅MACD金叉
            elif macd_histogram > 0:
                score += 15  # 仅柱状图为正
            else:
                score += 5   # 趋势较弱
            
            # 移动平均线分析 (25分)
            sma_5 = tech_data.get('sma_5', 0)
            sma_10 = tech_data.get('sma_10', 0)
            sma_20 = tech_data.get('sma_20', 0)
            current_price = tech_data.get('close_price', 0)
            
            if current_price > sma_5 > sma_10 > sma_20:
                score += 25  # 完美多头排列
            elif current_price > sma_10 > sma_20:
                score += 20  # 中期多头排列
            elif current_price > sma_20:
                score += 15  # 长期多头
            else:
                score += 5   # 趋势偏弱
            
            # 布林带分析 (20分)
            boll_upper = tech_data.get('boll_upper', 0)
            boll_middle = tech_data.get('boll_middle', 0)
            boll_lower = tech_data.get('boll_lower', 0)
            
            if current_price > boll_middle:
                if current_price < boll_upper * 0.95:  # 接近上轨但未突破
                    score += 20
                elif current_price > boll_upper:  # 突破上轨
                    score += 15
                else:
                    score += 10
            else:
                if current_price > boll_lower * 1.05:  # 接近下轨
                    score += 15  # 可能反弹
                else:
                    score += 5
            
            # EMA分析 (25分)
            ema_12 = tech_data.get('ema_12', 0)
            ema_26 = tech_data.get('ema_26', 0)
            
            if ema_12 > ema_26 and current_price > ema_12:
                score += 25  # EMA多头排列
            elif ema_12 > ema_26:
                score += 20  # EMA金叉
            elif current_price > ema_12:
                score += 15  # 价格在短期EMA之上
            else:
                score += 5
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"趋势指标分析失败: {e}")
            return 50.0
    
    def analyze_momentum_indicators(self, tech_data: Dict, daily_data: List[Dict]) -> float:
        """分析动量指标"""
        try:
            score = 0.0
            
            # RSI分析 (40分)
            rsi_14 = tech_data.get('rsi_14', 50)
            
            if 30 <= rsi_14 <= 70:
                score += 40  # RSI在正常区间
            elif 20 <= rsi_14 < 30:
                score += 35  # 超卖区域，可能反弹
            elif 70 < rsi_14 <= 80:
                score += 30  # 超买区域，但仍可持续
            elif rsi_14 < 20:
                score += 25  # 严重超卖
            else:  # rsi_14 > 80
                score += 20  # 严重超买
            
            # 价格动量分析 (30分)
            if len(daily_data) >= 5:
                recent_prices = [d['close_price'] for d in daily_data[-5:]]
                price_momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] * 100
                
                if 0 < price_momentum <= 3:
                    score += 30  # 温和上涨
                elif 3 < price_momentum <= 7:
                    score += 25  # 强势上涨
                elif price_momentum > 7:
                    score += 20  # 过度上涨
                elif -3 <= price_momentum < 0:
                    score += 15  # 小幅下跌
                else:
                    score += 10  # 大幅下跌
            else:
                score += 15  # 数据不足，给予中等分数
            
            # 成交量动量分析 (30分)
            if len(daily_data) >= 10:
                recent_volumes = [d['volume'] for d in daily_data[-10:]]
                avg_volume = np.mean(recent_volumes[:-1])
                current_volume = recent_volumes[-1]
                
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
                
                if 1.2 <= volume_ratio <= 2.0:
                    score += 30  # 成交量适度放大
                elif 2.0 < volume_ratio <= 3.0:
                    score += 25  # 成交量大幅放大
                elif volume_ratio > 3.0:
                    score += 20  # 成交量异常放大
                elif 0.8 <= volume_ratio < 1.2:
                    score += 20  # 成交量正常
                else:
                    score += 10  # 成交量萎缩
            else:
                score += 15
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"动量指标分析失败: {e}")
            return 50.0
    
    def analyze_volume_indicators(self, daily_data: List[Dict]) -> float:
        """分析成交量指标"""
        try:
            if len(daily_data) < 20:
                return 50.0
            
            score = 0.0
            
            # 成交量趋势分析 (50分)
            volumes = [d['volume'] for d in daily_data[-20:]]
            prices = [d['close_price'] for d in daily_data[-20:]]
            
            # 计算量价关系
            volume_trend = np.polyfit(range(len(volumes)), volumes, 1)[0]
            price_trend = np.polyfit(range(len(prices)), prices, 1)[0]
            
            if volume_trend > 0 and price_trend > 0:
                score += 50  # 量价齐升
            elif volume_trend > 0 and price_trend < 0:
                score += 20  # 量增价跌，可能见底
            elif volume_trend < 0 and price_trend > 0:
                score += 30  # 量缩价涨，可能调整
            else:
                score += 15  # 量价齐跌
            
            # 换手率分析 (50分)
            recent_turnover = [d.get('turnover_rate', 0) for d in daily_data[-5:]]
            avg_turnover = np.mean(recent_turnover) if recent_turnover else 0
            
            if 2 <= avg_turnover <= 8:
                score += 50  # 换手率适中
            elif 8 < avg_turnover <= 15:
                score += 40  # 换手率较高
            elif avg_turnover > 15:
                score += 25  # 换手率过高
            elif 1 <= avg_turnover < 2:
                score += 30  # 换手率偏低
            else:
                score += 20  # 换手率极低
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"成交量指标分析失败: {e}")
            return 50.0
    
    def calculate_technical_score(self, symbol: str, tech_data: Dict, daily_data: List[Dict]) -> Tuple[float, Dict]:
        """计算技术面综合评分"""
        try:
            # 分析各类指标
            trend_score = self.analyze_trend_indicators(tech_data)
            momentum_score = self.analyze_momentum_indicators(tech_data, daily_data)
            volume_score = self.analyze_volume_indicators(daily_data)
            
            # 计算加权总分
            total_score = (
                trend_score * self.indicators_weights['trend'] +
                momentum_score * self.indicators_weights['momentum'] +
                volume_score * self.indicators_weights['volume']
            )
            
            details = {
                'trend_score': trend_score,
                'momentum_score': momentum_score,
                'volume_score': volume_score,
                'weights': self.indicators_weights
            }
            
            return total_score, details
            
        except Exception as e:
            logger.error(f"计算{symbol}技术评分失败: {e}")
            return 50.0, {}

class FundamentalAnalyzer:
    """基本面分析器"""
    
    def __init__(self):
        self.factor_weights = {
            'valuation': 0.3,     # 估值指标权重30%
            'profitability': 0.4, # 盈利能力权重40%
            'growth': 0.3         # 成长性权重30%
        }
    
    def analyze_valuation(self, fund_data: Dict) -> float:
        """分析估值指标"""
        try:
            score = 0.0
            
            # PE分析 (40分)
            pe_ratio = fund_data.get('pe_ratio', 0)
            if 0 < pe_ratio <= 15:
                score += 40  # 低估值
            elif 15 < pe_ratio <= 25:
                score += 35  # 合理估值
            elif 25 < pe_ratio <= 40:
                score += 25  # 偏高估值
            elif pe_ratio > 40:
                score += 15  # 高估值
            else:
                score += 20  # 数据异常
            
            # PB分析 (30分)
            pb_ratio = fund_data.get('pb_ratio', 0)
            if 0 < pb_ratio <= 2:
                score += 30  # 低市净率
            elif 2 < pb_ratio <= 4:
                score += 25  # 合理市净率
            elif 4 < pb_ratio <= 8:
                score += 20  # 偏高市净率
            else:
                score += 15  # 高市净率
            
            # PS分析 (30分)
            ps_ratio = fund_data.get('ps_ratio', 0)
            if 0 < ps_ratio <= 3:
                score += 30  # 低市销率
            elif 3 < ps_ratio <= 6:
                score += 25  # 合理市销率
            elif 6 < ps_ratio <= 10:
                score += 20  # 偏高市销率
            else:
                score += 15  # 高市销率
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"估值指标分析失败: {e}")
            return 50.0
    
    def analyze_profitability(self, fund_data: Dict) -> float:
        """分析盈利能力"""
        try:
            score = 0.0
            
            # ROE分析 (40分)
            roe = fund_data.get('roe', 0)
            if roe >= 20:
                score += 40  # 优秀ROE
            elif roe >= 15:
                score += 35  # 良好ROE
            elif roe >= 10:
                score += 25  # 一般ROE
            elif roe >= 5:
                score += 15  # 较差ROE
            else:
                score += 10  # 很差ROE
            
            # 毛利率分析 (30分)
            gross_margin = fund_data.get('gross_profit_margin', 0)
            if gross_margin >= 50:
                score += 30  # 高毛利率
            elif gross_margin >= 30:
                score += 25  # 良好毛利率
            elif gross_margin >= 20:
                score += 20  # 一般毛利率
            elif gross_margin >= 10:
                score += 15  # 较低毛利率
            else:
                score += 10  # 很低毛利率
            
            # 净利率分析 (30分)
            net_margin = fund_data.get('net_profit_margin', 0)
            if net_margin >= 20:
                score += 30  # 高净利率
            elif net_margin >= 10:
                score += 25  # 良好净利率
            elif net_margin >= 5:
                score += 20  # 一般净利率
            elif net_margin >= 2:
                score += 15  # 较低净利率
            else:
                score += 10  # 很低净利率
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"盈利能力分析失败: {e}")
            return 50.0
    
    def analyze_growth(self, fund_data: Dict) -> float:
        """分析成长性"""
        try:
            score = 0.0
            
            # 营收增长率分析 (40分)
            revenue_growth = fund_data.get('revenue_growth', 0)
            if revenue_growth >= 30:
                score += 40  # 高增长
            elif revenue_growth >= 20:
                score += 35  # 良好增长
            elif revenue_growth >= 10:
                score += 25  # 一般增长
            elif revenue_growth >= 0:
                score += 15  # 低增长
            else:
                score += 10  # 负增长
            
            # 净利润增长率分析 (35分)
            profit_growth = fund_data.get('profit_growth', 0)
            if profit_growth >= 30:
                score += 35  # 高增长
            elif profit_growth >= 20:
                score += 30  # 良好增长
            elif profit_growth >= 10:
                score += 25  # 一般增长
            elif profit_growth >= 0:
                score += 15  # 低增长
            else:
                score += 10  # 负增长
            
            # EPS增长率分析 (25分)
            eps_growth = fund_data.get('eps_growth', 0)
            if eps_growth >= 25:
                score += 25  # 高增长
            elif eps_growth >= 15:
                score += 20  # 良好增长
            elif eps_growth >= 5:
                score += 15  # 一般增长
            elif eps_growth >= 0:
                score += 10  # 低增长
            else:
                score += 5   # 负增长
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"成长性分析失败: {e}")
            return 50.0
    
    def calculate_fundamental_score(self, symbol: str, fund_data: Dict) -> Tuple[float, Dict]:
        """计算基本面综合评分"""
        try:
            # 分析各类指标
            valuation_score = self.analyze_valuation(fund_data)
            profitability_score = self.analyze_profitability(fund_data)
            growth_score = self.analyze_growth(fund_data)
            
            # 计算加权总分
            total_score = (
                valuation_score * self.factor_weights['valuation'] +
                profitability_score * self.factor_weights['profitability'] +
                growth_score * self.factor_weights['growth']
            )
            
            details = {
                'valuation_score': valuation_score,
                'profitability_score': profitability_score,
                'growth_score': growth_score,
                'weights': self.factor_weights
            }
            
            return total_score, details
            
        except Exception as e:
            logger.error(f"计算{symbol}基本面评分失败: {e}")
            return 50.0, {}

class MarketAnalyzer:
    """市场表现分析器"""
    
    def analyze_market_performance(self, daily_data: List[Dict]) -> Tuple[float, Dict]:
        """分析市场表现"""
        try:
            if len(daily_data) < 10:
                return 50.0, {}
            
            score = 0.0
            
            # 相对强度分析 (40分)
            recent_returns = []
            for i in range(1, min(21, len(daily_data))):
                prev_price = daily_data[-i-1]['close_price']
                curr_price = daily_data[-i]['close_price']
                if prev_price > 0:
                    daily_return = (curr_price - prev_price) / prev_price
                    recent_returns.append(daily_return)
            
            if recent_returns:
                avg_return = np.mean(recent_returns)
                if avg_return > 0.02:
                    score += 40  # 强势表现
                elif avg_return > 0.01:
                    score += 35  # 良好表现
                elif avg_return > 0:
                    score += 25  # 一般表现
                elif avg_return > -0.01:
                    score += 15  # 弱势表现
                else:
                    score += 10  # 很弱表现
            else:
                score += 20
            
            # 波动率分析 (30分)
            if len(recent_returns) > 5:
                volatility = np.std(recent_returns)
                if volatility < 0.02:
                    score += 30  # 低波动
                elif volatility < 0.04:
                    score += 25  # 中等波动
                elif volatility < 0.06:
                    score += 20  # 较高波动
                else:
                    score += 15  # 高波动
            else:
                score += 20
            
            # 流动性分析 (30分)
            recent_amounts = [d.get('amount', 0) for d in daily_data[-5:]]
            avg_amount = np.mean(recent_amounts) if recent_amounts else 0
            
            if avg_amount > 1000000000:  # 10亿以上
                score += 30  # 高流动性
            elif avg_amount > 500000000:  # 5亿以上
                score += 25  # 良好流动性
            elif avg_amount > 100000000:  # 1亿以上
                score += 20  # 一般流动性
            elif avg_amount > 50000000:   # 5000万以上
                score += 15  # 较低流动性
            else:
                score += 10  # 低流动性
            
            details = {
                'avg_return': avg_return if 'avg_return' in locals() else 0,
                'volatility': volatility if 'volatility' in locals() else 0,
                'avg_amount': avg_amount,
                'liquidity_level': 'high' if avg_amount > 1000000000 else 'medium' if avg_amount > 100000000 else 'low'
            }
            
            return min(100.0, score), details
            
        except Exception as e:
            logger.error(f"市场表现分析失败: {e}")
            return 50.0, {}

class IntelligentStockSelector:
    """智能选股引擎"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.technical_analyzer = TechnicalAnalyzer()
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        
        # 因子权重配置
        self.factor_weights = {
            'technical': trading_config.TECHNICAL_WEIGHT,
            'fundamental': trading_config.FUNDAMENTAL_WEIGHT,
            'market': trading_config.MARKET_WEIGHT
        }
        
        logger.info("✅ 智能选股引擎初始化完成")
    
    def get_stock_data(self, symbol: str) -> Tuple[Dict, List[Dict], Dict]:
        """获取股票数据"""
        try:
            session = self.db_manager.get_session()
            
            # 获取最新技术指标
            tech_data = session.query(StockTechnicalIndicators).filter_by(
                symbol=symbol
            ).order_by(StockTechnicalIndicators.trade_date.desc()).first()
            
            # 获取最近30天日线数据
            daily_data = session.query(StockDailyData).filter_by(
                symbol=symbol
            ).order_by(StockDailyData.trade_date.desc()).limit(30).all()
            
            # 获取最新基本面数据
            fund_data = session.query(StockFundamentalData).filter_by(
                symbol=symbol
            ).order_by(StockFundamentalData.report_date.desc()).first()
            
            session.close()
            
            # 转换为字典格式
            tech_dict = {}
            if tech_data:
                tech_dict = {
                    'macd_dif': tech_data.macd_dif,
                    'macd_dea': tech_data.macd_dea,
                    'macd_histogram': tech_data.macd_histogram,
                    'sma_5': tech_data.sma_5,
                    'sma_10': tech_data.sma_10,
                    'sma_20': tech_data.sma_20,
                    'sma_60': tech_data.sma_60,
                    'ema_12': tech_data.ema_12,
                    'ema_26': tech_data.ema_26,
                    'rsi_14': tech_data.rsi_14,
                    'boll_upper': tech_data.boll_upper,
                    'boll_middle': tech_data.boll_middle,
                    'boll_lower': tech_data.boll_lower
                }
            
            daily_list = []
            if daily_data:
                for d in reversed(daily_data):  # 按时间正序
                    daily_list.append({
                        'trade_date': d.trade_date,
                        'close_price': d.close_price,
                        'volume': d.volume,
                        'amount': d.amount,
                        'turnover_rate': d.turnover_rate
                    })
                
                # 添加最新价格到技术指标
                if daily_list:
                    tech_dict['close_price'] = daily_list[-1]['close_price']
            
            fund_dict = {}
            if fund_data:
                fund_dict = {
                    'pe_ratio': fund_data.pe_ratio,
                    'pb_ratio': fund_data.pb_ratio,
                    'ps_ratio': fund_data.ps_ratio,
                    'roe': fund_data.roe,
                    'gross_profit_margin': fund_data.gross_profit_margin,
                    'net_profit_margin': fund_data.net_profit_margin,
                    'revenue_growth': fund_data.revenue_growth,
                    'profit_growth': fund_data.profit_growth,
                    'eps_growth': fund_data.eps_growth
                }
            
            return tech_dict, daily_list, fund_dict
            
        except Exception as e:
            logger.error(f"获取{symbol}数据失败: {e}")
            return {}, [], {}
    
    def calculate_stock_score(self, stock_info: Dict) -> Optional[StockScore]:
        """计算单只股票综合评分"""
        try:
            symbol = stock_info['symbol']
            name = stock_info['name']
            exchange = stock_info['exchange']
            
            # 获取股票数据
            tech_data, daily_data, fund_data = self.get_stock_data(symbol)
            
            if not tech_data and not daily_data:
                logger.warning(f"⚠️ {symbol} 缺少必要数据")
                return None
            
            # 计算各维度评分
            technical_score, tech_details = self.technical_analyzer.calculate_technical_score(
                symbol, tech_data, daily_data
            )
            
            fundamental_score, fund_details = self.fundamental_analyzer.calculate_fundamental_score(
                symbol, fund_data
            )
            
            market_score, market_details = self.market_analyzer.analyze_market_performance(daily_data)
            
            # 计算综合评分
            total_score = (
                technical_score * self.factor_weights['technical'] +
                fundamental_score * self.factor_weights['fundamental'] +
                market_score * self.factor_weights['market']
            )
            
            # 生成推荐等级和理由
            if total_score >= 85:
                recommendation = "强烈推荐"
                reason = "技术面强势，基本面优秀，市场表现活跃"
            elif total_score >= 75:
                recommendation = "推荐"
                reason = "综合表现良好，具有投资价值"
            elif total_score >= 65:
                recommendation = "观察"
                reason = "表现一般，建议继续观察"
            elif total_score >= 50:
                recommendation = "谨慎"
                reason = "存在一定风险，需谨慎投资"
            else:
                recommendation = "回避"
                reason = "综合表现较差，建议回避"
            
            # 详细信息
            details = {
                'technical_details': tech_details,
                'fundamental_details': fund_details,
                'market_details': market_details,
                'factor_weights': self.factor_weights,
                'data_quality': {
                    'has_technical': bool(tech_data),
                    'has_fundamental': bool(fund_data),
                    'daily_data_count': len(daily_data)
                }
            }
            
            return StockScore(
                symbol=symbol,
                name=name,
                exchange=exchange,
                total_score=round(total_score, 1),
                technical_score=round(technical_score, 1),
                fundamental_score=round(fundamental_score, 1),
                market_score=round(market_score, 1),
                rank=0,  # 将在排序后设置
                recommendation=recommendation,
                reason=reason,
                details=details
            )
            
        except Exception as e:
            logger.error(f"计算{stock_info['symbol']}评分失败: {e}")
            return None

    def select_stocks(self, max_count: int = None, min_score: float = None) -> List[StockScore]:
        """执行智能选股"""
        try:
            if max_count is None:
                max_count = trading_config.MAX_SELECTED_STOCKS
            if min_score is None:
                min_score = trading_config.MIN_STOCK_SCORE

            logger.info(f"🔍 开始智能选股 (最大数量:{max_count}, 最低评分:{min_score})")

            # 获取所有活跃股票
            session = self.db_manager.get_session()
            active_stocks = session.query(StockBasicInfo).filter_by(is_active=True).all()
            session.close()

            if not active_stocks:
                logger.warning("⚠️ 没有找到活跃股票")
                return []

            logger.info(f"📊 开始分析 {len(active_stocks)} 只股票...")

            # 计算每只股票的评分
            stock_scores = []
            processed = 0

            for stock in active_stocks:
                try:
                    stock_info = {
                        'symbol': stock.symbol,
                        'name': stock.name,
                        'exchange': stock.exchange
                    }

                    score = self.calculate_stock_score(stock_info)
                    if score and score.total_score >= min_score:
                        stock_scores.append(score)

                    processed += 1
                    if processed % 50 == 0:
                        logger.info(f"📈 已处理 {processed}/{len(active_stocks)} 只股票")

                except Exception as e:
                    logger.error(f"处理股票{stock.symbol}失败: {e}")
                    continue

            # 按总分排序
            stock_scores.sort(key=lambda x: x.total_score, reverse=True)

            # 设置排名并限制数量
            selected_stocks = stock_scores[:max_count]
            for i, score in enumerate(selected_stocks, 1):
                score.rank = i

            logger.info(f"✅ 智能选股完成，共选出 {len(selected_stocks)} 只股票")

            # 保存选股结果
            self.save_selection_results(selected_stocks)

            return selected_stocks

        except Exception as e:
            logger.error(f"智能选股失败: {e}")
            return []

    def save_selection_results(self, selected_stocks: List[StockScore]):
        """保存选股结果到数据库"""
        try:
            session = self.db_manager.get_session()

            # 生成选股批次ID
            selection_id = f"selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            for stock_score in selected_stocks:
                result = StockSelectionResults(
                    selection_id=selection_id,
                    symbol=stock_score.symbol,
                    selection_date=datetime.now(),
                    total_score=stock_score.total_score,
                    technical_score=stock_score.technical_score,
                    fundamental_score=stock_score.fundamental_score,
                    market_score=stock_score.market_score,
                    rank=stock_score.rank,
                    recommendation=stock_score.recommendation,
                    reason=stock_score.reason,
                    technical_weight=self.factor_weights['technical'],
                    fundamental_weight=self.factor_weights['fundamental'],
                    market_weight=self.factor_weights['market'],
                    is_selected=True
                )
                session.add(result)

            session.commit()
            session.close()

            logger.info(f"✅ 保存选股结果 {len(selected_stocks)} 条 (批次ID: {selection_id})")

        except Exception as e:
            logger.error(f"保存选股结果失败: {e}")
            if session:
                session.rollback()
                session.close()

    def get_latest_selection_results(self, limit: int = 50) -> List[Dict]:
        """获取最新选股结果"""
        try:
            session = self.db_manager.get_session()

            # 获取最新的选股批次
            latest_selection = session.query(StockSelectionResults).order_by(
                StockSelectionResults.selection_date.desc()
            ).first()

            if not latest_selection:
                session.close()
                return []

            # 获取该批次的所有结果
            results = session.query(StockSelectionResults).filter_by(
                selection_id=latest_selection.selection_id
            ).order_by(StockSelectionResults.rank).limit(limit).all()

            session.close()

            # 转换为字典格式
            selection_results = []
            for result in results:
                # 获取股票基本信息
                session = self.db_manager.get_session()
                stock_info = session.query(StockBasicInfo).filter_by(symbol=result.symbol).first()
                session.close()

                selection_results.append({
                    'symbol': result.symbol,
                    'name': stock_info.name if stock_info else result.symbol,
                    'exchange': stock_info.exchange if stock_info else '',
                    'total_score': result.total_score,
                    'technical_score': result.technical_score,
                    'fundamental_score': result.fundamental_score,
                    'market_score': result.market_score,
                    'rank': result.rank,
                    'recommendation': result.recommendation,
                    'reason': result.reason,
                    'selection_date': result.selection_date.isoformat()
                })

            return selection_results

        except Exception as e:
            logger.error(f"获取选股结果失败: {e}")
            return []

    def get_selection_summary(self) -> Dict:
        """获取选股摘要"""
        try:
            selected_stocks = self.get_latest_selection_results()

            if not selected_stocks:
                return {
                    "selected_stocks": [],
                    "selection_criteria": {
                        "technical_weight": self.factor_weights['technical'],
                        "fundamental_weight": self.factor_weights['fundamental'],
                        "market_weight": self.factor_weights['market'],
                        "min_score": trading_config.MIN_STOCK_SCORE,
                        "max_count": trading_config.MAX_SELECTED_STOCKS
                    },
                    "statistics": {
                        "total_selected": 0,
                        "avg_score": 0,
                        "score_distribution": {},
                        "recommendation_distribution": {}
                    },
                    "updated_at": datetime.now().isoformat()
                }

            # 计算统计信息
            total_selected = len(selected_stocks)
            avg_score = sum(stock['total_score'] for stock in selected_stocks) / total_selected

            # 评分分布
            score_ranges = {'90-100': 0, '80-89': 0, '70-79': 0, '60-69': 0, '50-59': 0}
            for stock in selected_stocks:
                score = stock['total_score']
                if score >= 90:
                    score_ranges['90-100'] += 1
                elif score >= 80:
                    score_ranges['80-89'] += 1
                elif score >= 70:
                    score_ranges['70-79'] += 1
                elif score >= 60:
                    score_ranges['60-69'] += 1
                else:
                    score_ranges['50-59'] += 1

            # 推荐等级分布
            recommendation_dist = {}
            for stock in selected_stocks:
                rec = stock['recommendation']
                recommendation_dist[rec] = recommendation_dist.get(rec, 0) + 1

            return {
                "selected_stocks": selected_stocks,
                "selection_criteria": {
                    "technical_weight": self.factor_weights['technical'],
                    "fundamental_weight": self.factor_weights['fundamental'],
                    "market_weight": self.factor_weights['market'],
                    "min_score": trading_config.MIN_STOCK_SCORE,
                    "max_count": trading_config.MAX_SELECTED_STOCKS
                },
                "statistics": {
                    "total_selected": total_selected,
                    "avg_score": round(avg_score, 1),
                    "score_distribution": score_ranges,
                    "recommendation_distribution": recommendation_dist
                },
                "updated_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取选股摘要失败: {e}")
            return {}

def main():
    """主函数 - 测试智能选股引擎"""
    print("🚀 启动VeighNa智能选股引擎测试")

    # 创建数据库管理器
    db_manager = DatabaseManager()

    # 创建智能选股引擎
    selector = IntelligentStockSelector(db_manager)

    # 执行选股
    print("\n🔍 执行智能选股...")
    selected_stocks = selector.select_stocks(max_count=20, min_score=60.0)

    if selected_stocks:
        print(f"\n📊 选股结果 (共 {len(selected_stocks)} 只):")
        print("-" * 100)
        print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'总分':<6} {'技术':<6} {'基本面':<6} {'市场':<6} {'推荐':<8}")
        print("-" * 100)

        for stock in selected_stocks[:10]:  # 显示前10只
            print(f"{stock.rank:<4} {stock.symbol:<8} {stock.name:<12} "
                  f"{stock.total_score:<6.1f} {stock.technical_score:<6.1f} "
                  f"{stock.fundamental_score:<6.1f} {stock.market_score:<6.1f} {stock.recommendation:<8}")

        # 获取选股摘要
        summary = selector.get_selection_summary()
        print(f"\n📈 选股摘要:")
        print(f"  选中股票数: {summary['statistics']['total_selected']}")
        print(f"  平均评分: {summary['statistics']['avg_score']}")
        print(f"  因子权重: 技术面{summary['selection_criteria']['technical_weight']*100:.0f}% "
              f"基本面{summary['selection_criteria']['fundamental_weight']*100:.0f}% "
              f"市场表现{summary['selection_criteria']['market_weight']*100:.0f}%")

        print(f"\n📊 评分分布:")
        for range_name, count in summary['statistics']['score_distribution'].items():
            print(f"  {range_name}: {count}只")

        print(f"\n🎯 推荐分布:")
        for rec, count in summary['statistics']['recommendation_distribution'].items():
            print(f"  {rec}: {count}只")

    else:
        print("❌ 没有选出符合条件的股票")

if __name__ == "__main__":
    main()
