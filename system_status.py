#!/usr/bin/env python3
"""
系统状态检查脚本
检查所有核心模块的运行状态
"""

import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_system_status():
    """检查系统状态"""
    try:
        logger.info("🔍 开始系统状态检查...")
        
        status_report = {
            'database': False,
            'data_collection': False,
            'stock_selector': False,
            'vnpy_backtesting': False,
            'trading_strategies': False,
            'portfolio_management': False,
            'web_interface': False
        }
        
        # 1. 检查数据库状态
        logger.info("1️⃣ 检查数据库状态...")
        try:
            from database_models import db_manager
            if db_manager.test_connection():
                logger.info("✅ 数据库连接正常")
                status_report['database'] = True
            else:
                logger.error("❌ 数据库连接失败")
        except Exception as e:
            logger.error(f"❌ 数据库检查异常: {e}")
        
        # 2. 检查数据采集模块
        logger.info("2️⃣ 检查数据采集模块...")
        try:
            from data_collection.collectors.market_collector import MarketDataCollector
            collector = MarketDataCollector()
            logger.info("✅ 数据采集模块正常")
            status_report['data_collection'] = True
        except Exception as e:
            logger.error(f"❌ 数据采集模块异常: {e}")
        
        # 3. 检查智能选股引擎
        logger.info("3️⃣ 检查智能选股引擎...")
        try:
            from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector
            selector = MultiFactorSelector()
            logger.info("✅ 智能选股引擎正常")
            status_report['stock_selector'] = True
        except Exception as e:
            logger.error(f"❌ 智能选股引擎异常: {e}")
        
        # 4. 检查VeighNa回测引擎
        logger.info("4️⃣ 检查VeighNa回测引擎...")
        try:
            from vnpy_integration.backtesting_engine import VnpyBacktestingEngine
            engine = VnpyBacktestingEngine()
            logger.info("✅ VeighNa回测引擎正常")
            status_report['vnpy_backtesting'] = True
        except Exception as e:
            logger.error(f"❌ VeighNa回测引擎异常: {e}")
        
        # 5. 检查交易策略层
        logger.info("5️⃣ 检查交易策略层...")
        try:
            from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
            from strategy_layer.trading_strategies.sell_strategy import SellStrategy
            buy_strategy = BuyStrategy()
            sell_strategy = SellStrategy()
            logger.info("✅ 交易策略层正常")
            status_report['trading_strategies'] = True
        except Exception as e:
            logger.error(f"❌ 交易策略层异常: {e}")
        
        # 6. 检查投资组合管理
        logger.info("6️⃣ 检查投资组合管理...")
        try:
            from portfolio_management.portfolio_builder.portfolio_builder import PortfolioBuilder
            builder = PortfolioBuilder()
            logger.info("✅ 投资组合管理正常")
            status_report['portfolio_management'] = True
        except Exception as e:
            logger.error(f"❌ 投资组合管理异常: {e}")
        
        # 7. 检查Web界面模块
        logger.info("7️⃣ 检查Web界面模块...")
        try:
            from frontend_ui.components.signal_monitor_dashboard import SignalMonitorDashboard
            logger.info("✅ Web界面模块正常")
            status_report['web_interface'] = True
        except Exception as e:
            logger.error(f"❌ Web界面模块异常: {e}")
        
        # 生成状态报告
        generate_status_report(status_report)
        
        return status_report
        
    except Exception as e:
        logger.error(f"❌ 系统状态检查异常: {e}")
        return {}

def generate_status_report(status_report):
    """生成状态报告"""
    try:
        logger.info("📊 系统状态报告:")
        logger.info("=" * 60)
        
        total_modules = len(status_report)
        working_modules = sum(status_report.values())
        
        logger.info(f"📈 系统健康度: {working_modules}/{total_modules} ({working_modules/total_modules*100:.1f}%)")
        logger.info("")
        
        # 详细状态
        module_names = {
            'database': '💾 数据库系统',
            'data_collection': '📊 数据采集系统',
            'stock_selector': '🧠 智能选股引擎',
            'vnpy_backtesting': '🚀 VeighNa回测系统',
            'trading_strategies': '📈 交易策略层',
            'portfolio_management': '💼 投资组合管理',
            'web_interface': '🌐 Web界面模块'
        }
        
        for module, status in status_report.items():
            name = module_names.get(module, module)
            status_icon = "✅" if status else "❌"
            status_text = "正常" if status else "异常"
            logger.info(f"  {status_icon} {name}: {status_text}")
        
        logger.info("")
        
        # 系统建议
        if working_modules == total_modules:
            logger.info("🎉 系统运行完全正常，所有模块工作正常！")
        elif working_modules >= total_modules * 0.8:
            logger.info("✅ 系统运行基本正常，少数模块需要检查")
        elif working_modules >= total_modules * 0.5:
            logger.info("⚠️ 系统运行存在问题，建议检查异常模块")
        else:
            logger.info("❌ 系统运行严重异常，需要立即修复")
        
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ 生成状态报告失败: {e}")

def show_system_info():
    """显示系统信息"""
    try:
        logger.info("🖥️ 系统信息:")
        logger.info(f"  🐍 Python版本: {sys.version}")
        logger.info(f"  📁 项目路径: {os.getcwd()}")
        logger.info(f"  🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("")
        
        # 检查关键依赖
        logger.info("📦 关键依赖检查:")
        dependencies = [
            'pandas', 'numpy', 'sqlalchemy', 'fastapi', 'uvicorn'
        ]
        
        for dep in dependencies:
            try:
                __import__(dep)
                logger.info(f"  ✅ {dep}: 已安装")
            except ImportError:
                logger.info(f"  ❌ {dep}: 未安装")
        
        logger.info("")
        
    except Exception as e:
        logger.error(f"❌ 显示系统信息失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🚀 量化交易系统状态检查")
        logger.info("=" * 60)
        
        # 显示系统信息
        show_system_info()
        
        # 检查系统状态
        status_report = check_system_status()
        
        # 返回状态
        if status_report:
            working_modules = sum(status_report.values())
            total_modules = len(status_report)
            
            if working_modules == total_modules:
                logger.info("✅ 系统状态检查完成，一切正常！")
                return 0
            elif working_modules >= total_modules * 0.8:
                logger.info("⚠️ 系统状态检查完成，基本正常！")
                return 0
            else:
                logger.error("❌ 系统状态检查完成，存在问题！")
                return 1
        else:
            logger.error("❌ 系统状态检查失败！")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断检查")
        return 1
    except Exception as e:
        logger.error(f"❌ 检查异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
