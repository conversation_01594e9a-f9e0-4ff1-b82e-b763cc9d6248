# 🎨 VeighNa量化交易系统 - 完整产品原型设计图

> **基于文档**: PRODUCT_DESIGN_DOCUMENTNEW.md + PRODUCT_FUNCTION_MODULE_TABLE.md + VNPY_STYLE_PROTOTYPE_DESIGN.md + STOCK_INVESTMENT_DECISION_SYSTEM.md  
> **设计风格**: vn.py专业量化交易界面风格  
> **版本**: v2.0  
> **日期**: 2024-12-27

---

## 🎯 产品原型设计概述

### 系统架构层次
基于产品设计文档，系统分为9个核心层次，45个主要功能模块：

1. **📊 数据采集层** - 多源数据采集和预处理
2. **💾 数据存储层** - 数据存储和管理  
3. **🔍 分析引擎层** - 数据分析和因子计算
4. **🚀 VeighNa回测系统** - 核心回测和策略验证
5. **📈 策略层** - 交易策略和风险管理
6. **💼 投资组合管理层** - 组合构建和管理
7. **⚡ 交易执行层** - 交易执行和订单管理
8. **📊 可视化展示层** - 用户界面和数据展示
9. **⚙️ 系统管理层** - 系统配置和运维管理

### 核心业务流程
```
智能选股 → K线分析 → 买入决策 → 持仓监控 → 卖出决策 → 投资记录
```

---

## 🖥️ 主界面原型设计

### 1. 系统主界面 (1920x1080)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 VeighNa量化交易系统 v2.0                    [连接状态] [系统时间] [用户] [设置] │
├─────────────────────────────────────────────────────────────────────────────────┤
│ [系统] [数据] [策略] [交易] [风控] [组合] [因子] [日志] [帮助]                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   功能导航面板   │ │                    主工作区域                          │ │
│ │                │ │                                                        │ │
│ │ 📊 数据管理      │ │  ┌─────────────────┐ ┌─────────────────────────────┐  │ │
│ │ ├─ 数据采集      │ │  │   实时行情监控   │ │        K线图表区域          │  │ │
│ │ ├─ 数据存储      │ │  │                │ │                            │  │ │
│ │ └─ 数据质量      │ │  │ 000001 平安银行  │ │     [1m][5m][15m][1h][1d]   │  │ │
│ │                │ │  │ 12.50  +0.25     │ │                            │  │ │
│ │ 🔍 智能选股      │ │  │ +2.04%  ↗       │ │     📈 K线 + 技术指标       │  │ │
│ │ ├─ 因子配置      │ │  │                │ │                            │  │ │
│ │ ├─ 选股策略      │ │  │ 600519 贵州茅台  │ │     🔴 买入信号标注          │  │ │
│ │ └─ 候选池        │ │  │ 1850.00 -15.00  │ │     🟢 卖出信号标注          │  │ │
│ │                │ │  │ -0.80%  ↘       │ │                            │  │ │
│ │ 🚀 策略回测      │ │  └─────────────────┘ └─────────────────────────────┘  │ │
│ │ ├─ VeighNa引擎   │ │                                                        │ │
│ │ ├─ 策略管理      │ │  ┌─────────────────────────────────────────────────┐  │ │
│ │ ├─ 参数优化      │ │  │                 交易信号面板                    │  │ │
│ │ └─ 绩效分析      │ │  │                                                │  │ │
│ │                │ │  │ 🔴 买入信号  600036 招商银行  评分:85  14:25:30   │  │ │
│ │ 💼 组合管理      │ │  │ 🟢 卖出信号  000858 五粮液   评分:78  14:20:15   │  │ │
│ │ ├─ 组合构建      │ │  │ ⚠️  风险预警  002415 海康威视 回撤:8%  14:18:45   │  │ │
│ │ ├─ 权重分配      │ │  │                                                │  │ │
│ │ └─ 再平衡        │ │  └─────────────────────────────────────────────────┘  │ │
│ │                │ │                                                        │ │
│ │ ⚡ 交易执行      │ │  ┌─────────────────────────────────────────────────┐  │ │
│ │ ├─ 订单管理      │ │  │                   持仓明细                      │  │ │
│ │ ├─ 成交回报      │ │  │                                                │  │ │
│ │ └─ 风险控制      │ │  │ 代码    名称    数量   成本价  现价   盈亏   比例 │  │ │
│ │                │ │  │ 000001  平安银行 1000   12.00  12.50  +500  4.2% │  │ │
│ │ ⚙️ 系统管理      │ │  │ 600036  招商银行  500   44.00  45.20  +600  7.6% │  │ │
│ │ ├─ 参数配置      │ │  │ 600519  贵州茅台  100  1800.0  1850.0 +5000 14.8%│  │ │
│ │ ├─ 日志查看      │ │  │                                                │  │ │
│ │ └─ 性能监控      │ │  └─────────────────────────────────────────────────┘  │ │
│ └─────────────────┘ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 状态栏: [数据连接:正常] [策略运行:3个] [总资产:¥1,250,000] [今日盈亏:+¥12,500]   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **顶部菜单栏**: 点击切换不同功能模块，实时显示连接状态
2. **左侧导航**: 树形结构展示功能模块，支持折叠展开
3. **主工作区**: 根据选择的功能模块动态加载对应界面
4. **实时数据**: WebSocket推送实时行情和交易信号
5. **状态栏**: 显示系统关键状态信息，异常时红色预警

---

## 📊 数据管理模块原型设计

### 2. 数据采集配置界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 数据管理 - ADATA数据源配置                                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   数据源列表     │ │                  数据采集配置                          │ │
│ │                │ │                                                        │ │
│ │ ✅ ADATA主接口   │ │  ┌─ 股票基础信息 ─────────────────────────────────────┐ │ │
│ │ 📊 实时行情      │ │  │ 数据源: stock.info.all_code()                    │ │ │
│ │ 📈 历史数据      │ │  │ 更新频率: 每日 09:00                             │ │ │
│ │ 💰 财务数据      │ │  │ 状态: ✅ 正常运行                                │ │ │
│ │ 📰 舆情数据      │ │  │ 最后更新: 2024-12-27 09:00:15                   │ │ │
│ │                │ │  └─────────────────────────────────────────────────┘ │ │
│ │ ⚙️ 采集器配置    │ │                                                        │ │
│ │ 1分钟数据 ✅     │ │  ┌─ 多时间周期采集器 ─────────────────────────────────┐ │ │
│ │ 5分钟数据 ✅     │ │  │ 1分钟数据: ✅ 运行中  最后采集: 14:29:00           │ │ │
│ │ 15分钟数据 ✅    │ │  │ 5分钟数据: ✅ 运行中  最后采集: 14:25:00           │ │ │
│ │ 1小时数据 ✅     │ │  │ 15分钟数据: ✅ 运行中 最后采集: 14:15:00           │ │ │
│ │ 4小时数据 ✅     │ │  │ 1小时数据: ✅ 运行中  最后采集: 14:00:00           │ │ │
│ │ 日线数据 ✅      │ │  │ 4小时数据: ✅ 运行中  最后采集: 12:00:00           │ │ │
│ │                │ │  │ 日线数据: ✅ 运行中   最后采集: 昨日 15:30:00       │ │ │
│ │ 📊 数据质量      │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 完整性检查 ✅    │ │                                                        │ │
│ │ 异常值检测 ⚠️    │ │  ┌─ 基本面数据采集 ───────────────────────────────────┐ │ │
│ │ 数据校验 ✅      │ │  │ 财务数据: stock.finance.get_financial_report()    │ │ │
│ │                │ │  │ 估值数据: stock.finance.get_core_index()          │ │ │
│ │ [启动采集]       │ │  │ 板块数据: 板块分类API                            │ │ │
│ │ [停止采集]       │ │  │ 舆情数据: sentiment.news.get_news_list()         │ │ │
│ │ [重置配置]       │ │  │ 更新频率: 每日 17:00 / 季度财报发布后             │ │ │
│ └─────────────────┘ │  │ 状态: ✅ 正常运行                                │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 数据统计: [股票数量: 5,127] [今日采集: 1,245,680条] [存储空间: 2.3GB/50GB]      │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **数据源管理**: 左侧列表显示所有数据源状态，点击查看详细配置
2. **实时状态**: 采集器状态实时更新，异常时显示警告图标
3. **配置操作**: 支持启动/停止采集，重置配置等操作
4. **数据质量**: 实时监控数据质量，异常时触发预警
5. **存储监控**: 底部显示存储使用情况，接近上限时预警

---

## 🔧 因子管理模块原型设计

### 3. 因子配置管理界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🔧 因子管理系统 - 多维度评分算法配置                                             │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   因子分类       │ │                    因子配置面板                        │ │
│ │                │ │                                                        │ │
│ │ 📈 技术面因子    │ │  ┌─ 动量因子配置 ─────────────────────────────────────┐ │ │
│ │ ├─ 动量因子 ✅   │ │  │ 因子ID: momentum_factor                           │ │ │
│ │ ├─ MA趋势 ✅     │ │  │ 因子名称: 动量因子                                │ │ │
│ │ ├─ RSI指标 ✅    │ │  │ 权重: 15.0% [━━━━━━━━━━━━━━━░░░░░] 15/100           │ │ │
│ │ ├─ 布林带 ✅     │ │  │ 状态: ✅ 启用                                     │ │ │
│ │ └─ MACD ❌       │ │  │                                                  │ │ │
│ │                │ │  │ 参数配置:                                         │ │ │
│ │ 💼 基本面因子    │ │  │ ├─ 时间窗口(window): 20 天                        │ │ │
│ │ ├─ 价值因子 ✅   │ │  │ ├─ 计算方法(method): 收益率                       │ │ │
│ │ ├─ 质量因子 ✅   │ │  │ ├─ 标准化方式(normalize): Z-Score                 │ │ │
│ │ └─ 成长因子 ✅   │ │  │ ├─ 最小值(min_value): -100                       │ │ │
│ │                │ │  │ ├─ 最大值(max_value): 100                         │ │ │
│ │ 📊 市场表现      │ │  │ └─ 默认值(default): 0                            │ │ │
│ │ ├─ 相对强度 ✅   │ │  │                                                  │ │ │
│ │ ├─ 资金流向 ✅   │ │  │ 计算公式:                                         │ │ │
│ │ └─ 波动率 ✅     │ │  │ momentum = (price_current / price_window_ago - 1) │ │ │
│ │                │ │  │ normalized = (momentum - mean) / std              │ │ │
│ │ 🔧 自定义因子    │ │  │ factor_score = max(min(normalized, 100), -100)   │ │ │
│ │ (暂无)          │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ [➕ 添加因子]    │ │  ┌─ 因子测试结果 ─────────────────────────────────────┐ │ │
│ │ [📥 导入配置]    │ │  │ 测试股票: 600519 贵州茅台                         │ │ │
│ │ [📤 导出配置]    │ │  │ 当前因子值: 68.5                                 │ │ │
│ │ [🔄 重置全部]    │ │  │ 因子排名: 15/3000                                │ │ │
│ │                │ │  │ 变化趋势: ↗️ 上升                                  │ │ │
│ └─────────────────┘ │  │ IC值: 0.085 (信息系数)                           │ │ │
│                    │  │ IR值: 1.25 (信息比率)                            │ │ │
│                    │  │ 回测收益: +15.2% (年化)                           │ │ │
│                    │  │ 最大回撤: -3.8%                                   │ │ │
│                    │  │                                                  │ │ │
│                    │  │ [▶️ 运行测试] [💾 保存配置] [📋 克隆因子]           │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 权重分配: [技术面:50%] [基本面:30%] [市场表现:20%] [总权重:100%] ✅ 权重验证通过  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **因子分类**: 左侧树形结构管理不同类型因子，支持拖拽排序
2. **参数配置**: 右侧面板动态显示选中因子的详细参数
3. **权重调整**: 滑动条实时调整因子权重，自动验证总权重
4. **实时测试**: 修改参数后可立即运行测试查看效果
5. **配置管理**: 支持导入导出配置，便于备份和共享

---

## 🚀 VeighNa回测模块原型设计

### 4. 策略回测界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🚀 VeighNa策略回测系统 - MultiTimeframeStrategy                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   策略配置       │ │                    回测结果展示                        │ │
│ │                │ │                                                        │ │
│ │ 📊 基本配置      │ │  ┌─ 策略绩效曲线 ─────────────────────────────────────┐ │ │
│ │ 股票代码:        │ │  │                                                  │ │ │
│ │ [600519      ▼] │ │  │     📈 净值曲线图                                │ │ │
│ │ 回测周期:        │ │  │                                                  │ │ │
│ │ 2023-01-01 至    │ │  │     策略收益: +25.8% (绿线)                      │ │ │
│ │ 2024-12-31      │ │  │     基准收益: +12.3% (灰线)                      │ │ │
│ │ 初始资金:        │ │  │     超额收益: +13.5%                            │ │ │
│ │ [100000     ] 元 │ │  │                                                  │ │ │
│ │                │ │  │     🔴🟢 买卖点标注                               │ │ │
│ │ 📈 策略参数      │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 快速均线:        │ │                                                        │ │
│ │ [5          ] 日 │ │  ┌─ 关键绩效指标 ─────────────────────────────────────┐ │ │
│ │ 慢速均线:        │ │  │ 总收益率:     +25.8%    年化收益:    +18.5%       │ │ │
│ │ [20         ] 日 │ │  │ 最大回撤:     -8.2%     夏普比率:    1.85        │ │ │
│ │ RSI周期:         │ │  │ 胜率:         68.5%     交易次数:    47次         │ │ │
│ │ [14         ] 日 │ │  │ 波动率:       12.8%     VaR(95%):   -2.8%        │ │ │
│ │                │ │  │ 索提诺比率:   2.15      卡尔马比率:  2.26         │ │ │
│ │ ⚠️ 风险控制      │ │  │ Beta系数:     0.85      Alpha系数:   0.06        │ │ │
│ │ 止损比例:        │ │  └─────────────────────────────────────────────────┘ │ │
│ │ [5.0        ] %  │ │                                                        │ │
│ │ 止盈比例:        │ │  ┌─ 交易统计分析 ─────────────────────────────────────┐ │ │
│ │ [15.0       ] %  │ │  │ 平均盈利:     +3.2%     平均亏损:    -1.8%       │ │ │
│ │ 最大仓位:        │ │  │ 盈亏比:       1.78      平均持仓:    8.5天        │ │ │
│ │ [1000       ] 股 │ │  │ 最大连胜:     6次       最大连亏:    3次          │ │ │
│ │                │ │  │ 最大单笔盈利: +8.5%     最大单笔亏损: -4.2%       │ │ │
│ │ [▶️ 开始回测]    │ │  └─────────────────────────────────────────────────┘ │ │
│ │ [⏹️ 停止回测]    │ │                                                        │ │
│ │ [🎯 参数优化]    │ │  ┌─ 回测进度 ─────────────────────────────────────────┐ │ │
│ │ [💾 保存策略]    │ │  │ 进度: ████████████████████░░ 90%                  │ │ │
│ │ [📊 生成报告]    │ │  │ 状态: 正在计算绩效指标...                         │ │ │
│ └─────────────────┘ │  │ 用时: 00:02:35 / 预计剩余: 00:00:18               │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 回测状态: [引擎:运行中] [数据:已加载] [策略:MultiTimeframe] [信号:47个] [完成:90%] │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **参数配置**: 左侧面板配置策略参数，支持参数验证
2. **实时进度**: 回测过程中显示实时进度和状态
3. **结果展示**: 右侧动态展示回测结果和绩效指标
4. **图表交互**: 净值曲线支持缩放、标注买卖点
5. **报告生成**: 完成后可生成详细的HTML/PDF报告

---

## 📈 智能选股模块原型设计

### 5. 选股筛选界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 智能选股系统 - 多维度评分筛选                                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   筛选条件       │ │                    推荐股票结果                        │ │
│ │                │ │                                                        │ │
│ │ 📈 技术面筛选    │ │  排名  代码    名称      综合评分  买入信号  推荐理由     │ │
│ │ MA多头排列 ✅    │ │  ──────────────────────────────────────────────────── │ │
│ │ MACD金叉 ✅      │ │  1     600519  贵州茅台    85.2    🔴 强烈  技术突破   │ │
│ │ RSI适中 ✅       │ │  2     000858  五粮液      82.8    🔴 强烈  量价齐升   │ │
│ │ 成交量放大 ✅    │ │  3     600036  招商银行    80.5    🔴 买入  金融回暖   │ │
│ │                │ │  4     000001  平安银行    78.3    🔴 买入  估值修复   │ │
│ │ 💼 基本面筛选    │ │  5     600887  伊利股份    76.9    ⚪ 观察  业绩稳定   │ │
│ │ PE合理 ✅        │ │  6     600276  恒瑞医药    75.8    ⚪ 观察  研发投入   │ │
│ │ ROE>15% ✅       │ │  7     002415  海康威视    74.2    🟢 卖出  增长放缓   │ │
│ │ 负债率<60% ✅    │ │  8     000002  万科A       72.1    🟢 卖出  地产调整   │ │
│ │ 营收增长>10% ✅  │ │  9     600519  中国平安    71.5    ⚪ 观察  转型阵痛   │ │
│ │                │ │  10    000858  招商蛇口    70.3    ⚪ 观察  区域优势   │ │
│ │ 📊 市场表现      │ │                                                        │ │
│ │ 相对强度>1.2 ✅  │ │  ┌─ 选中股票详情: 600519 贵州茅台 ─────────────────┐  │ │
│ │ 资金净流入 ✅    │ │  │ 📊 基本信息                                     │  │ │
│ │ 市场情绪积极 ✅  │ │  │ 当前价格: ¥1850.00 (+2.78% ↗)                  │  │ │
│ │                │ │  │ 总市值: ¥2.32万亿  流通市值: ¥2.32万亿           │  │ │
│ │ 🎯 综合设置      │ │  │ PE: 28.5  PB: 12.8  ROE: 25.6%                 │  │ │
│ │ 最低评分: 65分   │ │  │                                                │  │ │
│ │ 推荐数量: 15只   │ │  │ 📈 技术分析                                     │  │ │
│ │ 更新频率: 实时   │ │  │ MA5: 1848.5 ↗  MA20: 1835.2 ↗  多头排列        │  │ │
│ │                │ │  │ MACD: 金叉 ✅  RSI: 65.2  布林带: 接近上轨       │  │ │
│ │ [🔍 开始筛选]    │ │  │ 成交量: 1.8倍放大  资金流入: 积极               │  │ │
│ │ [🔄 重置条件]    │ │  │                                                │  │ │
│ │ [💾 保存方案]    │ │  │ 🎯 投资建议                                     │  │ │
│ └─────────────────┘ │  │ 买入信号: 🔴 强烈买入                           │  │ │
│                    │  │ 建议价格: ¥1845-1850                            │  │ │
│                    │  │ 目标价格: ¥1950 (+5.4%)                         │  │ │
│                    │  │ 止损价格: ¥1750 (-5.4%)                         │  │ │
│                    │  │ 风险等级: 中等 ⚠️                               │  │ │
│                    │  │                                                │  │ │
│                    │  │ [📈 查看K线] [➕ 加入自选] [🔔 设置提醒]          │  │ │
│                    │  └─────────────────────────────────────────────────┘  │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 筛选结果: [符合条件:156只] [推荐买入:8只] [观察:5只] [建议卖出:2只] [更新:实时] │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **条件筛选**: 左侧多维度筛选条件，支持自定义组合
2. **实时排序**: 结果列表按评分实时排序，支持多种排序方式
3. **详情展示**: 点击股票显示详细信息和投资建议
4. **快速操作**: 支持一键查看K线、加入自选、设置提醒
5. **方案管理**: 可保存筛选方案，便于重复使用

---

## 💼 投资组合管理原型设计

### 6. 组合管理界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 💼 投资组合管理 - 智能量化组合A                                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   组合概览       │ │                    持仓明细表格                        │ │
│ │                │ │                                                        │ │
│ │ 💰 总资产        │ │  代码    名称      数量    成本价   现价    市值     权重  │ │
│ │ ¥1,250,000      │ │  ────────────────────────────────────────────────────── │ │
│ │ +2.5% ↗         │ │  600519  贵州茅台   100   1800.0  1850.0  185000  14.8% │ │
│ │                │ │  000858  五粮液     500    180.0   185.0   92500   7.4% │ │
│ │ 📈 今日盈亏      │ │  600036  招商银行  2000     44.0    45.2   90400   7.2% │ │
│ │ +¥12,500        │ │  000001  平安银行  5000     12.0    12.5   62500   5.0% │ │
│ │ +1.2% ↗         │ │  002415  海康威视  2000     32.0    32.8   65600   5.2% │ │
│ │                │ │  ...     ...       ...      ...     ...     ...     ... │ │
│ │ 📊 持仓股票      │ │  现金余额                                  312500  25.0% │ │
│ │ 15只            │ │  ────────────────────────────────────────────────────── │ │
│ │                │ │  合计                                     1250000 100.0% │ │
│ │ 💵 现金比例      │ │                                                        │ │
│ │ 25.0%           │ │  ┌─ 组合绩效图表 ─────────────────────────────────────┐ │ │
│ │                │ │  │                                                  │ │ │
│ │ ⚖️ 再平衡建议    │ │  │     📈 组合净值曲线                              │ │ │
│ │ 触发条件:       │ │  │                                                  │ │ │
│ │ 权重偏离>5%     │ │  │     组合收益: +25.0% (蓝线)                      │ │ │
│ │                │ │  │     沪深300: +12.8% (灰线)                       │ │ │
│ │ 建议调整:       │ │  │     超额收益: +12.2%                            │ │ │
│ │ 减持:贵州茅台-2%│ │  │     最大回撤: -6.5%                             │ │ │
│ │ 增持:招商银行+1%│ │  │                                                  │ │ │
│ │ 预估成本:¥850   │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ [🔄 执行再平衡]  │ │  ┌─ 风险分析 ─────────────────────────────────────────┐ │ │
│ │ [📊 绩效报告]    │ │  │ 组合波动率: 15.2%    组合Beta: 0.88              │ │ │
│ │ [⚙️ 组合设置]    │ │  │ 跟踪误差: 4.2%       信息比率: 1.85              │ │ │
│ └─────────────────┘ │  │ 最大回撤: -6.5%      VaR(95%): -3.2%             │ │ │
│                    │  │                                                  │ │ │
│                    │  │ 行业配置: 金融35% 消费25% 科技20% 医药15% 其他5%  │ │ │
│                    │  │ 风格配置: 大盘60% 中盘30% 小盘10%                │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 组合状态: [运行中] [持仓:15只] [现金:25%] [今日调仓:0次] [风险等级:中等]        │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **组合概览**: 左侧实时显示组合关键指标和再平衡建议
2. **持仓明细**: 表格显示所有持仓，支持排序和筛选
3. **绩效图表**: 动态展示组合净值曲线和基准对比
4. **风险监控**: 实时计算和显示各种风险指标
5. **智能建议**: 基于算法给出再平衡和调仓建议

---

## ⚡ 交易执行模块原型设计

### 7. 订单管理界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ⚡ 交易执行系统 - 订单管理                                                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   交易接口       │ │                    订单管理表格                        │ │
│ │                │ │                                                        │ │
│ │ 🔗 连接状态      │ │  订单号     股票     方向  数量   价格    状态      时间   │ │
│ │ 模拟交易: ✅     │ │  ────────────────────────────────────────────────────── │ │
│ │ 券商API: ❌      │ │  20241227001 000001  买入  1000  12.50   已成交  09:30:15 │ │
│ │ CTP接口: ❌      │ │  20241227002 600519  买入   100  1800.0  已成交  10:15:30 │ │
│ │                │ │  20241227003 000858  卖出   500   185.0  待成交  14:25:00 │ │
│ │ 📊 交易统计      │ │  20241227004 600036  买入  2000   45.2   已撤销  14:20:15 │ │
│ │ 今日成交: 15笔   │ │  20241227005 002415  卖出  1000   32.8   部分成交 14:28:45 │ │
│ │ 成交金额: ¥2.1M  │ │  ...         ...     ...   ...    ...     ...      ...   │ │
│ │ 手续费: ¥630     │ │                                                        │ │
│ │ 滑点成本: ¥420   │ │  ┌─ 成交回报详情 ─────────────────────────────────────┐ │ │
│ │                │ │  │ 订单号: 20241227005                               │ │ │
│ │ ⚠️ 风险控制      │ │  │ 股票代码: 002415 海康威视                         │ │ │
│ │ 单股仓位: 8.5%   │ │  │ 交易方向: 卖出                                   │ │ │
│ │ 总仓位: 75.2%    │ │  │ 委托数量: 1000股                                 │ │ │
│ │ 可用资金: ¥312K  │ │  │ 委托价格: ¥32.80                                 │ │ │
│ │ 风险等级: 正常   │ │  │ 成交数量: 600股                                  │ │ │
│ │                │ │  │ 成交价格: ¥32.75                                 │ │ │
│ │ 🎯 智能下单      │ │  │ 成交金额: ¥19,650                                │ │ │
│ │ TWAP算法: ✅     │ │  │ 手续费: ¥5.90                                    │ │ │
│ │ VWAP算法: ✅     │ │  │ 滑点: -¥30 (-0.15%)                             │ │ │
│ │ 冰山算法: ❌     │ │  │ 订单状态: 部分成交                               │ │ │
│ │                │ │  │ 剩余数量: 400股                                  │ │ │
│ │ [📝 手动下单]    │ │  └─────────────────────────────────────────────────┘ │ │
│ │ [🔄 批量撤单]    │ │                                                        │ │
│ │ [📊 执行分析]    │ │  ┌─ 滑点分析 ─────────────────────────────────────────┐ │ │
│ └─────────────────┘ │  │ 今日平均滑点: -0.08%                              │ │ │
│                    │  │ 买入滑点: +0.12%     卖出滑点: -0.28%             │ │ │
│                    │  │ 滑点成本: ¥420       占成交额: 0.02%               │ │ │
│                    │  │                                                  │ │ │
│                    │  │ 📊 滑点分布图 [显示最近100笔交易的滑点分布]        │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 交易状态: [模拟模式] [今日成交:15笔] [待成交:3笔] [总成交额:¥2,100,000]         │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **接口管理**: 左侧显示各交易接口连接状态，支持切换
2. **订单监控**: 中央表格实时显示所有订单状态
3. **详情展示**: 点击订单显示详细成交回报信息
4. **滑点分析**: 实时统计和分析交易滑点情况
5. **风险控制**: 实时监控仓位和资金使用情况

---

## ⚙️ 系统管理模块原型设计

### 8. 系统监控界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ⚙️ 系统管理 - 性能监控与日志管理                                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   系统状态       │ │                    性能监控面板                        │ │
│ │                │ │                                                        │ │
│ │ 🖥️ 系统资源      │ │  ┌─ CPU使用率 ────────────────────────────────────────┐ │ │
│ │ CPU: 45.2%      │ │  │ 当前: 45.2% [████████████░░░░░░░░░░] 45/100         │ │ │
│ │ 内存: 62.8%     │ │  │ 平均: 38.5%  峰值: 78.2%  最低: 12.3%             │ │ │
│ │ 磁盘: 23.1%     │ │  │ 📊 CPU使用率趋势图 (最近24小时)                    │ │ │
│ │ 网络: 12.5MB/s  │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ 🔗 连接状态      │ │  ┌─ 内存使用情况 ─────────────────────────────────────┐ │ │
│ │ 数据库: ✅       │ │  │ 已用: 5.2GB/8GB [████████████████░░░░] 65%         │ │ │
│ │ Redis: ✅        │ │  │ 系统: 2.1GB  应用: 2.8GB  缓存: 0.3GB             │ │ │
│ │ Kafka: ✅        │ │  │ 📊 内存使用趋势图 (最近24小时)                     │ │ │
│ │ ADATA: ✅        │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ 📊 服务状态      │ │  ┌─ 数据库性能 ───────────────────────────────────────┐ │ │
│ │ 数据采集: ✅     │ │  │ 连接数: 25/100    QPS: 1,245    平均响应: 15ms    │ │ │
│ │ 因子计算: ✅     │ │  │ 慢查询: 3个       缓存命中: 89.5%                 │ │ │
│ │ 策略回测: ✅     │ │  │ 存储空间: 2.3GB/50GB (4.6%)                      │ │ │
│ │ 交易执行: ⚠️     │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 组合管理: ✅     │ │                                                        │ │
│ │                │ │  ┌─ 系统日志 (最近50条) ──────────────────────────────┐ │ │
│ │ 📋 日志管理      │ │  │ [14:30:15] INFO  数据采集完成，共采集1245条记录     │ │ │
│ │ 今日日志: 2,847  │ │  │ [14:29:45] WARN  股票000001数据延迟，重试中...     │ │ │
│ │ 错误日志: 12     │ │  │ [14:28:30] INFO  因子计算完成，候选池更新          │ │ │
│ │ 警告日志: 45     │ │  │ [14:27:15] ERROR 交易接口连接失败，切换备用接口    │ │ │
│ │                │ │  │ [14:26:00] INFO  策略回测启动，预计用时3分钟        │ │ │
│ │ [📄 查看日志]    │ │  │ [14:25:30] INFO  用户登录: <EMAIL>         │ │ │
│ │ [🔍 日志搜索]    │ │  │ [14:24:15] WARN  内存使用率超过60%，建议清理缓存   │ │ │
│ │ [📤 导出日志]    │ │  │ ...                                              │ │ │
│ │ [🗑️ 清理日志]    │ │  │                                                  │ │ │
│ └─────────────────┘ │  │ [🔍 搜索] [📄 详细] [⬇️ 更多] [🔄 刷新]            │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 系统运行时间: 15天3小时42分钟 | 最后重启: 2024-12-12 09:30:00 | 版本: v2.0.1    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **实时监控**: 左侧显示系统关键状态，异常时红色预警
2. **性能图表**: 右侧动态展示CPU、内存等性能趋势图
3. **日志管理**: 实时显示系统日志，支持搜索和过滤
4. **告警机制**: 资源使用超过阈值时自动告警
5. **操作便捷**: 支持一键导出日志、清理缓存等操作

---

## 🎨 设计规范与技术实现

### vn.py风格设计规范

#### 色彩方案
- **主背景色**: #2b2b2b (深灰)
- **面板背景**: #3c3c3c (中灰)  
- **边框颜色**: #555555 (浅灰)
- **文字颜色**: #ffffff (白色)
- **强调色**: #4a9eff (蓝色)
- **成功色**: #52c41a (绿色)
- **警告色**: #faad14 (橙色)
- **错误色**: #ff4d4f (红色)

#### 交互设计原则
1. **响应式反馈**: 所有操作都有即时视觉反馈
2. **状态指示**: 清晰的连接状态和运行状态显示
3. **数据驱动**: 界面元素根据数据状态动态更新
4. **专业布局**: 信息密度高，功能模块化
5. **快捷操作**: 支持键盘快捷键和右键菜单

### 技术实现架构

#### 前端技术栈
- **框架**: Electron + Vue 3 + TypeScript
- **UI库**: Element Plus (深色主题定制)
- **图表**: ECharts 5.x + TradingView
- **状态管理**: Pinia
- **构建工具**: Vite

#### 后端技术栈  
- **核心引擎**: VeighNa + Python 3.9+
- **数据库**: PostgreSQL + Redis
- **消息队列**: Apache Kafka
- **API框架**: FastAPI
- **数据源**: ADATA + 自定义适配器

#### 系统集成
- **实时通信**: WebSocket + Socket.IO
- **数据同步**: 事件驱动架构
- **缓存策略**: 多级缓存机制
- **监控告警**: Prometheus + Grafana
- **日志管理**: ELK Stack

---

## 🎉 产品原型设计总结

### 完整功能覆盖
✅ **9个核心层次**: 从数据采集到系统管理的完整覆盖  
✅ **45个主要模块**: 每个模块都有详细的原型设计  
✅ **专业界面风格**: 完全遵循vn.py的深色专业风格  
✅ **完整交互逻辑**: 每个界面都有详细的交互说明  
✅ **技术实现指导**: 提供完整的技术架构方案

### 核心业务流程
```
📊 智能选股 → 📈 K线分析 → 🔴 买入决策 → 💼 持仓监控 → 🟢 卖出决策
```

### 系统特色优势
1. **智能化**: 基于AI的选股和信号识别
2. **专业化**: VeighNa回测引擎提供机构级回测能力  
3. **可视化**: 丰富的图表和实时数据展示
4. **模块化**: 松耦合的模块设计，易于扩展
5. **实时性**: WebSocket实现毫秒级数据更新

---

## 📈 交易信号K线图核心原型设计

### 9. 实时交易信号K线图界面 (核心功能)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📈 实时交易信号监控 - K线图表信号展示                                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   信号监控列表   │ │                K线图表 + 交易信号展示                   │ │
│ │                │ │                                                        │ │
│ │ 🔴 买入信号(5只) │ │  当前股票: 600519 贵州茅台  ¥1850.00 (+2.78%)          │ │
│ │ 600519 贵州茅台  │ │  ┌─────────────────────────────────────────────────┐  │ │
│ │ 信号强度: ●●●●○  │ │  │ [1m][5m][15m][30m][1h][4h][1d] 📊 [MA][MACD][RSI] │  │ │
│ │ 评分: 85 🔴      │ │  │                                                │  │ │
│ │                │ │  │     📊 K线主图 (最近60根K线)                    │  │ │
│ │ 000858 五粮液    │ │  │                                                │  │ │
│ │ 信号强度: ●●●●○  │ │  │  1900┌─────────────────────────────────────────┐ │  │ │
│ │ 评分: 82 🔴      │ │  │      │                                        │ │  │ │
│ │                │ │  │  1850├─────🔴──────────────────────────────────┤ │  │ │
│ │ 600036 招商银行  │ │  │      │    ↑买入信号                           │ │  │ │
│ │ 信号强度: ●●●○○  │ │  │  1800├─────────MA5(黄线)─────────────────────┤ │  │ │
│ │ 评分: 78 🔴      │ │  │      │         MA20(蓝线)                     │ │  │ │
│ │                │ │  │  1750├─────────────────────────────────────────┤ │  │ │
│ │ 🟢 卖出信号(2只) │ │  │      │                                        │ │  │ │
│ │ 002415 海康威视  │ │  │  1700└─────────────────────────────────────────┘ │  │ │
│ │ 信号强度: ●●●●○  │ │  │       12/20  12/22  12/25  12/27 ← 当前        │  │ │
│ │ 评分: 75 🟢      │ │  │                                                │  │ │
│ │                │ │  │     🔴 买入信号点位标注:                        │  │ │
│ │ 000002 万科A     │ │  │     • 12/20: MA5金叉MA20 ✅                    │  │ │
│ │ 信号强度: ●●●○○  │ │  │     • 12/22: 突破前期高点 ✅                   │  │ │
│ │ 评分: 72 🟢      │ │  │     • 12/25: 成交量放大1.8倍 ✅                │  │ │
│ │                │ │  │     • 12/27: 当前强势买入信号 🔴                │  │ │
│ │ ⚪ 观察信号(8只)  │ │  └─────────────────────────────────────────────────┘  │ │
│ │ [查看全部]       │ │                                                        │ │
│ │                │ │  ┌─ 副图技术指标 + 信号确认 ──────────────────────────┐  │ │
│ │ 🔄 实时刷新      │ │  │ MACD: DIF>DEA 🔴金叉确认  柱状线转正 ✅         │  │ │
│ │ 最后更新:        │ │  │ RSI: 65.2 适中区间 ✅     未超买未超卖           │  │ │
│ │ 14:29:45        │ │  │ KDJ: K>D 🔴买入确认       J值65未超80            │  │ │
│ │                │ │  │ 成交量: 🔴放大信号确认     1.8倍放大积极          │  │ │
│ │ [⚙️ 信号设置]    │ │  │                                                │  │ │
│ │ [📊 历史信号]    │ │  │ 🎯 综合信号评分: 85分 🔴 强烈买入               │  │ │
│ │ [🔔 预警设置]    │ │  │ 📊 信号确信度: 78% (基于历史成功率)             │  │ │
│ └─────────────────┘ │  │ ⏰ 信号生成时间: 14:29:30                       │  │ │
│                    │  │ 💰 建议买入价格: ¥1845-1850                     │  │ │
│                    │  └─────────────────────────────────────────────────┘  │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 信号状态: [买入信号:5只] [卖出信号:2只] [观察:8只] [实时监控:开启] [延迟:50ms]  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **实时信号监控**: 左侧列表实时显示所有股票的买卖信号状态，按信号强度排序
2. **K线信号标注**: 主图直观显示🔴买入和🟢卖出信号点位，点击可查看详细原因
3. **多指标确认**: 副图显示MACD、RSI、KDJ等指标的信号确认状态
4. **信号评分系统**: 综合多个指标给出0-100分的信号强度评分
5. **一键操作**: 基于信号可直接执行买入卖出操作或设置价格提醒

---

## 📊 信号生成流程原型设计

### 10. 交易信号生成与确认界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🎯 交易信号生成引擎 - 基于VeighNa业务流程                                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   信号生成流程   │ │                    信号计算详情                        │ │
│ │                │ │                                                        │ │
│ │ 📊 数据输入      │ │  当前处理: 600519 贵州茅台                             │ │
│ │ ✅ K线数据加载   │ │  ┌─ 技术指标计算 ─────────────────────────────────────┐ │ │
│ │ ✅ 成交量数据    │ │  │ 📈 移动平均线计算:                               │ │ │
│ │ ✅ 基本面数据    │ │  │ MA5 = 1848.5 ↗ (上升趋势)                       │ │ │
│ │                │ │  │ MA20 = 1835.2 ↗ (上升趋势)                      │ │ │
│ │ 🔍 技术指标计算  │ │  │ 金叉状态: MA5 > MA20 ✅ (多头排列)               │ │ │
│ │ ✅ MA5/MA20     │ │  │                                                │ │ │
│ │ ✅ MACD指标     │ │  │ 📊 MACD指标计算:                                │ │ │
│ │ ✅ RSI指标      │ │  │ DIF = 12.5, DEA = 8.3, MACD = 4.2              │ │ │
│ │ ✅ KDJ指标      │ │  │ 金叉状态: DIF > DEA ✅ (买入信号)                │ │ │
│ │ ✅ 布林带       │ │  │ 柱状线: 转正 ✅ (动能增强)                       │ │ │
│ │ ✅ 成交量分析   │ │  │                                                │ │ │
│ │                │ │  │ 📈 RSI指标计算:                                 │ │ │
│ │ 🎯 信号生成     │ │  │ RSI = 65.2 (适中区间 30-70)                     │ │ │
│ │ 🔴 买入信号计算  │ │  │ 超买超卖: 未超买 ✅ (安全区间)                   │ │ │
│ │ 🟢 卖出信号计算  │ │  │                                                │ │ │
│ │ ⚪ 持有信号计算  │ │  │ 📊 成交量分析:                                  │ │ │
│ │                │ │  │ 当前成交量: 245万手                              │ │ │
│ │ 📋 信号确认     │ │  │ 量比: 1.8 (放大1.8倍) ✅                        │ │ │
│ │ ✅ 多指标共振    │ │  │ 资金流向: 净流入 ✅ (积极信号)                   │ │ │
│ │ ✅ 风险检查     │ │  └─────────────────────────────────────────────────┘ │ │
│ │ ✅ 仓位检查     │ │                                                        │ │
│ │                │ │  ┌─ 买入信号评分计算 ─────────────────────────────────┐ │ │
│ │ [🔄 重新计算]    │ │  │ 指标权重分配:                                    │ │ │
│ │ [📊 历史回测]    │ │  │ • MA金叉: 20分 ✅ (满分)                        │ │ │
│ │ [⚙️ 参数调整]    │ │  │ • MACD金叉: 20分 ✅ (满分)                      │ │ │
│ └─────────────────┘ │  │ • RSI适中: 15分 ✅ (满分)                       │ │ │
│                    │  │ • 成交量放大: 15分 ✅ (满分)                     │ │ │
│                    │  │ • 趋势确认: 10分 ✅ (满分)                       │ │ │
│                    │  │ • 基本面支撑: 5分 ✅ (满分)                      │ │ │
│                    │  │                                                │ │ │
│                    │  │ 🎯 综合评分: 85分 / 100分                        │ │ │
│                    │  │ 📊 信号强度: 🔴🔴🔴🔴○ (强烈买入)                │ │ │
│                    │  │ ⚠️ 风险评估: 中等风险                            │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    │                                                        │ │
│                    │  ┌─ 信号确认与执行 ───────────────────────────────────┐ │ │
│                    │  │ 🔍 信号验证:                                     │ │ │
│                    │  │ ✅ 评分 ≥ 70分 (当前85分)                        │ │ │
│                    │  │ ✅ 多指标共振确认                                │ │ │
│                    │  │ ✅ 风险控制检查通过                              │ │ │
│                    │  │ ✅ 仓位管理检查通过                              │ │ │
│                    │  │                                                │ │ │
│                    │  │ 🎯 最终信号: 🔴 生成买入信号                     │ │ │
│                    │  │ 💰 建议价格: ¥1845-1850                         │ │ │
│                    │  │ 🎯 目标价格: ¥1950 (+5.4%)                      │ │ │
│                    │  │ ⚠️ 止损价格: ¥1750 (-5.4%)                      │ │ │
│                    │  │                                                │ │ │
│                    │  │ [🔴 确认买入信号] [⚪ 标记观察] [❌ 忽略信号]      │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 处理状态: [计算完成] [信号:买入] [评分:85] [确信度:78%] [用时:0.15秒]           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **流程化处理**: 严格按照VeighNa业务流程进行信号生成，每步都有状态显示
2. **透明化计算**: 详细展示每个技术指标的计算过程和结果
3. **量化评分**: 将各指标按权重进行量化评分，最终得出综合评分
4. **多重确认**: 通过评分阈值、指标共振、风险检查等多重确认机制
5. **人工干预**: 支持人工确认、调整参数或忽略信号

---

## 📊 多股票信号监控大屏

### 11. 实时信号监控大屏界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ � 实时交易信号监控大屏 - 全市场信号扫描                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─ 信号统计概览 ─────────────────────────────────────────────────────────────┐   │
│ │ 🔴 买入信号: 23只  🟢 卖出信号: 15只  ⚪ 观察信号: 156只  📊 扫描股票: 3000只 │   │
│ │ 📈 信号成功率: 78.5%  ⏰ 最后更新: 14:29:45  🔄 扫描频率: 每30秒            │   │
│ └───────────────────────────────────────────────────────────────────────────┘   │
│                                                                                │
│ ┌─ 强烈买入信号 🔴 ─────────────────────────────────────────────────────────┐   │
│ │ 代码     名称       现价    涨跌幅   评分  信号强度  最新信号时间   操作     │   │
│ │ ──────────────────────────────────────────────────────────────────────── │   │
│ │ 600519  贵州茅台   1850.0  +2.78%   85   🔴🔴🔴🔴○  14:29:30    [买入]   │   │
│ │ 📊 MA金叉+MACD金叉+成交量放大  💰 建议价格: ¥1845-1850                    │   │
│ │                                                                          │   │
│ │ 000858  五粮液     185.0   +2.65%   82   🔴🔴🔴🔴○  14:28:15    [买入]   │   │
│ │ 📊 突破前期高点+量价齐升      💰 建议价格: ¥183-185                       │   │
│ │                                                                          │   │
│ │ 600036  招商银行   45.2    +2.73%   78   🔴🔴🔴○○  14:27:45    [买入]   │   │
│ │ 📊 金融股回暖+技术突破        💰 建议价格: ¥44.8-45.2                     │   │
│ │                                                                          │   │
│ │ 000001  平安银行   12.5    +4.17%   76   🔴🔴🔴○○  14:26:30    [买入]   │   │
│ │ 📊 估值修复+资金流入          💰 建议价格: ¥12.3-12.5                     │   │
│ │                                                                          │   │
│ │ [查看全部23只买入信号]                                                    │   │
│ └───────────────────────────────────────────────────────────────────────────┘   │
│                                                                                │
│ ┌─ 强烈卖出信号 🟢 ─────────────────────────────────────────────────────────┐   │
│ │ 代码     名称       现价    涨跌幅   评分  信号强度  最新信号时间   操作     │   │
│ │ ──────────────────────────────────────────────────────────────────────── │   │
│ │ 002415  海康威视   31.8    -0.63%   75   🟢🟢🟢🟢○  14:28:45    [卖出]   │   │
│ │ 📊 技术破位+成交量萎缩        💰 建议价格: ¥31.5-31.8                     │   │
│ │                                                                          │   │
│ │ 000002  万科A      17.8    -1.11%   72   🟢🟢🟢○○  14:27:20    [卖出]   │   │
│ │ 📊 地产调整+基本面转弱        💰 建议价格: ¥17.6-17.8                     │   │
│ │                                                                          │   │
│ │ 600519  中国平安   71.5    -0.85%   70   🟢🟢🟢○○  14:25:10    [卖出]   │   │
│ │ 📊 转型阵痛+业绩压力          💰 建议价格: ¥71.0-71.5                     │   │
│ │                                                                          │   │
│ │ [查看全部15只卖出信号]                                                    │   │
│ └───────────────────────────────────────────────────────────────────────────┘   │
│                                                                                │
│ ┌─ 信号热力图 ─────────────────────────────────────────────────────────────┐   │
│ │ 📊 行业信号分布:                                                         │   │
│ │ 金融 🔴🔴🔴🟢🟢 (买入3 卖出2)  消费 🔴🔴🟢 (买入2 卖出1)                  │   │
│ │ 科技 🔴🟢🟢🟢 (买入1 卖出3)    医药 🔴🔴 (买入2 卖出0)                    │   │
│ │ 地产 🟢🟢🟢 (买入0 卖出3)      能源 🔴 (买入1 卖出0)                      │   │
│ │                                                                          │   │
│ │ 📈 信号强度分布:                                                         │   │
│ │ 强烈信号(80+): 8只  中等信号(70-79): 15只  弱信号(60-69): 25只           │   │
│ └───────────────────────────────────────────────────────────────────────────┘   │
│                                                                                │
│ ┌─ 实时K线信号展示 ─────────────────────────────────────────────────────────┐   │
│ │ 选中股票: 600519 贵州茅台                                                │   │
│ │ ┌─ 迷你K线图 ─────────────────────────────────────────────────────────┐ │   │
│ │ │  1900┌─────────────────────────────────────────────────────────┐  │ │   │
│ │ │      │                                                        │  │ │   │
│ │ │  1850├─────🔴──────────────────────────────────────────────────┤  │ │   │
│ │ │      │    ↑14:29:30 买入信号                                   │  │ │   │
│ │ │  1800├─────────────────────────────────────────────────────────┤  │ │   │
│ │ │      │                                                        │  │ │   │
│ │ │  1750└─────────────────────────────────────────────────────────┘  │ │   │
│ │ │       12/25    12/26    12/27 ← 当前                            │ │   │
│ │ └─────────────────────────────────────────────────────────────────┘ │   │
│ │                                                                    │   │
│ │ 🎯 信号详情: MA5金叉MA20 + MACD金叉 + 成交量放大1.8倍                   │   │
│ │ 📊 技术指标: MA5↗ MA20↗ MACD金叉✅ RSI:65.2 KDJ:75,68,82             │   │
│ │ 💰 操作建议: 🔴 强烈买入 建议价格¥1845-1850 目标¥1950 止损¥1750        │   │
│ └───────────────────────────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 监控状态: [实时扫描] [信号:38个] [成功率:78.5%] [延迟:50ms] [自动刷新:30秒]    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **全市场扫描**: 实时扫描3000只股票，自动识别买卖信号
2. **分类展示**: 按信号类型和强度分类展示，便于快速筛选
3. **行业分析**: 信号热力图显示不同行业的信号分布情况
4. **详细展示**: 点击任意股票显示迷你K线图和详细信号信息
5. **一键操作**: 每个信号都可直接执行买入卖出或加入观察

---

## 🔄 信号回测验证界面

### 12. 历史信号成功率分析界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ �📈 K线图表分析 - 600519 贵州茅台 买入时机分析                                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   股票信息       │ │                    K线图表区域                         │ │
│ │                │ │                                                        │ │
│ │ 📊 基本信息      │ │  ┌─ 时间周期选择 ─────────────────────────────────────┐ │ │
│ │ 代码: 600519     │ │  │ [1分钟] [5分钟] [15分钟] [30分钟] [1小时] [日线]   │ │ │
│ │ 名称: 贵州茅台   │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 现价: ¥1850.00   │ │                                                        │ │
│ │ 涨跌: +50.00     │ │  ┌─ 主图K线 + 技术指标 ──────────────────────────────┐ │ │
│ │ 涨幅: +2.78% ↗   │ │  │                                                  │ │ │
│ │                │ │  │     📊 K线图表 (最近60个交易日)                   │ │ │
│ │ 💰 交易信息      │ │  │                                                  │ │ │
│ │ 成交量: 245万手  │ │  │     🕯️ 日K线 + MA5(黄线) + MA20(蓝线)            │ │ │
│ │ 成交额: 45.3亿   │ │  │                                                  │ │ │
│ │ 换手率: 1.95%    │ │  │     🔴 买入信号标注:                            │ │ │
│ │ 量比: 1.8        │ │  │     • 12-20: MA5上穿MA20 金叉                   │ │ │
│ │                │ │  │     • 12-22: 突破前期高点                       │ │ │
│ │ 📈 技术指标      │ │  │     • 12-25: 成交量放大突破                     │ │ │
│ │ MA5: 1848.5 ↗    │ │  │     • 12-27: 当前位置 ← 🔴 建议买入             │ │ │
│ │ MA20: 1835.2 ↗   │ │  │                                                  │ │ │
│ │ MACD: 金叉 ✅    │ │  │     支撑位: ¥1820 ¥1800 ¥1780                   │ │ │
│ │ RSI: 65.2        │ │  │     压力位: ¥1860 ¥1880 ¥1900                   │ │ │
│ │ KDJ: 75,68,82    │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ 🎯 买入建议      │ │  ┌─ 副图技术指标 ─────────────────────────────────────┐ │ │
│ │ 信号强度: 🔴🔴🔴  │ │  │ MACD: DIF>DEA 金叉 ✅  柱状线转正 ✅            │ │ │
│ │ 建议价格:        │ │  │ RSI: 65.2 (适中区间) ✅  未超买                 │ │ │
│ │ ¥1845-1850      │ │  │ KDJ: K线上穿D线 ✅  J值未超80                   │ │ │
│ │ 目标价格:        │ │  │ 成交量: 持续放大 ✅  资金流入积极                │ │ │
│ │ ¥1950 (+5.4%)   │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 止损价格:        │ │                                                        │ │
│ │ ¥1750 (-5.4%)   │ │  ┌─ 买入时机分析 ─────────────────────────────────────┐ │ │
│ │                │ │  │ ✅ 技术形态: 上升趋势确立，多头排列                │ │ │
│ │ [🔴 立即买入]    │ │  │ ✅ 成交量: 1.8倍放大，资金流入积极              │ │ │
│ │ [⚪ 加入观察]    │ │  │ ✅ 指标共振: MACD、RSI、KDJ同步看多             │ │ │
│ │ [🔔 价格提醒]    │ │  │ ⚠️ 风险提示: 接近前期高点，注意回调风险          │ │ │
│ └─────────────────┘ │  │                                                  │ │ │
│                    │  │ 🎯 综合判断: 🔴 强烈买入信号                     │ │ │
│                    │  │ 📊 成功概率: 78% (基于历史相似形态)              │ │ │
│                    │  │ ⏰ 最佳买入时间: 开盘后30分钟内                  │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 分析状态: [数据更新:实时] [信号检测:开启] [技术分析:完成] [建议:强烈买入]       │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **多周期切换**: 顶部时间周期按钮，点击切换不同时间框架
2. **图表交互**: 支持缩放、平移、十字光标查看历史数据
3. **指标叠加**: 可动态添加/移除技术指标，自定义参数
4. **信号标注**: 自动识别并标注买卖信号点位
5. **实时更新**: 价格和指标数据实时刷新，信号实时计算

---

## 💼 持仓监控模块详细原型设计

### 10. 持仓股票实时监控界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 💼 持仓股票监控 - 实时跟踪与卖出信号识别                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   持仓概况       │ │                    持仓股票详细监控                    │ │
│ │                │ │                                                        │ │
│ │ 💰 资产概况      │ │  代码    名称      持仓    成本价   现价    盈亏    信号  │ │
│ │ 总市值:          │ │  ──────────────────────────────────────────────────── │ │
│ │ ¥850,000        │ │  600519  贵州茅台   100   1800.0  1850.0  +5000  ⚪持有 │ │
│ │ 成本:            │ │  📊 技术状态: MA5>MA20 ✅  MACD金叉 ✅  RSI:65.2     │ │
│ │ ¥834,800        │ │  🎯 卖出信号: 无明显卖出信号，继续持有               │ │
│ │ 盈亏:            │ │  ⚠️ 风险提示: 接近前期高点，注意回调                │ │
│ │ +¥15,200(+1.82%)│ │                                                        │ │
│ │                │ │  000858  五粮液     500    180.0   185.0  +2500  ⚪持有 │ │
│ │ 📊 今日变化      │ │  📊 技术状态: 上升趋势 ✅  成交量放大 ✅  RSI:58.3   │ │
│ │ 今日盈亏:        │ │  🎯 卖出信号: 无卖出信号，趋势向好                   │ │
│ │ +¥8,500(+1.01%) │ │  💡 操作建议: 继续持有，关注¥190压力位               │ │
│ │ 涨停股票: 0只    │ │                                                        │ │
│ │ 跌停股票: 0只    │ │  600036  招商银行  2000     44.0    45.2  +2400  ⚪持有 │ │
│ │                │ │  📊 技术状态: 震荡上行 ✅  量价配合 ✅  RSI:62.1      │ │
│ │ 🔔 预警信息      │ │  🎯 卖出信号: 无卖出信号，金融股回暖                 │ │
│ │ 卖出信号: 2只    │ │  💡 操作建议: 持有，银行股估值修复中                 │ │
│ │ 止损预警: 0只    │ │                                                        │ │
│ │ 止盈提醒: 1只    │ │  000001  平安银行  3000     12.0    12.5  +1500  ⚪持有 │ │
│ │                │ │  📊 技术状态: 强势上涨 ✅  突破压力 ✅  RSI:71.5      │ │
│ │ 🎯 操作建议      │ │  🎯 卖出信号: RSI接近超买，关注回调风险               │ │
│ │ 建议卖出: 2只    │ │  ⚠️ 操作建议: 可考虑部分获利了结                    │ │
│ │ 建议加仓: 1只    │ │                                                        │ │
│ │ 继续持有: 5只    │ │  002415  海康威视  2000     32.0    31.8   -400  🟢卖出 │ │
│ │                │ │  📊 技术状态: 下降趋势 ❌  MA5<MA20 ❌  RSI:42.8      │ │
│ │ [📊 详细分析]    │ │  🎯 卖出信号: 🟢 强烈卖出 - 技术破位                │ │
│ │ [⚖️ 组合再平衡]  │ │  💡 操作建议: 建议及时止损，避免进一步亏损           │ │
│ │ [📈 绩效报告]    │ │                                                        │ │
│ └─────────────────┘ │  000002  万科A     5000     18.0    17.8  -1000  🟢卖出 │ │
│                    │  📊 技术状态: 弱势下跌 ❌  成交萎缩 ❌  RSI:35.2      │ │
│                    │  🎯 卖出信号: 🟢 卖出 - 地产调整，基本面转弱          │ │
│                    │  💡 操作建议: 建议减仓，等待行业企稳                   │ │
│                    │                                                        │ │
│                    │  600887  伊利股份  1000     35.0    36.2  +1200  ⚪持有 │ │
│                    │  📊 技术状态: 稳步上涨 ✅  基本面稳 ✅  RSI:55.8      │ │
│                    │  🎯 卖出信号: 无卖出信号，消费股稳健                   │ │
│                    │  💡 操作建议: 长期持有，防御性品种                     │ │
│                    │                                                        │ │
│                    │  600276  恒瑞医药   300     65.0    68.5  +1050  ⚪持有 │ │
│                    │  📊 技术状态: 反弹上涨 ✅  研发投入 ✅  RSI:61.3      │ │
│                    │  🎯 卖出信号: 无卖出信号，医药股回暖                   │ │
│                    │  💡 操作建议: 持有，关注创新药进展                     │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 监控状态: [实时更新] [信号检测:开启] [预警:2个] [建议操作:卖出2只] [风控:正常] │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **实时监控**: 持仓数据每秒更新，价格变化实时反映
2. **智能分析**: 每只股票都有技术状态和卖出信号分析
3. **预警机制**: 左侧概况显示各类预警信息，异常时弹窗提醒
4. **操作建议**: 基于算法给出具体的操作建议和理由
5. **快速操作**: 支持一键查看详细分析、执行交易操作

---

## 🟢 卖出信号分析详细原型设计

### 11. 专业卖出时机判断界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🟢 卖出信号分析 - 002415 海康威视 卖出时机分析                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   持仓信息       │ │                    K线卖出分析                         │ │
│ │                │ │                                                        │ │
│ │ 📊 持仓详情      │ │  ┌─ 卖出信号识别 ─────────────────────────────────────┐ │ │
│ │ 代码: 002415     │ │  │                                                  │ │ │
│ │ 名称: 海康威视   │ │  │     📉 K线图表 (最近30个交易日)                   │ │ │
│ │ 持仓: 2000股     │ │  │                                                  │ │ │
│ │ 成本: ¥32.00     │ │  │     🟢 卖出信号标注:                            │ │ │
│ │ 现价: ¥31.80     │ │  │     • 12-15: MA5下穿MA20 死叉 ❌                │ │ │
│ │ 盈亏: -¥400      │ │  │     • 12-18: 跌破重要支撑位 ❌                  │ │ │
│ │ 盈亏率: -1.25%   │ │  │     • 12-22: 成交量萎缩下跌 ❌                  │ │ │
│ │                │ │  │     • 12-27: 当前位置 ← 🟢 建议卖出             │ │ │
│ │ 💰 成本分析      │ │  │                                                  │ │ │
│ │ 总成本: ¥64,000  │ │  │     支撑位: ¥31.5 ¥31.0 ¥30.5 (已跌破)          │ │ │
│ │ 现市值: ¥63,600  │ │  │     压力位: ¥32.5 ¥33.0 ¥33.5                   │ │ │
│ │ 手续费: ¥19.2    │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 净亏损: -¥419.2  │ │                                                        │ │
│ │                │ │  ┌─ 技术指标分析 ─────────────────────────────────────┐ │ │
│ │ 📈 技术状态      │ │  │ MA5: 31.85 ↘  MA20: 32.15 ↘  死叉形态 ❌        │ │ │
│ │ MA5: 31.85 ↘     │ │  │ MACD: DIF<DEA 死叉 ❌  柱状线转负 ❌            │ │ │
│ │ MA20: 32.15 ↘    │ │  │ RSI: 42.8 (偏弱) ❌  趋势转弱                   │ │ │
│ │ MACD: 死叉 ❌    │ │  │ KDJ: K线下穿D线 ❌  J值跌破20                   │ │ │
│ │ RSI: 42.8 ↘      │ │  │ 成交量: 持续萎缩 ❌  资金流出明显                │ │ │
│ │ KDJ: 38,42,35    │ │  └─────────────────────────────────────────────────┘ │ │
│ │                │ │                                                        │ │
│ │ 🎯 卖出建议      │ │  ┌─ 卖出时机分析 ─────────────────────────────────────┐ │ │
│ │ 信号强度: 🟢🟢🟢  │ │  │ ❌ 技术形态: 下降趋势确立，空头排列              │ │ │
│ │ 建议操作:        │ │  │ ❌ 成交量: 持续萎缩，资金流出明显                │ │ │
│ │ 🟢 立即卖出      │ │  │ ❌ 指标共振: MACD、RSI、KDJ同步看空             │ │ │
│ │ 目标价格:        │ │  │ ⚠️ 基本面: 行业增长放缓，竞争加剧                │ │ │
│ │ ¥30.50 (-4.1%)  │ │  │                                                  │ │ │
│ │ 止损价格:        │ │  │ 🎯 综合判断: 🟢 强烈卖出信号                     │ │ │
│ │ ¥30.00 (-6.3%)  │ │  │ 📊 下跌概率: 82% (基于历史相似形态)              │ │ │
│ │                │ │  │ ⏰ 最佳卖出时间: 开盘后立即卖出                  │ │ │
│ │ [🟢 立即卖出]    │ │  │ 💡 风险提示: 继续持有可能面临更大亏损            │ │ │
│ │ [⚪ 继续观察]    │ │  └─────────────────────────────────────────────────┘ │ │
│ │ [🔔 价格提醒]    │ │                                                        │ │
│ └─────────────────┘ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 卖出分析: [信号:强烈卖出] [风险:高] [建议:立即卖出] [预期跌幅:4-6%] [确信度:82%]│
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **持仓分析**: 左侧详细显示持仓成本、盈亏情况
2. **技术诊断**: 全面分析技术指标，识别卖出信号
3. **风险评估**: 基于历史数据计算下跌概率和预期跌幅
4. **决策支持**: 提供明确的操作建议和最佳时机
5. **一键操作**: 支持立即卖出或设置价格提醒

---

## 📊 投资决策工作台原型设计

### 12. 完整投资决策流程界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📈 VeighNa投资决策系统                           [连接状态] [时间] [用户] [设置] │
├─────────────────────────────────────────────────────────────────────────────────┤
│ [智能选股] [K线分析] [买入决策] [持仓监控] [卖出决策] [投资记录] [系统设置]      │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   推荐股票池     │ │                    K线图表分析区域                      │ │
│ │                │ │                                                        │ │
│ │ 📊 今日推荐(15只)│ │  当前分析: 600519 贵州茅台                             │ │
│ │                │ │  ┌─────────────────────────────────────────────────┐  │ │
│ │ 🔴 买入机会      │ │  │ [1m][5m][15m][30m][1h][4h][1d] 📊 技术指标      │  │ │
│ │ 600519 贵州茅台  │ │  │                                                │  │ │
│ │ 评分:85 ↗ 强势   │ │  │     📈 K线图 + MA5/MA20 + MACD + RSI           │  │ │
│ │ 买入信号: 🔴     │ │  │                                                │  │ │
│ │                │ │  │     🔴 买入信号: MA金叉 + MACD金叉 + RSI>30      │  │ │
│ │ 000858 五粮液    │ │  │     📊 成交量放大 1.8倍                        │  │ │
│ │ 评分:82 ↗ 强势   │ │  │     💰 建议买入价格: ¥1845-1850                │  │ │
│ │ 买入信号: 🔴     │ │  │                                                │  │ │
│ │                │ │  └─────────────────────────────────────────────────┘  │ │
│ │ 600036 招商银行  │ │                                                        │ │
│ │ 评分:80 → 平稳   │ │  ┌─ 技术分析详情 ─────────────────────────────────┐  │ │
│ │ 观察信号: ⚪     │ │  │ MA5: 1848.5 ↗  MA20: 1835.2 ↗  多头排列 ✅     │  │ │
│ │                │ │  │ MACD: DIF>DEA 金叉 ✅  柱状线转正 ✅            │  │ │
│ │ 🟢 卖出机会      │ │  │ RSI: 65.2 (30-70区间) ✅  未超买 ✅            │  │ │
│ │ 002415 海康威视  │ │  │ 布林带: 价格接近上轨 ⚠️  注意压力位             │  │ │
│ │ 评分:75 ↘ 转弱   │ │  │ 成交量: 放大1.8倍 ✅  资金流入积极              │  │ │
│ │ 卖出信号: 🟢     │ │  │                                                │  │ │
│ │                │ │  │ 综合判断: 🔴 强烈买入信号                       │  │ │
│ │ [🔄 刷新推荐]    │ │  │ 风险等级: 中等 ⚠️                              │  │ │
│ │ [⚙️ 选股设置]    │ │  └─────────────────────────────────────────────────┘  │ │
│ └─────────────────┘ └─────────────────────────────────────────────────────────┘ │
│                                                                                │ │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                           持仓股票监控面板                                   │ │
│ │                                                                            │ │
│ │ 📊 持仓概况: 总市值 ¥850,000 | 今日盈亏 +¥15,200 (+1.82%) | 持仓 8只        │ │
│ │                                                                            │ │
│ │ 代码    名称      数量   成本价   现价    盈亏     涨跌幅   卖出信号  操作    │ │
│ │ ──────────────────────────────────────────────────────────────────────────── │ │
│ │ 600519  贵州茅台   100   1800.0  1850.0  +5000   +2.78%   ⚪ 持有  [分析]  │ │
│ │ 000858  五粮液     500    180.0   185.0  +2500   +2.78%   ⚪ 持有  [分析]  │ │
│ │ 600036  招商银行  2000     44.0    45.2  +2400   +2.73%   ⚪ 持有  [分析]  │ │
│ │ 000001  平安银行  3000     12.0    12.5  +1500   +4.17%   ⚪ 持有  [分析]  │ │
│ │ 002415  海康威视  2000     32.0    31.8   -400   -0.63%   🟢 卖出  [分析]  │ │
│ │ 600887  伊利股份  1000     35.0    36.2  +1200   +3.43%   ⚪ 持有  [分析]  │ │
│ │ 000002  万科A     5000     18.0    17.8  -1000   -1.11%   🟢 卖出  [分析]  │ │
│ │ 600276  恒瑞医药   300    65.0    68.5  +1050   +5.38%   ⚪ 持有  [分析]  │ │
│ │                                                                            │ │
│ │ [📊 组合分析] [⚖️ 再平衡] [📈 绩效报告] [⚙️ 风控设置]                        │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 状态: [推荐更新:09:30] [持仓监控:实时] [信号检测:开启] [风控状态:正常]          │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **一体化工作台**: 集成选股、分析、监控功能于一个界面
2. **实时数据流**: 推荐股票、K线数据、持仓信息实时更新
3. **智能联动**: 点击推荐股票自动加载K线分析
4. **快速决策**: 基于分析结果可直接执行买入卖出操作
5. **全程跟踪**: 从选股到持仓的完整投资流程跟踪

---

## 📊 参数优化模块原型设计

### 13. 策略参数优化界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🎯 策略参数优化系统 - 遗传算法 + 网格搜索                                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   优化配置       │ │                    优化结果展示                        │ │
│ │                │ │                                                        │ │
│ │ 📊 优化目标      │ │  ┌─ 参数优化进度 ─────────────────────────────────────┐ │ │
│ │ 主要目标:        │ │  │ 当前代数: 45/100  最优个体: #1247                  │ │ │
│ │ ☑️ 夏普比率最大   │ │  │ 进度: ████████████████░░░░░░ 45%                  │ │ │
│ │ ☑️ 最大回撤最小   │ │  │ 预计剩余时间: 00:15:30                           │ │ │
│ │ ☑️ 年化收益>15%   │ │  │ 当前最优夏普比率: 2.35                           │ │ │
│ │                │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 📈 参数范围      │ │                                                        │ │
│ │ 快速均线:        │ │  ┌─ 最优参数组合 ─────────────────────────────────────┐ │ │
│ │ [3  - 10 ] 步长1 │ │  │ 参数名称        当前值    最优值    改进幅度        │ │ │
│ │ 慢速均线:        │ │  │ ──────────────────────────────────────────────── │ │ │
│ │ [15 - 30 ] 步长1 │ │  │ 快速均线(MA_S)    5        7       +40%收益      │ │ │
│ │ RSI周期:         │ │  │ 慢速均线(MA_L)   20       25       +15%胜率      │ │ │
│ │ [10 - 20 ] 步长1 │ │  │ RSI周期(RSI_P)   14       16       +8%夏普比率   │ │ │
│ │ 止损比例:        │ │  │ 止损比例(STOP)   5%       4%       -12%最大回撤  │ │ │
│ │ [3% - 8% ] 步长1%│ │  │ 止盈比例(TAKE)  15%      18%       +25%盈亏比    │ │ │
│ │ 止盈比例:        │ │  └─────────────────────────────────────────────────┘ │ │
│ │ [10%-25%] 步长1% │ │                                                        │ │
│ │                │ │  ┌─ 优化算法对比 ─────────────────────────────────────┐ │ │
│ │ 🔧 算法选择      │ │  │ 算法类型      迭代次数   最优夏普   最大回撤   用时  │ │ │
│ │ ☑️ 遗传算法       │ │  │ ──────────────────────────────────────────────── │ │ │
│ │ ☑️ 网格搜索       │ │  │ 遗传算法        45       2.35      -6.2%    15min │ │ │
│ │ ☑️ 随机搜索       │ │  │ 网格搜索       156       2.28      -6.8%    25min │ │ │
│ │ ☑️ 贝叶斯优化     │ │  │ 随机搜索        89       2.15      -7.1%    12min │ │ │
│ │                │ │  │ 贝叶斯优化      23       2.31      -6.5%     8min │ │ │
│ │ 🎯 优化设置      │ │  │                                                  │ │ │
│ │ 种群大小: 50     │ │  │ 🏆 推荐算法: 遗传算法 (综合表现最佳)              │ │ │
│ │ 最大代数: 100    │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 变异率: 0.1      │ │                                                        │ │
│ │ 交叉率: 0.8      │ │  ┌─ 优化结果验证 ─────────────────────────────────────┐ │ │
│ │                │ │  │ 样本外测试期间: 2024-01-01 至 2024-12-27           │ │ │
│ │ [▶️ 开始优化]    │ │  │ 优化前绩效: 收益+12.5% 夏普1.85 回撤-8.2%         │ │ │
│ │ [⏹️ 停止优化]    │ │  │ 优化后绩效: 收益+18.7% 夏普2.35 回撤-6.2%         │ │ │
│ │ [💾 保存参数]    │ │  │ 改进效果: 收益+49% 夏普+27% 回撤-24%              │ │ │
│ │ [📊 详细报告]    │ │  │                                                  │ │ │
│ └─────────────────┘ │  │ ✅ 验证通过，建议采用优化后参数                   │ │ │
│                    │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 优化状态: [算法:遗传算法] [进度:45%] [最优夏普:2.35] [预计完成:00:15:30]       │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **多目标优化**: 支持设置多个优化目标和权重
2. **算法对比**: 同时运行多种优化算法，自动推荐最佳
3. **实时监控**: 优化过程实时显示进度和中间结果
4. **参数验证**: 优化完成后进行样本外验证
5. **一键应用**: 验证通过后可直接应用优化参数

---

## 📈 绩效分析模块原型设计

### 14. 策略绩效分析报告界面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📈 策略绩效分析报告 - MultiTimeframeStrategy 年度总结                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   绩效概览       │ │                    绩效图表展示                        │ │
│ │                │ │                                                        │ │
│ │ 💰 收益指标      │ │  ┌─ 净值曲线对比 ─────────────────────────────────────┐ │ │
│ │ 总收益率:        │ │  │                                                  │ │ │
│ │ +25.8% 🟢       │ │  │     📈 策略净值曲线 vs 基准指数                   │ │ │
│ │ 年化收益率:      │ │  │                                                  │ │ │
│ │ +18.5% 🟢       │ │  │     策略收益: +25.8% (蓝线)                      │ │ │
│ │ 基准收益率:      │ │  │     沪深300: +12.3% (灰线)                       │ │ │
│ │ +12.3% (沪深300) │ │  │     超额收益: +13.5%                            │ │ │
│ │ 超额收益:        │ │  │                                                  │ │ │
│ │ +13.5% 🟢       │ │  │     📊 月度收益分布                              │ │ │
│ │                │ │  │     盈利月份: 8个  亏损月份: 4个                  │ │ │
│ │ ⚠️ 风险指标      │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 最大回撤:        │ │                                                        │ │
│ │ -8.2% 🟡        │ │  ┌─ 风险收益散点图 ───────────────────────────────────┐ │ │
│ │ 波动率:          │ │  │                                                  │ │ │
│ │ 12.8% 🟢        │ │  │     📊 风险-收益象限图                           │ │ │
│ │ VaR(95%):       │ │  │                                                  │ │ │
│ │ -2.8% 🟢        │ │  │     当前策略: 高收益-中风险象限 ✅                │ │ │
│ │ 夏普比率:        │ │  │     基准指数: 中收益-中风险象限                   │ │ │
│ │ 1.85 🟢         │ │  │     同类策略平均: 低收益-高风险象限               │ │ │
│ │                │ │  │                                                  │ │ │
│ │ 📊 交易统计      │ │  │     🎯 策略定位: 优秀                            │ │ │
│ │ 交易次数: 47次   │ │  └─────────────────────────────────────────────────┘ │ │
│ │ 胜率: 68.5% 🟢   │ │                                                        │ │
│ │ 盈亏比: 1.78 🟢  │ │  ┌─ 回撤分析 ─────────────────────────────────────────┐ │ │
│ │ 平均持仓: 8.5天  │ │  │ 最大回撤: -8.2%  发生时间: 2024-03-15              │ │ │
│ │ 换手率: 2.3倍    │ │  │ 回撤持续: 15天   恢复时间: 25天                   │ │ │
│ │                │ │  │ 回撤次数: 3次    平均回撤: -4.1%                   │ │ │
│ │ [📊 详细报告]    │ │  │                                                  │ │ │
│ │ [📤 导出Excel]   │ │  │ 📊 回撤分布图                                    │ │ │
│ │ [📧 邮件发送]    │ │  │ 回撤恢复能力: 强 ✅                              │ │ │
│ └─────────────────┘ │  └─────────────────────────────────────────────────┘ │ │
│                    └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 绩效评级: A级 🏆 | 风险等级: 中等 ⚠️ | 推荐指数: ⭐⭐⭐⭐⭐ | 适合投资者: 进取型  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 交互逻辑说明：
1. **多维度评估**: 从收益、风险、交易等多角度分析绩效
2. **可视化展示**: 丰富的图表展示绩效变化趋势
3. **基准对比**: 与市场基准和同类策略对比
4. **风险分析**: 详细的回撤分析和风险指标
5. **报告导出**: 支持多种格式的报告导出和分享

---

## 📱 移动端原型设计

### 15. 移动端主界面设计 (375x812)

```
┌─────────────────────────────────────────┐
│ 📊 VeighNa量化交易     🔔 ⚙️ 👤        │
├─────────────────────────────────────────┤
│ ┌─ 今日概况 ─────────────────────────┐   │
│ │ 💰 总资产: ¥1,250,000              │   │
│ │ 📈 今日盈亏: +¥12,500 (+1.2%) ↗   │   │
│ │ 📊 持仓股票: 8只 | 现金: 25%       │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 推荐股票 ─────────────────────────┐   │
│ │ 🔴 600519 贵州茅台  评分:85 ↗      │   │
│ │    ¥1850.00 (+2.78%)  [买入]      │   │
│ │                                   │   │
│ │ 🔴 000858 五粮液    评分:82 ↗      │   │
│ │    ¥185.00 (+2.78%)   [买入]      │   │
│ │                                   │   │
│ │ 🟢 002415 海康威视  评分:75 ↘      │   │
│ │    ¥31.80 (-0.63%)    [卖出]      │   │
│ │                                   │   │
│ │ [查看更多推荐]                     │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 持仓监控 ─────────────────────────┐   │
│ │ 600519 贵州茅台    +¥5000 🟢      │   │
│ │ 100股 ¥1800→¥1850  [分析]         │   │
│ │                                   │   │
│ │ 002415 海康威视    -¥400 🔴        │   │
│ │ 2000股 ¥32.0→¥31.8 [卖出]         │   │
│ │                                   │   │
│ │ [查看全部持仓]                     │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 快速操作 ─────────────────────────┐   │
│ │ [📊 K线分析] [💼 组合管理]          │   │
│ │ [⚡ 交易执行] [📈 绩效报告]          │   │
│ └───────────────────────────────────┘   │
├─────────────────────────────────────────┤
│ [🏠首页] [📊选股] [📈分析] [💼持仓] [⚙️设置] │
└─────────────────────────────────────────┘
```

### 16. 移动端K线分析界面

```
┌─────────────────────────────────────────┐
│ ← 600519 贵州茅台           🔔 ⭐ ⚙️    │
├─────────────────────────────────────────┤
│ ¥1850.00  +¥50.00  +2.78% ↗           │
│                                         │
│ ┌─ K线图表 ─────────────────────────┐   │
│ │ [1m][5m][15m][1h][1d] 📊          │   │
│ │                                   │   │
│ │     📈 K线图 + MA5/MA20            │   │
│ │                                   │   │
│ │     🔴 买入信号标注                │   │
│ │     📊 成交量柱状图                │   │
│ │                                   │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 技术指标 ─────────────────────────┐   │
│ │ MA5: 1848.5 ↗  MA20: 1835.2 ↗     │   │
│ │ MACD: 金叉 ✅   RSI: 65.2          │   │
│ │ KDJ: 75,68,82  成交量: 1.8倍 ↗     │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 买入建议 ─────────────────────────┐   │
│ │ 🔴 强烈买入信号                    │   │
│ │ 建议价格: ¥1845-1850               │   │
│ │ 目标价格: ¥1950 (+5.4%)            │   │
│ │ 止损价格: ¥1750 (-5.4%)            │   │
│ │                                   │   │
│ │ [🔴 立即买入] [⚪ 加入观察]         │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 快速操作 ─────────────────────────┐   │
│ │ [📊 更多指标] [🔔 价格提醒]         │   │
│ │ [📈 历史分析] [💬 分享观点]         │   │
│ └───────────────────────────────────┘   │
├─────────────────────────────────────────┤
│ [🏠首页] [📊选股] [📈分析] [💼持仓] [⚙️设置] │
└─────────────────────────────────────────┘
```

#### 移动端交互逻辑说明：
1. **触控优化**: 按钮大小适配手指操作，支持滑动手势
2. **信息精简**: 显示最关键信息，详细信息通过点击展开
3. **快速操作**: 常用功能一键直达，减少操作步骤
4. **离线缓存**: 关键数据本地缓存，网络不佳时也能查看
5. **推送通知**: 重要信号和预警通过推送及时提醒

---

## 💻 完整技术实现架构

### 系统架构设计

#### 1. 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        前端展示层                                │
├─────────────────────────────────────────────────────────────────┤
│ Web端(Electron+Vue3)  │  移动端(React Native)  │  API接口层      │
├─────────────────────────────────────────────────────────────────┤
│                        业务逻辑层                                │
├─────────────────────────────────────────────────────────────────┤
│ 智能选股服务 │ VeighNa回测引擎 │ 组合管理服务 │ 交易执行服务    │
├─────────────────────────────────────────────────────────────────┤
│                        数据服务层                                │
├─────────────────────────────────────────────────────────────────┤
│ 数据采集服务 │ 因子计算服务 │ 实时数据服务 │ 历史数据服务    │
├─────────────────────────────────────────────────────────────────┤
│                        基础设施层                                │
├─────────────────────────────────────────────────────────────────┤
│ PostgreSQL │ Redis │ Kafka │ Elasticsearch │ 监控告警系统      │
└─────────────────────────────────────────────────────────────────┘
```

#### 2. 核心技术栈
```yaml
前端技术:
  桌面端: Electron + Vue 3 + TypeScript + Element Plus
  移动端: React Native + TypeScript + Ant Design Mobile
  图表库: ECharts 5.x + TradingView Charting Library
  状态管理: Pinia (Vue) / Redux Toolkit (React Native)

后端技术:
  核心框架: FastAPI + Python 3.9+
  量化引擎: VeighNa + QuantLib + NumPy + Pandas
  数据库: PostgreSQL + Redis + ClickHouse
  消息队列: Apache Kafka + RabbitMQ

数据源:
  股票数据: ADATA + Wind + 同花顺iFinD
  基本面: 财务数据API + 新闻舆情API
  实时行情: WebSocket + TCP推送

部署运维:
  容器化: Docker + Kubernetes
  监控: Prometheus + Grafana + ELK Stack
  CI/CD: GitLab CI + Jenkins
  云服务: 阿里云 / 腾讯云 / AWS
```

#### 3. 数据库设计
```sql
-- 股票基础信息表
CREATE TABLE stock_info (
    stock_code VARCHAR(10) PRIMARY KEY,
    stock_name VARCHAR(50) NOT NULL,
    list_date DATE,
    industry VARCHAR(50),
    market VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 多时间周期行情数据表
CREATE TABLE market_data (
    id BIGSERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    trade_date DATE NOT NULL,
    trade_time TIME,
    timeframe VARCHAR(10) NOT NULL, -- 1m, 5m, 15m, 1h, 1d
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    amount DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (trade_date);

-- 因子配置表
CREATE TABLE factor_config (
    factor_id VARCHAR(50) PRIMARY KEY,
    factor_name VARCHAR(100) NOT NULL,
    factor_type VARCHAR(20) NOT NULL, -- technical, fundamental, market
    weight DECIMAL(5,4) DEFAULT 0,
    parameters JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 股票评分表
CREATE TABLE stock_scores (
    id BIGSERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    score_date DATE NOT NULL,
    total_score DECIMAL(5,2),
    technical_score DECIMAL(5,2),
    fundamental_score DECIMAL(5,2),
    market_score DECIMAL(5,2),
    signal_type VARCHAR(10), -- buy, sell, hold
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 回测结果表
CREATE TABLE backtest_results (
    id BIGSERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    stock_code VARCHAR(10) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    initial_capital DECIMAL(15,2),
    final_capital DECIMAL(15,2),
    total_return DECIMAL(8,4),
    annual_return DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    sharpe_ratio DECIMAL(8,4),
    win_rate DECIMAL(5,4),
    trade_count INTEGER,
    parameters JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 投资组合表
CREATE TABLE portfolios (
    id BIGSERIAL PRIMARY KEY,
    portfolio_name VARCHAR(100) NOT NULL,
    user_id INTEGER NOT NULL,
    total_value DECIMAL(15,2),
    cash_ratio DECIMAL(5,4),
    risk_level VARCHAR(20),
    rebalance_frequency VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 持仓明细表
CREATE TABLE positions (
    id BIGSERIAL PRIMARY KEY,
    portfolio_id INTEGER REFERENCES portfolios(id),
    stock_code VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    cost_price DECIMAL(10,3),
    current_price DECIMAL(10,3),
    market_value DECIMAL(15,2),
    weight DECIMAL(5,4),
    profit_loss DECIMAL(15,2),
    profit_loss_ratio DECIMAL(8,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 交易订单表
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_id VARCHAR(50) UNIQUE NOT NULL,
    portfolio_id INTEGER REFERENCES portfolios(id),
    stock_code VARCHAR(10) NOT NULL,
    order_type VARCHAR(10) NOT NULL, -- buy, sell
    quantity INTEGER NOT NULL,
    price DECIMAL(10,3),
    status VARCHAR(20) NOT NULL, -- pending, filled, cancelled, partial
    filled_quantity INTEGER DEFAULT 0,
    filled_price DECIMAL(10,3),
    commission DECIMAL(10,2),
    slippage DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. API接口设计
```python
# FastAPI 接口示例
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import asyncio

app = FastAPI(title="VeighNa量化交易系统API", version="2.0")

# 数据模型
class StockScore(BaseModel):
    stock_code: str
    stock_name: str
    total_score: float
    technical_score: float
    fundamental_score: float
    market_score: float
    signal_type: str
    recommendation: str

class BacktestConfig(BaseModel):
    strategy_name: str
    stock_code: str
    start_date: str
    end_date: str
    initial_capital: float
    parameters: dict

class BacktestResult(BaseModel):
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    trade_count: int

# API路由
@app.get("/api/stock-selection/recommended", response_model=List[StockScore])
async def get_recommended_stocks(limit: int = 15):
    """获取推荐股票列表"""
    # 调用智能选股服务
    stocks = await stock_selection_service.get_recommended_stocks(limit)
    return stocks

@app.post("/api/backtest/run", response_model=BacktestResult)
async def run_backtest(config: BacktestConfig):
    """运行策略回测"""
    # 调用VeighNa回测引擎
    result = await vnpy_backtest_service.run_backtest(config)
    return result

@app.get("/api/portfolio/{portfolio_id}/positions")
async def get_portfolio_positions(portfolio_id: int):
    """获取投资组合持仓"""
    positions = await portfolio_service.get_positions(portfolio_id)
    return positions

@app.post("/api/orders/create")
async def create_order(order_data: dict):
    """创建交易订单"""
    order = await trading_service.create_order(order_data)
    return order

@app.websocket("/ws/market-data")
async def websocket_market_data(websocket: WebSocket):
    """实时行情数据推送"""
    await websocket.accept()
    while True:
        # 推送实时数据
        data = await market_data_service.get_realtime_data()
        await websocket.send_json(data)
        await asyncio.sleep(1)
```

#### 5. 前端组件实现
```vue
<!-- Vue 3 组件示例 -->
<template>
  <div class="vnpy-trading-system">
    <!-- 主界面布局 -->
    <div class="main-layout">
      <!-- 左侧导航 -->
      <div class="sidebar">
        <navigation-tree :modules="navigationModules" @select="onModuleSelect" />
      </div>

      <!-- 中央工作区 -->
      <div class="main-content">
        <component :is="currentComponent" :data="componentData" />
      </div>

      <!-- 右侧信息面板 -->
      <div class="info-panel">
        <market-watch :stocks="watchList" />
        <signal-alerts :signals="tradingSignals" />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <connection-status :status="connectionStatus" />
      <system-info :info="systemInfo" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'
import { useApiService } from '@/composables/useApiService'

// 响应式数据
const currentComponent = ref('StockSelection')
const componentData = reactive({})
const navigationModules = ref([])
const watchList = ref([])
const tradingSignals = ref([])
const connectionStatus = ref('connected')
const systemInfo = reactive({})

// WebSocket连接
const { socket, isConnected } = useWebSocket('ws://localhost:8080/ws')

// API服务
const { get, post } = useApiService()

// 生命周期
onMounted(async () => {
  await initializeSystem()
  setupWebSocketListeners()
})

// 方法
async function initializeSystem() {
  try {
    // 加载导航模块
    navigationModules.value = await get('/api/navigation/modules')

    // 加载自选股
    watchList.value = await get('/api/watchlist')

    // 加载系统信息
    Object.assign(systemInfo, await get('/api/system/info'))
  } catch (error) {
    console.error('系统初始化失败:', error)
  }
}

function setupWebSocketListeners() {
  socket.on('market_data', (data) => {
    // 更新行情数据
    updateMarketData(data)
  })

  socket.on('trading_signal', (data) => {
    // 处理交易信号
    tradingSignals.value.unshift(data)
  })

  socket.on('system_alert', (data) => {
    // 处理系统告警
    showAlert(data)
  })
}

function onModuleSelect(module: string) {
  currentComponent.value = module
  loadModuleData(module)
}

async function loadModuleData(module: string) {
  try {
    const data = await get(`/api/modules/${module}/data`)
    Object.assign(componentData, data)
  } catch (error) {
    console.error(`加载模块${module}数据失败:`, error)
  }
}
</script>

<style scoped>
.vnpy-trading-system {
  height: 100vh;
  background-color: #2b2b2b;
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
}

.main-layout {
  display: flex;
  height: calc(100vh - 32px);
}

.sidebar {
  width: 250px;
  background-color: #3c3c3c;
  border-right: 1px solid #555555;
}

.main-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.info-panel {
  width: 300px;
  background-color: #3c3c3c;
  border-left: 1px solid #555555;
  padding: 16px;
}

.status-bar {
  height: 32px;
  background-color: #2b2b2b;
  border-top: 1px solid #555555;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
}
</style>
```

---

## 🎉 产品原型设计完整总结

### 📊 设计覆盖范围

#### 功能模块完整覆盖
✅ **16个详细界面原型**: 每个核心功能都有完整的界面设计
✅ **9个系统层次**: 从数据采集到系统管理的全覆盖
✅ **45个主要模块**: 基于功能模块表的完整实现
✅ **200+个功能点**: 涵盖所有业务场景和用户需求

#### 设计风格统一性
✅ **vn.py专业风格**: 完全遵循量化交易专业界面标准
✅ **深色主题**: 减少眼部疲劳的专业配色方案
✅ **数据密集展示**: 高信息密度的专业布局
✅ **状态指示清晰**: 实时状态和连接状态明确显示

#### 交互逻辑完整性
✅ **详细交互说明**: 每个界面都有5点交互逻辑说明
✅ **用户体验优化**: 考虑专业用户的操作习惯
✅ **响应式设计**: 支持桌面端和移动端适配
✅ **实时数据更新**: WebSocket实现毫秒级数据刷新

### 🚀 核心业务流程设计

#### 完整投资决策链路
```
📊 智能选股 → 📈 K线分析 → 🔴 买入决策 → 💼 持仓监控 → 🟢 卖出决策 → 📊 绩效分析
```

#### 关键功能亮点
1. **智能选股系统**: 多维度评分算法，技术面+基本面+市场表现
2. **VeighNa回测引擎**: 专业机构级回测能力，参数优化和绩效分析
3. **实时监控系统**: 持仓股票实时跟踪，智能信号识别
4. **风险管理体系**: 多层次风险控制，止盈止损自动化
5. **投资组合管理**: 智能权重分配，再平衡建议

### 💻 技术实现架构

#### 前端技术栈
- **桌面端**: Electron + Vue 3 + TypeScript + Element Plus
- **移动端**: React Native + TypeScript + Ant Design Mobile
- **图表库**: ECharts 5.x + TradingView Charting Library
- **状态管理**: Pinia + Redux Toolkit

#### 后端技术栈
- **核心框架**: FastAPI + Python 3.9+
- **量化引擎**: VeighNa + QuantLib + NumPy + Pandas
- **数据库**: PostgreSQL + Redis + ClickHouse
- **消息队列**: Apache Kafka + RabbitMQ

#### 数据源集成
- **股票数据**: ADATA + Wind + 同花顺iFinD
- **实时行情**: WebSocket + TCP推送
- **基本面数据**: 财务数据API + 新闻舆情API

### 📱 多端适配设计

#### 桌面端特色
- **专业布局**: 三栏式布局，信息密度高
- **多窗口支持**: 支持多屏幕和窗口管理
- **快捷键操作**: 专业用户的高效操作方式
- **详细数据展示**: 完整的数据表格和图表

#### 移动端特色
- **触控优化**: 适配手指操作的按钮和手势
- **信息精简**: 显示最关键信息，层级清晰
- **离线缓存**: 关键数据本地存储
- **推送通知**: 重要信号及时提醒

### 🎯 产品竞争优势

#### 技术优势
1. **专业回测引擎**: 基于VeighNa的机构级回测能力
2. **智能因子系统**: 可配置的多维度评分算法
3. **实时数据处理**: 毫秒级数据更新和信号识别
4. **多时间周期**: 1分钟到日线的全周期分析

#### 用户体验优势
1. **一体化工作台**: 选股、分析、交易、监控一站式
2. **智能决策支持**: AI辅助的投资决策建议
3. **专业界面设计**: 符合量化交易用户习惯
4. **多端同步**: 桌面端和移动端数据同步

#### 业务价值优势
1. **提高选股效率**: 智能筛选优质投资标的
2. **精准把握时机**: 技术分析识别最佳买卖点
3. **降低投资风险**: 多层次风险控制体系
4. **优化投资收益**: 科学的组合管理和再平衡

### 📈 实施建议

#### 开发优先级
1. **第一阶段** (3个月): 数据采集 + 智能选股 + 基础界面
2. **第二阶段** (3个月): VeighNa回测 + K线分析 + 因子管理
3. **第三阶段** (3个月): 投资组合 + 交易执行 + 移动端
4. **第四阶段** (2个月): 系统优化 + 高级功能 + 性能调优

#### 团队配置建议
- **前端开发**: 3-4人 (Vue3 + React Native)
- **后端开发**: 4-5人 (Python + FastAPI + VeighNa)
- **数据工程**: 2-3人 (数据采集 + 清洗 + 存储)
- **量化研究**: 2-3人 (策略开发 + 因子研究)
- **测试运维**: 2人 (测试 + 部署 + 监控)

#### 风险控制
1. **数据质量**: 建立完善的数据质量监控体系
2. **系统稳定**: 高可用架构设计，故障自动恢复
3. **安全防护**: 数据加密、权限控制、审计日志
4. **合规要求**: 符合金融监管要求，风险提示完善

### 🏆 项目价值总结

这个完整的产品原型设计文档为VeighNa量化交易系统提供了：

✅ **专业完整的界面设计**: 16个详细界面原型，覆盖全业务流程
✅ **清晰的交互逻辑**: 每个界面都有详细的交互说明
✅ **统一的设计风格**: 完全遵循vn.py专业量化交易界面标准
✅ **完整的技术架构**: 从前端到后端的完整实现方案
✅ **多端适配方案**: 桌面端和移动端的完整设计
✅ **可执行的实施计划**: 明确的开发优先级和团队配置建议

这个设计方案将帮助开发团队构建一个**专业、完整、易用**的量化交易系统，为用户提供从**智能选股到投资决策**的全流程支持，实现**选好股票 → 买在低点 → 持有优质 → 卖在高点**的理想投资流程！

🚀 **让量化投资更智能，让投资决策更科学！**

---

## 🎯 重要补充：基于业务流程的核心交易信号原型设计

### 📋 新增的关键界面原型

基于您的反馈，我已经重新审视了业务流程图和架构设计图，并补充了以下关键的交易信号原型设计：

#### ✅ 核心交易信号界面（已补充）

1. **🔴🟢 实时交易信号K线图界面** (第9个原型)
   - **核心功能**: 直观展示买入🔴和卖出🟢信号在K线图上的标注
   - **业务价值**: 用户可以直观看到信号在K线图上的具体位置
   - **交互特色**: 实时信号监控列表 + K线图信号标注 + 多指标确认

2. **⚙️ 交易信号生成与确认界面** (第10个原型)
   - **核心功能**: 完整展示VeighNa业务流程中的信号生成过程
   - **业务价值**: 透明化展示信号计算过程，提高用户信任度
   - **流程特色**: 数据输入→技术指标计算→信号生成→多重确认→最终输出

3. **📊 实时信号监控大屏界面** (第11个原型)
   - **核心功能**: 全市场3000只股票的实时信号扫描和分类展示
   - **业务价值**: 批量监控，不错过任何交易机会
   - **展示特色**: 买入信号🔴 + 卖出信号🟢 + 行业热力图 + 迷你K线图

4. **🔄 历史信号成功率分析界面** (第12个原型)
   - **核心功能**: 基于历史数据验证信号的实际成功率
   - **业务价值**: 用数据证明信号的有效性，持续优化算法
   - **分析特色**: 成功率统计 + 趋势分析 + 失败原因分析 + 优化建议

### 🎯 业务流程完整覆盖

现在的原型设计完全按照业务流程图设计，实现了：

```
📊 数据采集 → 🔍 因子计算 → 🎯 信号生成 → 📈 K线展示 → 🔴🟢 买卖决策 → 💼 持仓监控 → 📊 绩效分析
```

#### 🔑 关键特色亮点

1. **直观的K线信号标注**: 🔴买入和🟢卖出信号直接在K线图上显示
2. **完整的信号生成流程**: 严格按照VeighNa业务流程设计
3. **量化评分系统**: 技术指标转化为0-100分的量化评分
4. **实时全市场扫描**: 3000只股票的实时信号监控
5. **历史成功率验证**: 78.4%的买入信号成功率，80.5%的卖出信号成功率

### 📈 核心业务价值

- **提高决策效率**: 用户可以快速识别买卖时机
- **降低投资风险**: 基于历史数据验证的高成功率信号
- **增强用户信任**: 透明的信号生成过程和成功率统计
- **全面市场覆盖**: 不错过任何投资机会

现在的产品原型设计真正做到了**基于业务流程的完整覆盖**，特别是核心的K线交易信号展示功能！🎯
