#!/usr/bin/env python3
"""
VeighNa量化交易系统启动脚本
一键启动完整的量化交易系统
"""

import sys
import os
import time
import subprocess
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('vnpy_system.log')
    ]
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask_cors', 
        'numpy',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n📦 请运行以下命令安装依赖:")
        print("   pip install flask flask-cors numpy pandas")
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'static/css',
        'static/js', 
        'static/images',
        'templates',
        'logs',
        'data',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ 目录结构创建完成")

def display_banner():
    """显示启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 VeighNa量化交易系统 v2.0                                ║
║                                                              ║
║    📊 实时行情监控  🔍 智能选股引擎  📈 交易信号系统           ║
║    💼 投资组合管理  🌐 Web界面      ⚡ 高性能回测             ║
║                                                              ║
║    开发者: VeighNa团队                                        ║
║    版本: v2.0.0                                              ║
║    构建时间: 2025-07-28                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def start_system():
    """启动系统"""
    try:
        display_banner()
        
        print("🔍 检查系统环境...")
        if not check_dependencies():
            return False
        
        print("📁 创建目录结构...")
        create_directories()
        
        print("🚀 启动VeighNa量化交易系统...")
        
        # 导入并启动Web服务器
        from web_server import main
        main()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断系统启动")
        return False
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        print(f"❌ 系统启动失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            'flask', 'flask-cors', 'numpy', 'pandas'
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == '--install-deps':
            install_dependencies()
            return
        elif sys.argv[1] == '--help':
            print("""
VeighNa量化交易系统启动脚本

用法:
    python start_system.py              # 启动系统
    python start_system.py --install-deps  # 安装依赖包
    python start_system.py --help          # 显示帮助信息

系统功能:
    📊 实时行情数据采集和监控
    🔍 多因子智能选股引擎  
    📈 技术指标交易信号生成
    💼 投资组合管理和风险控制
    🌐 Web界面和API服务
    ⚡ VeighNa回测引擎集成

访问地址:
    Web界面: http://localhost:8080
    API文档: http://localhost:8080/api
            """)
            return
    
    start_system()

if __name__ == "__main__":
    main()
