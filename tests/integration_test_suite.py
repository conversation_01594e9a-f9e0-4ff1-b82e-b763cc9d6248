"""
系统集成测试套件
进行端到端系统测试，确保各模块协同工作，符合产品设计要求
"""

import unittest
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from business_flow.trading_flow_controller import TradingFlowController, FlowStage
from intelligent_stock_selection.stock_selector import StockSelector
from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy
from trading_execution.trading_engine.simulation_engine import SimulationTradingEngine
from trading_execution.order_manager.order_manager import OrderManager
from portfolio_management.portfolio_manager import PortfolioManager
from database_models import db_manager

logger = logging.getLogger(__name__)

class SystemIntegrationTest(unittest.TestCase):
    """系统集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("\n" + "="*80)
        print("🚀 开始系统集成测试")
        print("="*80)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 初始化测试数据库
        cls.setup_test_database()
        
        # 初始化核心组件
        cls.trading_engine = SimulationTradingEngine(initial_capital=1000000)
        cls.order_manager = OrderManager(cls.trading_engine)
        cls.stock_selector = StockSelector()
        cls.buy_strategy = BuyStrategy()
        cls.sell_strategy = SellStrategy()
        cls.portfolio_manager = PortfolioManager()
        cls.flow_controller = TradingFlowController()
        
        print("✅ 测试环境初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        try:
            # 停止所有服务
            if hasattr(cls, 'flow_controller'):
                cls.flow_controller.stop()
            
            if hasattr(cls, 'trading_engine'):
                cls.trading_engine.stop()
            
            if hasattr(cls, 'order_manager'):
                cls.order_manager.stop_monitoring()
            
            print("\n" + "="*80)
            print("🏁 系统集成测试完成")
            print("="*80)
            
        except Exception as e:
            print(f"❌ 测试清理失败: {e}")
    
    @classmethod
    def setup_test_database(cls):
        """设置测试数据库"""
        try:
            # 这里应该设置测试数据库
            # 简化处理：使用内存数据库或模拟数据
            print("📊 设置测试数据库...")
            
        except Exception as e:
            print(f"❌ 设置测试数据库失败: {e}")
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.test_symbols = ["000001", "600519", "000858", "600036", "002415"]
        self.test_start_time = datetime.now()
    
    def tearDown(self):
        """每个测试方法的清理"""
        test_duration = (datetime.now() - self.test_start_time).total_seconds()
        print(f"⏱️ 测试耗时: {test_duration:.2f}秒")
    
    def test_01_database_connection(self):
        """测试1: 数据库连接"""
        print("\n📊 测试1: 数据库连接")
        
        try:
            # 测试数据库连接
            with db_manager.get_session() as session:
                result = session.execute("SELECT 1").fetchone()
                self.assertIsNotNone(result)
            
            print("✅ 数据库连接正常")
            
        except Exception as e:
            self.fail(f"❌ 数据库连接失败: {e}")
    
    def test_02_stock_selection_module(self):
        """测试2: 智能选股模块"""
        print("\n🎯 测试2: 智能选股模块")
        
        try:
            # 测试选股功能
            selected_stocks = self.stock_selector.select_stocks(limit=5)
            
            self.assertIsInstance(selected_stocks, list)
            self.assertGreater(len(selected_stocks), 0)
            
            # 验证选股结果格式
            for stock in selected_stocks:
                self.assertIn('symbol', stock)
                self.assertIn('total_score', stock)
                self.assertGreater(stock['total_score'], 0)
            
            print(f"✅ 智能选股完成: {len(selected_stocks)}只股票")
            
        except Exception as e:
            self.fail(f"❌ 智能选股模块测试失败: {e}")
    
    def test_03_buy_strategy_module(self):
        """测试3: 买入策略模块"""
        print("\n🔴 测试3: 买入策略模块")
        
        try:
            # 测试买入策略
            buy_signals = self.buy_strategy.generate_buy_signals(self.test_symbols)
            
            self.assertIsInstance(buy_signals, list)
            
            # 如果有买入信号，验证信号格式
            if buy_signals:
                signal = buy_signals[0]
                self.assertTrue(hasattr(signal, 'symbol'))
                self.assertTrue(hasattr(signal, 'signal_strength'))
                self.assertTrue(hasattr(signal, 'signal_time'))
                
                print(f"✅ 买入策略测试完成: {len(buy_signals)}个信号")
            else:
                print("ℹ️ 当前无买入信号")
            
        except Exception as e:
            self.fail(f"❌ 买入策略模块测试失败: {e}")
    
    def test_04_sell_strategy_module(self):
        """测试4: 卖出策略模块"""
        print("\n🟢 测试4: 卖出策略模块")
        
        try:
            # 创建模拟持仓
            mock_positions = [
                {
                    'symbol': '000001',
                    'entry_price': 12.0,
                    'quantity': 1000,
                    'entry_date': datetime.now() - timedelta(days=5)
                }
            ]
            
            # 测试卖出策略
            sell_signals = self.sell_strategy.generate_sell_signals(mock_positions)
            
            self.assertIsInstance(sell_signals, list)
            
            print(f"✅ 卖出策略测试完成: {len(sell_signals)}个信号")
            
        except Exception as e:
            self.fail(f"❌ 卖出策略模块测试失败: {e}")
    
    def test_05_trading_engine_module(self):
        """测试5: 交易引擎模块"""
        print("\n📈 测试5: 交易引擎模块")
        
        try:
            # 启动交易引擎
            self.trading_engine.start()
            
            # 测试账户信息
            account_info = self.trading_engine.get_account_info()
            self.assertIsInstance(account_info, dict)
            self.assertIn('initial_capital', account_info)
            self.assertIn('available_cash', account_info)
            
            # 测试订单提交
            from trading_execution.trading_engine.simulation_engine import OrderSide, OrderType
            
            order_id = self.trading_engine.submit_order(
                symbol="000001",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=100
            )
            
            self.assertIsNotNone(order_id)
            self.assertNotEqual(order_id, "")
            
            # 等待订单处理
            time.sleep(2)
            
            # 检查订单状态
            order = self.trading_engine.get_order(order_id)
            self.assertIsNotNone(order)
            
            print(f"✅ 交易引擎测试完成: 订单{order_id}")
            
        except Exception as e:
            self.fail(f"❌ 交易引擎模块测试失败: {e}")
    
    def test_06_order_manager_module(self):
        """测试6: 订单管理模块"""
        print("\n📋 测试6: 订单管理模块")
        
        try:
            # 启动订单管理器
            self.order_manager.start_monitoring()
            
            # 测试订单提交
            from trading_execution.order_manager.order_manager import OrderRequest
            from trading_execution.trading_engine.simulation_engine import OrderSide, OrderType
            
            order_request = OrderRequest(
                symbol="600519",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=100
            )
            
            result = self.order_manager.submit_order(order_request)
            
            self.assertIsInstance(result, dict)
            self.assertIn('success', result)
            
            if result['success']:
                order_id = result['order_id']
                
                # 测试订单状态查询
                order_status = self.order_manager.get_order_status(order_id)
                self.assertIsNotNone(order_status)
                
                print(f"✅ 订单管理测试完成: 订单{order_id}")
            else:
                print(f"ℹ️ 订单提交失败: {result['reason']}")
            
        except Exception as e:
            self.fail(f"❌ 订单管理模块测试失败: {e}")
    
    def test_07_portfolio_manager_module(self):
        """测试7: 投资组合管理模块"""
        print("\n💼 测试7: 投资组合管理模块")
        
        try:
            # 测试组合创建
            portfolio_config = {
                'name': 'test_portfolio',
                'initial_capital': 1000000,
                'max_positions': 10,
                'rebalance_frequency': 'weekly'
            }
            
            portfolio_id = self.portfolio_manager.create_portfolio(portfolio_config)
            self.assertIsNotNone(portfolio_id)
            
            # 测试组合信息获取
            portfolio_info = self.portfolio_manager.get_portfolio_info(portfolio_id)
            self.assertIsInstance(portfolio_info, dict)
            
            print(f"✅ 投资组合管理测试完成: 组合{portfolio_id}")
            
        except Exception as e:
            self.fail(f"❌ 投资组合管理模块测试失败: {e}")
    
    def test_08_business_flow_integration(self):
        """测试8: 业务流程集成"""
        print("\n🔄 测试8: 业务流程集成")
        
        try:
            # 启动业务流程控制器
            self.flow_controller.start()
            
            # 等待流程启动
            time.sleep(3)
            
            # 检查流程状态
            flow_status = self.flow_controller.get_flow_status()
            self.assertIsInstance(flow_status, dict)
            self.assertIn('is_running', flow_status)
            self.assertTrue(flow_status['is_running'])
            
            # 等待一些流程执行
            time.sleep(10)
            
            # 再次检查状态
            updated_status = self.flow_controller.get_flow_status()
            self.assertIsInstance(updated_status, dict)
            
            print("✅ 业务流程集成测试完成")
            
        except Exception as e:
            self.fail(f"❌ 业务流程集成测试失败: {e}")
    
    def test_09_end_to_end_workflow(self):
        """测试9: 端到端工作流"""
        print("\n🎯 测试9: 端到端工作流")
        
        try:
            print("  📊 步骤1: 智能选股")
            selected_stocks = self.stock_selector.select_stocks(limit=3)
            self.assertGreater(len(selected_stocks), 0)
            
            print("  📈 步骤2: K线分析")
            # 模拟K线分析过程
            analyzed_stocks = []
            for stock in selected_stocks:
                # 这里应该调用技术分析模块
                analyzed_stocks.append({
                    **stock,
                    'technical_score': 75.0,
                    'analysis_time': datetime.now()
                })
            
            print("  🔴 步骤3: 买入决策")
            symbols = [stock['symbol'] for stock in analyzed_stocks]
            buy_signals = self.buy_strategy.generate_buy_signals(symbols)
            
            print("  💼 步骤4: 持仓监控")
            # 模拟持仓监控
            positions = self.trading_engine.get_all_positions()
            self.assertIsInstance(positions, dict)
            
            print("  🟢 步骤5: 卖出决策")
            # 如果有持仓，测试卖出决策
            if positions:
                position_list = [
                    {
                        'symbol': symbol,
                        'entry_price': pos.avg_price,
                        'quantity': pos.quantity,
                        'entry_date': datetime.now() - timedelta(days=1)
                    }
                    for symbol, pos in positions.items()
                    if pos.quantity > 0
                ]
                
                if position_list:
                    sell_signals = self.sell_strategy.generate_sell_signals(position_list)
                    print(f"    生成卖出信号: {len(sell_signals)}个")
            
            print("  📊 步骤6: 投资记录")
            # 模拟投资记录
            investment_record = {
                'workflow_id': f"E2E_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'selected_stocks': len(selected_stocks),
                'buy_signals': len(buy_signals),
                'positions': len(positions),
                'test_time': datetime.now()
            }
            
            print("✅ 端到端工作流测试完成")
            print(f"    选股: {investment_record['selected_stocks']}只")
            print(f"    买入信号: {investment_record['buy_signals']}个")
            print(f"    持仓: {investment_record['positions']}个")
            
        except Exception as e:
            self.fail(f"❌ 端到端工作流测试失败: {e}")
    
    def test_10_performance_benchmark(self):
        """测试10: 性能基准测试"""
        print("\n⚡ 测试10: 性能基准测试")
        
        try:
            # 测试选股性能
            start_time = time.time()
            selected_stocks = self.stock_selector.select_stocks(limit=10)
            selection_time = time.time() - start_time
            
            self.assertLess(selection_time, 30.0)  # 选股应在30秒内完成
            print(f"    选股性能: {selection_time:.2f}秒 ✅")
            
            # 测试策略性能
            if selected_stocks:
                symbols = [stock['symbol'] for stock in selected_stocks[:5]]
                
                start_time = time.time()
                buy_signals = self.buy_strategy.generate_buy_signals(symbols)
                strategy_time = time.time() - start_time
                
                self.assertLess(strategy_time, 10.0)  # 策略计算应在10秒内完成
                print(f"    策略性能: {strategy_time:.2f}秒 ✅")
            
            # 测试交易引擎性能
            start_time = time.time()
            account_info = self.trading_engine.get_account_info()
            engine_time = time.time() - start_time
            
            self.assertLess(engine_time, 1.0)  # 账户查询应在1秒内完成
            print(f"    引擎性能: {engine_time:.3f}秒 ✅")
            
            print("✅ 性能基准测试通过")
            
        except Exception as e:
            self.fail(f"❌ 性能基准测试失败: {e}")
    
    def test_11_error_handling(self):
        """测试11: 错误处理"""
        print("\n🛡️ 测试11: 错误处理")
        
        try:
            # 测试无效股票代码
            invalid_symbols = ["INVALID001", "NOTEXIST"]
            buy_signals = self.buy_strategy.generate_buy_signals(invalid_symbols)
            self.assertIsInstance(buy_signals, list)  # 应该返回空列表而不是异常
            
            # 测试无效订单
            from trading_execution.trading_engine.simulation_engine import OrderSide, OrderType
            
            order_id = self.trading_engine.submit_order(
                symbol="INVALID",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=-100  # 无效数量
            )
            
            # 应该返回空字符串或处理错误
            self.assertIsInstance(order_id, str)
            
            print("✅ 错误处理测试通过")
            
        except Exception as e:
            self.fail(f"❌ 错误处理测试失败: {e}")
    
    def test_12_data_consistency(self):
        """测试12: 数据一致性"""
        print("\n🔍 测试12: 数据一致性")
        
        try:
            # 测试账户数据一致性
            account_info = self.trading_engine.get_account_info()
            
            initial_capital = account_info.get('initial_capital', 0)
            available_cash = account_info.get('available_cash', 0)
            total_value = account_info.get('total_value', 0)
            
            # 验证数据逻辑一致性
            self.assertGreaterEqual(initial_capital, 0)
            self.assertGreaterEqual(available_cash, 0)
            self.assertGreaterEqual(total_value, 0)
            
            print("✅ 数据一致性测试通过")
            
        except Exception as e:
            self.fail(f"❌ 数据一致性测试失败: {e}")


def run_integration_tests():
    """运行集成测试"""
    try:
        # 创建测试套件
        test_suite = unittest.TestLoader().loadTestsFromTestCase(SystemIntegrationTest)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(test_suite)
        
        # 输出测试结果
        print("\n" + "="*80)
        print("📊 测试结果统计")
        print("="*80)
        print(f"总测试数: {result.testsRun}")
        print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        
        if result.failures:
            print("\n❌ 失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n💥 错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
        
        # 返回测试是否全部通过
        return len(result.failures) == 0 and len(result.errors) == 0

    except Exception as e:
        print(f"❌ 运行集成测试失败: {e}")
        return False


def run_quick_test():
    """运行快速测试（核心功能）"""
    try:
        print("\n🚀 运行快速集成测试...")

        # 只运行核心测试
        test_methods = [
            'test_01_database_connection',
            'test_02_stock_selection_module',
            'test_05_trading_engine_module',
            'test_08_business_flow_integration'
        ]

        suite = unittest.TestSuite()
        for method in test_methods:
            suite.addTest(SystemIntegrationTest(method))

        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)

        return len(result.failures) == 0 and len(result.errors) == 0

    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='系统集成测试')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    args = parser.parse_args()

    if args.quick:
        success = run_quick_test()
    else:
        success = run_integration_tests()

    sys.exit(0 if success else 1)
