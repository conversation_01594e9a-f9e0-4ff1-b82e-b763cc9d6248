#!/usr/bin/env python3
"""
生成完整的模拟数据，确保系统正常运行
"""

import sqlite3
import random
import logging
from datetime import datetime, timedelta
import pandas as pd

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self):
        self.db_path = 'vnpy_trading.db'
        
        # 模拟股票列表
        self.mock_stocks = [
            ('000001', '平安银行', 'SZ'), ('000002', '万科A', 'SZ'), ('000858', '五粮液', 'SZ'),
            ('600000', '浦发银行', 'SH'), ('600036', '招商银行', 'SH'), ('600519', '贵州茅台', 'SH'),
            ('600887', '伊利股份', 'SH'), ('000858', '五粮液', 'SZ'), ('002415', '海康威视', 'SZ'),
            ('000725', '京东方A', 'SZ'), ('600276', '恒瑞医药', 'SH'), ('000063', '中兴通讯', 'SZ'),
            ('002594', '比亚迪', 'SZ'), ('600031', '三一重工', 'SH'), ('000002', '万科A', 'SZ'),
            ('600009', '上海机场', 'SH'), ('000876', '新希望', 'SZ'), ('600104', '上汽集团', 'SH'),
            ('002304', '洋河股份', 'SZ'), ('600585', '海螺水泥', 'SH'), ('000338', '潍柴动力', 'SZ'),
            ('600048', '保利发展', 'SH'), ('000651', '格力电器', 'SZ'), ('600703', '三安光电', 'SH'),
            ('002142', '宁波银行', 'SZ'), ('600660', '福耀玻璃', 'SH'), ('000568', '泸州老窖', 'SZ'),
            ('600196', '复星医药', 'SH'), ('002027', '分众传媒', 'SZ'), ('600309', '万华化学', 'SH'),
            ('000895', '双汇发展', 'SZ'), ('600690', '海尔智家', 'SH'), ('002475', '立讯精密', 'SZ'),
            ('600438', '通威股份', 'SH'), ('000100', 'TCL科技', 'SZ'), ('600741', '华域汽车', 'SH'),
            ('002230', '科大讯飞', 'SZ'), ('600893', '航发动力', 'SH'), ('000977', '浪潮信息', 'SZ'),
            ('600570', '恒生电子', 'SH'), ('002008', '大族激光', 'SZ'), ('600588', '用友网络', 'SH'),
            ('000166', '申万宏源', 'SZ'), ('600837', '海通证券', 'SH'), ('002236', '大华股份', 'SZ'),
            ('600030', '中信证券', 'SH'), ('000776', '广发证券', 'SZ'), ('600999', '招商证券', 'SH'),
            ('002352', '顺丰控股', 'SZ'), ('600029', '南方航空', 'SH'), ('000858', '五粮液', 'SZ'),
            ('600115', '东方航空', 'SH'), ('002024', '苏宁易购', 'SZ'), ('600606', '绿地控股', 'SH')
        ]
    
    def clear_database(self):
        """清空数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清空所有表
            tables = [
                'stock_basic_info', 'stock_daily_data', 'stock_technical_indicators',
                'stock_fundamental_data', 'stock_selection_results', 'trading_signals'
            ]
            
            for table in tables:
                cursor.execute(f"DELETE FROM {table}")
            
            conn.commit()
            conn.close()
            logger.info("✅ 数据库清空完成")
            
        except Exception as e:
            logger.error(f"❌ 清空数据库失败: {e}")
    
    def generate_stock_basic_info(self):
        """生成股票基本信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, name, exchange in self.mock_stocks:
                cursor.execute("""
                    INSERT OR REPLACE INTO stock_basic_info 
                    (symbol, name, exchange, is_active) 
                    VALUES (?, ?, ?, ?)
                """, (symbol, name, exchange, True))
            
            conn.commit()
            conn.close()
            logger.info(f"✅ 生成 {len(self.mock_stocks)} 只股票基本信息")
            
        except Exception as e:
            logger.error(f"❌ 生成股票基本信息失败: {e}")
    
    def generate_daily_data(self):
        """生成日线数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 生成最近60天的数据
            end_date = datetime.now()
            
            for symbol, name, exchange in self.mock_stocks:
                base_price = random.uniform(8, 200)  # 基础价格
                
                for i in range(60):
                    trade_date = end_date - timedelta(days=i)
                    
                    # 跳过周末
                    if trade_date.weekday() >= 5:
                        continue
                    
                    # 生成价格数据（有一定的趋势性）
                    price_change = random.uniform(-0.05, 0.05)  # 日涨跌幅-5%到5%
                    base_price = max(1.0, base_price * (1 + price_change))
                    
                    open_price = base_price * random.uniform(0.98, 1.02)
                    close_price = base_price
                    high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
                    low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)
                    
                    volume = random.randint(1000000, 50000000)  # 成交量
                    amount = volume * close_price  # 成交额
                    turnover_rate = random.uniform(0.5, 8.0)  # 换手率
                    pe_ratio = random.uniform(8, 50)  # 市盈率
                    pb_ratio = random.uniform(0.8, 8)  # 市净率
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO stock_daily_data 
                        (symbol, trade_date, open_price, high_price, low_price, close_price, 
                         volume, amount, turnover_rate, pe_ratio, pb_ratio) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol, trade_date.strftime('%Y-%m-%d'),
                        round(open_price, 2), round(high_price, 2), round(low_price, 2), round(close_price, 2),
                        volume, round(amount, 2), round(turnover_rate, 2), round(pe_ratio, 2), round(pb_ratio, 2)
                    ))
            
            conn.commit()
            conn.close()
            logger.info(f"✅ 生成日线数据完成")
            
        except Exception as e:
            logger.error(f"❌ 生成日线数据失败: {e}")
    
    def generate_technical_indicators(self):
        """生成技术指标数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, name, exchange in self.mock_stocks:
                # 获取最新交易日期
                cursor.execute("""
                    SELECT MAX(trade_date) FROM stock_daily_data WHERE symbol = ?
                """, (symbol,))
                
                latest_date = cursor.fetchone()[0]
                if not latest_date:
                    continue
                
                # 生成技术指标
                cursor.execute("""
                    INSERT OR REPLACE INTO stock_technical_indicators 
                    (symbol, trade_date, macd_dif, macd_dea, macd_histogram, 
                     sma_5, sma_10, sma_20, sma_60, ema_12, ema_26, 
                     rsi_14, boll_upper, boll_middle, boll_lower) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    symbol, latest_date,
                    round(random.uniform(-0.5, 0.5), 4),    # MACD DIF
                    round(random.uniform(-0.3, 0.3), 4),    # MACD DEA
                    round(random.uniform(-0.2, 0.2), 4),    # MACD Histogram
                    round(random.uniform(10, 50), 2),       # SMA 5
                    round(random.uniform(10, 50), 2),       # SMA 10
                    round(random.uniform(10, 50), 2),       # SMA 20
                    round(random.uniform(10, 50), 2),       # SMA 60
                    round(random.uniform(10, 50), 2),       # EMA 12
                    round(random.uniform(10, 50), 2),       # EMA 26
                    round(random.uniform(20, 80), 2),       # RSI 14
                    round(random.uniform(15, 60), 2),       # Bollinger Upper
                    round(random.uniform(10, 50), 2),       # Bollinger Middle
                    round(random.uniform(5, 45), 2),        # Bollinger Lower
                ))
            
            conn.commit()
            conn.close()
            logger.info(f"✅ 生成技术指标数据完成")
            
        except Exception as e:
            logger.error(f"❌ 生成技术指标数据失败: {e}")
    
    def generate_fundamental_data(self):
        """生成基本面数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, name, exchange in self.mock_stocks:
                cursor.execute("""
                    INSERT OR REPLACE INTO stock_fundamental_data 
                    (symbol, report_date, pe_ratio, pb_ratio, ps_ratio, 
                     roe, gross_profit_margin, net_profit_margin, 
                     revenue_growth, profit_growth, eps_growth) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    symbol, datetime.now().strftime('%Y-%m-%d'),
                    round(random.uniform(5, 50), 2),        # PE ratio
                    round(random.uniform(0.5, 8), 2),       # PB ratio
                    round(random.uniform(1, 10), 2),        # PS ratio
                    round(random.uniform(5, 25), 2),        # ROE
                    round(random.uniform(10, 60), 2),       # Gross profit margin
                    round(random.uniform(2, 30), 2),        # Net profit margin
                    round(random.uniform(-20, 50), 2),      # Revenue growth
                    round(random.uniform(-30, 80), 2),      # Profit growth
                    round(random.uniform(-50, 100), 2),     # EPS growth
                ))
            
            conn.commit()
            conn.close()
            logger.info(f"✅ 生成基本面数据完成")
            
        except Exception as e:
            logger.error(f"❌ 生成基本面数据失败: {e}")
    
    def verify_data(self):
        """验证数据完整性"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查各表数据量
            tables = [
                ('stock_basic_info', '股票基本信息'),
                ('stock_daily_data', '日线数据'),
                ('stock_technical_indicators', '技术指标'),
                ('stock_fundamental_data', '基本面数据')
            ]
            
            logger.info("📊 数据验证结果:")
            for table, desc in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"  {desc}: {count} 条记录")
            
            # 检查是否有完整数据的股票
            cursor.execute("""
                SELECT s.symbol, s.name, 
                       COUNT(DISTINCT d.trade_date) as daily_count,
                       COUNT(DISTINCT t.trade_date) as tech_count,
                       COUNT(DISTINCT f.report_date) as fund_count
                FROM stock_basic_info s
                LEFT JOIN stock_daily_data d ON s.symbol = d.symbol
                LEFT JOIN stock_technical_indicators t ON s.symbol = t.symbol
                LEFT JOIN stock_fundamental_data f ON s.symbol = f.symbol
                GROUP BY s.symbol, s.name
                HAVING daily_count > 0 AND tech_count > 0 AND fund_count > 0
                ORDER BY s.symbol
                LIMIT 10
            """)
            
            complete_stocks = cursor.fetchall()
            logger.info(f"✅ 有完整数据的股票: {len(complete_stocks)} 只")
            
            for stock in complete_stocks[:5]:
                logger.info(f"  {stock[0]} {stock[1]}: 日线{stock[2]}天, 技术指标{stock[3]}个, 基本面{stock[4]}个")
            
            conn.close()
            return len(complete_stocks) > 0
            
        except Exception as e:
            logger.error(f"❌ 数据验证失败: {e}")
            return False

def main():
    """主函数"""
    print("🎲 VeighNa模拟数据生成器")
    print("=" * 50)
    
    generator = MockDataGenerator()
    
    # 1. 清空数据库
    print("1. 清空现有数据...")
    generator.clear_database()
    
    # 2. 生成股票基本信息
    print("2. 生成股票基本信息...")
    generator.generate_stock_basic_info()
    
    # 3. 生成日线数据
    print("3. 生成日线数据...")
    generator.generate_daily_data()
    
    # 4. 生成技术指标
    print("4. 生成技术指标...")
    generator.generate_technical_indicators()
    
    # 5. 生成基本面数据
    print("5. 生成基本面数据...")
    generator.generate_fundamental_data()
    
    # 6. 验证数据
    print("6. 验证数据完整性...")
    if generator.verify_data():
        print("✅ 数据生成完成！")
        print("\n现在可以启动VeighNa系统:")
        print("python web_server.py")
    else:
        print("❌ 数据验证失败")

if __name__ == "__main__":
    main()
