# VeighNa量化交易系统 V2.0 Web版本

## 🚀 项目概述

本项目是基于**产品原型设计图**严格实现的VeighNa量化交易系统Web版本，包含完整的业务功能模块和专业的用户界面。

## 📍 Web访问地址

### 方式一：直接访问HTML文件
```bash
# 启动Web界面
python run_web_system.py
```
**访问地址**: `file:///Users/<USER>/Desktop/vnpy-project/vnpy_web_system.html`

### 方式二：后端服务器（推荐）
```bash
# 启动后端服务器
python simple_backend.py
```
**访问地址**: `http://localhost:8080`

## 🎯 核心功能模块

### 1. 📊 数据管理
- **数据采集**: 实时股票数据采集和处理
- **数据存储**: 多时间周期数据存储管理
- **数据质量**: 数据质量监控和验证

### 2. 🔍 智能选股
- **因子配置**: 多维度因子配置和权重设置
- **选股策略**: 基于多因子模型的智能选股
- **候选池**: 股票候选池管理和筛选

### 3. 🚀 策略回测
- **VeighNa引擎**: 专业回测引擎集成
- **策略管理**: 交易策略创建和管理
- **参数优化**: 策略参数自动优化
- **绩效分析**: 详细的回测绩效分析

### 4. 💼 组合管理
- **组合构建**: 基于选股结果的组合构建
- **权重分配**: 智能权重分配算法
- **再平衡**: 定期组合再平衡策略

### 5. ⚡ 交易执行
- **订单管理**: 交易订单创建和管理
- **成交回报**: 实时成交信息反馈
- **风险控制**: 多层次风险控制机制

### 6. ⚙️ 系统管理
- **参数配置**: 系统参数配置管理
- **日志查看**: 系统运行日志查看
- **性能监控**: 系统性能实时监控

## 🎨 界面设计特色

### VeighNa专业风格
- **深色主题**: 专业的深色界面主题
- **布局设计**: 严格按照产品原型设计图实现
- **颜色方案**: 
  - 主色调: `#1890ff` (蓝色)
  - 背景色: `#1f1f1f` (深灰)
  - 面板色: `#262626` (中灰)
  - 边框色: `#3f3f3f` (浅灰)

### 功能区域划分
1. **顶部标题栏** (50px): 系统标题、连接状态、时间、用户信息
2. **主导航栏** (40px): 系统、数据、策略、交易等主要功能标签
3. **左侧导航面板** (250px): 详细功能模块导航
4. **主工作区域**: 核心功能展示区域
5. **状态栏** (30px): 系统状态、资产信息等

### 核心组件
- **实时行情监控**: 多股票实时价格和涨跌幅显示
- **K线图表区域**: 支持多时间周期切换的技术分析图表
- **交易信号面板**: 买入卖出信号实时提示
- **持仓明细表**: 投资组合持仓详情
- **系统状态监控**: 实时系统运行状态

## 📊 数据接口

### API端点
- `GET /api/system/status` - 系统状态
- `GET /api/market/realtime` - 实时行情
- `GET /api/signals/all` - 交易信号
- `GET /api/portfolio/current` - 投资组合
- `GET /api/stock/selection` - 智能选股结果
- `GET /api/backtest/results` - 回测结果

### 数据格式
所有API返回JSON格式数据，包含时间戳和状态信息。

## 🔧 技术架构

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 响应式设计和动画效果
- **JavaScript**: 交互逻辑和数据处理
- **Vue.js**: 数据绑定和组件化（可选）

### 后端技术栈
- **Python**: 核心业务逻辑
- **HTTP Server**: 内置HTTP服务器
- **JSON API**: RESTful API接口
- **CORS**: 跨域资源共享支持

### 数据流程
1. **数据采集** → **数据处理** → **策略分析** → **信号生成** → **组合管理** → **交易执行**
2. **实时数据推送** → **前端界面更新** → **用户交互** → **后端处理** → **结果反馈**

## 🚀 快速启动

### 1. 环境准备
```bash
# 确保Python环境
python --version  # Python 3.8+

# 进入项目目录
cd /Users/<USER>/Desktop/vnpy-project
```

### 2. 启动Web界面
```bash
# 方式一：直接启动HTML界面
python run_web_system.py

# 方式二：启动完整后端服务
python simple_backend.py
```

### 3. 访问系统
- **Web界面**: 浏览器自动打开或手动访问
- **API文档**: 查看接口文档和测试
- **实时数据**: 系统自动更新数据

## 📖 使用指南

### 界面操作
1. **主导航**: 点击顶部标签切换主要功能模块
2. **侧边导航**: 点击左侧菜单进入具体功能页面
3. **时间周期**: 点击K线图上方按钮切换时间周期
4. **实时更新**: 数据自动刷新，无需手动操作

### 功能使用
1. **查看行情**: 实时行情面板显示股票价格变动
2. **分析信号**: 交易信号面板提供买卖建议
3. **管理组合**: 持仓明细表显示投资组合状态
4. **监控系统**: 状态栏显示系统运行状态

## 🎯 业务流程

### 完整业务链路
```
数据采集 → 数据清洗 → 因子计算 → 智能选股 → 策略回测 → 组合构建 → 风险控制 → 交易执行 → 绩效分析
```

### 核心算法
1. **多因子选股模型**: 技术面50% + 基本面30% + 市场表现20%
2. **风险控制机制**: 单股≤10%, 总仓位≤80%, 行业分散
3. **组合优化算法**: 基于马科维茨理论的权重优化
4. **回测验证系统**: VeighNa专业回测引擎

## 📊 系统特色

### 1. 严格按照产品原型设计
- 界面布局完全按照设计图实现
- 功能模块严格对应业务需求
- 用户体验符合专业交易系统标准

### 2. 完整的业务功能
- 涵盖量化交易全流程
- 支持多种交易策略
- 提供专业的风险管理

### 3. 专业的技术实现
- 模块化架构设计
- 高性能数据处理
- 实时数据推送机制

### 4. 优秀的用户体验
- 响应式界面设计
- 直观的操作流程
- 丰富的数据可视化

## 🔮 未来扩展

### 功能扩展
- [ ] 更多技术指标支持
- [ ] 机器学习算法集成
- [ ] 多市场数据支持
- [ ] 移动端适配

### 技术优化
- [ ] WebSocket实时推送
- [ ] 数据缓存优化
- [ ] 性能监控增强
- [ ] 安全性提升

## 📞 技术支持

如有问题或建议，请联系技术团队。

---

**VeighNa量化交易系统 V2.0** - 专业、可靠、高效的量化交易解决方案
