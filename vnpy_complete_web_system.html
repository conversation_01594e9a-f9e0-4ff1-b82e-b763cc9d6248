<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeighNa量化交易系统 v2.0 - 完整Web版本</title>
    <style>
        /* VeighNa专业风格样式 - 严格按照产品原型设计 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background-color: #1f1f1f;
            color: #ffffff;
            font-size: 12px;
            line-height: 1.4;
            overflow: hidden;
        }

        /* 顶部标题栏 */
        .header {
            height: 50px;
            background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
            border-bottom: 1px solid #3f3f3f;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-left h1 {
            font-size: 16px;
            font-weight: bold;
            color: #1890ff;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .connection-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            background-color: #52c41a;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .system-time, .user-info {
            font-size: 11px;
            color: #8c8c8c;
        }

        .settings-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s;
        }

        .settings-btn:hover {
            background: #40a9ff;
        }

        /* 主导航栏 */
        .main-nav {
            height: 40px;
            background: #262626;
            border-bottom: 1px solid #3f3f3f;
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: fixed;
            top: 50px;
            left: 0;
            right: 0;
            z-index: 999;
        }

        .nav-btn {
            background: transparent;
            color: #8c8c8c;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
            margin-right: 4px;
            transition: all 0.2s;
        }

        .nav-btn:hover {
            background: #3f3f3f;
            color: #ffffff;
        }

        .nav-btn.active {
            background: #1890ff;
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            display: flex;
            height: calc(100vh - 120px);
            margin-top: 90px;
            margin-bottom: 30px;
        }

        /* 左侧功能导航面板 */
        .sidebar {
            width: 250px;
            background: #262626;
            border-right: 1px solid #3f3f3f;
            overflow-y: auto;
        }

        .function-nav {
            padding: 10px;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section h3 {
            font-size: 13px;
            color: #1890ff;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #1f1f1f;
            border-radius: 4px;
        }

        .nav-section ul {
            list-style: none;
        }

        .nav-section li {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            color: #8c8c8c;
            border-radius: 4px;
            margin-bottom: 2px;
            transition: all 0.2s;
        }

        .nav-section li:hover {
            background: #3f3f3f;
            color: #ffffff;
        }

        .nav-section li.active {
            background: #1890ff;
            color: white;
        }

        /* 主工作区域 */
        .workspace {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
        }

        /* 通用面板样式 */
        .panel {
            background: #262626;
            border: 1px solid #3f3f3f;
            border-radius: 8px;
            padding: 15px;
        }

        .panel h3 {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 15px;
            border-bottom: 1px solid #3f3f3f;
            padding-bottom: 8px;
        }

        /* 实时行情监控 */
        .realtime-monitor {
            grid-column: 1;
            grid-row: 1;
        }

        .stock-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .stock-item {
            display: grid;
            grid-template-columns: 60px 80px 60px 60px 80px;
            gap: 10px;
            padding: 8px;
            background: #1f1f1f;
            border-radius: 4px;
            font-size: 11px;
            align-items: center;
            transition: all 0.3s;
        }

        .stock-item:hover {
            background: #2f2f2f;
        }

        .stock-item.price-up {
            border-left: 3px solid #52c41a;
        }

        .stock-item.price-down {
            border-left: 3px solid #ff4d4f;
        }

        .stock-code {
            font-weight: bold;
            color: #1890ff;
        }

        .stock-name {
            color: #ffffff;
        }

        .stock-price {
            font-weight: bold;
            color: #ffffff;
        }

        .price-up .stock-change,
        .price-up .stock-change-pct {
            color: #52c41a;
            font-weight: bold;
        }

        .price-down .stock-change,
        .price-down .stock-change-pct {
            color: #ff4d4f;
            font-weight: bold;
        }

        /* K线图表区域 */
        .chart-area {
            grid-column: 2;
            grid-row: 1 / 3;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #3f3f3f;
            padding-bottom: 8px;
        }

        .timeframe-buttons {
            display: flex;
            gap: 4px;
        }

        .tf-btn {
            background: #3f3f3f;
            color: #8c8c8c;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .tf-btn:hover {
            background: #4f4f4f;
            color: #ffffff;
        }

        .tf-btn.active {
            background: #1890ff;
            color: white;
        }

        .chart-container {
            height: 300px;
            background: #1f1f1f;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8c8c8c;
            font-size: 14px;
            position: relative;
            border: 1px dashed #3f3f3f;
        }

        .chart-signals {
            margin-top: 10px;
            text-align: center;
        }

        .signal-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            font-size: 11px;
        }

        .buy-signal {
            color: #ff4d4f;
        }

        .sell-signal {
            color: #52c41a;
        }

        /* 交易信号面板 */
        .signal-panel {
            grid-column: 1;
            grid-row: 2;
        }

        .signal-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
        }

        .signal-item {
            display: grid;
            grid-template-columns: 30px 80px 120px 80px 100px;
            gap: 15px;
            padding: 10px;
            background: #1f1f1f;
            border-radius: 4px;
            font-size: 11px;
            align-items: center;
            transition: all 0.3s;
        }

        .signal-item:hover {
            background: #2f2f2f;
        }

        .signal-item.buy {
            border-left: 3px solid #ff4d4f;
        }

        .signal-item.sell {
            border-left: 3px solid #52c41a;
        }

        .signal-icon {
            font-size: 14px;
        }

        .signal-type {
            font-weight: bold;
            color: #ffffff;
        }

        .signal-stock {
            color: #1890ff;
        }

        .signal-score {
            color: #faad14;
            font-weight: bold;
        }

        .signal-time {
            color: #8c8c8c;
        }

        /* 持仓明细 */
        .position-panel {
            grid-column: 1 / -1;
            grid-row: 3;
        }

        .position-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .position-table th {
            background: #1f1f1f;
            color: #8c8c8c;
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #3f3f3f;
        }

        .position-table td {
            padding: 8px;
            border-bottom: 1px solid #3f3f3f;
            color: #ffffff;
        }

        .position-table tr:hover {
            background: #2f2f2f;
        }

        .position-table tr.profit td:nth-child(6) {
            color: #52c41a;
            font-weight: bold;
        }

        .position-table tr.loss td:nth-child(6) {
            color: #ff4d4f;
            font-weight: bold;
        }

        /* 状态栏 */
        .status-bar {
            height: 30px;
            background: #262626;
            border-top: 1px solid #3f3f3f;
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .status-item {
            margin-right: 30px;
            font-size: 11px;
            color: #8c8c8c;
        }

        .status-item .normal {
            color: #52c41a;
        }

        .status-item .running {
            color: #1890ff;
        }

        .status-item .asset {
            color: #faad14;
            font-weight: bold;
        }

        .status-item .profit {
            color: #52c41a;
            font-weight: bold;
        }

        .status-item .loss {
            color: #ff4d4f;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 200px;
            }
            
            .workspace {
                grid-template-columns: 1fr;
            }
            
            .chart-area {
                grid-column: 1;
                grid-row: 2;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
            
            .header-right .user-info {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <h1>📊 VeighNa量化交易系统 v2.0</h1>
            </div>
            <div class="header-right">
                <span class="connection-status">连接正常</span>
                <span class="system-time" id="current-time"></span>
                <span class="user-info">用户: Admin</span>
                <button class="settings-btn" onclick="showSettings()">⚙️ 设置</button>
            </div>
        </header>

        <!-- 主导航栏 -->
        <nav class="main-nav">
            <button class="nav-btn active" onclick="switchTab(this, 'system')">系统</button>
            <button class="nav-btn" onclick="switchTab(this, 'data')">数据</button>
            <button class="nav-btn" onclick="switchTab(this, 'strategy')">策略</button>
            <button class="nav-btn" onclick="switchTab(this, 'trading')">交易</button>
            <button class="nav-btn" onclick="switchTab(this, 'risk')">风控</button>
            <button class="nav-btn" onclick="switchTab(this, 'portfolio')">组合</button>
            <button class="nav-btn" onclick="switchTab(this, 'factor')">因子</button>
            <button class="nav-btn" onclick="switchTab(this, 'log')">日志</button>
            <button class="nav-btn" onclick="switchTab(this, 'help')">帮助</button>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧功能导航面板 -->
            <aside class="sidebar">
                <div class="function-nav">
                    <div class="nav-section">
                        <h3>📊 数据管理</h3>
                        <ul>
                            <li class="active" onclick="switchView(this, 'data-collection')">📈 数据采集</li>
                            <li onclick="switchView(this, 'data-storage')">💾 数据存储</li>
                            <li onclick="switchView(this, 'data-quality')">🔍 数据质量</li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>🔍 智能选股</h3>
                        <ul>
                            <li onclick="switchView(this, 'factor-config')">🔧 因子配置</li>
                            <li onclick="switchView(this, 'stock-selection')">🎯 选股策略</li>
                            <li onclick="switchView(this, 'candidate-pool')">📋 候选池</li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>🚀 策略回测</h3>
                        <ul>
                            <li onclick="switchView(this, 'vnpy-engine')">⚡ VeighNa引擎</li>
                            <li onclick="switchView(this, 'strategy-management')">📈 策略管理</li>
                            <li onclick="switchView(this, 'parameter-optimization')">🎛️ 参数优化</li>
                            <li onclick="switchView(this, 'performance-analysis')">📊 绩效分析</li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>💼 组合管理</h3>
                        <ul>
                            <li onclick="switchView(this, 'portfolio-construction')">🏗️ 组合构建</li>
                            <li onclick="switchView(this, 'weight-allocation')">⚖️ 权重分配</li>
                            <li onclick="switchView(this, 'rebalancing')">🔄 再平衡</li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>⚡ 交易执行</h3>
                        <ul>
                            <li onclick="switchView(this, 'order-management')">📋 订单管理</li>
                            <li onclick="switchView(this, 'trade-report')">📊 成交回报</li>
                            <li onclick="switchView(this, 'risk-control')">🛡️ 风险控制</li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>⚙️ 系统管理</h3>
                        <ul>
                            <li onclick="switchView(this, 'system-config')">🔧 参数配置</li>
                            <li onclick="switchView(this, 'log-viewer')">📝 日志查看</li>
                            <li onclick="switchView(this, 'performance-monitor')">📊 性能监控</li>
                        </ul>
                    </div>
                </div>
            </aside>

            <!-- 主工作区域 -->
            <section class="workspace">
                <!-- 实时行情监控 -->
                <div class="panel realtime-monitor">
                    <h3>实时行情监控</h3>
                    <div class="stock-list" id="stock-list">
                        <!-- 股票数据将通过JavaScript动态更新 -->
                    </div>
                </div>

                <!-- K线图表区域 -->
                <div class="panel chart-area">
                    <div class="chart-header">
                        <h3>K线图表区域</h3>
                        <div class="timeframe-buttons">
                            <button class="tf-btn" onclick="switchTimeframe(this, '1m')">1m</button>
                            <button class="tf-btn" onclick="switchTimeframe(this, '5m')">5m</button>
                            <button class="tf-btn" onclick="switchTimeframe(this, '15m')">15m</button>
                            <button class="tf-btn" onclick="switchTimeframe(this, '1h')">1h</button>
                            <button class="tf-btn active" onclick="switchTimeframe(this, '1d')">1d</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="mock-kline">
                            <div class="kline-placeholder">
                                <h4 style="color: #1890ff; margin-bottom: 10px;">📈 K线图 + 技术指标</h4>
                                <p>多时间周期K线图表</p>
                                <p>支持技术指标叠加</p>
                                <p>实时买卖信号标注</p>
                                <p style="margin-top: 10px; color: #1890ff;">当前周期: <span id="current-timeframe">1d</span></p>
                            </div>
                        </div>
                    </div>
                    <div class="chart-signals">
                        <div class="signal-legend">
                            <span class="buy-signal">🔴 买入信号标注</span>
                            <span class="sell-signal">🟢 卖出信号标注</span>
                        </div>
                    </div>
                </div>

                <!-- 交易信号面板 -->
                <div class="panel signal-panel">
                    <h3>交易信号面板</h3>
                    <div class="signal-list" id="signal-list">
                        <!-- 交易信号将通过JavaScript动态更新 -->
                    </div>
                </div>

                <!-- 持仓明细 -->
                <div class="panel position-panel">
                    <h3>持仓明细</h3>
                    <table class="position-table">
                        <thead>
                            <tr>
                                <th>代码</th>
                                <th>名称</th>
                                <th>数量</th>
                                <th>成本价</th>
                                <th>现价</th>
                                <th>盈亏</th>
                                <th>比例</th>
                            </tr>
                        </thead>
                        <tbody id="position-tbody">
                            <!-- 持仓数据将通过JavaScript动态更新 -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <span class="status-item">
                数据连接: <span class="normal" id="data-status">正常</span>
            </span>
            <span class="status-item">
                策略运行: <span class="running" id="strategy-count">3个</span>
            </span>
            <span class="status-item">
                总资产: <span class="asset" id="total-assets">¥1,000,000</span>
            </span>
            <span class="status-item">
                今日盈亏: <span class="profit" id="today-pnl">+¥12,500</span>
            </span>
        </footer>
    </div>

    <script>
        // 全局变量
        let currentTimeframe = '1d';
        let currentTab = 'system';
        let currentView = 'data-collection';
        
        // 模拟数据
        const mockStocks = [
            { symbol: '000001', name: '平安银行', price: 12.85, change: 0.25, changePct: 1.98, volume: 1250000 },
            { symbol: '600519', name: '贵州茅台', price: 1680.00, change: -15.00, changePct: -0.88, volume: 890000 },
            { symbol: '000858', name: '五粮液', price: 128.50, change: -2.30, changePct: -1.76, volume: 1100000 },
            { symbol: '600036', name: '招商银行', price: 45.20, change: 1.20, changePct: 2.73, volume: 980000 },
            { symbol: '002415', name: '海康威视', price: 28.90, change: 0.50, changePct: 1.76, volume: 1350000 }
        ];
        
        const mockSignals = [
            { type: 'buy', symbol: '000001', name: '平安银行', score: 85.2, time: '14:25:30' },
            { type: 'sell', symbol: '000858', name: '五粮液', score: 42.1, time: '14:20:15' },
            { type: 'buy', symbol: '600036', name: '招商银行', score: 88.1, time: '14:18:45' }
        ];
        
        const mockPositions = [
            { symbol: '000001', name: '平安银行', quantity: 10000, avgPrice: 12.50, currentPrice: 12.85, pnl: 3500, weight: 12.9 },
            { symbol: '600036', name: '招商银行', quantity: 5000, avgPrice: 44.00, currentPrice: 45.20, pnl: 6000, weight: 22.6 },
            { symbol: '600519', name: '贵州茅台', quantity: 300, avgPrice: 1650.0, currentPrice: 1680.0, pnl: 9000, weight: 50.4 }
        ];
        
        // 初始化函数
        function init() {
            updateTime();
            updateStockList();
            updateSignalList();
            updatePositionTable();
            
            // 启动定时更新
            setInterval(updateTime, 1000);
            setInterval(updateData, 5000);
            
            console.log('🚀 VeighNa量化交易系统 V2.0 Web版本已加载');
            console.log('✅ 严格按照产品原型设计图实现');
            console.log('📊 包含完整的业务功能模块');
        }
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = 
                now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
        }
        
        // 更新股票列表
        function updateStockList() {
            const stockList = document.getElementById('stock-list');
            stockList.innerHTML = '';
            
            mockStocks.forEach(stock => {
                // 添加随机波动
                const priceChange = (Math.random() - 0.5) * 0.1;
                stock.price += priceChange;
                stock.change += priceChange;
                stock.changePct = (stock.change / (stock.price - stock.change)) * 100;
                
                const stockItem = document.createElement('div');
                stockItem.className = `stock-item ${stock.change > 0 ? 'price-up' : 'price-down'}`;
                stockItem.innerHTML = `
                    <div class="stock-code">${stock.symbol}</div>
                    <div class="stock-name">${stock.name}</div>
                    <div class="stock-price">${stock.price.toFixed(2)}</div>
                    <div class="stock-change">${stock.change > 0 ? '+' : ''}${stock.change.toFixed(2)}</div>
                    <div class="stock-change-pct">${stock.changePct > 0 ? '+' : ''}${stock.changePct.toFixed(2)}% ${stock.change > 0 ? '↗' : '↘'}</div>
                `;
                stockList.appendChild(stockItem);
            });
        }
        
        // 更新交易信号
        function updateSignalList() {
            const signalList = document.getElementById('signal-list');
            signalList.innerHTML = '';
            
            mockSignals.forEach(signal => {
                const signalItem = document.createElement('div');
                signalItem.className = `signal-item ${signal.type}`;
                signalItem.innerHTML = `
                    <span class="signal-icon">${signal.type === 'buy' ? '🔴' : '🟢'}</span>
                    <span class="signal-type">${signal.type === 'buy' ? '买入信号' : '卖出信号'}</span>
                    <span class="signal-stock">${signal.symbol} ${signal.name}</span>
                    <span class="signal-score">评分:${signal.score}</span>
                    <span class="signal-time">${signal.time}</span>
                `;
                signalList.appendChild(signalItem);
            });
        }
        
        // 更新持仓表格
        function updatePositionTable() {
            const tbody = document.getElementById('position-tbody');
            tbody.innerHTML = '';
            
            mockPositions.forEach(position => {
                const row = document.createElement('tr');
                row.className = position.pnl > 0 ? 'profit' : 'loss';
                row.innerHTML = `
                    <td>${position.symbol}</td>
                    <td>${position.name}</td>
                    <td>${position.quantity.toLocaleString()}</td>
                    <td>${position.avgPrice.toFixed(2)}</td>
                    <td>${position.currentPrice.toFixed(2)}</td>
                    <td>${position.pnl > 0 ? '+' : ''}${position.pnl.toLocaleString()}</td>
                    <td>${position.weight.toFixed(1)}%</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 更新数据
        function updateData() {
            updateStockList();
            // 可以添加更多数据更新逻辑
        }
        
        // 切换主标签
        function switchTab(element, tabKey) {
            document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
            element.classList.add('active');
            currentTab = tabKey;
            console.log(`切换到标签: ${tabKey}`);
        }
        
        // 切换视图
        function switchView(element, viewKey) {
            const section = element.closest('.nav-section');
            section.querySelectorAll('li').forEach(li => li.classList.remove('active'));
            element.classList.add('active');
            currentView = viewKey;
            console.log(`切换到视图: ${viewKey}`);
        }
        
        // 切换时间周期
        function switchTimeframe(element, timeframe) {
            document.querySelectorAll('.tf-btn').forEach(btn => btn.classList.remove('active'));
            element.classList.add('active');
            currentTimeframe = timeframe;
            document.getElementById('current-timeframe').textContent = timeframe;
            console.log(`切换时间周期: ${timeframe}`);
        }
        
        // 显示设置
        function showSettings() {
            alert('设置功能开发中...\n\n当前系统状态:\n✅ 数据连接正常\n✅ 策略运行正常\n✅ 风控系统正常');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
