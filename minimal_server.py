#!/usr/bin/env python3
"""
最小化VeighNa Web服务器 - 确保能正常运行
"""

from flask import Flask, jsonify, render_template_string
import json
from datetime import datetime
import random

app = Flask(__name__)

# 简单的HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeighNa量化交易系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: #252b3d;
            border-radius: 8px;
        }
        .panel {
            background: #252b3d;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #3a4158;
        }
        .btn {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0099cc;
        }
        .result {
            background: #1a1f2e;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            border: 1px solid #3a4158;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #00ff88; }
        .error { color: #ff4757; }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #3a4158;
        }
        th {
            background: #3a4158;
        }
        .price-up { color: #00ff88; }
        .price-down { color: #ff4757; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 VeighNa量化交易系统</h1>
            <p>专业版量化交易平台</p>
            <p>当前时间: <span id="current-time"></span></p>
        </div>

        <div class="grid">
            <div class="panel">
                <h3>📊 实时行情</h3>
                <button class="btn" onclick="loadStockData()">刷新数据</button>
                <div id="stock-data" class="result"></div>
            </div>

            <div class="panel">
                <h3>🧠 智能选股</h3>
                <button class="btn" onclick="loadSelectionData()">查看选股</button>
                <div id="selection-data" class="result"></div>
            </div>

            <div class="panel">
                <h3>📈 交易信号</h3>
                <button class="btn" onclick="loadSignalData()">查看信号</button>
                <div id="signal-data" class="result"></div>
            </div>

            <div class="panel">
                <h3>⚙️ 系统状态</h3>
                <button class="btn" onclick="loadSystemStatus()">检查状态</button>
                <div id="system-status" class="result"></div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 加载股票数据
        async function loadStockData() {
            const resultDiv = document.getElementById('stock-data');
            resultDiv.innerHTML = '<span style="color: #ffa726;">正在加载...</span>';
            
            try {
                const response = await fetch('/api/realtime-data');
                const data = await response.json();
                
                if (data.success) {
                    let html = '<table><tr><th>代码</th><th>名称</th><th>价格</th><th>涨跌</th><th>涨跌幅</th></tr>';
                    data.data.stocks.slice(0, 10).forEach(stock => {
                        const changeClass = stock.change > 0 ? 'price-up' : stock.change < 0 ? 'price-down' : '';
                        const changeSign = stock.change > 0 ? '+' : '';
                        html += `<tr>
                            <td>${stock.symbol}</td>
                            <td>${stock.name}</td>
                            <td class="${changeClass}">¥${stock.price}</td>
                            <td class="${changeClass}">${changeSign}${stock.change}</td>
                            <td class="${changeClass}">${changeSign}${stock.change_pct}%</td>
                        </tr>`;
                    });
                    html += '</table>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 加载失败: ${data.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 加载选股数据
        async function loadSelectionData() {
            const resultDiv = document.getElementById('selection-data');
            resultDiv.innerHTML = '<span style="color: #ffa726;">正在加载...</span>';
            
            try {
                const response = await fetch('/api/stock-selection');
                const data = await response.json();
                
                if (data.success) {
                    let html = '<table><tr><th>排名</th><th>代码</th><th>名称</th><th>总分</th><th>推荐</th></tr>';
                    data.data.selected_stocks.slice(0, 10).forEach(stock => {
                        html += `<tr>
                            <td>${stock.rank}</td>
                            <td>${stock.symbol}</td>
                            <td>${stock.name}</td>
                            <td>${stock.total_score}</td>
                            <td>${stock.recommendation}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 加载失败: ${data.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 加载信号数据
        async function loadSignalData() {
            const resultDiv = document.getElementById('signal-data');
            resultDiv.innerHTML = '<span style="color: #ffa726;">正在加载...</span>';
            
            try {
                const response = await fetch('/api/trading-signals');
                const data = await response.json();
                
                if (data.success) {
                    let html = '<h4>买入信号:</h4><table><tr><th>代码</th><th>名称</th><th>价格</th><th>置信度</th><th>原因</th></tr>';
                    data.data.buy_signals.forEach(signal => {
                        html += `<tr>
                            <td>${signal.symbol}</td>
                            <td>${signal.name}</td>
                            <td>¥${signal.price}</td>
                            <td>${(signal.confidence * 100).toFixed(0)}%</td>
                            <td>${signal.reason}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    
                    html += '<h4>卖出信号:</h4><table><tr><th>代码</th><th>名称</th><th>价格</th><th>置信度</th><th>原因</th></tr>';
                    data.data.sell_signals.forEach(signal => {
                        html += `<tr>
                            <td>${signal.symbol}</td>
                            <td>${signal.name}</td>
                            <td>¥${signal.price}</td>
                            <td>${(signal.confidence * 100).toFixed(0)}%</td>
                            <td>${signal.reason}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 加载失败: ${data.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 加载系统状态
        async function loadSystemStatus() {
            const resultDiv = document.getElementById('system-status');
            resultDiv.innerHTML = '<span style="color: #ffa726;">正在加载...</span>';
            
            try {
                const response = await fetch('/api/system/status');
                const data = await response.json();
                
                if (data.success) {
                    const status = data.data.business_flow;
                    let html = `
                        <p><strong>系统健康:</strong> <span class="success">${status.system_health}</span></p>
                        <p><strong>数据采集:</strong> ${status.data_collection_status}</p>
                        <p><strong>选股状态:</strong> ${status.stock_selection_status}</p>
                        <p><strong>信号跟踪:</strong> ${status.signal_tracking_status}</p>
                        <p><strong>跟踪股票:</strong> ${status.total_stocks_tracked} 只</p>
                        <p><strong>活跃信号:</strong> ${status.active_signals_count} 个</p>
                        <p><strong>系统版本:</strong> ${data.data.version}</p>
                    `;
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 加载失败: ${data.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 页面加载完成后自动加载数据
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadSystemStatus();
                loadStockData();
            }, 1000);
        });
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/realtime-data')
def get_realtime_data():
    """获取实时数据API"""
    # 模拟股票数据
    stocks = [
        {'symbol': '000001', 'name': '平安银行', 'price': 12.85, 'change': 0.35, 'change_pct': 2.80},
        {'symbol': '600519', 'name': '贵州茅台', 'price': 1680.00, 'change': -15.00, 'change_pct': -0.88},
        {'symbol': '000858', 'name': '五粮液', 'price': 128.50, 'change': -2.25, 'change_pct': -1.72},
        {'symbol': '600036', 'name': '招商银行', 'price': 45.20, 'change': 1.18, 'change_pct': 2.68},
        {'symbol': '002415', 'name': '海康威视', 'price': 28.90, 'change': 0.48, 'change_pct': 1.69},
        {'symbol': '000725', 'name': '京东方A', 'price': 3.85, 'change': -0.05, 'change_pct': -1.28},
        {'symbol': '600276', 'name': '恒瑞医药', 'price': 58.20, 'change': 2.10, 'change_pct': 3.74},
        {'symbol': '000063', 'name': '中兴通讯', 'price': 32.50, 'change': 1.25, 'change_pct': 4.00},
        {'symbol': '002594', 'name': '比亚迪', 'price': 245.80, 'change': -8.20, 'change_pct': -3.23},
        {'symbol': '600031', 'name': '三一重工', 'price': 18.90, 'change': 0.65, 'change_pct': 3.56}
    ]
    
    # 添加随机波动
    for stock in stocks:
        fluctuation = random.uniform(-0.02, 0.02)
        stock['price'] = round(stock['price'] * (1 + fluctuation), 2)
        stock['change'] = round(stock['change'] * (1 + fluctuation), 2)
        stock['change_pct'] = round(stock['change_pct'] * (1 + fluctuation), 2)
    
    return jsonify({
        'success': True,
        'data': {
            'stocks': stocks,
            'market_summary': {
                'total_stocks': len(stocks),
                'up_count': len([s for s in stocks if s['change'] > 0]),
                'down_count': len([s for s in stocks if s['change'] < 0]),
                'unchanged_count': len([s for s in stocks if s['change'] == 0])
            }
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/stock-selection')
def get_stock_selection():
    """获取智能选股结果API"""
    selected_stocks = [
        {'rank': 1, 'symbol': '600276', 'name': '恒瑞医药', 'total_score': 92.5, 'recommendation': '强烈推荐'},
        {'rank': 2, 'symbol': '000063', 'name': '中兴通讯', 'total_score': 89.2, 'recommendation': '强烈推荐'},
        {'rank': 3, 'symbol': '600031', 'name': '三一重工', 'total_score': 86.8, 'recommendation': '推荐'},
        {'rank': 4, 'symbol': '002415', 'name': '海康威视', 'total_score': 84.3, 'recommendation': '推荐'},
        {'rank': 5, 'symbol': '000001', 'name': '平安银行', 'total_score': 82.1, 'recommendation': '推荐'},
        {'rank': 6, 'symbol': '600036', 'name': '招商银行', 'total_score': 79.6, 'recommendation': '观察'},
        {'rank': 7, 'symbol': '000858', 'name': '五粮液', 'total_score': 77.4, 'recommendation': '观察'},
        {'rank': 8, 'symbol': '600519', 'name': '贵州茅台', 'total_score': 75.2, 'recommendation': '观察'}
    ]
    
    return jsonify({
        'success': True,
        'data': {
            'selected_stocks': selected_stocks,
            'statistics': {
                'total_selected': len(selected_stocks),
                'avg_score': sum(s['total_score'] for s in selected_stocks) / len(selected_stocks)
            }
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/trading-signals')
def get_trading_signals():
    """获取交易信号API"""
    buy_signals = [
        {'symbol': '000001', 'name': '平安银行', 'price': 12.85, 'confidence': 0.9, 'reason': 'MACD金叉形成，成交量放大'},
        {'symbol': '600031', 'name': '三一重工', 'price': 18.90, 'confidence': 0.85, 'reason': 'RSI超卖反弹，技术面转强'},
        {'symbol': '002415', 'name': '海康威视', 'price': 28.90, 'confidence': 0.8, 'reason': '均线多头排列，突破压力位'}
    ]
    
    sell_signals = [
        {'symbol': '002594', 'name': '比亚迪', 'price': 245.80, 'confidence': 0.75, 'reason': 'RSI超买回调，价格遇阻'},
        {'symbol': '000725', 'name': '京东方A', 'price': 3.85, 'confidence': 0.7, 'reason': 'MACD死叉形成，成交量萎缩'}
    ]
    
    return jsonify({
        'success': True,
        'data': {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'statistics': {
                'total_signals': len(buy_signals) + len(sell_signals),
                'buy_signals': len(buy_signals),
                'sell_signals': len(sell_signals),
                'success_rate': 0.78
            }
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/system/status')
def get_system_status():
    """获取系统状态API"""
    return jsonify({
        'success': True,
        'data': {
            'business_flow': {
                'data_collection_status': 'running',
                'stock_selection_status': 'completed',
                'signal_tracking_status': 'running',
                'system_health': 'healthy',
                'total_stocks_tracked': 30,
                'active_signals_count': 8,
                'is_running': True
            },
            'web_server': 'running',
            'database': 'connected',
            'version': '2.0.0 Professional',
            'uptime': 3600
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == "__main__":
    print("🚀 启动VeighNa最小化Web服务器")
    print("=" * 50)
    print("🌐 Web界面: http://localhost:8080")
    print("📊 实时数据API: http://localhost:8080/api/realtime-data")
    print("🧠 智能选股API: http://localhost:8080/api/stock-selection")
    print("📈 交易信号API: http://localhost:8080/api/trading-signals")
    print("⚙️ 系统状态API: http://localhost:8080/api/system/status")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=8080, debug=False)
