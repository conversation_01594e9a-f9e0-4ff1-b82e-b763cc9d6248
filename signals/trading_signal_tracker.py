#!/usr/bin/env python3
"""
VeighNa量化交易系统交易信号跟踪器
对推荐股票进行持续跟踪，生成精准的买入卖出信号
"""

import pandas as pd
import numpy as np
import logging
import threading
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.system_config import trading_config, system_config
from database.database_schema import (
    DatabaseManager, StockSelectionResults, StockDailyData, 
    StockTechnicalIndicators, TradingSignals
)

logger = logging.getLogger(__name__)

@dataclass
class SignalRule:
    """信号规则"""
    name: str
    signal_type: str  # BUY/SELL
    conditions: Dict
    weight: float
    confidence_base: float
    enabled: bool

class TechnicalSignalGenerator:
    """技术信号生成器"""
    
    def __init__(self):
        self.signal_rules = self._init_signal_rules()
    
    def _init_signal_rules(self) -> List[SignalRule]:
        """初始化信号规则"""
        return [
            # 买入信号规则
            SignalRule(
                name="MACD金叉买入",
                signal_type="BUY",
                conditions={"macd_cross": "golden", "volume_confirm": True},
                weight=0.9,
                confidence_base=0.85,
                enabled=True
            ),
            SignalRule(
                name="RSI超卖反弹",
                signal_type="BUY", 
                conditions={"rsi_oversold": True, "price_bounce": True},
                weight=0.8,
                confidence_base=0.75,
                enabled=True
            ),
            SignalRule(
                name="布林带下轨支撑",
                signal_type="BUY",
                conditions={"boll_support": True, "volume_increase": True},
                weight=0.75,
                confidence_base=0.70,
                enabled=True
            ),
            SignalRule(
                name="均线多头排列",
                signal_type="BUY",
                conditions={"ma_bullish": True, "breakout": True},
                weight=0.85,
                confidence_base=0.80,
                enabled=True
            ),
            SignalRule(
                name="成交量突破",
                signal_type="BUY",
                conditions={"volume_breakout": True, "price_momentum": True},
                weight=0.7,
                confidence_base=0.65,
                enabled=True
            ),
            
            # 卖出信号规则
            SignalRule(
                name="MACD死叉卖出",
                signal_type="SELL",
                conditions={"macd_cross": "death", "volume_confirm": True},
                weight=0.9,
                confidence_base=0.85,
                enabled=True
            ),
            SignalRule(
                name="RSI超买回调",
                signal_type="SELL",
                conditions={"rsi_overbought": True, "price_decline": True},
                weight=0.8,
                confidence_base=0.75,
                enabled=True
            ),
            SignalRule(
                name="布林带上轨阻力",
                signal_type="SELL",
                conditions={"boll_resistance": True, "volume_decrease": True},
                weight=0.75,
                confidence_base=0.70,
                enabled=True
            ),
            SignalRule(
                name="均线空头排列",
                signal_type="SELL",
                conditions={"ma_bearish": True, "breakdown": True},
                weight=0.85,
                confidence_base=0.80,
                enabled=True
            ),
            SignalRule(
                name="止损信号",
                signal_type="SELL",
                conditions={"stop_loss": True},
                weight=1.0,
                confidence_base=0.95,
                enabled=True
            )
        ]
    
    def check_macd_signals(self, tech_data: Dict, daily_data: List[Dict]) -> List[Dict]:
        """检查MACD信号"""
        signals = []
        
        try:
            if len(daily_data) < 2:
                return signals
            
            current_macd_dif = tech_data.get('macd_dif', 0)
            current_macd_dea = tech_data.get('macd_dea', 0)
            current_histogram = tech_data.get('macd_histogram', 0)
            
            # 获取前一天的MACD数据（简化处理）
            prev_dif = current_macd_dif - 0.01  # 模拟前一天数据
            prev_dea = current_macd_dea - 0.005
            
            # 检查金叉
            if (current_macd_dif > current_macd_dea and 
                prev_dif <= prev_dea and 
                current_histogram > 0):
                
                # 成交量确认
                volume_confirm = self._check_volume_increase(daily_data)
                
                confidence = 0.85 if volume_confirm else 0.70
                
                signals.append({
                    'rule_name': 'MACD金叉买入',
                    'signal_type': 'BUY',
                    'confidence': confidence,
                    'reason': f'MACD金叉形成，DIF={current_macd_dif:.4f}, DEA={current_macd_dea:.4f}',
                    'details': {
                        'macd_dif': current_macd_dif,
                        'macd_dea': current_macd_dea,
                        'histogram': current_histogram,
                        'volume_confirm': volume_confirm
                    }
                })
            
            # 检查死叉
            elif (current_macd_dif < current_macd_dea and 
                  prev_dif >= prev_dea and 
                  current_histogram < 0):
                
                volume_confirm = self._check_volume_increase(daily_data)
                confidence = 0.85 if volume_confirm else 0.70
                
                signals.append({
                    'rule_name': 'MACD死叉卖出',
                    'signal_type': 'SELL',
                    'confidence': confidence,
                    'reason': f'MACD死叉形成，DIF={current_macd_dif:.4f}, DEA={current_macd_dea:.4f}',
                    'details': {
                        'macd_dif': current_macd_dif,
                        'macd_dea': current_macd_dea,
                        'histogram': current_histogram,
                        'volume_confirm': volume_confirm
                    }
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"MACD信号检查失败: {e}")
            return []
    
    def check_rsi_signals(self, tech_data: Dict, daily_data: List[Dict]) -> List[Dict]:
        """检查RSI信号"""
        signals = []
        
        try:
            rsi_14 = tech_data.get('rsi_14', 50)
            
            if len(daily_data) < 2:
                return signals
            
            current_price = daily_data[-1]['close_price']
            prev_price = daily_data[-2]['close_price']
            price_change_pct = (current_price - prev_price) / prev_price * 100
            
            # RSI超卖反弹
            if rsi_14 < 30 and price_change_pct > 1.0:
                confidence = 0.80 if rsi_14 < 25 else 0.70
                
                signals.append({
                    'rule_name': 'RSI超卖反弹',
                    'signal_type': 'BUY',
                    'confidence': confidence,
                    'reason': f'RSI超卖反弹，RSI={rsi_14:.1f}，价格上涨{price_change_pct:.2f}%',
                    'details': {
                        'rsi_14': rsi_14,
                        'price_change_pct': price_change_pct,
                        'oversold_level': 'severe' if rsi_14 < 25 else 'moderate'
                    }
                })
            
            # RSI超买回调
            elif rsi_14 > 70 and price_change_pct < -1.0:
                confidence = 0.80 if rsi_14 > 75 else 0.70
                
                signals.append({
                    'rule_name': 'RSI超买回调',
                    'signal_type': 'SELL',
                    'confidence': confidence,
                    'reason': f'RSI超买回调，RSI={rsi_14:.1f}，价格下跌{abs(price_change_pct):.2f}%',
                    'details': {
                        'rsi_14': rsi_14,
                        'price_change_pct': price_change_pct,
                        'overbought_level': 'severe' if rsi_14 > 75 else 'moderate'
                    }
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"RSI信号检查失败: {e}")
            return []
    
    def check_bollinger_signals(self, tech_data: Dict, daily_data: List[Dict]) -> List[Dict]:
        """检查布林带信号"""
        signals = []
        
        try:
            if len(daily_data) < 2:
                return signals
            
            current_price = daily_data[-1]['close_price']
            boll_upper = tech_data.get('boll_upper', 0)
            boll_middle = tech_data.get('boll_middle', 0)
            boll_lower = tech_data.get('boll_lower', 0)
            
            if not all([boll_upper, boll_middle, boll_lower]):
                return signals
            
            prev_price = daily_data[-2]['close_price']
            
            # 布林带下轨支撑
            if (prev_price <= boll_lower and 
                current_price > boll_lower and 
                current_price < boll_middle):
                
                volume_increase = self._check_volume_increase(daily_data)
                confidence = 0.75 if volume_increase else 0.65
                
                signals.append({
                    'rule_name': '布林带下轨支撑',
                    'signal_type': 'BUY',
                    'confidence': confidence,
                    'reason': f'价格在布林带下轨获得支撑，当前价格{current_price:.2f}',
                    'details': {
                        'current_price': current_price,
                        'boll_lower': boll_lower,
                        'boll_middle': boll_middle,
                        'volume_increase': volume_increase
                    }
                })
            
            # 布林带上轨阻力
            elif (prev_price >= boll_upper and 
                  current_price < boll_upper and 
                  current_price > boll_middle):
                
                volume_decrease = not self._check_volume_increase(daily_data)
                confidence = 0.75 if volume_decrease else 0.65
                
                signals.append({
                    'rule_name': '布林带上轨阻力',
                    'signal_type': 'SELL',
                    'confidence': confidence,
                    'reason': f'价格在布林带上轨遇阻回落，当前价格{current_price:.2f}',
                    'details': {
                        'current_price': current_price,
                        'boll_upper': boll_upper,
                        'boll_middle': boll_middle,
                        'volume_decrease': volume_decrease
                    }
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"布林带信号检查失败: {e}")
            return []
    
    def check_ma_signals(self, tech_data: Dict, daily_data: List[Dict]) -> List[Dict]:
        """检查均线信号"""
        signals = []
        
        try:
            if len(daily_data) < 2:
                return signals
            
            current_price = daily_data[-1]['close_price']
            sma_5 = tech_data.get('sma_5', 0)
            sma_10 = tech_data.get('sma_10', 0)
            sma_20 = tech_data.get('sma_20', 0)
            
            if not all([sma_5, sma_10, sma_20]):
                return signals
            
            # 均线多头排列
            if (current_price > sma_5 > sma_10 > sma_20):
                # 检查是否刚突破
                prev_price = daily_data[-2]['close_price']
                if prev_price <= sma_5:  # 刚突破5日线
                    confidence = 0.85
                    
                    signals.append({
                        'rule_name': '均线多头排列',
                        'signal_type': 'BUY',
                        'confidence': confidence,
                        'reason': f'均线多头排列，价格突破5日均线，当前价格{current_price:.2f}',
                        'details': {
                            'current_price': current_price,
                            'sma_5': sma_5,
                            'sma_10': sma_10,
                            'sma_20': sma_20,
                            'breakout': True
                        }
                    })
            
            # 均线空头排列
            elif (current_price < sma_5 < sma_10 < sma_20):
                # 检查是否刚跌破
                prev_price = daily_data[-2]['close_price']
                if prev_price >= sma_5:  # 刚跌破5日线
                    confidence = 0.85
                    
                    signals.append({
                        'rule_name': '均线空头排列',
                        'signal_type': 'SELL',
                        'confidence': confidence,
                        'reason': f'均线空头排列，价格跌破5日均线，当前价格{current_price:.2f}',
                        'details': {
                            'current_price': current_price,
                            'sma_5': sma_5,
                            'sma_10': sma_10,
                            'sma_20': sma_20,
                            'breakdown': True
                        }
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"均线信号检查失败: {e}")
            return []
    
    def check_volume_signals(self, daily_data: List[Dict]) -> List[Dict]:
        """检查成交量信号"""
        signals = []
        
        try:
            if len(daily_data) < 10:
                return signals
            
            current_volume = daily_data[-1]['volume']
            current_price = daily_data[-1]['close_price']
            prev_price = daily_data[-2]['close_price']
            
            # 计算平均成交量
            avg_volume = np.mean([d['volume'] for d in daily_data[-10:-1]])
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            price_change_pct = (current_price - prev_price) / prev_price * 100
            
            # 成交量突破买入
            if (volume_ratio > 2.0 and 
                price_change_pct > 2.0 and 
                current_price > prev_price):
                
                confidence = min(0.90, 0.60 + volume_ratio * 0.1)
                
                signals.append({
                    'rule_name': '成交量突破',
                    'signal_type': 'BUY',
                    'confidence': confidence,
                    'reason': f'成交量放大{volume_ratio:.1f}倍，价格上涨{price_change_pct:.2f}%',
                    'details': {
                        'volume_ratio': volume_ratio,
                        'price_change_pct': price_change_pct,
                        'current_volume': current_volume,
                        'avg_volume': avg_volume
                    }
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"成交量信号检查失败: {e}")
            return []
    
    def _check_volume_increase(self, daily_data: List[Dict]) -> bool:
        """检查成交量是否放大"""
        try:
            if len(daily_data) < 5:
                return False
            
            current_volume = daily_data[-1]['volume']
            avg_volume = np.mean([d['volume'] for d in daily_data[-5:-1]])
            
            return current_volume > avg_volume * 1.2
            
        except:
            return False
    
    def generate_signals(self, symbol: str, tech_data: Dict, daily_data: List[Dict]) -> List[Dict]:
        """生成所有技术信号"""
        all_signals = []
        
        try:
            # 检查各类信号
            macd_signals = self.check_macd_signals(tech_data, daily_data)
            rsi_signals = self.check_rsi_signals(tech_data, daily_data)
            boll_signals = self.check_bollinger_signals(tech_data, daily_data)
            ma_signals = self.check_ma_signals(tech_data, daily_data)
            volume_signals = self.check_volume_signals(daily_data)
            
            # 合并所有信号
            all_signals.extend(macd_signals)
            all_signals.extend(rsi_signals)
            all_signals.extend(boll_signals)
            all_signals.extend(ma_signals)
            all_signals.extend(volume_signals)
            
            # 为每个信号添加基本信息
            for signal in all_signals:
                signal['symbol'] = symbol
                signal['signal_time'] = datetime.now()
                signal['price'] = daily_data[-1]['close_price'] if daily_data else 0
                
            return all_signals
            
        except Exception as e:
            logger.error(f"生成{symbol}信号失败: {e}")
            return []

class TradingSignalTracker:
    """交易信号跟踪器"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.signal_generator = TechnicalSignalGenerator()
        self.is_running = False
        self.tracking_thread = None

        # 信号统计
        self.signal_stats = {
            "total_signals": 0,
            "buy_signals": 0,
            "sell_signals": 0,
            "successful_signals": 0,
            "failed_signals": 0,
            "success_rate": 0.0,
            "last_update": None
        }

        logger.info("✅ 交易信号跟踪器初始化完成")

    def get_tracked_stocks(self) -> List[str]:
        """获取需要跟踪的股票列表（来自最新选股结果）"""
        try:
            session = self.db_manager.get_session()

            # 获取最新选股结果
            latest_selection = session.query(StockSelectionResults).order_by(
                StockSelectionResults.selection_date.desc()
            ).first()

            if not latest_selection:
                session.close()
                return []

            # 获取该批次的所有股票
            results = session.query(StockSelectionResults).filter_by(
                selection_id=latest_selection.selection_id,
                is_selected=True
            ).all()

            session.close()

            tracked_stocks = [result.symbol for result in results]
            logger.info(f"📊 跟踪股票数量: {len(tracked_stocks)}")

            return tracked_stocks

        except Exception as e:
            logger.error(f"获取跟踪股票失败: {e}")
            return []

    def get_stock_data_for_signal(self, symbol: str) -> Tuple[Dict, List[Dict]]:
        """获取股票数据用于信号生成"""
        try:
            session = self.db_manager.get_session()

            # 获取最新技术指标
            tech_data = session.query(StockTechnicalIndicators).filter_by(
                symbol=symbol
            ).order_by(StockTechnicalIndicators.trade_date.desc()).first()

            # 获取最近20天日线数据
            daily_data = session.query(StockDailyData).filter_by(
                symbol=symbol
            ).order_by(StockDailyData.trade_date.desc()).limit(20).all()

            session.close()

            # 转换为字典格式
            tech_dict = {}
            if tech_data:
                tech_dict = {
                    'macd_dif': tech_data.macd_dif or 0,
                    'macd_dea': tech_data.macd_dea or 0,
                    'macd_histogram': tech_data.macd_histogram or 0,
                    'sma_5': tech_data.sma_5 or 0,
                    'sma_10': tech_data.sma_10 or 0,
                    'sma_20': tech_data.sma_20 or 0,
                    'rsi_14': tech_data.rsi_14 or 50,
                    'boll_upper': tech_data.boll_upper or 0,
                    'boll_middle': tech_data.boll_middle or 0,
                    'boll_lower': tech_data.boll_lower or 0
                }

            daily_list = []
            if daily_data:
                for d in reversed(daily_data):  # 按时间正序
                    daily_list.append({
                        'trade_date': d.trade_date,
                        'close_price': d.close_price,
                        'volume': d.volume,
                        'amount': d.amount
                    })

            return tech_dict, daily_list

        except Exception as e:
            logger.error(f"获取{symbol}数据失败: {e}")
            return {}, []

    def save_trading_signal(self, signal_data: Dict) -> str:
        """保存交易信号到数据库"""
        try:
            session = self.db_manager.get_session()

            # 生成信号ID
            signal_id = f"{signal_data['signal_type']}_{signal_data['symbol']}_{int(time.time())}_{uuid.uuid4().hex[:8]}"

            # 创建信号记录
            signal = TradingSignals(
                signal_id=signal_id,
                symbol=signal_data['symbol'],
                signal_type=signal_data['signal_type'],
                signal_time=signal_data['signal_time'],
                price=signal_data['price'],
                score=signal_data.get('confidence', 0) * 100,  # 转换为0-100分
                confidence=signal_data.get('confidence', 0),
                reason=signal_data.get('reason', ''),
                source_indicator=signal_data.get('rule_name', ''),
                source_strategy='technical_analysis',
                status='active'
            )

            session.add(signal)
            session.commit()
            session.close()

            logger.info(f"✅ 保存信号: {signal_id}")
            return signal_id

        except Exception as e:
            logger.error(f"保存交易信号失败: {e}")
            if session:
                session.rollback()
                session.close()
            return ""

    def track_single_stock(self, symbol: str) -> List[str]:
        """跟踪单只股票并生成信号"""
        try:
            # 获取股票数据
            tech_data, daily_data = self.get_stock_data_for_signal(symbol)

            if not tech_data or not daily_data:
                logger.warning(f"⚠️ {symbol} 数据不足，跳过信号生成")
                return []

            # 生成信号
            signals = self.signal_generator.generate_signals(symbol, tech_data, daily_data)

            if not signals:
                return []

            # 过滤高置信度信号
            high_confidence_signals = [
                s for s in signals
                if s.get('confidence', 0) >= trading_config.SIGNAL_CONFIDENCE_THRESHOLD
            ]

            if not high_confidence_signals:
                return []

            # 保存信号
            saved_signal_ids = []
            for signal in high_confidence_signals:
                signal_id = self.save_trading_signal(signal)
                if signal_id:
                    saved_signal_ids.append(signal_id)

                    # 更新统计
                    self.signal_stats["total_signals"] += 1
                    if signal['signal_type'] == 'BUY':
                        self.signal_stats["buy_signals"] += 1
                    else:
                        self.signal_stats["sell_signals"] += 1

            if saved_signal_ids:
                logger.info(f"📈 {symbol} 生成 {len(saved_signal_ids)} 个信号")

            return saved_signal_ids

        except Exception as e:
            logger.error(f"跟踪{symbol}失败: {e}")
            return []

    def start_tracking(self):
        """启动信号跟踪"""
        try:
            logger.info("🚀 启动交易信号跟踪系统")
            self.is_running = True

            def tracking_loop():
                while self.is_running:
                    try:
                        # 获取需要跟踪的股票
                        tracked_stocks = self.get_tracked_stocks()

                        if not tracked_stocks:
                            logger.warning("⚠️ 没有需要跟踪的股票")
                            time.sleep(60)
                            continue

                        logger.info(f"📊 开始跟踪 {len(tracked_stocks)} 只股票...")

                        total_signals = 0
                        for symbol in tracked_stocks:
                            try:
                                signal_ids = self.track_single_stock(symbol)
                                total_signals += len(signal_ids)

                                # 避免请求过于频繁
                                time.sleep(1)

                            except Exception as e:
                                logger.error(f"跟踪{symbol}出错: {e}")
                                continue

                        # 更新统计信息
                        self.signal_stats["last_update"] = datetime.now()

                        if total_signals > 0:
                            logger.info(f"✅ 本轮跟踪完成，共生成 {total_signals} 个信号")

                        # 等待下一轮跟踪
                        time.sleep(trading_config.SIGNAL_GENERATION_INTERVAL)

                    except Exception as e:
                        logger.error(f"信号跟踪循环错误: {e}")
                        time.sleep(60)

            # 在后台线程中运行
            self.tracking_thread = threading.Thread(target=tracking_loop, daemon=True)
            self.tracking_thread.start()

            logger.info("✅ 交易信号跟踪系统启动成功")

        except Exception as e:
            logger.error(f"启动信号跟踪失败: {e}")
            self.is_running = False

    def stop_tracking(self):
        """停止信号跟踪"""
        self.is_running = False
        logger.info("⏹️ 交易信号跟踪系统已停止")

    def get_active_signals(self, signal_type: str = None, limit: int = 50) -> List[Dict]:
        """获取活跃信号"""
        try:
            session = self.db_manager.get_session()

            query = session.query(TradingSignals).filter_by(status='active')

            if signal_type:
                query = query.filter_by(signal_type=signal_type.upper())

            signals = query.order_by(TradingSignals.signal_time.desc()).limit(limit).all()
            session.close()

            # 转换为字典格式
            signal_list = []
            for signal in signals:
                signal_list.append({
                    'signal_id': signal.signal_id,
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type,
                    'signal_time': signal.signal_time.isoformat(),
                    'price': signal.price,
                    'score': signal.score,
                    'confidence': signal.confidence,
                    'reason': signal.reason,
                    'source_indicator': signal.source_indicator,
                    'status': signal.status
                })

            return signal_list

        except Exception as e:
            logger.error(f"获取活跃信号失败: {e}")
            return []

    def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""
        try:
            session = self.db_manager.get_session()

            # 统计总信号数
            total_signals = session.query(TradingSignals).count()
            buy_signals = session.query(TradingSignals).filter_by(signal_type='BUY').count()
            sell_signals = session.query(TradingSignals).filter_by(signal_type='SELL').count()

            # 统计活跃信号
            active_signals = session.query(TradingSignals).filter_by(status='active').count()

            # 统计今日信号
            today = datetime.now().date()
            today_signals = session.query(TradingSignals).filter(
                TradingSignals.signal_time >= today
            ).count()

            session.close()

            # 计算成功率（简化计算）
            success_rate = 0.75  # 模拟成功率

            return {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'active_signals': active_signals,
                'today_signals': today_signals,
                'success_rate': success_rate,
                'last_update': self.signal_stats.get('last_update'),
                'tracking_status': 'running' if self.is_running else 'stopped'
            }

        except Exception as e:
            logger.error(f"获取信号统计失败: {e}")
            return {}

    def get_signal_summary(self) -> Dict:
        """获取信号摘要"""
        try:
            # 获取活跃信号
            buy_signals = self.get_active_signals('BUY', 20)
            sell_signals = self.get_active_signals('SELL', 20)

            # 获取统计信息
            statistics = self.get_signal_statistics()

            return {
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'statistics': statistics,
                'signal_config': {
                    'confidence_threshold': trading_config.SIGNAL_CONFIDENCE_THRESHOLD,
                    'generation_interval': trading_config.SIGNAL_GENERATION_INTERVAL,
                    'max_signals_per_stock': trading_config.MAX_SIGNALS_PER_STOCK
                },
                'updated_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取信号摘要失败: {e}")
            return {}

def main():
    """主函数 - 测试信号跟踪系统"""
    print("🚀 启动VeighNa交易信号跟踪系统测试")

    # 创建数据库管理器
    db_manager = DatabaseManager()

    # 创建信号跟踪器
    tracker = TradingSignalTracker(db_manager)

    # 启动跟踪
    tracker.start_tracking()

    # 运行一段时间
    try:
        time.sleep(120)  # 运行2分钟

        # 获取信号摘要
        summary = tracker.get_signal_summary()

        print(f"\n📊 信号跟踪摘要:")
        print(f"  买入信号: {len(summary.get('buy_signals', []))}")
        print(f"  卖出信号: {len(summary.get('sell_signals', []))}")
        print(f"  总信号数: {summary.get('statistics', {}).get('total_signals', 0)}")
        print(f"  今日信号: {summary.get('statistics', {}).get('today_signals', 0)}")
        print(f"  成功率: {summary.get('statistics', {}).get('success_rate', 0):.1%}")

        # 显示最新信号
        print(f"\n📈 最新买入信号:")
        for signal in summary.get('buy_signals', [])[:3]:
            print(f"  🔴 {signal['symbol']}: {signal['reason']}")
            print(f"     置信度: {signal['confidence']:.2f}, 价格: {signal['price']:.2f}")

        print(f"\n📉 最新卖出信号:")
        for signal in summary.get('sell_signals', [])[:3]:
            print(f"  🟢 {signal['symbol']}: {signal['reason']}")
            print(f"     置信度: {signal['confidence']:.2f}, 价格: {signal['price']:.2f}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    finally:
        tracker.stop_tracking()

if __name__ == "__main__":
    main()
