#!/usr/bin/env python3
"""
VeighNa量化交易系统智能选股引擎
实现多因子选股模型、股票评分算法、候选池管理等功能
"""

import numpy as np
import pandas as pd
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import math
import random

from core_data_engine import DatabaseManager, data_engine

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """股票评分数据结构"""
    symbol: str
    name: str
    total_score: float
    technical_score: float
    fundamental_score: float
    market_score: float
    rank: int
    recommendation: str
    reason: str
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'name': self.name,
            'total_score': self.total_score,
            'technical_score': self.technical_score,
            'fundamental_score': self.fundamental_score,
            'market_score': self.market_score,
            'rank': self.rank,
            'recommendation': self.recommendation,
            'reason': self.reason
        }

class TechnicalAnalyzer:
    """技术分析器"""
    
    def __init__(self):
        self.indicators = {}
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, prices: List[float]) -> Tuple[float, float, float]:
        """计算MACD指标"""
        if len(prices) < 26:
            return 0.0, 0.0, 0.0
        
        prices_array = np.array(prices)
        
        # 计算EMA
        ema12 = pd.Series(prices).ewm(span=12).mean().iloc[-1]
        ema26 = pd.Series(prices).ewm(span=26).mean().iloc[-1]
        
        macd_line = ema12 - ema26
        signal_line = pd.Series([macd_line]).ewm(span=9).mean().iloc[-1]
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def calculate_bollinger_bands(self, prices: List[float], period: int = 20) -> Tuple[float, float, float]:
        """计算布林带"""
        if len(prices) < period:
            current_price = prices[-1] if prices else 0
            return current_price, current_price * 1.02, current_price * 0.98
        
        prices_array = np.array(prices[-period:])
        sma = np.mean(prices_array)
        std = np.std(prices_array)
        
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        
        return sma, upper_band, lower_band
    
    def analyze_technical_score(self, symbol: str, prices: List[float]) -> float:
        """计算技术分析评分"""
        if not prices or len(prices) < 5:
            return 50.0
        
        score = 0.0
        current_price = prices[-1]
        
        # RSI评分 (30分)
        rsi = self.calculate_rsi(prices)
        if 30 <= rsi <= 70:
            rsi_score = 30
        elif rsi < 30:
            rsi_score = 25  # 超卖，稍微扣分
        else:
            rsi_score = 20  # 超买，扣分较多
        score += rsi_score
        
        # MACD评分 (25分)
        macd_line, signal_line, histogram = self.calculate_macd(prices)
        if macd_line > signal_line and histogram > 0:
            macd_score = 25
        elif macd_line > signal_line:
            macd_score = 20
        else:
            macd_score = 15
        score += macd_score
        
        # 布林带评分 (25分)
        sma, upper_band, lower_band = self.calculate_bollinger_bands(prices)
        if lower_band <= current_price <= upper_band:
            bb_score = 25
        elif current_price < lower_band:
            bb_score = 20  # 可能超卖
        else:
            bb_score = 15  # 可能超买
        score += bb_score
        
        # 趋势评分 (20分)
        if len(prices) >= 5:
            recent_trend = (prices[-1] - prices[-5]) / prices[-5] * 100
            if recent_trend > 2:
                trend_score = 20
            elif recent_trend > 0:
                trend_score = 15
            elif recent_trend > -2:
                trend_score = 10
            else:
                trend_score = 5
        else:
            trend_score = 10
        score += trend_score
        
        return min(100.0, max(0.0, score))

class FundamentalAnalyzer:
    """基本面分析器"""
    
    def __init__(self):
        # 模拟基本面数据
        self.fundamental_data = {
            "000001": {"pe": 5.2, "pb": 0.8, "roe": 12.5, "debt_ratio": 0.15, "growth": 8.2},
            "600519": {"pe": 28.5, "pb": 8.2, "roe": 25.8, "debt_ratio": 0.12, "growth": 15.6},
            "000858": {"pe": 22.1, "pb": 4.5, "roe": 18.9, "debt_ratio": 0.25, "growth": 12.3},
            "600036": {"pe": 6.8, "pb": 1.2, "roe": 15.2, "debt_ratio": 0.18, "growth": 9.8},
            "002415": {"pe": 18.5, "pb": 3.2, "roe": 16.8, "debt_ratio": 0.22, "growth": 11.5},
            "000002": {"pe": 12.5, "pb": 1.8, "roe": 8.5, "debt_ratio": 0.45, "growth": 5.2},
            "600000": {"pe": 4.8, "pb": 0.6, "roe": 10.2, "debt_ratio": 0.20, "growth": 6.8},
            "000166": {"pe": 15.2, "pb": 1.5, "roe": 7.8, "debt_ratio": 0.35, "growth": 4.5},
            "600887": {"pe": 25.8, "pb": 5.2, "roe": 22.5, "debt_ratio": 0.15, "growth": 18.2},
            "002594": {"pe": 35.2, "pb": 6.8, "roe": 28.5, "debt_ratio": 0.18, "growth": 25.8}
        }
    
    def analyze_fundamental_score(self, symbol: str) -> float:
        """计算基本面评分"""
        data = self.fundamental_data.get(symbol, {})
        if not data:
            return 50.0
        
        score = 0.0
        
        # PE评分 (25分)
        pe = data.get('pe', 20)
        if 5 <= pe <= 15:
            pe_score = 25
        elif 15 < pe <= 25:
            pe_score = 20
        elif pe < 5:
            pe_score = 15  # 可能有问题
        else:
            pe_score = 10
        score += pe_score
        
        # PB评分 (20分)
        pb = data.get('pb', 2)
        if 0.5 <= pb <= 3:
            pb_score = 20
        elif pb < 0.5:
            pb_score = 15
        else:
            pb_score = 10
        score += pb_score
        
        # ROE评分 (25分)
        roe = data.get('roe', 10)
        if roe >= 20:
            roe_score = 25
        elif roe >= 15:
            roe_score = 20
        elif roe >= 10:
            roe_score = 15
        else:
            roe_score = 10
        score += roe_score
        
        # 负债率评分 (15分)
        debt_ratio = data.get('debt_ratio', 0.3)
        if debt_ratio <= 0.2:
            debt_score = 15
        elif debt_ratio <= 0.4:
            debt_score = 12
        else:
            debt_score = 8
        score += debt_score
        
        # 成长性评分 (15分)
        growth = data.get('growth', 5)
        if growth >= 15:
            growth_score = 15
        elif growth >= 10:
            growth_score = 12
        elif growth >= 5:
            growth_score = 10
        else:
            growth_score = 5
        score += growth_score
        
        return min(100.0, max(0.0, score))

class MarketAnalyzer:
    """市场表现分析器"""
    
    def analyze_market_score(self, symbol: str, volume: int, turnover: float, change_pct: float) -> float:
        """计算市场表现评分"""
        score = 0.0
        
        # 成交量评分 (30分)
        if volume >= 2000000:
            volume_score = 30
        elif volume >= 1000000:
            volume_score = 25
        elif volume >= 500000:
            volume_score = 20
        else:
            volume_score = 15
        score += volume_score
        
        # 换手率评分 (30分) - 基于成交额估算
        estimated_turnover_rate = min(10.0, turnover / 1000000000 * 100)  # 简化计算
        if 2 <= estimated_turnover_rate <= 8:
            turnover_score = 30
        elif estimated_turnover_rate > 8:
            turnover_score = 25
        elif estimated_turnover_rate > 1:
            turnover_score = 20
        else:
            turnover_score = 15
        score += turnover_score
        
        # 涨跌幅评分 (40分)
        if -2 <= change_pct <= 5:
            change_score = 40
        elif 5 < change_pct <= 8:
            change_score = 35
        elif -5 <= change_pct < -2:
            change_score = 30
        else:
            change_score = 20
        score += change_score
        
        return min(100.0, max(0.0, score))

class IntelligentStockSelector:
    """智能选股引擎"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.technical_analyzer = TechnicalAnalyzer()
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        
        # 因子权重配置
        self.factor_weights = {
            'technical': 0.5,    # 技术面权重50%
            'fundamental': 0.3,  # 基本面权重30%
            'market': 0.2        # 市场表现权重20%
        }
        
        logger.info("✅ 智能选股引擎初始化完成")
    
    def get_stock_price_history(self, symbol: str, days: int = 30) -> List[float]:
        """获取股票价格历史数据"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT price FROM stock_data 
                    WHERE symbol = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (symbol, days))
                
                prices = [row[0] for row in cursor.fetchall()]
                return list(reversed(prices))  # 按时间正序返回
        except Exception as e:
            logger.error(f"获取价格历史失败: {e}")
            return []
    
    def calculate_stock_score(self, stock_data: Dict) -> StockScore:
        """计算单只股票的综合评分"""
        symbol = stock_data['symbol']
        name = stock_data['name']
        
        # 获取价格历史
        price_history = self.get_stock_price_history(symbol)
        if not price_history:
            # 如果没有历史数据，使用当前价格生成模拟历史
            current_price = stock_data['price']
            price_history = [current_price * (1 + random.uniform(-0.02, 0.02)) for _ in range(20)]
            price_history.append(current_price)
        
        # 计算各维度评分
        technical_score = self.technical_analyzer.analyze_technical_score(symbol, price_history)
        fundamental_score = self.fundamental_analyzer.analyze_fundamental_score(symbol)
        market_score = self.market_analyzer.analyze_market_score(
            symbol, stock_data['volume'], stock_data['turnover'], stock_data['change_pct']
        )
        
        # 计算综合评分
        total_score = (
            technical_score * self.factor_weights['technical'] +
            fundamental_score * self.factor_weights['fundamental'] +
            market_score * self.factor_weights['market']
        )
        
        # 生成推荐等级和理由
        if total_score >= 85:
            recommendation = "强烈推荐"
            reason = "技术面强势，基本面优秀，市场表现活跃"
        elif total_score >= 75:
            recommendation = "推荐"
            reason = "综合表现良好，具有投资价值"
        elif total_score >= 65:
            recommendation = "观察"
            reason = "表现一般，建议继续观察"
        else:
            recommendation = "回避"
            reason = "综合表现较差，建议回避"
        
        return StockScore(
            symbol=symbol,
            name=name,
            total_score=round(total_score, 1),
            technical_score=round(technical_score, 1),
            fundamental_score=round(fundamental_score, 1),
            market_score=round(market_score, 1),
            rank=0,  # 将在排序后设置
            recommendation=recommendation,
            reason=reason
        )
    
    def select_stocks(self, min_score: float = 65.0, max_count: int = 10) -> List[StockScore]:
        """执行智能选股"""
        try:
            # 获取最新股票数据
            stocks_data = self.db_manager.get_latest_stock_data(limit=50)
            
            if not stocks_data:
                logger.warning("没有可用的股票数据")
                return []
            
            # 计算每只股票的评分
            stock_scores = []
            for stock_data in stocks_data:
                try:
                    score = self.calculate_stock_score(stock_data)
                    if score.total_score >= min_score:
                        stock_scores.append(score)
                except Exception as e:
                    logger.error(f"计算股票 {stock_data['symbol']} 评分失败: {e}")
                    continue
            
            # 按总分排序
            stock_scores.sort(key=lambda x: x.total_score, reverse=True)
            
            # 设置排名
            for i, score in enumerate(stock_scores[:max_count], 1):
                score.rank = i
            
            logger.info(f"✅ 智能选股完成，共选出 {len(stock_scores[:max_count])} 只股票")
            return stock_scores[:max_count]
            
        except Exception as e:
            logger.error(f"智能选股失败: {e}")
            return []
    
    def get_selection_summary(self) -> Dict:
        """获取选股结果摘要"""
        selected_stocks = self.select_stocks()
        
        return {
            "selected_stocks": [stock.to_dict() for stock in selected_stocks],
            "selection_criteria": {
                "technical_weight": self.factor_weights['technical'],
                "fundamental_weight": self.factor_weights['fundamental'],
                "market_weight": self.factor_weights['market'],
                "min_score": 65.0
            },
            "total_scanned": len(self.db_manager.get_latest_stock_data(limit=50)),
            "selected_count": len(selected_stocks),
            "updated_at": datetime.now().isoformat()
        }

# 全局选股引擎实例
stock_selector = IntelligentStockSelector(data_engine.db_manager)

def main():
    """主函数 - 测试智能选股引擎"""
    try:
        print("🚀 启动VeighNa智能选股引擎测试")
        
        # 启动数据引擎
        data_engine.start()
        
        # 等待数据采集
        import time
        time.sleep(5)
        
        # 执行智能选股
        print("\n🔍 执行智能选股...")
        selected_stocks = stock_selector.select_stocks()
        
        print(f"\n📊 选股结果 (共 {len(selected_stocks)} 只):")
        print("-" * 80)
        print(f"{'排名':<4} {'代码':<8} {'名称':<10} {'总分':<6} {'技术':<6} {'基本面':<6} {'市场':<6} {'推荐':<8}")
        print("-" * 80)
        
        for stock in selected_stocks:
            print(f"{stock.rank:<4} {stock.symbol:<8} {stock.name:<10} "
                  f"{stock.total_score:<6.1f} {stock.technical_score:<6.1f} "
                  f"{stock.fundamental_score:<6.1f} {stock.market_score:<6.1f} {stock.recommendation:<8}")
        
        # 获取选股摘要
        summary = stock_selector.get_selection_summary()
        print(f"\n📈 选股摘要:")
        print(f"  扫描股票数: {summary['total_scanned']}")
        print(f"  选中股票数: {summary['selected_count']}")
        print(f"  因子权重: 技术面{summary['selection_criteria']['technical_weight']*100:.0f}% "
              f"基本面{summary['selection_criteria']['fundamental_weight']*100:.0f}% "
              f"市场表现{summary['selection_criteria']['market_weight']*100:.0f}%")
        
        # 停止数据引擎
        data_engine.stop()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        data_engine.stop()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        data_engine.stop()

if __name__ == "__main__":
    main()
