#!/usr/bin/env python3
"""
量化交易系统简化Web应用
快速启动的Web界面版本
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="量化交易系统 Web API",
    description="量化交易系统Web界面",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """主页"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>量化交易系统</title>
        <style>
            body { 
                font-family: 'Microsoft YaHei', Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: #1a1a1a; 
                color: #ffffff; 
            }
            .header { 
                text-align: center; 
                padding: 30px; 
                background: linear-gradient(135deg, #2d2d2d, #3d3d3d); 
                border-radius: 12px; 
                margin-bottom: 30px; 
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
            }
            .card { 
                background: linear-gradient(135deg, #2d2d2d, #3d3d3d); 
                padding: 25px; 
                border-radius: 12px; 
                margin-bottom: 25px; 
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }
            .api-grid { 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
                gap: 20px; 
                margin-top: 20px;
            }
            .api-card { 
                background: #4d4d4d; 
                padding: 20px; 
                border-radius: 8px; 
                text-align: center;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }
            .api-card:hover { 
                background: #5d5d5d; 
                border-color: #4CAF50;
                transform: translateY(-2px);
            }
            .api-link { 
                color: #4CAF50; 
                text-decoration: none; 
                font-weight: bold;
                font-size: 16px;
            }
            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                background: #4CAF50;
                border-radius: 50%;
                margin-right: 8px;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
            .feature-list {
                list-style: none;
                padding: 0;
            }
            .feature-list li {
                padding: 10px 0;
                border-bottom: 1px solid #444;
            }
            .feature-list li:last-child {
                border-bottom: none;
            }
        </style>
        <script>
            async function loadSystemStatus() {
                try {
                    const response = await fetch('/api/system/status');
                    const data = await response.json();
                    document.getElementById('system-status').innerHTML = 
                        `<span class="status-indicator"></span>系统状态: ${data.status} (健康度: ${data.health_score.toFixed(1)}%)`;
                } catch (error) {
                    document.getElementById('system-status').innerHTML = 
                        '<span style="color: #f44336;">⚠️ 系统状态获取失败</span>';
                }
            }
            
            window.onload = function() {
                loadSystemStatus();
                setInterval(loadSystemStatus, 30000); // 30秒更新一次
            }
        </script>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 量化交易系统 V2.0</h1>
                <p>专业量化交易系统 Web 界面</p>
                <p id="system-status">正在加载系统状态...</p>
                <p>当前时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
            </div>
            
            <div class="card">
                <h2>📊 API 接口</h2>
                <div class="api-grid">
                    <div class="api-card">
                        <h3>🔧 系统状态</h3>
                        <a href="/api/system/status" class="api-link">查看系统状态</a>
                    </div>
                    <div class="api-card">
                        <h3>📈 股票评分</h3>
                        <a href="/api/stocks/scores" class="api-link">获取股票评分</a>
                    </div>
                    <div class="api-card">
                        <h3>🟢 买入信号</h3>
                        <a href="/api/signals/buy" class="api-link">查看买入信号</a>
                    </div>
                    <div class="api-card">
                        <h3>🔴 卖出信号</h3>
                        <a href="/api/signals/sell" class="api-link">查看卖出信号</a>
                    </div>
                    <div class="api-card">
                        <h3>💼 投资组合</h3>
                        <a href="/api/portfolio/current" class="api-link">当前组合</a>
                    </div>
                    <div class="api-card">
                        <h3>📊 市场概览</h3>
                        <a href="/api/market/summary" class="api-link">市场数据</a>
                    </div>
                    <div class="api-card">
                        <h3>📚 API文档</h3>
                        <a href="/docs" class="api-link">Swagger文档</a>
                    </div>
                    <div class="api-card">
                        <h3>📖 ReDoc</h3>
                        <a href="/redoc" class="api-link">ReDoc文档</a>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>🎯 系统功能</h2>
                <ul class="feature-list">
                    <li>📊 实时股票数据采集与分析</li>
                    <li>🧠 多维度智能选股算法 (技术面50% + 基本面30% + 市场表现20%)</li>
                    <li>🚀 VeighNa专业回测引擎集成</li>
                    <li>📈 智能买入/卖出策略信号</li>
                    <li>💼 投资组合管理与优化</li>
                    <li>📊 实时市场监控大屏</li>
                    <li>⚙️ 完整的系统管理功能</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查核心模块
        modules = {}
        
        try:
            from database_models import db_manager
            modules["database"] = db_manager.test_connection()
        except:
            modules["database"] = False
            
        try:
            from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector
            modules["stock_selector"] = True
        except:
            modules["stock_selector"] = False
            
        try:
            from vnpy_integration.backtesting_engine import VnpyBacktestingEngine
            modules["backtesting_engine"] = True
        except:
            modules["backtesting_engine"] = False
            
        try:
            from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
            modules["trading_strategies"] = True
        except:
            modules["trading_strategies"] = False
            
        try:
            from portfolio_management.portfolio_builder.portfolio_builder import PortfolioBuilder
            modules["portfolio_management"] = True
        except:
            modules["portfolio_management"] = False
        
        # 计算健康度
        health_score = sum(modules.values()) / len(modules) * 100
        
        return {
            "status": "running" if health_score > 80 else "warning" if health_score > 50 else "error",
            "timestamp": datetime.now().isoformat(),
            "modules": modules,
            "health_score": health_score,
            "uptime": "运行中",
            "version": "2.0.0"
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "health_score": 0
        }

@app.get("/api/stocks/scores")
async def get_stock_scores():
    """获取股票评分"""
    # 模拟股票评分数据
    mock_scores = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "total_score": 88.5,
            "technical_score": 85.2,
            "fundamental_score": 82.1,
            "market_score": 79.8,
            "signal_type": "BUY",
            "recommendation": "强烈推荐"
        },
        {
            "symbol": "600519",
            "name": "贵州茅台",
            "total_score": 92.3,
            "technical_score": 89.1,
            "fundamental_score": 95.6,
            "market_score": 88.2,
            "signal_type": "BUY",
            "recommendation": "强烈推荐"
        },
        {
            "symbol": "000858",
            "name": "五粮液",
            "total_score": 75.6,
            "technical_score": 72.3,
            "fundamental_score": 78.9,
            "market_score": 74.1,
            "signal_type": "OBSERVE",
            "recommendation": "观察"
        }
    ]
    
    return mock_scores

@app.get("/api/signals/buy")
async def get_buy_signals():
    """获取买入信号"""
    signals = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "signal_time": datetime.now().isoformat(),
            "price": 12.85,
            "score": 88.5,
            "reason": "技术指标多头排列，成交量放大，突破关键阻力位"
        },
        {
            "symbol": "600519",
            "name": "贵州茅台",
            "signal_time": datetime.now().isoformat(),
            "price": 1680.00,
            "score": 92.3,
            "reason": "基本面优秀，技术面突破，资金流入明显"
        }
    ]
    
    return signals

@app.get("/api/signals/sell")
async def get_sell_signals():
    """获取卖出信号"""
    signals = [
        {
            "symbol": "002304",
            "name": "洋河股份",
            "signal_time": datetime.now().isoformat(),
            "price": 98.50,
            "score": 42.1,
            "reason": "技术指标转弱，跌破重要支撑位，资金流出"
        }
    ]
    
    return signals

@app.get("/api/portfolio/current")
async def get_current_portfolio():
    """获取当前投资组合"""
    portfolio = {
        "total_value": 1000000.0,
        "cash": 200000.0,
        "positions": [
            {
                "symbol": "000001",
                "name": "平安银行",
                "quantity": 10000,
                "avg_price": 12.50,
                "current_price": 12.85,
                "market_value": 128500.0,
                "pnl": 3500.0,
                "pnl_pct": 2.8,
                "weight": 12.85
            },
            {
                "symbol": "600519",
                "name": "贵州茅台",
                "quantity": 300,
                "avg_price": 1650.00,
                "current_price": 1680.00,
                "market_value": 504000.0,
                "pnl": 9000.0,
                "pnl_pct": 1.8,
                "weight": 50.4
            }
        ],
        "performance": {
            "total_pnl": 12500.0,
            "total_pnl_pct": 1.25,
            "today_pnl": 2800.0,
            "today_pnl_pct": 0.28
        },
        "updated_at": datetime.now().isoformat()
    }
    
    return portfolio

@app.get("/api/market/summary")
async def get_market_summary():
    """获取市场概览"""
    summary = {
        "indices": {
            "上证指数": {"value": 3245.68, "change": 12.45, "change_pct": 0.38},
            "深证成指": {"value": 10856.32, "change": -25.67, "change_pct": -0.24},
            "创业板指": {"value": 2187.45, "change": 8.92, "change_pct": 0.41}
        },
        "market_stats": {
            "total_stocks": 4800,
            "rising_stocks": 2650,
            "falling_stocks": 1890,
            "unchanged_stocks": 260,
            "limit_up": 45,
            "limit_down": 12
        },
        "signals": {
            "buy_signals": 23,
            "sell_signals": 8,
            "observe_signals": 156
        },
        "updated_at": datetime.now().isoformat()
    }
    
    return summary

def main():
    """主函数"""
    try:
        host = "0.0.0.0"
        port = 8000
        
        print("🌐 启动量化交易系统 Web 应用")
        print(f"📍 访问地址: http://{host}:{port}")
        print(f"📍 本地访问: http://localhost:{port}")
        print(f"📚 API文档: http://localhost:{port}/docs")
        print(f"📖 ReDoc文档: http://localhost:{port}/redoc")
        print("=" * 60)
        
        # 启动Web服务器
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("⚠️ 用户中断Web应用")
    except Exception as e:
        print(f"❌ Web应用启动失败: {e}")
        return 1

if __name__ == "__main__":
    main()
