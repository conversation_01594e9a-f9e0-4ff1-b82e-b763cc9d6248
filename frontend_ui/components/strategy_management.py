"""
策略管理界面组件
按照产品设计原型实现完整的策略管理功能
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from frontend_ui.styles.vnpy_style import VnpyStyleManager
from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy

logger = logging.getLogger(__name__)

class StrategyStatus:
    """策略状态"""
    STOPPED = "STOPPED"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    ERROR = "ERROR"

class StrategyManagement(QWidget):
    """策略管理组件"""
    
    # 信号定义
    strategy_started = pyqtSignal(str)      # 策略启动
    strategy_stopped = pyqtSignal(str)      # 策略停止
    strategy_modified = pyqtSignal(str, dict)  # 策略修改
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.style_manager = VnpyStyleManager()
        
        # 策略实例管理
        self.strategies = {}
        self.strategy_configs = {}
        
        # 界面组件
        self.strategy_list = None
        self.strategy_detail = None
        self.parameter_panel = None
        self.performance_panel = None
        
        self.init_ui()
        self.apply_styles()
        self.load_strategies()
        
        logger.info("⚙️ 策略管理组件初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 左侧策略列表
        strategy_list_widget = self.create_strategy_list()
        layout.addWidget(strategy_list_widget, 1)
        
        # 右侧策略详情
        strategy_detail_widget = self.create_strategy_detail()
        layout.addWidget(strategy_detail_widget, 2)
    
    def create_strategy_list(self) -> QWidget:
        """创建策略列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题和工具栏
        title_layout = QHBoxLayout()
        
        title_label = QLabel("📋 策略列表")
        title_label.setObjectName("strategy_list_title")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 新建策略按钮
        new_strategy_btn = QPushButton("➕ 新建")
        new_strategy_btn.setObjectName("new_strategy_btn")
        new_strategy_btn.clicked.connect(self.create_new_strategy)
        title_layout.addWidget(new_strategy_btn)
        
        layout.addLayout(title_layout)
        
        # 策略列表
        self.strategy_list_widget = QListWidget()
        self.strategy_list_widget.setObjectName("strategy_list")
        self.strategy_list_widget.itemClicked.connect(self.on_strategy_selected)
        layout.addWidget(self.strategy_list_widget)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        start_all_btn = QPushButton("🚀 全部启动")
        start_all_btn.setObjectName("batch_btn")
        start_all_btn.clicked.connect(self.start_all_strategies)
        batch_layout.addWidget(start_all_btn)
        
        stop_all_btn = QPushButton("⏹️ 全部停止")
        stop_all_btn.setObjectName("batch_btn")
        stop_all_btn.clicked.connect(self.stop_all_strategies)
        batch_layout.addWidget(stop_all_btn)
        
        layout.addLayout(batch_layout)
        
        return widget
    
    def create_strategy_detail(self) -> QWidget:
        """创建策略详情"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 策略信息栏
        self.create_strategy_info_bar(layout)
        
        # 标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("strategy_tab")
        
        # 参数配置标签页
        self.parameter_panel = self.create_parameter_panel()
        tab_widget.addTab(self.parameter_panel, "⚙️ 参数配置")
        
        # 性能监控标签页
        self.performance_panel = self.create_performance_panel()
        tab_widget.addTab(self.performance_panel, "📊 性能监控")
        
        # 交易记录标签页
        trade_panel = self.create_trade_panel()
        tab_widget.addTab(trade_panel, "📈 交易记录")
        
        # 风险控制标签页
        risk_panel = self.create_risk_panel()
        tab_widget.addTab(risk_panel, "🛡️ 风险控制")
        
        layout.addWidget(tab_widget)
        
        return widget
    
    def create_strategy_info_bar(self, layout: QVBoxLayout):
        """创建策略信息栏"""
        info_widget = QWidget()
        info_widget.setObjectName("strategy_info_bar")
        info_layout = QHBoxLayout(info_widget)
        
        # 策略名称
        self.strategy_name_label = QLabel("策略名称: 未选择")
        self.strategy_name_label.setObjectName("strategy_name")
        info_layout.addWidget(self.strategy_name_label)
        
        info_layout.addStretch()
        
        # 策略状态
        self.strategy_status_label = QLabel("状态: 停止")
        self.strategy_status_label.setObjectName("strategy_status")
        info_layout.addWidget(self.strategy_status_label)
        
        # 控制按钮
        self.start_btn = QPushButton("🚀 启动")
        self.start_btn.setObjectName("control_btn")
        self.start_btn.clicked.connect(self.start_strategy)
        info_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setObjectName("control_btn")
        self.stop_btn.clicked.connect(self.stop_strategy)
        info_layout.addWidget(self.stop_btn)
        
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.setObjectName("control_btn")
        self.edit_btn.clicked.connect(self.edit_strategy)
        info_layout.addWidget(self.edit_btn)
        
        layout.addWidget(info_widget)
    
    def create_parameter_panel(self) -> QWidget:
        """创建参数配置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 参数表格
        self.parameter_table = QTableWidget()
        self.parameter_table.setObjectName("parameter_table")
        self.parameter_table.setColumnCount(4)
        self.parameter_table.setHorizontalHeaderLabels([
            "参数名称", "当前值", "默认值", "说明"
        ])
        
        # 设置表格样式
        header = self.parameter_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.parameter_table)
        
        # 参数操作按钮
        param_btn_layout = QHBoxLayout()
        
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.setObjectName("param_btn")
        reset_btn.clicked.connect(self.reset_parameters)
        param_btn_layout.addWidget(reset_btn)
        
        save_btn = QPushButton("💾 保存配置")
        save_btn.setObjectName("param_btn")
        save_btn.clicked.connect(self.save_parameters)
        param_btn_layout.addWidget(save_btn)
        
        param_btn_layout.addStretch()
        
        layout.addLayout(param_btn_layout)
        
        return widget
    
    def create_performance_panel(self) -> QWidget:
        """创建性能监控面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性能指标卡片
        metrics_layout = QGridLayout()
        
        # 收益率卡片
        return_card = self.create_metric_card("总收益率", "0.00%", "green")
        metrics_layout.addWidget(return_card, 0, 0)
        
        # 夏普比率卡片
        sharpe_card = self.create_metric_card("夏普比率", "0.00", "blue")
        metrics_layout.addWidget(sharpe_card, 0, 1)
        
        # 最大回撤卡片
        drawdown_card = self.create_metric_card("最大回撤", "0.00%", "red")
        metrics_layout.addWidget(drawdown_card, 0, 2)
        
        # 胜率卡片
        winrate_card = self.create_metric_card("胜率", "0.00%", "orange")
        metrics_layout.addWidget(winrate_card, 1, 0)
        
        # 交易次数卡片
        trades_card = self.create_metric_card("交易次数", "0", "purple")
        metrics_layout.addWidget(trades_card, 1, 1)
        
        # 平均持仓天数卡片
        holding_card = self.create_metric_card("平均持仓", "0天", "cyan")
        metrics_layout.addWidget(holding_card, 1, 2)
        
        layout.addLayout(metrics_layout)
        
        # 性能图表区域
        chart_widget = QWidget()
        chart_widget.setObjectName("performance_chart")
        chart_widget.setMinimumHeight(200)
        
        chart_layout = QVBoxLayout(chart_widget)
        chart_label = QLabel("📊 收益曲线图 (开发中)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_widget)
        
        return widget
    
    def create_metric_card(self, title: str, value: str, color: str) -> QWidget:
        """创建指标卡片"""
        card = QWidget()
        card.setObjectName("metric_card")
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setObjectName("metric_title")
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setObjectName("metric_value")
        value_label.setStyleSheet(f"color: {color}; font-size: 18px; font-weight: bold;")
        layout.addWidget(value_label)
        
        return card
    
    def create_trade_panel(self) -> QWidget:
        """创建交易记录面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 交易记录表格
        self.trade_table = QTableWidget()
        self.trade_table.setObjectName("trade_table")
        self.trade_table.setColumnCount(8)
        self.trade_table.setHorizontalHeaderLabels([
            "时间", "股票", "方向", "数量", "价格", "金额", "盈亏", "状态"
        ])
        
        # 设置表格样式
        header = self.trade_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.trade_table)
        
        # 交易统计
        stats_layout = QHBoxLayout()
        
        self.total_trades_label = QLabel("总交易: 0笔")
        stats_layout.addWidget(self.total_trades_label)
        
        self.profit_trades_label = QLabel("盈利: 0笔")
        stats_layout.addWidget(self.profit_trades_label)
        
        self.loss_trades_label = QLabel("亏损: 0笔")
        stats_layout.addWidget(self.loss_trades_label)
        
        stats_layout.addStretch()
        
        export_btn = QPushButton("📤 导出记录")
        export_btn.setObjectName("export_btn")
        export_btn.clicked.connect(self.export_trades)
        stats_layout.addWidget(export_btn)
        
        layout.addLayout(stats_layout)
        
        return widget
    
    def create_risk_panel(self) -> QWidget:
        """创建风险控制面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 风险参数设置
        risk_params_group = QGroupBox("🛡️ 风险参数")
        risk_params_layout = QFormLayout(risk_params_group)
        
        self.max_position_spin = QDoubleSpinBox()
        self.max_position_spin.setRange(0.01, 1.0)
        self.max_position_spin.setSingleStep(0.01)
        self.max_position_spin.setValue(0.1)
        self.max_position_spin.setSuffix("%")
        risk_params_layout.addRow("单股最大仓位:", self.max_position_spin)
        
        self.max_drawdown_spin = QDoubleSpinBox()
        self.max_drawdown_spin.setRange(0.01, 0.5)
        self.max_drawdown_spin.setSingleStep(0.01)
        self.max_drawdown_spin.setValue(0.15)
        self.max_drawdown_spin.setSuffix("%")
        risk_params_layout.addRow("最大回撤限制:", self.max_drawdown_spin)
        
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(0.01, 0.2)
        self.stop_loss_spin.setSingleStep(0.01)
        self.stop_loss_spin.setValue(0.05)
        self.stop_loss_spin.setSuffix("%")
        risk_params_layout.addRow("止损比例:", self.stop_loss_spin)
        
        layout.addWidget(risk_params_group)
        
        # 风险监控
        risk_monitor_group = QGroupBox("📊 风险监控")
        risk_monitor_layout = QVBoxLayout(risk_monitor_group)
        
        # 风险指标表格
        self.risk_table = QTableWidget()
        self.risk_table.setObjectName("risk_table")
        self.risk_table.setColumnCount(3)
        self.risk_table.setHorizontalHeaderLabels([
            "风险指标", "当前值", "限制值"
        ])
        
        risk_monitor_layout.addWidget(self.risk_table)
        
        layout.addWidget(risk_monitor_group)
        
        layout.addStretch()
        
        return widget
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet(self.style_manager.get_strategy_management_style())
    
    def load_strategies(self):
        """加载策略列表"""
        try:
            # 模拟策略数据
            strategies = [
                {
                    'id': 'buy_strategy_001',
                    'name': '多因子买入策略',
                    'type': 'BUY',
                    'status': StrategyStatus.STOPPED,
                    'description': '基于技术面、基本面、市场表现的综合买入策略'
                },
                {
                    'id': 'sell_strategy_001',
                    'name': '多因子卖出策略',
                    'type': 'SELL',
                    'status': StrategyStatus.STOPPED,
                    'description': '基于止盈止损、技术转弱的综合卖出策略'
                }
            ]
            
            self.strategy_list_widget.clear()
            
            for strategy in strategies:
                item = QListWidgetItem()
                widget = self.create_strategy_item(strategy)
                item.setSizeHint(widget.sizeHint())
                item.setData(Qt.UserRole, strategy)
                
                self.strategy_list_widget.addItem(item)
                self.strategy_list_widget.setItemWidget(item, widget)
            
            logger.info(f"📋 加载策略列表: {len(strategies)}个策略")
            
        except Exception as e:
            logger.error(f"❌ 加载策略列表失败: {e}")
    
    def create_strategy_item(self, strategy: Dict[str, Any]) -> QWidget:
        """创建策略项目组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 策略名称和状态
        header_layout = QHBoxLayout()
        
        name_label = QLabel(strategy['name'])
        name_label.setObjectName("strategy_item_name")
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        # 状态指示器
        status_label = QLabel(self.get_status_text(strategy['status']))
        status_label.setObjectName(f"status_{strategy['status'].lower()}")
        header_layout.addWidget(status_label)
        
        layout.addLayout(header_layout)
        
        # 策略描述
        desc_label = QLabel(strategy['description'])
        desc_label.setObjectName("strategy_item_desc")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        return widget
    
    def get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            StrategyStatus.STOPPED: "⏹️ 已停止",
            StrategyStatus.RUNNING: "🚀 运行中",
            StrategyStatus.PAUSED: "⏸️ 已暂停",
            StrategyStatus.ERROR: "❌ 错误"
        }
        return status_map.get(status, "❓ 未知")
    
    def on_strategy_selected(self, item):
        """策略选择事件"""
        try:
            strategy = item.data(Qt.UserRole)
            if strategy:
                self.load_strategy_detail(strategy)
                
        except Exception as e:
            logger.error(f"❌ 处理策略选择失败: {e}")
    
    def load_strategy_detail(self, strategy: Dict[str, Any]):
        """加载策略详情"""
        try:
            # 更新策略信息栏
            self.strategy_name_label.setText(f"策略名称: {strategy['name']}")
            self.strategy_status_label.setText(f"状态: {self.get_status_text(strategy['status'])}")
            
            # 更新参数表格
            self.load_strategy_parameters(strategy)
            
            # 更新性能数据
            self.load_strategy_performance(strategy)
            
            # 更新交易记录
            self.load_strategy_trades(strategy)
            
            logger.info(f"📋 加载策略详情: {strategy['name']}")
            
        except Exception as e:
            logger.error(f"❌ 加载策略详情失败: {e}")
    
    def load_strategy_parameters(self, strategy: Dict[str, Any]):
        """加载策略参数"""
        try:
            # 模拟参数数据
            if strategy['type'] == 'BUY':
                parameters = [
                    ("最小综合评分", "70", "65", "买入信号的最小综合评分要求"),
                    ("最小技术评分", "60", "55", "技术面评分的最小要求"),
                    ("目标收益率", "15%", "15%", "预期目标收益率"),
                    ("止损比例", "5%", "5%", "最大止损比例"),
                    ("最大持仓比例", "10%", "10%", "单股最大持仓比例")
                ]
            else:
                parameters = [
                    ("最大综合评分", "75", "80", "卖出信号的最大综合评分阈值"),
                    ("止盈比例", "15%", "15%", "目标止盈比例"),
                    ("止损比例", "5%", "5%", "最大止损比例"),
                    ("最大持仓天数", "30", "30", "最大持仓天数限制")
                ]
            
            self.parameter_table.setRowCount(len(parameters))
            
            for i, (name, current, default, desc) in enumerate(parameters):
                self.parameter_table.setItem(i, 0, QTableWidgetItem(name))
                
                # 当前值可编辑
                current_item = QTableWidgetItem(current)
                current_item.setFlags(current_item.flags() | Qt.ItemIsEditable)
                self.parameter_table.setItem(i, 1, current_item)
                
                self.parameter_table.setItem(i, 2, QTableWidgetItem(default))
                self.parameter_table.setItem(i, 3, QTableWidgetItem(desc))
            
        except Exception as e:
            logger.error(f"❌ 加载策略参数失败: {e}")
    
    def load_strategy_performance(self, strategy: Dict[str, Any]):
        """加载策略性能"""
        try:
            # 模拟性能数据
            # 实际应该从数据库或策略实例获取真实数据
            pass
            
        except Exception as e:
            logger.error(f"❌ 加载策略性能失败: {e}")
    
    def load_strategy_trades(self, strategy: Dict[str, Any]):
        """加载策略交易记录"""
        try:
            # 模拟交易记录
            # 实际应该从数据库获取真实交易记录
            pass
            
        except Exception as e:
            logger.error(f"❌ 加载策略交易记录失败: {e}")
    
    def create_new_strategy(self):
        """创建新策略"""
        QMessageBox.information(self, "新建策略", "新建策略功能开发中...")
    
    def start_strategy(self):
        """启动策略"""
        QMessageBox.information(self, "启动策略", "启动策略功能开发中...")
    
    def stop_strategy(self):
        """停止策略"""
        QMessageBox.information(self, "停止策略", "停止策略功能开发中...")
    
    def edit_strategy(self):
        """编辑策略"""
        QMessageBox.information(self, "编辑策略", "编辑策略功能开发中...")
    
    def start_all_strategies(self):
        """启动所有策略"""
        QMessageBox.information(self, "批量启动", "批量启动功能开发中...")
    
    def stop_all_strategies(self):
        """停止所有策略"""
        QMessageBox.information(self, "批量停止", "批量停止功能开发中...")
    
    def reset_parameters(self):
        """重置参数"""
        reply = QMessageBox.question(
            self, "重置参数", 
            "确定要重置所有参数为默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重置参数逻辑
            QMessageBox.information(self, "重置完成", "参数已重置为默认值")
    
    def save_parameters(self):
        """保存参数"""
        QMessageBox.information(self, "保存成功", "参数配置已保存")
    
    def export_trades(self):
        """导出交易记录"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出交易记录", 
            f"strategy_trades_{datetime.now().strftime('%Y%m%d')}.csv",
            "CSV Files (*.csv)"
        )
        
        if filename:
            QMessageBox.information(self, "导出成功", f"交易记录已导出到: {filename}")


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    widget = StrategyManagement()
    widget.show()
    
    sys.exit(app.exec_())
