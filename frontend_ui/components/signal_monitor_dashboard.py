"""
实时信号监控大屏 - 按照产品设计原型实现
全市场信号监控大屏界面，实时展示买入/卖出信号
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg
import pandas as pd
import numpy as np

from frontend_ui.styles.vnpy_style import VnpyStyleManager
from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy

logger = logging.getLogger(__name__)

class SignalType:
    """信号类型"""
    BUY = "BUY"
    SELL = "SELL"
    OBSERVE = "OBSERVE"

class SignalMonitorDashboard(QWidget):
    """实时信号监控大屏"""
    
    # 信号定义
    signal_alert = pyqtSignal(str, dict)  # 信号预警
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.style_manager = VnpyStyleManager()
        
        # 数据管理
        self.signal_data = []
        self.market_summary = {}
        self.alert_rules = {}
        
        # 策略实例
        self.buy_strategy = BuyStrategy()
        self.sell_strategy = SellStrategy()
        
        # 界面组件
        self.summary_cards = {}
        self.signal_table = None
        self.alert_panel = None
        self.chart_widgets = {}
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_dashboard)
        self.update_timer.start(3000)  # 3秒更新一次
        
        self.init_ui()
        self.apply_styles()
        
        logger.info("📊 实时信号监控大屏初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题栏
        self.create_title_bar(layout)
        
        # 市场概览卡片
        self.create_summary_cards(layout)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧信号列表
        left_panel = self.create_signal_list_panel()
        content_layout.addWidget(left_panel, 2)
        
        # 右侧图表和预警
        right_panel = self.create_chart_and_alert_panel()
        content_layout.addWidget(right_panel, 3)
        
        layout.addLayout(content_layout, 1)
        
        # 底部状态栏
        self.create_status_bar(layout)
    
    def create_title_bar(self, layout: QVBoxLayout):
        """创建标题栏"""
        title_widget = QWidget()
        title_widget.setObjectName("title_bar")
        title_layout = QHBoxLayout(title_widget)
        
        # 主标题
        title_label = QLabel("📊 实时信号监控大屏")
        title_label.setObjectName("main_title")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 时间显示
        self.time_label = QLabel()
        self.time_label.setObjectName("time_display")
        self.update_time_display()
        title_layout.addWidget(self.time_label)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setObjectName("refresh_btn")
        refresh_btn.clicked.connect(self.manual_refresh)
        title_layout.addWidget(refresh_btn)
        
        # 设置按钮
        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setObjectName("settings_btn")
        settings_btn.clicked.connect(self.open_settings)
        title_layout.addWidget(settings_btn)
        
        layout.addWidget(title_widget)
    
    def create_summary_cards(self, layout: QVBoxLayout):
        """创建市场概览卡片"""
        cards_widget = QWidget()
        cards_layout = QHBoxLayout(cards_widget)
        
        # 定义卡片数据
        card_configs = [
            ("🔴 买入信号", "buy_signals", "0", "#ff4d4f"),
            ("🟢 卖出信号", "sell_signals", "0", "#52c41a"),
            ("⚪ 观察信号", "observe_signals", "0", "#faad14"),
            ("📈 涨停股票", "limit_up", "0", "#ff7875"),
            ("📉 跌停股票", "limit_down", "0", "#95de64"),
            ("💰 总市值", "total_market_cap", "0万亿", "#1890ff")
        ]
        
        for title, key, value, color in card_configs:
            card = self.create_summary_card(title, value, color)
            self.summary_cards[key] = card
            cards_layout.addWidget(card)
        
        layout.addWidget(cards_widget)
    
    def create_summary_card(self, title: str, value: str, color: str) -> QWidget:
        """创建概览卡片"""
        card = QWidget()
        card.setObjectName("summary_card")
        layout = QVBoxLayout(card)
        
        # 标题
        title_label = QLabel(title)
        title_label.setObjectName("card_title")
        layout.addWidget(title_label)
        
        # 数值
        value_label = QLabel(value)
        value_label.setObjectName("card_value")
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        layout.addWidget(value_label)
        
        # 存储value_label引用以便更新
        card.value_label = value_label
        
        return card
    
    def create_signal_list_panel(self) -> QWidget:
        """创建信号列表面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 面板标题
        title_label = QLabel("📋 实时信号列表")
        title_label.setObjectName("panel_title")
        layout.addWidget(title_label)
        
        # 过滤器
        filter_layout = QHBoxLayout()
        
        # 信号类型过滤
        type_combo = QComboBox()
        type_combo.addItems(["全部信号", "买入信号", "卖出信号", "观察信号"])
        type_combo.currentTextChanged.connect(self.filter_signals)
        filter_layout.addWidget(QLabel("类型:"))
        filter_layout.addWidget(type_combo)
        
        # 强度过滤
        strength_combo = QComboBox()
        strength_combo.addItems(["全部强度", "强烈信号", "一般信号", "弱信号"])
        strength_combo.currentTextChanged.connect(self.filter_signals)
        filter_layout.addWidget(QLabel("强度:"))
        filter_layout.addWidget(strength_combo)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 信号表格
        self.signal_table = QTableWidget()
        self.signal_table.setObjectName("signal_table")
        self.signal_table.setColumnCount(8)
        self.signal_table.setHorizontalHeaderLabels([
            "时间", "股票代码", "股票名称", "信号类型", "信号强度", 
            "当前价格", "评分", "操作"
        ])
        
        # 设置表格样式
        header = self.signal_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.signal_table.setAlternatingRowColors(True)
        self.signal_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        layout.addWidget(self.signal_table)
        
        return panel
    
    def create_chart_and_alert_panel(self) -> QWidget:
        """创建图表和预警面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("chart_tabs")
        
        # 信号分布图
        signal_chart = self.create_signal_distribution_chart()
        tab_widget.addTab(signal_chart, "📊 信号分布")
        
        # 市场热力图
        heatmap_chart = self.create_market_heatmap()
        tab_widget.addTab(heatmap_chart, "🔥 市场热力图")
        
        # 实时预警面板
        alert_panel = self.create_alert_panel()
        tab_widget.addTab(alert_panel, "🔔 实时预警")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def create_signal_distribution_chart(self) -> QWidget:
        """创建信号分布图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 图表控制
        control_layout = QHBoxLayout()
        
        time_combo = QComboBox()
        time_combo.addItems(["最近1小时", "最近4小时", "今日", "最近3天"])
        control_layout.addWidget(QLabel("时间范围:"))
        control_layout.addWidget(time_combo)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 图表区域
        chart_widget = pg.PlotWidget()
        chart_widget.setObjectName("signal_chart")
        chart_widget.setLabel('left', '信号数量')
        chart_widget.setLabel('bottom', '时间')
        chart_widget.showGrid(x=True, y=True, alpha=0.3)
        chart_widget.setBackground('#2b2b2b')
        
        self.chart_widgets['signal_distribution'] = chart_widget
        layout.addWidget(chart_widget)
        
        return widget
    
    def create_market_heatmap(self) -> QWidget:
        """创建市场热力图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 热力图说明
        info_label = QLabel("🔥 市场热力图 - 颜色深度表示信号强度")
        info_label.setObjectName("info_label")
        layout.addWidget(info_label)
        
        # 热力图区域（简化实现）
        heatmap_area = QWidget()
        heatmap_area.setObjectName("heatmap_area")
        heatmap_area.setMinimumHeight(300)
        
        heatmap_layout = QVBoxLayout(heatmap_area)
        placeholder_label = QLabel("📊 市场热力图 (开发中)")
        placeholder_label.setAlignment(Qt.AlignCenter)
        heatmap_layout.addWidget(placeholder_label)
        
        layout.addWidget(heatmap_area)
        
        return widget
    
    def create_alert_panel(self) -> QWidget:
        """创建预警面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 预警规则设置
        rules_group = QGroupBox("🔔 预警规则")
        rules_layout = QFormLayout(rules_group)
        
        # 强烈买入信号预警
        strong_buy_check = QCheckBox("强烈买入信号预警")
        strong_buy_check.setChecked(True)
        rules_layout.addRow(strong_buy_check)
        
        # 大量卖出信号预警
        mass_sell_check = QCheckBox("大量卖出信号预警")
        mass_sell_check.setChecked(True)
        rules_layout.addRow(mass_sell_check)
        
        # 异常波动预警
        abnormal_check = QCheckBox("异常波动预警")
        abnormal_check.setChecked(True)
        rules_layout.addRow(abnormal_check)
        
        layout.addWidget(rules_group)
        
        # 预警历史
        history_group = QGroupBox("📋 预警历史")
        history_layout = QVBoxLayout(history_group)
        
        self.alert_list = QListWidget()
        self.alert_list.setObjectName("alert_list")
        history_layout.addWidget(self.alert_list)
        
        layout.addWidget(history_group)
        
        return widget
    
    def create_status_bar(self, layout: QVBoxLayout):
        """创建状态栏"""
        status_widget = QWidget()
        status_widget.setObjectName("status_bar")
        status_layout = QHBoxLayout(status_widget)
        
        # 连接状态
        self.connection_label = QLabel("🟢 数据连接正常")
        self.connection_label.setObjectName("connection_status")
        status_layout.addWidget(self.connection_label)
        
        status_layout.addStretch()
        
        # 更新状态
        self.update_status_label = QLabel("最后更新: --:--:--")
        self.update_status_label.setObjectName("update_status")
        status_layout.addWidget(self.update_status_label)
        
        # 数据统计
        self.data_stats_label = QLabel("监控股票: 0 | 活跃信号: 0")
        self.data_stats_label.setObjectName("data_stats")
        status_layout.addWidget(self.data_stats_label)
        
        layout.addWidget(status_widget)
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QWidget#title_bar {
            background-color: #1f1f1f;
            border-bottom: 2px solid #3f3f3f;
            padding: 10px;
        }
        
        QLabel#main_title {
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
        }
        
        QLabel#time_display {
            color: #52c41a;
            font-size: 16px;
            font-family: monospace;
        }
        
        QWidget#summary_card {
            background-color: #262626;
            border: 1px solid #3f3f3f;
            border-radius: 8px;
            padding: 15px;
            margin: 5px;
        }
        
        QLabel#card_title {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        QLabel#panel_title {
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        QTableWidget#signal_table {
            background-color: #1f1f1f;
            alternate-background-color: #262626;
            color: #ffffff;
            gridline-color: #3f3f3f;
            selection-background-color: #1890ff;
        }
        
        QTableWidget#signal_table::item {
            padding: 8px;
            border-bottom: 1px solid #3f3f3f;
        }
        
        QWidget#status_bar {
            background-color: #1f1f1f;
            border-top: 1px solid #3f3f3f;
            padding: 8px;
        }
        
        QLabel#connection_status, QLabel#update_status, QLabel#data_stats {
            color: #ffffff;
            font-size: 12px;
        }
        
        QPushButton#refresh_btn, QPushButton#settings_btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton#refresh_btn:hover, QPushButton#settings_btn:hover {
            background-color: #40a9ff;
        }
        """
        
        self.setStyleSheet(style)
    
    def update_dashboard(self):
        """更新大屏数据"""
        try:
            # 更新时间显示
            self.update_time_display()
            
            # 更新信号数据
            self.update_signal_data()
            
            # 更新概览卡片
            self.update_summary_cards()
            
            # 更新信号表格
            self.update_signal_table()
            
            # 更新图表
            self.update_charts()
            
            # 更新状态
            self.update_status_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logger.error(f"❌ 更新大屏数据失败: {e}")
    
    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.setText(current_time)
    
    def update_signal_data(self):
        """更新信号数据"""
        try:
            # 模拟获取实时信号数据
            # 实际应该从信号生成系统获取
            
            import random
            
            # 生成模拟信号
            symbols = ["000001", "000002", "600519", "000858", "600036", "002415"]
            new_signals = []
            
            for symbol in symbols:
                if random.random() < 0.3:  # 30%概率生成信号
                    signal_type = random.choice([SignalType.BUY, SignalType.SELL, SignalType.OBSERVE])
                    strength = random.uniform(0.5, 1.0)
                    price = random.uniform(10, 100)
                    score = random.randint(60, 95)
                    
                    signal = {
                        'time': datetime.now(),
                        'symbol': symbol,
                        'name': f"股票{symbol}",
                        'type': signal_type,
                        'strength': strength,
                        'price': price,
                        'score': score
                    }
                    
                    new_signals.append(signal)
            
            # 添加新信号到列表（保持最近100条）
            self.signal_data.extend(new_signals)
            self.signal_data = self.signal_data[-100:]
            
            # 检查预警条件
            self.check_alerts(new_signals)
            
        except Exception as e:
            logger.error(f"❌ 更新信号数据失败: {e}")
    
    def update_summary_cards(self):
        """更新概览卡片"""
        try:
            # 统计信号数量
            buy_count = len([s for s in self.signal_data if s['type'] == SignalType.BUY])
            sell_count = len([s for s in self.signal_data if s['type'] == SignalType.SELL])
            observe_count = len([s for s in self.signal_data if s['type'] == SignalType.OBSERVE])
            
            # 更新卡片数值
            self.summary_cards['buy_signals'].value_label.setText(str(buy_count))
            self.summary_cards['sell_signals'].value_label.setText(str(sell_count))
            self.summary_cards['observe_signals'].value_label.setText(str(observe_count))
            
            # 模拟其他数据
            import random
            self.summary_cards['limit_up'].value_label.setText(str(random.randint(10, 50)))
            self.summary_cards['limit_down'].value_label.setText(str(random.randint(5, 30)))
            self.summary_cards['total_market_cap'].value_label.setText(f"{random.randint(80, 120)}万亿")
            
        except Exception as e:
            logger.error(f"❌ 更新概览卡片失败: {e}")
    
    def update_signal_table(self):
        """更新信号表格"""
        try:
            # 获取最近的信号
            recent_signals = sorted(self.signal_data, key=lambda x: x['time'], reverse=True)[:50]
            
            self.signal_table.setRowCount(len(recent_signals))
            
            for i, signal in enumerate(recent_signals):
                # 时间
                time_item = QTableWidgetItem(signal['time'].strftime('%H:%M:%S'))
                self.signal_table.setItem(i, 0, time_item)
                
                # 股票代码
                symbol_item = QTableWidgetItem(signal['symbol'])
                self.signal_table.setItem(i, 1, symbol_item)
                
                # 股票名称
                name_item = QTableWidgetItem(signal['name'])
                self.signal_table.setItem(i, 2, name_item)
                
                # 信号类型
                type_item = QTableWidgetItem(signal['type'])
                if signal['type'] == SignalType.BUY:
                    type_item.setForeground(QColor('#ff4d4f'))
                elif signal['type'] == SignalType.SELL:
                    type_item.setForeground(QColor('#52c41a'))
                else:
                    type_item.setForeground(QColor('#faad14'))
                self.signal_table.setItem(i, 3, type_item)
                
                # 信号强度
                strength_text = "强烈" if signal['strength'] > 0.8 else "一般" if signal['strength'] > 0.6 else "弱"
                strength_item = QTableWidgetItem(strength_text)
                self.signal_table.setItem(i, 4, strength_item)
                
                # 当前价格
                price_item = QTableWidgetItem(f"¥{signal['price']:.2f}")
                self.signal_table.setItem(i, 5, price_item)
                
                # 评分
                score_item = QTableWidgetItem(f"{signal['score']}分")
                self.signal_table.setItem(i, 6, score_item)
                
                # 操作按钮
                action_btn = QPushButton("详情")
                action_btn.setObjectName("action_btn")
                action_btn.clicked.connect(lambda checked, s=signal: self.show_signal_detail(s))
                self.signal_table.setCellWidget(i, 7, action_btn)
            
        except Exception as e:
            logger.error(f"❌ 更新信号表格失败: {e}")
    
    def update_charts(self):
        """更新图表"""
        try:
            # 更新信号分布图
            if 'signal_distribution' in self.chart_widgets:
                chart = self.chart_widgets['signal_distribution']
                chart.clear()
                
                # 按时间统计信号数量
                if self.signal_data:
                    # 简化处理：显示最近1小时的信号分布
                    now = datetime.now()
                    hour_ago = now - timedelta(hours=1)
                    
                    recent_signals = [s for s in self.signal_data if s['time'] >= hour_ago]
                    
                    if recent_signals:
                        # 按10分钟间隔统计
                        time_buckets = {}
                        for signal in recent_signals:
                            bucket = signal['time'].replace(minute=signal['time'].minute//10*10, second=0, microsecond=0)
                            time_buckets[bucket] = time_buckets.get(bucket, 0) + 1
                        
                        if time_buckets:
                            times = sorted(time_buckets.keys())
                            counts = [time_buckets[t] for t in times]
                            x_data = [(t - times[0]).total_seconds()/60 for t in times]  # 转换为分钟
                            
                            chart.plot(x_data, counts, pen='cyan', symbol='o', symbolBrush='cyan')
            
        except Exception as e:
            logger.error(f"❌ 更新图表失败: {e}")
    
    def check_alerts(self, new_signals: List[Dict[str, Any]]):
        """检查预警条件"""
        try:
            for signal in new_signals:
                # 强烈买入信号预警
                if signal['type'] == SignalType.BUY and signal['strength'] > 0.8:
                    alert_msg = f"🔴 强烈买入信号: {signal['symbol']} {signal['name']}"
                    self.add_alert(alert_msg, "HIGH")
                
                # 大量卖出信号预警
                elif signal['type'] == SignalType.SELL and signal['strength'] > 0.7:
                    alert_msg = f"🟢 强烈卖出信号: {signal['symbol']} {signal['name']}"
                    self.add_alert(alert_msg, "MEDIUM")
                
                # 高评分信号预警
                if signal['score'] >= 90:
                    alert_msg = f"⭐ 高评分信号: {signal['symbol']} 评分{signal['score']}分"
                    self.add_alert(alert_msg, "HIGH")
            
        except Exception as e:
            logger.error(f"❌ 检查预警条件失败: {e}")
    
    def add_alert(self, message: str, level: str):
        """添加预警"""
        try:
            alert_time = datetime.now().strftime('%H:%M:%S')
            alert_text = f"[{alert_time}] {message}"
            
            item = QListWidgetItem(alert_text)
            
            # 根据级别设置颜色
            if level == "HIGH":
                item.setForeground(QColor('#ff4d4f'))
            elif level == "MEDIUM":
                item.setForeground(QColor('#faad14'))
            else:
                item.setForeground(QColor('#52c41a'))
            
            self.alert_list.insertItem(0, item)
            
            # 保持最近50条预警
            if self.alert_list.count() > 50:
                self.alert_list.takeItem(50)
            
            # 发射预警信号
            self.signal_alert.emit(message, {'level': level, 'time': alert_time})
            
        except Exception as e:
            logger.error(f"❌ 添加预警失败: {e}")
    
    def filter_signals(self):
        """过滤信号"""
        # 简化处理：重新更新表格
        self.update_signal_table()
    
    def show_signal_detail(self, signal: Dict[str, Any]):
        """显示信号详情"""
        try:
            detail_dialog = QDialog(self)
            detail_dialog.setWindowTitle(f"信号详情 - {signal['symbol']}")
            detail_dialog.setModal(True)
            detail_dialog.resize(400, 300)
            
            layout = QVBoxLayout(detail_dialog)
            
            # 信号信息
            info_text = f"""
股票代码: {signal['symbol']}
股票名称: {signal['name']}
信号类型: {signal['type']}
信号强度: {signal['strength']:.2f}
当前价格: ¥{signal['price']:.2f}
综合评分: {signal['score']}分
生成时间: {signal['time'].strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            info_label = QLabel(info_text)
            info_label.setWordWrap(True)
            layout.addWidget(info_label)
            
            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(detail_dialog.close)
            layout.addWidget(close_btn)
            
            detail_dialog.exec_()
            
        except Exception as e:
            logger.error(f"❌ 显示信号详情失败: {e}")
    
    def manual_refresh(self):
        """手动刷新"""
        self.update_dashboard()
    
    def open_settings(self):
        """打开设置"""
        QMessageBox.information(self, "设置", "监控大屏设置功能开发中...")


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    dashboard = SignalMonitorDashboard()
    dashboard.showMaximized()  # 全屏显示
    
    sys.exit(app.exec_())
