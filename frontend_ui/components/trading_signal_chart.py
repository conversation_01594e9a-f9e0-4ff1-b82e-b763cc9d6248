"""
实时交易信号K线图组件 - 产品设计核心功能
按照原型设计图实现完整的K线图表+交易信号展示
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg
import pandas as pd
import numpy as np

from frontend_ui.styles.vnpy_style import VnpyStyleManager
from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy

logger = logging.getLogger(__name__)

class SignalType(object):
    """信号类型"""
    BUY = "BUY"
    SELL = "SELL"
    OBSERVE = "OBSERVE"

class TradingSignalChart(QWidget):
    """实时交易信号K线图组件"""
    
    # 信号定义
    signal_selected = pyqtSignal(str, dict)  # 信号选择
    symbol_changed = pyqtSignal(str)         # 股票切换
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.style_manager = VnpyStyleManager()
        
        # 数据管理
        self.current_symbol = ""
        self.market_data = pd.DataFrame()
        self.signal_data = []
        self.current_timeframe = "1d"
        
        # 策略实例
        self.buy_strategy = BuyStrategy()
        self.sell_strategy = SellStrategy()
        
        # 界面组件
        self.signal_list = None
        self.chart_widget = None
        self.indicator_widget = None
        self.signal_detail_widget = None
        
        # 图表数据
        self.candlestick_item = None
        self.volume_item = None
        self.ma_items = {}
        self.signal_items = []
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_signals)
        self.update_timer.start(5000)  # 5秒更新一次
        
        self.init_ui()
        self.apply_styles()
        
        logger.info("📈 实时交易信号K线图组件初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 左侧信号监控列表
        self.create_signal_list()
        layout.addWidget(self.signal_list, 1)
        
        # 右侧K线图表区域
        chart_area = self.create_chart_area()
        layout.addWidget(chart_area, 4)
    
    def create_signal_list(self) -> QWidget:
        """创建信号监控列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("📊 实时信号监控")
        title_label.setObjectName("signal_list_title")
        layout.addWidget(title_label)
        
        # 信号分类标签
        self.create_signal_categories(layout)
        
        # 信号列表
        self.signal_list_widget = QListWidget()
        self.signal_list_widget.setObjectName("signal_list")
        self.signal_list_widget.itemClicked.connect(self.on_signal_selected)
        layout.addWidget(self.signal_list_widget)
        
        # 控制按钮
        self.create_control_buttons(layout)
        
        # 更新状态
        status_layout = QHBoxLayout()
        self.update_status_label = QLabel("最后更新: --:--:--")
        self.update_status_label.setObjectName("update_status")
        status_layout.addWidget(self.update_status_label)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setObjectName("refresh_btn")
        refresh_btn.clicked.connect(self.manual_refresh)
        status_layout.addWidget(refresh_btn)
        
        layout.addLayout(status_layout)
        
        self.signal_list = widget
        return widget
    
    def create_signal_categories(self, layout: QVBoxLayout):
        """创建信号分类"""
        categories = [
            ("🔴 买入信号", SignalType.BUY, "buy_signals"),
            ("🟢 卖出信号", SignalType.SELL, "sell_signals"),
            ("⚪ 观察信号", SignalType.OBSERVE, "observe_signals")
        ]
        
        self.category_labels = {}
        
        for text, signal_type, key in categories:
            label = QLabel(f"{text}(0只)")
            label.setObjectName(f"category_{key}")
            layout.addWidget(label)
            self.category_labels[signal_type] = label
    
    def create_control_buttons(self, layout: QVBoxLayout):
        """创建控制按钮"""
        button_layout = QVBoxLayout()
        
        buttons = [
            ("⚙️ 信号设置", self.open_signal_settings),
            ("📊 历史信号", self.show_signal_history),
            ("🔔 预警设置", self.open_alert_settings)
        ]
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.setObjectName("control_btn")
            btn.clicked.connect(callback)
            button_layout.addWidget(btn)
        
        layout.addLayout(button_layout)
    
    def create_chart_area(self) -> QWidget:
        """创建K线图表区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 股票信息栏
        self.create_stock_info_bar(layout)
        
        # 时间周期选择
        self.create_timeframe_selector(layout)
        
        # 主图表（K线）
        self.create_main_chart(layout)
        
        # 副图指标
        self.create_indicator_chart(layout)
        
        # 信号详情面板
        self.create_signal_detail_panel(layout)
        
        return widget
    
    def create_stock_info_bar(self, layout: QVBoxLayout):
        """创建股票信息栏"""
        info_widget = QWidget()
        info_layout = QHBoxLayout(info_widget)
        
        self.stock_name_label = QLabel("当前股票: 请选择股票")
        self.stock_name_label.setObjectName("stock_name")
        info_layout.addWidget(self.stock_name_label)
        
        info_layout.addStretch()
        
        self.stock_price_label = QLabel("¥0.00 (0.00%)")
        self.stock_price_label.setObjectName("stock_price")
        info_layout.addWidget(self.stock_price_label)
        
        layout.addWidget(info_widget)
    
    def create_timeframe_selector(self, layout: QVBoxLayout):
        """创建时间周期选择器"""
        selector_widget = QWidget()
        selector_layout = QHBoxLayout(selector_widget)
        
        # 时间周期按钮
        timeframes = [
            ("1m", "1分钟"),
            ("5m", "5分钟"),
            ("15m", "15分钟"),
            ("30m", "30分钟"),
            ("1h", "1小时"),
            ("4h", "4小时"),
            ("1d", "日线")
        ]
        
        self.timeframe_buttons = {}
        self.timeframe_group = QButtonGroup()
        
        for tf, name in timeframes:
            btn = QPushButton(name)
            btn.setObjectName("timeframe_btn")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, t=tf: self.change_timeframe(t))
            
            self.timeframe_buttons[tf] = btn
            self.timeframe_group.addButton(btn)
            selector_layout.addWidget(btn)
        
        # 默认选择日线
        self.timeframe_buttons["1d"].setChecked(True)
        
        selector_layout.addStretch()
        
        # 技术指标选择
        indicator_label = QLabel("📊")
        selector_layout.addWidget(indicator_label)
        
        indicators = ["MA", "MACD", "RSI", "KDJ", "BOLL"]
        for indicator in indicators:
            btn = QPushButton(indicator)
            btn.setObjectName("indicator_btn")
            btn.setCheckable(True)
            btn.setChecked(True)  # 默认显示所有指标
            selector_layout.addWidget(btn)
        
        layout.addWidget(selector_widget)
    
    def create_main_chart(self, layout: QVBoxLayout):
        """创建主图表"""
        # 使用pyqtgraph创建高性能图表
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setObjectName("main_chart")
        self.chart_widget.setLabel('left', '价格', units='¥')
        self.chart_widget.setLabel('bottom', '时间')
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置背景色
        self.chart_widget.setBackground('#2b2b2b')
        
        layout.addWidget(self.chart_widget, 3)
    
    def create_indicator_chart(self, layout: QVBoxLayout):
        """创建指标图表"""
        self.indicator_widget = pg.PlotWidget()
        self.indicator_widget.setObjectName("indicator_chart")
        self.indicator_widget.setLabel('left', '指标值')
        self.indicator_widget.setLabel('bottom', '时间')
        self.indicator_widget.showGrid(x=True, y=True, alpha=0.3)
        self.indicator_widget.setBackground('#2b2b2b')
        
        layout.addWidget(self.indicator_widget, 1)
    
    def create_signal_detail_panel(self, layout: QVBoxLayout):
        """创建信号详情面板"""
        detail_widget = QWidget()
        detail_widget.setObjectName("signal_detail_panel")
        detail_layout = QVBoxLayout(detail_widget)
        
        # 标题
        title_label = QLabel("🎯 信号详情 + 确认")
        title_label.setObjectName("detail_title")
        detail_layout.addWidget(title_label)
        
        # 信号详情表格
        self.signal_detail_table = QTableWidget()
        self.signal_detail_table.setObjectName("signal_detail_table")
        self.signal_detail_table.setColumnCount(4)
        self.signal_detail_table.setHorizontalHeaderLabels([
            "指标", "当前值", "信号", "确认状态"
        ])
        
        # 设置表格样式
        header = self.signal_detail_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        detail_layout.addWidget(self.signal_detail_table)
        
        # 综合评分显示
        score_layout = QHBoxLayout()
        
        self.comprehensive_score_label = QLabel("🎯 综合信号评分: --分")
        self.comprehensive_score_label.setObjectName("comprehensive_score")
        score_layout.addWidget(self.comprehensive_score_label)
        
        self.confidence_label = QLabel("📊 信号确信度: --%")
        self.confidence_label.setObjectName("confidence_label")
        score_layout.addWidget(self.confidence_label)
        
        detail_layout.addLayout(score_layout)
        
        # 建议信息
        suggestion_layout = QHBoxLayout()
        
        self.signal_time_label = QLabel("⏰ 信号生成时间: --:--:--")
        suggestion_layout.addWidget(self.signal_time_label)
        
        self.suggested_price_label = QLabel("💰 建议价格: ¥--.--")
        suggestion_layout.addWidget(self.suggested_price_label)
        
        detail_layout.addLayout(suggestion_layout)
        
        layout.addWidget(detail_widget, 1)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet(self.style_manager.get_trading_signal_chart_style())
    
    def update_signals(self):
        """更新信号数据"""
        try:
            if not self.current_symbol:
                return
            
            # 获取买入信号
            buy_signals = self.buy_strategy.generate_buy_signals([self.current_symbol])
            
            # 获取卖出信号（需要持仓信息）
            # 这里简化处理，实际应该从持仓管理器获取
            sell_signals = []
            
            # 更新信号列表
            self.update_signal_list(buy_signals, sell_signals)
            
            # 更新图表信号标注
            self.update_chart_signals(buy_signals, sell_signals)
            
            # 更新状态
            self.update_status_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logger.error(f"❌ 更新信号失败: {e}")
    
    def update_signal_list(self, buy_signals: List, sell_signals: List):
        """更新信号列表"""
        try:
            self.signal_list_widget.clear()
            
            # 添加买入信号
            for signal in buy_signals:
                item = QListWidgetItem()
                widget = self.create_signal_item(signal, SignalType.BUY)
                item.setSizeHint(widget.sizeHint())
                self.signal_list_widget.addItem(item)
                self.signal_list_widget.setItemWidget(item, widget)
            
            # 添加卖出信号
            for signal in sell_signals:
                item = QListWidgetItem()
                widget = self.create_signal_item(signal, SignalType.SELL)
                item.setSizeHint(widget.sizeHint())
                self.signal_list_widget.addItem(item)
                self.signal_list_widget.setItemWidget(item, widget)
            
            # 更新分类标签
            self.category_labels[SignalType.BUY].setText(f"🔴 买入信号({len(buy_signals)}只)")
            self.category_labels[SignalType.SELL].setText(f"🟢 卖出信号({len(sell_signals)}只)")
            self.category_labels[SignalType.OBSERVE].setText(f"⚪ 观察信号(0只)")
            
        except Exception as e:
            logger.error(f"❌ 更新信号列表失败: {e}")
    
    def create_signal_item(self, signal, signal_type: str) -> QWidget:
        """创建信号项目组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 股票信息
        stock_layout = QHBoxLayout()
        
        symbol_label = QLabel(signal.symbol)
        symbol_label.setObjectName("signal_symbol")
        stock_layout.addWidget(symbol_label)
        
        stock_layout.addStretch()
        
        # 信号强度
        strength_widget = self.create_strength_indicator(signal.signal_strength)
        stock_layout.addWidget(strength_widget)
        
        layout.addLayout(stock_layout)
        
        # 评分信息
        score_layout = QHBoxLayout()
        
        score_label = QLabel(f"评分: {getattr(signal, 'comprehensive_score', 0):.0f}")
        score_label.setObjectName("signal_score")
        score_layout.addWidget(score_label)
        
        score_layout.addStretch()
        
        # 信号类型标识
        type_label = QLabel("🔴" if signal_type == SignalType.BUY else "🟢")
        type_label.setObjectName("signal_type")
        score_layout.addWidget(type_label)
        
        layout.addLayout(score_layout)
        
        return widget
    
    def create_strength_indicator(self, strength: float) -> QWidget:
        """创建信号强度指示器"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 5个圆点表示强度
        for i in range(5):
            dot = QLabel("●")
            if i < int(strength * 5):
                dot.setStyleSheet("color: #52c41a;")  # 绿色
            else:
                dot.setStyleSheet("color: #555555;")  # 灰色
            layout.addWidget(dot)
        
        return widget
    
    def update_chart_signals(self, buy_signals: List, sell_signals: List):
        """更新图表信号标注"""
        try:
            # 清除旧的信号标注
            for item in self.signal_items:
                self.chart_widget.removeItem(item)
            self.signal_items.clear()
            
            # 添加买入信号标注
            for signal in buy_signals:
                if signal.symbol == self.current_symbol:
                    self.add_signal_annotation(signal, SignalType.BUY)
            
            # 添加卖出信号标注
            for signal in sell_signals:
                if signal.symbol == self.current_symbol:
                    self.add_signal_annotation(signal, SignalType.SELL)
            
        except Exception as e:
            logger.error(f"❌ 更新图表信号失败: {e}")
    
    def add_signal_annotation(self, signal, signal_type: str):
        """添加信号标注"""
        try:
            # 创建信号标注点
            x = 0  # 简化处理，实际应该根据时间计算
            y = getattr(signal, 'signal_price', 100)
            
            # 创建标注
            if signal_type == SignalType.BUY:
                color = 'red'
                symbol = '▲'
            else:
                color = 'green'
                symbol = '▼'
            
            # 添加到图表
            scatter = pg.ScatterPlotItem([x], [y], symbol=symbol, size=15, brush=color)
            self.chart_widget.addItem(scatter)
            self.signal_items.append(scatter)
            
        except Exception as e:
            logger.error(f"❌ 添加信号标注失败: {e}")
    
    def on_signal_selected(self, item):
        """信号选择事件"""
        try:
            # 获取选中的信号数据
            # 这里需要从item中提取信号信息
            signal_data = {}  # 简化处理
            
            # 更新信号详情面板
            self.update_signal_detail(signal_data)
            
            # 发射信号
            self.signal_selected.emit(self.current_symbol, signal_data)
            
        except Exception as e:
            logger.error(f"❌ 处理信号选择失败: {e}")
    
    def update_signal_detail(self, signal_data: Dict[str, Any]):
        """更新信号详情"""
        try:
            # 更新详情表格
            indicators = [
                ("MACD", "DIF>DEA", "🔴金叉确认", "✅"),
                ("RSI", "65.2", "适中区间", "✅"),
                ("KDJ", "K>D", "🔴买入确认", "✅"),
                ("成交量", "1.8倍", "🔴放大信号", "✅")
            ]
            
            self.signal_detail_table.setRowCount(len(indicators))
            
            for i, (indicator, value, signal, status) in enumerate(indicators):
                self.signal_detail_table.setItem(i, 0, QTableWidgetItem(indicator))
                self.signal_detail_table.setItem(i, 1, QTableWidgetItem(value))
                self.signal_detail_table.setItem(i, 2, QTableWidgetItem(signal))
                self.signal_detail_table.setItem(i, 3, QTableWidgetItem(status))
            
            # 更新评分信息
            self.comprehensive_score_label.setText("🎯 综合信号评分: 85分 🔴 强烈买入")
            self.confidence_label.setText("📊 信号确信度: 78% (基于历史成功率)")
            self.signal_time_label.setText(f"⏰ 信号生成时间: {datetime.now().strftime('%H:%M:%S')}")
            self.suggested_price_label.setText("💰 建议买入价格: ¥1845-1850")
            
        except Exception as e:
            logger.error(f"❌ 更新信号详情失败: {e}")
    
    def change_timeframe(self, timeframe: str):
        """切换时间周期"""
        try:
            self.current_timeframe = timeframe
            logger.info(f"📊 切换时间周期: {timeframe}")
            
            # 重新加载数据
            self.load_market_data()
            
        except Exception as e:
            logger.error(f"❌ 切换时间周期失败: {e}")
    
    def set_symbol(self, symbol: str):
        """设置当前股票"""
        try:
            self.current_symbol = symbol
            
            # 更新股票信息
            self.stock_name_label.setText(f"当前股票: {symbol}")
            
            # 加载市场数据
            self.load_market_data()
            
            # 发射信号
            self.symbol_changed.emit(symbol)
            
            logger.info(f"📊 切换股票: {symbol}")
            
        except Exception as e:
            logger.error(f"❌ 设置股票失败: {e}")
    
    def load_market_data(self):
        """加载市场数据"""
        try:
            if not self.current_symbol:
                return
            
            # 这里应该从数据源加载真实的市场数据
            # 简化处理：生成模拟数据
            dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
            prices = 100 + np.cumsum(np.random.randn(100) * 0.02)
            
            self.market_data = pd.DataFrame({
                'date': dates,
                'open': prices,
                'high': prices * 1.02,
                'low': prices * 0.98,
                'close': prices,
                'volume': np.random.randint(1000000, 5000000, 100)
            })
            
            # 更新图表
            self.update_chart()
            
        except Exception as e:
            logger.error(f"❌ 加载市场数据失败: {e}")
    
    def update_chart(self):
        """更新图表"""
        try:
            if self.market_data.empty:
                return
            
            # 清除旧数据
            self.chart_widget.clear()
            
            # 绘制K线（简化为线图）
            x = np.arange(len(self.market_data))
            y = self.market_data['close'].values
            
            self.chart_widget.plot(x, y, pen='white', name='价格')
            
            # 绘制移动平均线
            if len(y) >= 20:
                ma20 = pd.Series(y).rolling(20).mean()
                self.chart_widget.plot(x, ma20, pen='yellow', name='MA20')
            
        except Exception as e:
            logger.error(f"❌ 更新图表失败: {e}")

    def manual_refresh(self):
        """手动刷新"""
        self.update_signals()

    def open_signal_settings(self):
        """打开信号设置"""
        from frontend_ui.dialogs.signal_settings_dialog import SignalSettingsDialog
        dialog = SignalSettingsDialog(self)
        dialog.exec_()

    def show_signal_history(self):
        """显示历史信号"""
        from frontend_ui.dialogs.signal_history_dialog import SignalHistoryDialog
        dialog = SignalHistoryDialog(self)
        dialog.exec_()

    def open_alert_settings(self):
        """打开预警设置"""
        from frontend_ui.dialogs.alert_settings_dialog import AlertSettingsDialog
        dialog = AlertSettingsDialog(self)
        dialog.exec_()


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    widget = TradingSignalChart()
    widget.set_symbol("000001")
    widget.show()
    
    sys.exit(app.exec_())
