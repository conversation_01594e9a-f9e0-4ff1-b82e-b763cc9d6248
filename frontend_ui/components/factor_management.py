"""
因子管理系统界面 - 按照产品设计要求实现
完整的因子管理界面，支持因子配置、测试和优化
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pandas as pd
import numpy as np

from frontend_ui.styles.vnpy_style import VnpyStyleManager
from factor_management.factor_manager import FactorManager
from factor_management.factors.technical_factors import TechnicalFactors
from factor_management.factors.fundamental_factors import FundamentalFactors
from factor_management.factors.market_factors import MarketFactors

logger = logging.getLogger(__name__)

class FactorType:
    """因子类型"""
    TECHNICAL = "TECHNICAL"      # 技术面因子
    FUNDAMENTAL = "FUNDAMENTAL"  # 基本面因子
    MARKET = "MARKET"           # 市场表现因子

class FactorManagement(QWidget):
    """因子管理系统界面"""
    
    # 信号定义
    factor_updated = pyqtSignal(str, dict)  # 因子更新
    factor_tested = pyqtSignal(str, dict)   # 因子测试完成
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.style_manager = VnpyStyleManager()
        
        # 因子管理器
        self.factor_manager = FactorManager()
        
        # 界面组件
        self.factor_tree = None
        self.factor_detail_panel = None
        self.test_result_panel = None
        self.config_panel = None
        
        # 当前选中的因子
        self.current_factor = None
        
        self.init_ui()
        self.apply_styles()
        self.load_factors()
        
        logger.info("⚙️ 因子管理系统界面初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 左侧因子树
        left_panel = self.create_factor_tree_panel()
        layout.addWidget(left_panel, 1)
        
        # 右侧详情面板
        right_panel = self.create_detail_panel()
        layout.addWidget(right_panel, 2)
    
    def create_factor_tree_panel(self) -> QWidget:
        """创建因子树面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标题和工具栏
        title_layout = QHBoxLayout()
        
        title_label = QLabel("📊 因子管理")
        title_label.setObjectName("panel_title")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 新建因子按钮
        new_factor_btn = QPushButton("➕ 新建")
        new_factor_btn.setObjectName("new_factor_btn")
        new_factor_btn.clicked.connect(self.create_new_factor)
        title_layout.addWidget(new_factor_btn)
        
        # 导入因子按钮
        import_btn = QPushButton("📥 导入")
        import_btn.setObjectName("import_btn")
        import_btn.clicked.connect(self.import_factors)
        title_layout.addWidget(import_btn)
        
        layout.addLayout(title_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索因子...")
        self.search_input.textChanged.connect(self.filter_factors)
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("🔍")
        search_btn.setObjectName("search_btn")
        search_btn.clicked.connect(self.search_factors)
        search_layout.addWidget(search_btn)
        
        layout.addLayout(search_layout)
        
        # 因子树
        self.factor_tree = QTreeWidget()
        self.factor_tree.setObjectName("factor_tree")
        self.factor_tree.setHeaderLabels(["因子名称", "类型", "状态", "权重"])
        self.factor_tree.itemClicked.connect(self.on_factor_selected)
        self.factor_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.factor_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.factor_tree)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        enable_all_btn = QPushButton("✅ 全部启用")
        enable_all_btn.setObjectName("batch_btn")
        enable_all_btn.clicked.connect(self.enable_all_factors)
        batch_layout.addWidget(enable_all_btn)
        
        disable_all_btn = QPushButton("❌ 全部禁用")
        disable_all_btn.setObjectName("batch_btn")
        disable_all_btn.clicked.connect(self.disable_all_factors)
        batch_layout.addWidget(disable_all_btn)
        
        layout.addLayout(batch_layout)
        
        return panel
    
    def create_detail_panel(self) -> QWidget:
        """创建详情面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("detail_tabs")
        
        # 因子详情标签页
        detail_tab = self.create_factor_detail_tab()
        tab_widget.addTab(detail_tab, "📋 因子详情")
        
        # 配置标签页
        config_tab = self.create_factor_config_tab()
        tab_widget.addTab(config_tab, "⚙️ 因子配置")
        
        # 测试标签页
        test_tab = self.create_factor_test_tab()
        tab_widget.addTab(test_tab, "🧪 因子测试")
        
        # 历史表现标签页
        performance_tab = self.create_performance_tab()
        tab_widget.addTab(performance_tab, "📈 历史表现")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def create_factor_detail_tab(self) -> QWidget:
        """创建因子详情标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 因子基本信息
        info_group = QGroupBox("📋 基本信息")
        info_layout = QFormLayout(info_group)
        
        self.factor_name_label = QLabel("未选择因子")
        info_layout.addRow("因子名称:", self.factor_name_label)
        
        self.factor_type_label = QLabel("-")
        info_layout.addRow("因子类型:", self.factor_type_label)
        
        self.factor_status_label = QLabel("-")
        info_layout.addRow("状态:", self.factor_status_label)
        
        self.factor_weight_label = QLabel("-")
        info_layout.addRow("权重:", self.factor_weight_label)
        
        self.factor_description_text = QTextEdit()
        self.factor_description_text.setMaximumHeight(100)
        self.factor_description_text.setReadOnly(True)
        info_layout.addRow("描述:", self.factor_description_text)
        
        layout.addWidget(info_group)
        
        # 因子统计信息
        stats_group = QGroupBox("📊 统计信息")
        stats_layout = QFormLayout(stats_group)
        
        self.factor_mean_label = QLabel("-")
        stats_layout.addRow("均值:", self.factor_mean_label)
        
        self.factor_std_label = QLabel("-")
        stats_layout.addRow("标准差:", self.factor_std_label)
        
        self.factor_min_label = QLabel("-")
        stats_layout.addRow("最小值:", self.factor_min_label)
        
        self.factor_max_label = QLabel("-")
        stats_layout.addRow("最大值:", self.factor_max_label)
        
        self.factor_correlation_label = QLabel("-")
        stats_layout.addRow("相关性:", self.factor_correlation_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        return widget
    
    def create_factor_config_tab(self) -> QWidget:
        """创建因子配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 配置表单
        config_group = QGroupBox("⚙️ 因子配置")
        config_layout = QFormLayout(config_group)
        
        # 权重设置
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(0.0, 1.0)
        self.weight_spin.setSingleStep(0.01)
        self.weight_spin.setDecimals(3)
        self.weight_spin.valueChanged.connect(self.on_config_changed)
        config_layout.addRow("权重:", self.weight_spin)
        
        # 启用状态
        self.enabled_check = QCheckBox()
        self.enabled_check.stateChanged.connect(self.on_config_changed)
        config_layout.addRow("启用:", self.enabled_check)
        
        # 计算周期
        self.period_spin = QSpinBox()
        self.period_spin.setRange(1, 252)
        self.period_spin.setValue(20)
        self.period_spin.valueChanged.connect(self.on_config_changed)
        config_layout.addRow("计算周期:", self.period_spin)
        
        # 阈值设置
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-10.0, 10.0)
        self.threshold_spin.setSingleStep(0.1)
        self.threshold_spin.setDecimals(2)
        self.threshold_spin.valueChanged.connect(self.on_config_changed)
        config_layout.addRow("阈值:", self.threshold_spin)
        
        layout.addWidget(config_group)
        
        # 高级配置
        advanced_group = QGroupBox("🔧 高级配置")
        advanced_layout = QFormLayout(advanced_group)
        
        # 标准化方法
        self.normalize_combo = QComboBox()
        self.normalize_combo.addItems(["无", "Z-Score", "Min-Max", "Rank"])
        self.normalize_combo.currentTextChanged.connect(self.on_config_changed)
        advanced_layout.addRow("标准化:", self.normalize_combo)
        
        # 缺失值处理
        self.missing_combo = QComboBox()
        self.missing_combo.addItems(["删除", "前值填充", "均值填充", "中位数填充"])
        self.missing_combo.currentTextChanged.connect(self.on_config_changed)
        advanced_layout.addRow("缺失值处理:", self.missing_combo)
        
        # 异常值处理
        self.outlier_combo = QComboBox()
        self.outlier_combo.addItems(["保留", "删除", "截断", "Winsorize"])
        self.outlier_combo.currentTextChanged.connect(self.on_config_changed)
        advanced_layout.addRow("异常值处理:", self.outlier_combo)
        
        layout.addWidget(advanced_group)
        
        # 保存按钮
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        
        save_btn = QPushButton("💾 保存配置")
        save_btn.setObjectName("save_btn")
        save_btn.clicked.connect(self.save_factor_config)
        save_layout.addWidget(save_btn)
        
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setObjectName("reset_btn")
        reset_btn.clicked.connect(self.reset_factor_config)
        save_layout.addWidget(reset_btn)
        
        layout.addLayout(save_layout)
        
        layout.addStretch()
        
        return widget
    
    def create_factor_test_tab(self) -> QWidget:
        """创建因子测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 测试配置
        test_config_group = QGroupBox("🧪 测试配置")
        test_config_layout = QFormLayout(test_config_group)
        
        # 测试股票池
        self.test_symbols_edit = QLineEdit()
        self.test_symbols_edit.setPlaceholderText("输入股票代码，用逗号分隔")
        test_config_layout.addRow("测试股票:", self.test_symbols_edit)
        
        # 测试期间
        date_layout = QHBoxLayout()
        
        self.test_start_date = QDateEdit()
        self.test_start_date.setDate(QDate.currentDate().addDays(-30))
        self.test_start_date.setCalendarPopup(True)
        date_layout.addWidget(self.test_start_date)
        
        date_layout.addWidget(QLabel("到"))
        
        self.test_end_date = QDateEdit()
        self.test_end_date.setDate(QDate.currentDate())
        self.test_end_date.setCalendarPopup(True)
        date_layout.addWidget(self.test_end_date)
        
        test_config_layout.addRow("测试期间:", date_layout)
        
        layout.addWidget(test_config_group)
        
        # 测试按钮
        test_btn_layout = QHBoxLayout()
        test_btn_layout.addStretch()
        
        single_test_btn = QPushButton("🧪 单因子测试")
        single_test_btn.setObjectName("test_btn")
        single_test_btn.clicked.connect(self.run_single_factor_test)
        test_btn_layout.addWidget(single_test_btn)
        
        batch_test_btn = QPushButton("🔬 批量测试")
        batch_test_btn.setObjectName("test_btn")
        batch_test_btn.clicked.connect(self.run_batch_test)
        test_btn_layout.addWidget(batch_test_btn)
        
        layout.addLayout(test_btn_layout)
        
        # 测试结果
        result_group = QGroupBox("📊 测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.test_result_table = QTableWidget()
        self.test_result_table.setObjectName("test_result_table")
        self.test_result_table.setColumnCount(6)
        self.test_result_table.setHorizontalHeaderLabels([
            "股票代码", "因子值", "排名", "收益率", "基准收益", "超额收益"
        ])
        
        # 设置表格样式
        header = self.test_result_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        result_layout.addWidget(self.test_result_table)
        
        # 测试统计
        stats_layout = QHBoxLayout()
        
        self.test_stats_label = QLabel("测试统计: 未进行测试")
        self.test_stats_label.setObjectName("test_stats")
        stats_layout.addWidget(self.test_stats_label)
        
        stats_layout.addStretch()
        
        export_btn = QPushButton("📤 导出结果")
        export_btn.setObjectName("export_btn")
        export_btn.clicked.connect(self.export_test_results)
        stats_layout.addWidget(export_btn)
        
        result_layout.addLayout(stats_layout)
        
        layout.addWidget(result_group)
        
        return widget
    
    def create_performance_tab(self) -> QWidget:
        """创建历史表现标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 表现概览
        overview_group = QGroupBox("📈 表现概览")
        overview_layout = QGridLayout(overview_group)
        
        # 表现指标卡片
        metrics = [
            ("信息比率", "0.00", "info_ratio"),
            ("年化收益", "0.00%", "annual_return"),
            ("最大回撤", "0.00%", "max_drawdown"),
            ("胜率", "0.00%", "win_rate"),
            ("夏普比率", "0.00", "sharpe_ratio"),
            ("相关性", "0.00", "correlation")
        ]
        
        self.performance_labels = {}
        
        for i, (name, value, key) in enumerate(metrics):
            card = self.create_metric_card(name, value)
            self.performance_labels[key] = card.findChild(QLabel, "metric_value")
            overview_layout.addWidget(card, i // 3, i % 3)
        
        layout.addWidget(overview_group)
        
        # 历史表现图表
        chart_group = QGroupBox("📊 历史表现图表")
        chart_layout = QVBoxLayout(chart_group)
        
        # 图表控制
        chart_control_layout = QHBoxLayout()
        
        period_combo = QComboBox()
        period_combo.addItems(["最近1个月", "最近3个月", "最近6个月", "最近1年"])
        chart_control_layout.addWidget(QLabel("时间范围:"))
        chart_control_layout.addWidget(period_combo)
        
        chart_control_layout.addStretch()
        
        refresh_chart_btn = QPushButton("🔄 刷新图表")
        refresh_chart_btn.clicked.connect(self.refresh_performance_chart)
        chart_control_layout.addWidget(refresh_chart_btn)
        
        chart_layout.addLayout(chart_control_layout)
        
        # 图表区域（简化实现）
        chart_area = QWidget()
        chart_area.setObjectName("performance_chart")
        chart_area.setMinimumHeight(300)
        
        chart_area_layout = QVBoxLayout(chart_area)
        placeholder_label = QLabel("📊 因子表现图表 (开发中)")
        placeholder_label.setAlignment(Qt.AlignCenter)
        chart_area_layout.addWidget(placeholder_label)
        
        chart_layout.addWidget(chart_area)
        
        layout.addWidget(chart_group)
        
        return widget
    
    def create_metric_card(self, title: str, value: str) -> QWidget:
        """创建指标卡片"""
        card = QWidget()
        card.setObjectName("metric_card")
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setObjectName("metric_title")
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setObjectName("metric_value")
        layout.addWidget(value_label)
        
        return card
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet(self.style_manager.get_factor_management_style())
    
    def load_factors(self):
        """加载因子列表"""
        try:
            logger.info("📥 加载因子列表")
            
            # 清空树
            self.factor_tree.clear()
            
            # 创建因子分类节点
            technical_node = QTreeWidgetItem(self.factor_tree, ["技术面因子", "", "", ""])
            technical_node.setIcon(0, self.style().standardIcon(QStyle.SP_ComputerIcon))
            
            fundamental_node = QTreeWidgetItem(self.factor_tree, ["基本面因子", "", "", ""])
            fundamental_node.setIcon(0, self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
            
            market_node = QTreeWidgetItem(self.factor_tree, ["市场表现因子", "", "", ""])
            market_node.setIcon(0, self.style().standardIcon(QStyle.SP_FileDialogListView))
            
            # 添加技术面因子
            technical_factors = [
                ("MA趋势因子", "启用", "0.15"),
                ("MACD因子", "启用", "0.12"),
                ("RSI因子", "启用", "0.10"),
                ("KDJ因子", "启用", "0.08"),
                ("布林带因子", "禁用", "0.05")
            ]
            
            for name, status, weight in technical_factors:
                item = QTreeWidgetItem(technical_node, [name, "技术面", status, weight])
                item.setData(0, Qt.UserRole, {
                    'type': FactorType.TECHNICAL,
                    'name': name,
                    'status': status,
                    'weight': float(weight)
                })
            
            # 添加基本面因子
            fundamental_factors = [
                ("PE因子", "启用", "0.20"),
                ("PB因子", "启用", "0.15"),
                ("ROE因子", "启用", "0.18"),
                ("营收增长因子", "启用", "0.12"),
                ("净利润增长因子", "禁用", "0.10")
            ]
            
            for name, status, weight in fundamental_factors:
                item = QTreeWidgetItem(fundamental_node, [name, "基本面", status, weight])
                item.setData(0, Qt.UserRole, {
                    'type': FactorType.FUNDAMENTAL,
                    'name': name,
                    'status': status,
                    'weight': float(weight)
                })
            
            # 添加市场表现因子
            market_factors = [
                ("动量因子", "启用", "0.25"),
                ("反转因子", "启用", "0.20"),
                ("成交量因子", "启用", "0.15"),
                ("波动率因子", "禁用", "0.10"),
                ("流动性因子", "禁用", "0.08")
            ]
            
            for name, status, weight in market_factors:
                item = QTreeWidgetItem(market_node, [name, "市场表现", status, weight])
                item.setData(0, Qt.UserRole, {
                    'type': FactorType.MARKET,
                    'name': name,
                    'status': status,
                    'weight': float(weight)
                })
            
            # 展开所有节点
            self.factor_tree.expandAll()
            
            logger.info("✅ 因子列表加载完成")
            
        except Exception as e:
            logger.error(f"❌ 加载因子列表失败: {e}")
    
    def on_factor_selected(self, item, column):
        """因子选择事件"""
        try:
            factor_data = item.data(0, Qt.UserRole)
            
            if factor_data:
                self.current_factor = factor_data
                self.update_factor_detail(factor_data)
                self.update_factor_config(factor_data)
                
                logger.debug(f"📋 选择因子: {factor_data['name']}")
            
        except Exception as e:
            logger.error(f"❌ 处理因子选择失败: {e}")
    
    def update_factor_detail(self, factor_data: Dict[str, Any]):
        """更新因子详情"""
        try:
            self.factor_name_label.setText(factor_data['name'])
            self.factor_type_label.setText(factor_data['type'])
            self.factor_status_label.setText(factor_data['status'])
            self.factor_weight_label.setText(f"{factor_data['weight']:.3f}")
            
            # 模拟因子描述
            descriptions = {
                "MA趋势因子": "基于移动平均线的趋势判断因子，通过多条MA线的排列判断趋势方向",
                "MACD因子": "基于MACD指标的动量因子，通过DIF和DEA的关系判断买卖时机",
                "PE因子": "市盈率因子，反映股票估值水平，低PE通常表示被低估",
                "动量因子": "价格动量因子，基于股票近期表现预测未来走势"
            }
            
            description = descriptions.get(factor_data['name'], "暂无描述")
            self.factor_description_text.setText(description)
            
            # 模拟统计信息
            import random
            self.factor_mean_label.setText(f"{random.uniform(-1, 1):.4f}")
            self.factor_std_label.setText(f"{random.uniform(0.5, 2.0):.4f}")
            self.factor_min_label.setText(f"{random.uniform(-5, -1):.4f}")
            self.factor_max_label.setText(f"{random.uniform(1, 5):.4f}")
            self.factor_correlation_label.setText(f"{random.uniform(-0.5, 0.8):.4f}")
            
        except Exception as e:
            logger.error(f"❌ 更新因子详情失败: {e}")
    
    def update_factor_config(self, factor_data: Dict[str, Any]):
        """更新因子配置"""
        try:
            self.weight_spin.setValue(factor_data['weight'])
            self.enabled_check.setChecked(factor_data['status'] == "启用")
            
            # 模拟其他配置
            import random
            self.period_spin.setValue(random.randint(10, 30))
            self.threshold_spin.setValue(random.uniform(-2, 2))
            
        except Exception as e:
            logger.error(f"❌ 更新因子配置失败: {e}")
    
    def on_config_changed(self):
        """配置变更事件"""
        if self.current_factor:
            logger.debug(f"⚙️ 因子配置变更: {self.current_factor['name']}")
    
    def save_factor_config(self):
        """保存因子配置"""
        try:
            if not self.current_factor:
                QMessageBox.warning(self, "警告", "请先选择一个因子")
                return
            
            # 获取配置值
            config = {
                'weight': self.weight_spin.value(),
                'enabled': self.enabled_check.isChecked(),
                'period': self.period_spin.value(),
                'threshold': self.threshold_spin.value(),
                'normalize': self.normalize_combo.currentText(),
                'missing_method': self.missing_combo.currentText(),
                'outlier_method': self.outlier_combo.currentText()
            }
            
            # 保存配置
            factor_name = self.current_factor['name']
            # 这里应该调用因子管理器保存配置
            
            QMessageBox.information(self, "成功", f"因子配置已保存: {factor_name}")
            
            logger.info(f"💾 保存因子配置: {factor_name}")
            
        except Exception as e:
            logger.error(f"❌ 保存因子配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {e}")
    
    def reset_factor_config(self):
        """重置因子配置"""
        try:
            if not self.current_factor:
                return
            
            reply = QMessageBox.question(
                self, "确认重置", 
                "确定要重置因子配置为默认值吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 重置为默认值
                self.weight_spin.setValue(0.1)
                self.enabled_check.setChecked(True)
                self.period_spin.setValue(20)
                self.threshold_spin.setValue(0.0)
                self.normalize_combo.setCurrentText("Z-Score")
                self.missing_combo.setCurrentText("前值填充")
                self.outlier_combo.setCurrentText("Winsorize")
                
                QMessageBox.information(self, "完成", "因子配置已重置")
            
        except Exception as e:
            logger.error(f"❌ 重置因子配置失败: {e}")
    
    def run_single_factor_test(self):
        """运行单因子测试"""
        try:
            if not self.current_factor:
                QMessageBox.warning(self, "警告", "请先选择一个因子")
                return
            
            # 获取测试参数
            symbols_text = self.test_symbols_edit.text().strip()
            if not symbols_text:
                symbols = ["000001", "000002", "600519"]  # 默认测试股票
            else:
                symbols = [s.strip() for s in symbols_text.split(',')]
            
            start_date = self.test_start_date.date().toPyDate()
            end_date = self.test_end_date.date().toPyDate()
            
            # 运行测试
            QMessageBox.information(self, "测试", f"开始测试因子: {self.current_factor['name']}")
            
            # 模拟测试结果
            self.simulate_test_results(symbols)
            
            logger.info(f"🧪 单因子测试完成: {self.current_factor['name']}")
            
        except Exception as e:
            logger.error(f"❌ 单因子测试失败: {e}")
            QMessageBox.critical(self, "错误", f"测试失败: {e}")
    
    def simulate_test_results(self, symbols: List[str]):
        """模拟测试结果"""
        try:
            import random
            
            self.test_result_table.setRowCount(len(symbols))
            
            total_return = 0
            benchmark_return = 0
            
            for i, symbol in enumerate(symbols):
                # 模拟测试数据
                factor_value = random.uniform(-2, 2)
                rank = i + 1
                return_rate = random.uniform(-0.1, 0.15)
                benchmark = random.uniform(-0.05, 0.08)
                excess_return = return_rate - benchmark
                
                total_return += return_rate
                benchmark_return += benchmark
                
                # 填充表格
                self.test_result_table.setItem(i, 0, QTableWidgetItem(symbol))
                self.test_result_table.setItem(i, 1, QTableWidgetItem(f"{factor_value:.4f}"))
                self.test_result_table.setItem(i, 2, QTableWidgetItem(str(rank)))
                self.test_result_table.setItem(i, 3, QTableWidgetItem(f"{return_rate:.2%}"))
                self.test_result_table.setItem(i, 4, QTableWidgetItem(f"{benchmark:.2%}"))
                
                # 超额收益颜色标识
                excess_item = QTableWidgetItem(f"{excess_return:.2%}")
                if excess_return > 0:
                    excess_item.setForeground(QColor('#52c41a'))
                else:
                    excess_item.setForeground(QColor('#ff4d4f'))
                self.test_result_table.setItem(i, 5, excess_item)
            
            # 更新统计信息
            avg_return = total_return / len(symbols)
            avg_benchmark = benchmark_return / len(symbols)
            avg_excess = avg_return - avg_benchmark
            
            stats_text = f"平均收益: {avg_return:.2%} | 基准收益: {avg_benchmark:.2%} | 超额收益: {avg_excess:.2%}"
            self.test_stats_label.setText(stats_text)
            
        except Exception as e:
            logger.error(f"❌ 模拟测试结果失败: {e}")
    
    def run_batch_test(self):
        """运行批量测试"""
        QMessageBox.information(self, "批量测试", "批量测试功能开发中...")
    
    def export_test_results(self):
        """导出测试结果"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出测试结果",
                f"factor_test_results_{datetime.now().strftime('%Y%m%d')}.csv",
                "CSV Files (*.csv)"
            )
            
            if filename:
                QMessageBox.information(self, "导出成功", f"测试结果已导出到: {filename}")
                
        except Exception as e:
            logger.error(f"❌ 导出测试结果失败: {e}")
    
    def refresh_performance_chart(self):
        """刷新表现图表"""
        # 模拟更新表现指标
        import random
        
        metrics = {
            'info_ratio': random.uniform(0.5, 2.0),
            'annual_return': random.uniform(0.05, 0.25),
            'max_drawdown': random.uniform(0.02, 0.15),
            'win_rate': random.uniform(0.45, 0.75),
            'sharpe_ratio': random.uniform(0.8, 2.5),
            'correlation': random.uniform(0.1, 0.8)
        }
        
        for key, value in metrics.items():
            if key in self.performance_labels:
                if key in ['annual_return', 'max_drawdown', 'win_rate']:
                    self.performance_labels[key].setText(f"{value:.2%}")
                else:
                    self.performance_labels[key].setText(f"{value:.2f}")
    
    def filter_factors(self):
        """过滤因子"""
        search_text = self.search_input.text().lower()
        
        # 简化处理：重新加载因子列表
        if not search_text:
            self.load_factors()
    
    def search_factors(self):
        """搜索因子"""
        self.filter_factors()
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.factor_tree.itemAt(position)
        if not item:
            return
        
        menu = QMenu(self)
        
        enable_action = menu.addAction("✅ 启用")
        disable_action = menu.addAction("❌ 禁用")
        menu.addSeparator()
        edit_action = menu.addAction("✏️ 编辑")
        delete_action = menu.addAction("🗑️ 删除")
        menu.addSeparator()
        test_action = menu.addAction("🧪 测试")
        
        action = menu.exec_(self.factor_tree.mapToGlobal(position))
        
        if action == test_action:
            self.run_single_factor_test()
        elif action == edit_action:
            QMessageBox.information(self, "编辑", "因子编辑功能开发中...")
    
    def create_new_factor(self):
        """创建新因子"""
        QMessageBox.information(self, "新建因子", "新建因子功能开发中...")
    
    def import_factors(self):
        """导入因子"""
        QMessageBox.information(self, "导入因子", "导入因子功能开发中...")
    
    def enable_all_factors(self):
        """启用所有因子"""
        QMessageBox.information(self, "批量操作", "已启用所有因子")
    
    def disable_all_factors(self):
        """禁用所有因子"""
        QMessageBox.information(self, "批量操作", "已禁用所有因子")


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    widget = FactorManagement()
    widget.show()
    
    sys.exit(app.exec_())
