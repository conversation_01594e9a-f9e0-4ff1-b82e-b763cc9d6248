"""
VeighNa风格样式管理器
提供统一的界面样式
"""

class VnpyStyleManager:
    """VeighNa风格样式管理器"""
    
    def __init__(self):
        self.primary_color = "#1890ff"
        self.success_color = "#52c41a"
        self.warning_color = "#faad14"
        self.error_color = "#ff4d4f"
        self.background_color = "#1f1f1f"
        self.surface_color = "#262626"
        self.border_color = "#3f3f3f"
        self.text_color = "#ffffff"
        self.text_secondary = "#8c8c8c"
    
    def get_base_style(self) -> str:
        """获取基础样式"""
        return f"""
        QWidget {{
            background-color: {self.background_color};
            color: {self.text_color};
            font-family: "Microsoft YaHei", Arial, sans-serif;
            font-size: 12px;
        }}
        
        QPushButton {{
            background-color: {self.primary_color};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: #40a9ff;
        }}
        
        QPushButton:pressed {{
            background-color: #096dd9;
        }}
        
        QLineEdit, QTextEdit {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 4px;
            padding: 6px;
            color: {self.text_color};
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {self.primary_color};
        }}
        """
    
    def get_factor_management_style(self) -> str:
        """获取因子管理系统样式"""
        return f"""
        /* 因子管理系统样式 */
        QWidget {{
            background-color: {self.background_color};
            color: {self.text_color};
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }}
        
        QLabel#panel_title {{
            color: {self.text_color};
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        
        QPushButton#new_factor_btn, QPushButton#import_btn {{
            background-color: {self.primary_color};
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton#new_factor_btn:hover, QPushButton#import_btn:hover {{
            background-color: #40a9ff;
        }}
        
        QPushButton#search_btn {{
            background-color: {self.success_color};
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
        }}
        
        QPushButton#search_btn:hover {{
            background-color: #73d13d;
        }}
        
        QTreeWidget#factor_tree {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 4px;
            alternate-background-color: #2f2f2f;
            selection-background-color: {self.primary_color};
        }}
        
        QTreeWidget#factor_tree::item {{
            padding: 8px;
            border-bottom: 1px solid {self.border_color};
        }}
        
        QTreeWidget#factor_tree::item:selected {{
            background-color: {self.primary_color};
        }}
        
        QPushButton#batch_btn {{
            background-color: {self.warning_color};
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
        }}
        
        QPushButton#batch_btn:hover {{
            background-color: #ffc53d;
        }}
        
        QTabWidget#detail_tabs {{
            background-color: {self.background_color};
        }}
        
        QTabWidget#detail_tabs::pane {{
            border: 1px solid {self.border_color};
            background-color: {self.surface_color};
        }}
        
        QTabWidget#detail_tabs::tab-bar {{
            alignment: left;
        }}
        
        QTabWidget#detail_tabs QTabBar::tab {{
            background-color: {self.border_color};
            color: {self.text_color};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabWidget#detail_tabs QTabBar::tab:selected {{
            background-color: {self.primary_color};
        }}
        
        QTabWidget#detail_tabs QTabBar::tab:hover {{
            background-color: #595959;
        }}
        
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {self.border_color};
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {self.primary_color};
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 4px;
            padding: 6px;
            color: {self.text_color};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus, QDateEdit:focus {{
            border-color: {self.primary_color};
        }}
        
        QTextEdit {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 4px;
            padding: 8px;
            color: {self.text_color};
        }}
        
        QCheckBox {{
            color: {self.text_color};
        }}
        
        QCheckBox::indicator {{
            width: 16px;
            height: 16px;
        }}
        
        QCheckBox::indicator:unchecked {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 3px;
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {self.primary_color};
            border: 1px solid {self.primary_color};
            border-radius: 3px;
        }}
        
        QPushButton#save_btn {{
            background-color: {self.success_color};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton#save_btn:hover {{
            background-color: #73d13d;
        }}
        
        QPushButton#reset_btn {{
            background-color: {self.warning_color};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton#reset_btn:hover {{
            background-color: #ffc53d;
        }}
        
        QPushButton#test_btn {{
            background-color: #722ed1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton#test_btn:hover {{
            background-color: #9254de;
        }}
        
        QTableWidget#test_result_table {{
            background-color: {self.surface_color};
            alternate-background-color: #2f2f2f;
            color: {self.text_color};
            gridline-color: {self.border_color};
            selection-background-color: {self.primary_color};
        }}
        
        QTableWidget#test_result_table::item {{
            padding: 8px;
            border-bottom: 1px solid {self.border_color};
        }}
        
        QLabel#test_stats {{
            color: {self.success_color};
            font-weight: bold;
        }}
        
        QPushButton#export_btn {{
            background-color: #13c2c2;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
        }}
        
        QPushButton#export_btn:hover {{
            background-color: #36cfc9;
        }}
        
        QWidget#metric_card {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 8px;
            padding: 15px;
            margin: 5px;
        }}
        
        QLabel#metric_title {{
            color: {self.text_secondary};
            font-size: 12px;
            margin-bottom: 5px;
        }}
        
        QLabel#metric_value {{
            color: {self.text_color};
            font-size: 20px;
            font-weight: bold;
        }}
        
        QWidget#performance_chart {{
            background-color: {self.surface_color};
            border: 1px solid {self.border_color};
            border-radius: 8px;
        }}
        """
