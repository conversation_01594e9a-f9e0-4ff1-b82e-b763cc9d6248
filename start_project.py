#!/usr/bin/env python3
"""
量化交易系统启动脚本
完整的项目启动和部署脚本
"""

import logging
import sys
import os
import subprocess
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProjectStarter:
    """项目启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        
    def start_project(self):
        """启动项目"""
        try:
            logger.info("🚀 开始启动量化交易系统...")
            
            # 1. 检查Python环境
            if not self.check_python_environment():
                return False
            
            # 2. 检查和安装依赖
            if not self.check_and_install_dependencies():
                return False
            
            # 3. 检查数据库配置
            if not self.check_database_configuration():
                return False
            
            # 4. 初始化数据库
            if not self.initialize_database():
                return False
            
            # 5. 启动核心服务
            if not self.start_core_services():
                return False
            
            # 6. 启动Web界面
            if not self.start_web_interface():
                return False
            
            logger.info("🎉 量化交易系统启动成功！")
            self.show_startup_info()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 项目启动失败: {e}")
            return False
    
    def check_python_environment(self):
        """检查Python环境"""
        try:
            logger.info("1️⃣ 检查Python环境...")
            
            # 检查Python版本
            python_version = sys.version_info
            logger.info(f"  Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            
            if python_version < (3, 8):
                logger.error("❌ Python版本过低，需要3.8或更高版本")
                return False
            
            # 检查虚拟环境
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                logger.info("✅ 运行在虚拟环境中")
            else:
                logger.warning("⚠️ 未使用虚拟环境，建议使用虚拟环境")
            
            logger.info("✅ Python环境检查通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ Python环境检查失败: {e}")
            return False
    
    def check_and_install_dependencies(self):
        """检查和安装依赖"""
        try:
            logger.info("2️⃣ 检查和安装依赖...")
            
            if not self.requirements_file.exists():
                logger.error("❌ requirements.txt文件不存在")
                return False
            
            # 检查关键依赖
            critical_packages = [
                'sqlalchemy',
                'pandas',
                'numpy',
                'fastapi',
                'uvicorn'
            ]
            
            missing_packages = []
            for package in critical_packages:
                try:
                    __import__(package)
                    logger.info(f"✅ {package} 已安装")
                except ImportError:
                    missing_packages.append(package)
                    logger.warning(f"⚠️ {package} 未安装")
            
            if missing_packages:
                logger.info("📦 安装缺失的依赖包...")
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
                    ], check=True, capture_output=True, text=True)
                    logger.info("✅ 依赖安装完成")
                except subprocess.CalledProcessError as e:
                    logger.error(f"❌ 依赖安装失败: {e}")
                    return False
            
            logger.info("✅ 依赖检查完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 依赖检查失败: {e}")
            return False
    
    def check_database_configuration(self):
        """检查数据库配置"""
        try:
            logger.info("3️⃣ 检查数据库配置...")
            
            # 运行数据库测试脚本
            test_script = self.project_root / "test_database.py"
            if test_script.exists():
                result = subprocess.run([
                    sys.executable, str(test_script)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ 数据库配置检查通过")
                    return True
                else:
                    logger.error(f"❌ 数据库配置检查失败: {result.stderr}")
                    return False
            else:
                logger.warning("⚠️ 数据库测试脚本不存在，跳过检查")
                return True
            
        except Exception as e:
            logger.error(f"❌ 数据库配置检查失败: {e}")
            return False
    
    def initialize_database(self):
        """初始化数据库"""
        try:
            logger.info("4️⃣ 初始化数据库...")
            
            # 运行数据库初始化脚本
            init_script = self.project_root / "init_database.py"
            if init_script.exists():
                result = subprocess.run([
                    sys.executable, str(init_script)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ 数据库初始化完成")
                    return True
                else:
                    logger.error(f"❌ 数据库初始化失败: {result.stderr}")
                    return False
            else:
                logger.warning("⚠️ 数据库初始化脚本不存在，跳过初始化")
                return True
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            return False
    
    def start_core_services(self):
        """启动核心服务"""
        try:
            logger.info("5️⃣ 启动核心服务...")
            
            # 检查核心模块是否可以导入
            core_modules = [
                'database_models',
                'data_collection.collectors.market_collector',
                'analysis_engine.stock_selector',
                'strategy_layer.trading_strategies.buy_strategy',
                'vnpy_integration.backtesting_engine'
            ]
            
            for module in core_modules:
                try:
                    __import__(module)
                    logger.info(f"✅ 核心模块 {module} 加载成功")
                except ImportError as e:
                    logger.error(f"❌ 核心模块 {module} 加载失败: {e}")
                    return False
            
            logger.info("✅ 核心服务启动完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 核心服务启动失败: {e}")
            return False
    
    def start_web_interface(self):
        """启动Web界面"""
        try:
            logger.info("6️⃣ 启动Web界面...")
            
            # 检查是否有Web应用入口
            web_apps = [
                self.project_root / "main.py",
                self.project_root / "app.py",
                self.project_root / "web_app.py"
            ]
            
            web_app = None
            for app in web_apps:
                if app.exists():
                    web_app = app
                    break
            
            if web_app:
                logger.info(f"✅ 找到Web应用入口: {web_app.name}")
                logger.info("🌐 Web界面准备就绪，可以手动启动")
                logger.info(f"   启动命令: python {web_app.name}")
            else:
                logger.info("ℹ️ 未找到Web应用入口，系统以命令行模式运行")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Web界面启动失败: {e}")
            return False
    
    def show_startup_info(self):
        """显示启动信息"""
        try:
            logger.info("📊 系统启动信息:")
            logger.info("=" * 60)
            logger.info(f"  🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  📁 项目路径: {self.project_root}")
            logger.info(f"  🐍 Python版本: {sys.version}")
            logger.info(f"  💾 工作目录: {os.getcwd()}")
            
            # 显示可用功能
            logger.info("🔧 可用功能模块:")
            logger.info("  ├── 📊 数据采集系统 (ADATA集成)")
            logger.info("  ├── 🧠 智能选股引擎 (多维度评分)")
            logger.info("  ├── 🚀 VeighNa回测系统 (专业回测)")
            logger.info("  ├── 📈 交易策略层 (买入/卖出策略)")
            logger.info("  ├── 💼 投资组合管理 (组合构建)")
            logger.info("  ├── ⚡ 交易执行层 (模拟/实盘)")
            logger.info("  ├── 📊 可视化展示 (监控大屏)")
            logger.info("  └── ⚙️ 系统管理 (配置/日志)")
            
            # 显示使用说明
            logger.info("📖 使用说明:")
            logger.info("  1. 数据采集: python -m data_collection.collectors.market_collector")
            logger.info("  2. 智能选股: python -m analysis_engine.stock_selector")
            logger.info("  3. 策略回测: python -m vnpy_integration.backtesting_engine")
            logger.info("  4. 监控大屏: python -m frontend_ui.components.signal_monitor_dashboard")
            logger.info("  5. 因子管理: python -m frontend_ui.components.factor_management")
            
            logger.info("=" * 60)
            logger.info("🎉 量化交易系统已成功启动！")
            
        except Exception as e:
            logger.error(f"❌ 显示启动信息失败: {e}")

def main():
    """主函数"""
    try:
        starter = ProjectStarter()
        
        if starter.start_project():
            logger.info("✅ 项目启动成功")
            return 0
        else:
            logger.error("❌ 项目启动失败")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断启动")
        return 1
    except Exception as e:
        logger.error(f"❌ 启动异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
