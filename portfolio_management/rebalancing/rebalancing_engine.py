"""
再平衡引擎
实现投资组合的动态再平衡策略
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RebalanceFrequency(Enum):
    """再平衡频率"""
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    QUARTERLY = "QUARTERLY"
    SEMI_ANNUAL = "SEMI_ANNUAL"
    ANNUAL = "ANNUAL"

class RebalanceTrigger(Enum):
    """再平衡触发条件"""
    TIME_BASED = "TIME_BASED"           # 基于时间
    THRESHOLD_BASED = "THRESHOLD_BASED" # 基于阈值
    VOLATILITY_BASED = "VOLATILITY_BASED" # 基于波动率
    PERFORMANCE_BASED = "PERFORMANCE_BASED" # 基于绩效

@dataclass
class RebalanceAction:
    """再平衡操作"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    current_weight: float
    target_weight: float
    weight_diff: float
    current_value: float
    target_value: float
    shares_to_trade: int
    trade_value: float
    reason: str
    priority: int

@dataclass
class RebalanceResult:
    """再平衡结果"""
    rebalance_date: date
    trigger_reason: str
    actions: List[RebalanceAction]
    total_turnover: float
    transaction_cost: float
    expected_improvement: float
    portfolio_drift: float

class RebalancingEngine:
    """再平衡引擎"""
    
    def __init__(self):
        # 再平衡参数
        self.frequency = RebalanceFrequency.MONTHLY
        self.weight_threshold = 0.05        # 权重偏离阈值5%
        self.min_trade_value = 1000         # 最小交易金额1000元
        self.transaction_cost_rate = 0.003  # 交易成本0.3%
        self.max_turnover = 0.50           # 最大换手率50%
        
        # 触发条件
        self.volatility_threshold = 0.25    # 波动率阈值25%
        self.performance_threshold = -0.10  # 绩效阈值-10%
        self.correlation_threshold = 0.80   # 相关性阈值80%
        
        # 再平衡历史
        self.rebalance_history: List[RebalanceResult] = []
        self.last_rebalance_date: Optional[date] = None
        
        logger.info("🔄 再平衡引擎初始化完成")
        logger.info(f"  - 再平衡频率: {self.frequency.value}")
        logger.info(f"  - 权重阈值: {self.weight_threshold:.1%}")
        logger.info(f"  - 最大换手率: {self.max_turnover:.1%}")
    
    def check_rebalance_need(self,
                           current_portfolio: Dict[str, Dict[str, Any]],
                           target_weights: Dict[str, float],
                           current_prices: Dict[str, float],
                           portfolio_performance: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检查是否需要再平衡
        
        Args:
            current_portfolio: 当前组合持仓
            target_weights: 目标权重
            current_prices: 当前价格
            portfolio_performance: 组合绩效
            
        Returns:
            再平衡需求分析结果
        """
        try:
            logger.info("🔍 检查再平衡需求...")
            
            # 计算当前权重
            current_weights = self._calculate_current_weights(current_portfolio, current_prices)
            
            # 检查各种触发条件
            triggers = []
            
            # 1. 时间触发
            time_trigger = self._check_time_trigger()
            if time_trigger['triggered']:
                triggers.append(time_trigger)
            
            # 2. 权重偏离触发
            weight_trigger = self._check_weight_drift_trigger(current_weights, target_weights)
            if weight_trigger['triggered']:
                triggers.append(weight_trigger)
            
            # 3. 波动率触发
            if portfolio_performance:
                volatility_trigger = self._check_volatility_trigger(portfolio_performance)
                if volatility_trigger['triggered']:
                    triggers.append(volatility_trigger)
                
                # 4. 绩效触发
                performance_trigger = self._check_performance_trigger(portfolio_performance)
                if performance_trigger['triggered']:
                    triggers.append(performance_trigger)
            
            # 综合判断
            need_rebalance = len(triggers) > 0
            
            # 计算组合偏离度
            portfolio_drift = self._calculate_portfolio_drift(current_weights, target_weights)
            
            result = {
                'need_rebalance': need_rebalance,
                'triggers': triggers,
                'portfolio_drift': portfolio_drift,
                'current_weights': current_weights,
                'target_weights': target_weights,
                'weight_differences': {
                    symbol: current_weights.get(symbol, 0) - target_weights.get(symbol, 0)
                    for symbol in set(list(current_weights.keys()) + list(target_weights.keys()))
                }
            }
            
            logger.info(f"✅ 再平衡检查完成: {'需要' if need_rebalance else '不需要'}再平衡")
            logger.info(f"  - 触发条件: {len(triggers)}个")
            logger.info(f"  - 组合偏离: {portfolio_drift:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 检查再平衡需求失败: {e}")
            return {'need_rebalance': False, 'triggers': [], 'portfolio_drift': 0}
    
    def execute_rebalance(self,
                         current_portfolio: Dict[str, Dict[str, Any]],
                         target_weights: Dict[str, float],
                         current_prices: Dict[str, float],
                         total_portfolio_value: float,
                         trigger_reason: str = "Manual") -> Optional[RebalanceResult]:
        """
        执行再平衡
        
        Args:
            current_portfolio: 当前组合
            target_weights: 目标权重
            current_prices: 当前价格
            total_portfolio_value: 组合总价值
            trigger_reason: 触发原因
            
        Returns:
            再平衡结果
        """
        try:
            logger.info("🔄 开始执行再平衡...")
            
            # 计算当前权重
            current_weights = self._calculate_current_weights(current_portfolio, current_prices)
            
            # 生成再平衡操作
            actions = self._generate_rebalance_actions(
                current_portfolio, current_weights, target_weights, 
                current_prices, total_portfolio_value
            )
            
            if not actions:
                logger.info("📊 无需执行再平衡操作")
                return None
            
            # 优化操作顺序
            optimized_actions = self._optimize_rebalance_actions(actions)
            
            # 计算交易成本和换手率
            total_turnover = sum(abs(action.trade_value) for action in optimized_actions) / total_portfolio_value
            transaction_cost = total_turnover * self.transaction_cost_rate * total_portfolio_value
            
            # 检查换手率限制
            if total_turnover > self.max_turnover:
                logger.warning(f"⚠️ 换手率过高: {total_turnover:.2%} > {self.max_turnover:.2%}")
                optimized_actions = self._reduce_turnover(optimized_actions, total_portfolio_value)
                total_turnover = sum(abs(action.trade_value) for action in optimized_actions) / total_portfolio_value
                transaction_cost = total_turnover * self.transaction_cost_rate * total_portfolio_value
            
            # 计算预期改善
            expected_improvement = self._estimate_rebalance_benefit(
                current_weights, target_weights, transaction_cost, total_portfolio_value
            )
            
            # 计算组合偏离
            portfolio_drift = self._calculate_portfolio_drift(current_weights, target_weights)
            
            # 创建再平衡结果
            rebalance_result = RebalanceResult(
                rebalance_date=datetime.now().date(),
                trigger_reason=trigger_reason,
                actions=optimized_actions,
                total_turnover=total_turnover,
                transaction_cost=transaction_cost,
                expected_improvement=expected_improvement,
                portfolio_drift=portfolio_drift
            )
            
            # 更新历史记录
            self.rebalance_history.append(rebalance_result)
            self.last_rebalance_date = rebalance_result.rebalance_date
            
            logger.info(f"✅ 再平衡执行完成")
            logger.info(f"  - 操作数量: {len(optimized_actions)}个")
            logger.info(f"  - 总换手率: {total_turnover:.2%}")
            logger.info(f"  - 交易成本: ¥{transaction_cost:,.0f}")
            logger.info(f"  - 预期改善: {expected_improvement:.2%}")
            
            return rebalance_result
            
        except Exception as e:
            logger.error(f"❌ 执行再平衡失败: {e}")
            return None
    
    def _calculate_current_weights(self,
                                  current_portfolio: Dict[str, Dict[str, Any]],
                                  current_prices: Dict[str, float]) -> Dict[str, float]:
        """计算当前权重"""
        try:
            current_weights = {}
            total_value = 0
            
            # 计算各股票当前市值
            for symbol, position in current_portfolio.items():
                shares = position.get('shares', 0)
                current_price = current_prices.get(symbol, position.get('avg_price', 0))
                market_value = shares * current_price
                
                current_weights[symbol] = market_value
                total_value += market_value
            
            # 转换为权重
            if total_value > 0:
                for symbol in current_weights:
                    current_weights[symbol] = current_weights[symbol] / total_value
            
            return current_weights
            
        except Exception as e:
            logger.error(f"❌ 计算当前权重失败: {e}")
            return {}
    
    def _check_time_trigger(self) -> Dict[str, Any]:
        """检查时间触发条件"""
        try:
            if self.last_rebalance_date is None:
                return {
                    'triggered': True,
                    'type': RebalanceTrigger.TIME_BASED,
                    'reason': '首次再平衡',
                    'urgency': 'medium'
                }
            
            days_since_last = (datetime.now().date() - self.last_rebalance_date).days
            
            # 根据频率设置触发天数
            trigger_days = {
                RebalanceFrequency.DAILY: 1,
                RebalanceFrequency.WEEKLY: 7,
                RebalanceFrequency.MONTHLY: 30,
                RebalanceFrequency.QUARTERLY: 90,
                RebalanceFrequency.SEMI_ANNUAL: 180,
                RebalanceFrequency.ANNUAL: 365
            }
            
            required_days = trigger_days.get(self.frequency, 30)
            triggered = days_since_last >= required_days
            
            return {
                'triggered': triggered,
                'type': RebalanceTrigger.TIME_BASED,
                'reason': f'距离上次再平衡{days_since_last}天',
                'urgency': 'low' if days_since_last < required_days * 1.5 else 'medium'
            }
            
        except Exception as e:
            logger.error(f"❌ 检查时间触发失败: {e}")
            return {'triggered': False}
    
    def _check_weight_drift_trigger(self,
                                   current_weights: Dict[str, float],
                                   target_weights: Dict[str, float]) -> Dict[str, Any]:
        """检查权重偏离触发条件"""
        try:
            max_drift = 0
            drift_symbol = None
            
            all_symbols = set(list(current_weights.keys()) + list(target_weights.keys()))
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = target_weights.get(symbol, 0)
                drift = abs(current_weight - target_weight)
                
                if drift > max_drift:
                    max_drift = drift
                    drift_symbol = symbol
            
            triggered = max_drift > self.weight_threshold
            
            if triggered:
                urgency = 'high' if max_drift > self.weight_threshold * 2 else 'medium'
            else:
                urgency = 'low'
            
            return {
                'triggered': triggered,
                'type': RebalanceTrigger.THRESHOLD_BASED,
                'reason': f'{drift_symbol}权重偏离{max_drift:.2%}',
                'max_drift': max_drift,
                'drift_symbol': drift_symbol,
                'urgency': urgency
            }
            
        except Exception as e:
            logger.error(f"❌ 检查权重偏离触发失败: {e}")
            return {'triggered': False}
    
    def _check_volatility_trigger(self, portfolio_performance: Dict[str, Any]) -> Dict[str, Any]:
        """检查波动率触发条件"""
        try:
            current_volatility = portfolio_performance.get('volatility', 0)
            triggered = current_volatility > self.volatility_threshold
            
            return {
                'triggered': triggered,
                'type': RebalanceTrigger.VOLATILITY_BASED,
                'reason': f'组合波动率{current_volatility:.2%}过高',
                'current_volatility': current_volatility,
                'urgency': 'high' if current_volatility > self.volatility_threshold * 1.5 else 'medium'
            }
            
        except Exception as e:
            logger.error(f"❌ 检查波动率触发失败: {e}")
            return {'triggered': False}
    
    def _check_performance_trigger(self, portfolio_performance: Dict[str, Any]) -> Dict[str, Any]:
        """检查绩效触发条件"""
        try:
            current_return = portfolio_performance.get('total_return', 0)
            triggered = current_return < self.performance_threshold
            
            return {
                'triggered': triggered,
                'type': RebalanceTrigger.PERFORMANCE_BASED,
                'reason': f'组合收益率{current_return:.2%}过低',
                'current_return': current_return,
                'urgency': 'high' if current_return < self.performance_threshold * 2 else 'medium'
            }
            
        except Exception as e:
            logger.error(f"❌ 检查绩效触发失败: {e}")
            return {'triggered': False}
    
    def _calculate_portfolio_drift(self,
                                  current_weights: Dict[str, float],
                                  target_weights: Dict[str, float]) -> float:
        """计算组合偏离度"""
        try:
            all_symbols = set(list(current_weights.keys()) + list(target_weights.keys()))
            
            total_drift = 0
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = target_weights.get(symbol, 0)
                drift = abs(current_weight - target_weight)
                total_drift += drift
            
            # 总偏离度除以2（因为买入和卖出的偏离会重复计算）
            portfolio_drift = total_drift / 2
            
            return portfolio_drift
            
        except Exception as e:
            logger.error(f"❌ 计算组合偏离度失败: {e}")
            return 0
    
    def _generate_rebalance_actions(self,
                                   current_portfolio: Dict[str, Dict[str, Any]],
                                   current_weights: Dict[str, float],
                                   target_weights: Dict[str, float],
                                   current_prices: Dict[str, float],
                                   total_portfolio_value: float) -> List[RebalanceAction]:
        """生成再平衡操作"""
        try:
            actions = []
            all_symbols = set(list(current_weights.keys()) + list(target_weights.keys()))
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = target_weights.get(symbol, 0)
                weight_diff = target_weight - current_weight
                
                # 忽略微小差异
                if abs(weight_diff) < 0.001:  # 0.1%
                    continue
                
                current_value = current_weight * total_portfolio_value
                target_value = target_weight * total_portfolio_value
                trade_value = target_value - current_value
                
                # 忽略小额交易
                if abs(trade_value) < self.min_trade_value:
                    continue
                
                current_price = current_prices.get(symbol, 0)
                if current_price == 0:
                    continue
                
                # 计算交易股数
                shares_to_trade = int(trade_value / current_price / 100) * 100  # 按手取整
                actual_trade_value = shares_to_trade * current_price
                
                if shares_to_trade == 0:
                    continue
                
                # 确定操作类型
                if shares_to_trade > 0:
                    action_type = 'buy'
                    reason = f"增持至目标权重{target_weight:.2%}"
                    priority = 2
                else:
                    action_type = 'sell'
                    reason = f"减持至目标权重{target_weight:.2%}"
                    priority = 3
                
                # 特殊情况处理
                if target_weight == 0:
                    action_type = 'sell'
                    reason = "清仓退出"
                    priority = 4
                elif current_weight == 0:
                    action_type = 'buy'
                    reason = "新建仓位"
                    priority = 1
                
                action = RebalanceAction(
                    symbol=symbol,
                    action=action_type,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    weight_diff=weight_diff,
                    current_value=current_value,
                    target_value=target_value,
                    shares_to_trade=abs(shares_to_trade),
                    trade_value=actual_trade_value,
                    reason=reason,
                    priority=priority
                )
                
                actions.append(action)
            
            return actions
            
        except Exception as e:
            logger.error(f"❌ 生成再平衡操作失败: {e}")
            return []
    
    def _optimize_rebalance_actions(self, actions: List[RebalanceAction]) -> List[RebalanceAction]:
        """优化再平衡操作顺序"""
        try:
            # 按优先级排序：清仓 > 新建 > 增持 > 减持
            actions.sort(key=lambda x: x.priority, reverse=True)
            
            return actions
            
        except Exception as e:
            logger.error(f"❌ 优化再平衡操作失败: {e}")
            return actions
    
    def _reduce_turnover(self,
                        actions: List[RebalanceAction],
                        total_portfolio_value: float) -> List[RebalanceAction]:
        """降低换手率"""
        try:
            # 按交易金额排序，优先执行大额交易
            actions.sort(key=lambda x: abs(x.trade_value), reverse=True)
            
            reduced_actions = []
            cumulative_turnover = 0
            max_turnover_value = self.max_turnover * total_portfolio_value
            
            for action in actions:
                if cumulative_turnover + abs(action.trade_value) <= max_turnover_value:
                    reduced_actions.append(action)
                    cumulative_turnover += abs(action.trade_value)
                else:
                    # 部分执行
                    remaining_capacity = max_turnover_value - cumulative_turnover
                    if remaining_capacity > self.min_trade_value:
                        # 调整交易规模
                        scale_factor = remaining_capacity / abs(action.trade_value)
                        action.shares_to_trade = int(action.shares_to_trade * scale_factor / 100) * 100
                        action.trade_value = action.shares_to_trade * (action.trade_value / abs(action.trade_value))
                        
                        if action.shares_to_trade > 0:
                            reduced_actions.append(action)
                    
                    break
            
            logger.info(f"📉 换手率优化: {len(actions)} -> {len(reduced_actions)} 个操作")
            
            return reduced_actions
            
        except Exception as e:
            logger.error(f"❌ 降低换手率失败: {e}")
            return actions
    
    def _estimate_rebalance_benefit(self,
                                   current_weights: Dict[str, float],
                                   target_weights: Dict[str, float],
                                   transaction_cost: float,
                                   total_portfolio_value: float) -> float:
        """估计再平衡收益"""
        try:
            # 简化估计：基于权重偏离度和交易成本
            portfolio_drift = self._calculate_portfolio_drift(current_weights, target_weights)
            
            # 假设再平衡能够带来的年化收益改善
            drift_benefit = portfolio_drift * 0.5  # 偏离度的50%作为潜在收益改善
            
            # 扣除交易成本
            cost_ratio = transaction_cost / total_portfolio_value
            net_benefit = drift_benefit - cost_ratio
            
            return net_benefit
            
        except Exception as e:
            logger.error(f"❌ 估计再平衡收益失败: {e}")
            return 0
    
    def get_rebalance_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取再平衡历史"""
        try:
            history = []
            
            for result in self.rebalance_history[-limit:]:
                history.append({
                    'date': result.rebalance_date.isoformat(),
                    'trigger_reason': result.trigger_reason,
                    'actions_count': len(result.actions),
                    'total_turnover': result.total_turnover,
                    'transaction_cost': result.transaction_cost,
                    'expected_improvement': result.expected_improvement,
                    'portfolio_drift': result.portfolio_drift
                })
            
            return history
            
        except Exception as e:
            logger.error(f"❌ 获取再平衡历史失败: {e}")
            return []
    
    def get_rebalance_settings(self) -> Dict[str, Any]:
        """获取再平衡设置"""
        return {
            'frequency': self.frequency.value,
            'weight_threshold': self.weight_threshold,
            'min_trade_value': self.min_trade_value,
            'transaction_cost_rate': self.transaction_cost_rate,
            'max_turnover': self.max_turnover,
            'volatility_threshold': self.volatility_threshold,
            'performance_threshold': self.performance_threshold,
            'last_rebalance_date': self.last_rebalance_date.isoformat() if self.last_rebalance_date else None,
            'rebalance_count': len(self.rebalance_history)
        }
