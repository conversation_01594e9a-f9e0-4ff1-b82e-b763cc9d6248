"""
绩效归因分析器
分析投资组合收益的来源和贡献
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AttributionResult:
    """归因分析结果"""
    symbol: str
    weight: float
    return_contribution: float
    excess_return: float
    selection_effect: float
    allocation_effect: float
    interaction_effect: float
    total_contribution: float

@dataclass
class SectorAttribution:
    """行业归因"""
    sector: str
    portfolio_weight: float
    benchmark_weight: float
    portfolio_return: float
    benchmark_return: float
    allocation_effect: float
    selection_effect: float
    interaction_effect: float
    total_effect: float

class AttributionAnalyzer:
    """绩效归因分析器"""
    
    def __init__(self):
        # 基准设置
        self.benchmark_return = 0.08        # 基准年化收益8%
        self.risk_free_rate = 0.03          # 无风险利率3%
        
        # 归因分析参数
        self.attribution_period = 252       # 归因分析周期（交易日）
        self.min_weight_threshold = 0.001   # 最小权重阈值
        
        logger.info("📊 绩效归因分析器初始化完成")
        logger.info(f"  - 基准收益率: {self.benchmark_return:.1%}")
        logger.info(f"  - 无风险利率: {self.risk_free_rate:.1%}")
    
    def analyze_performance_attribution(self,
                                      portfolio_data: Dict[str, Any],
                                      benchmark_data: Dict[str, Any] = None,
                                      period_days: int = 252) -> Dict[str, Any]:
        """
        分析绩效归因
        
        Args:
            portfolio_data: 组合数据
            benchmark_data: 基准数据
            period_days: 分析周期天数
            
        Returns:
            归因分析结果
        """
        try:
            logger.info("📊 开始绩效归因分析...")
            
            # 提取组合信息
            portfolio_stocks = portfolio_data.get('stocks', [])
            portfolio_return = portfolio_data.get('total_return', 0)
            
            if not portfolio_stocks:
                logger.error("❌ 组合股票数据为空")
                return {}
            
            # 股票级别归因
            stock_attributions = self._analyze_stock_attribution(
                portfolio_stocks, portfolio_return, period_days
            )
            
            # 行业级别归因
            sector_attributions = self._analyze_sector_attribution(
                portfolio_stocks, benchmark_data, period_days
            )
            
            # 风格归因
            style_attribution = self._analyze_style_attribution(
                portfolio_stocks, portfolio_return
            )
            
            # 因子归因
            factor_attribution = self._analyze_factor_attribution(
                portfolio_stocks, portfolio_return
            )
            
            # 综合归因摘要
            attribution_summary = self._generate_attribution_summary(
                stock_attributions, sector_attributions, style_attribution, factor_attribution
            )
            
            result = {
                'analysis_date': datetime.now().isoformat(),
                'analysis_period_days': period_days,
                'portfolio_return': portfolio_return,
                'benchmark_return': self.benchmark_return,
                'excess_return': portfolio_return - self.benchmark_return,
                'stock_attributions': stock_attributions,
                'sector_attributions': sector_attributions,
                'style_attribution': style_attribution,
                'factor_attribution': factor_attribution,
                'attribution_summary': attribution_summary
            }
            
            logger.info("✅ 绩效归因分析完成")
            logger.info(f"  - 组合收益: {portfolio_return:.2%}")
            logger.info(f"  - 超额收益: {portfolio_return - self.benchmark_return:.2%}")
            logger.info(f"  - 分析股票: {len(stock_attributions)}只")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 绩效归因分析失败: {e}")
            return {}
    
    def _analyze_stock_attribution(self,
                                  portfolio_stocks: List[Dict[str, Any]],
                                  portfolio_return: float,
                                  period_days: int) -> List[Dict[str, Any]]:
        """分析股票级别归因"""
        try:
            stock_attributions = []
            
            for stock in portfolio_stocks:
                symbol = stock.get('symbol', '')
                weight = stock.get('weight', 0)
                
                if weight < self.min_weight_threshold:
                    continue
                
                # 估计股票收益率
                stock_return = self._estimate_stock_return(stock, period_days)
                
                # 计算收益贡献
                return_contribution = weight * stock_return
                
                # 计算超额收益
                excess_return = stock_return - self.benchmark_return
                
                # 选股效应（简化计算）
                selection_effect = weight * excess_return
                
                # 配置效应（假设基准权重为等权重）
                benchmark_weight = 1.0 / len(portfolio_stocks)  # 简化处理
                allocation_effect = (weight - benchmark_weight) * self.benchmark_return
                
                # 交互效应
                interaction_effect = (weight - benchmark_weight) * excess_return
                
                # 总贡献
                total_contribution = return_contribution
                
                attribution = {
                    'symbol': symbol,
                    'weight': weight,
                    'stock_return': stock_return,
                    'return_contribution': return_contribution,
                    'excess_return': excess_return,
                    'selection_effect': selection_effect,
                    'allocation_effect': allocation_effect,
                    'interaction_effect': interaction_effect,
                    'total_contribution': total_contribution,
                    'attribution_rank': 0  # 后续排序
                }
                
                stock_attributions.append(attribution)
            
            # 按贡献度排序
            stock_attributions.sort(key=lambda x: x['total_contribution'], reverse=True)
            
            # 添加排名
            for i, attribution in enumerate(stock_attributions, 1):
                attribution['attribution_rank'] = i
            
            logger.info(f"📈 股票归因分析完成: {len(stock_attributions)}只股票")
            
            return stock_attributions
            
        except Exception as e:
            logger.error(f"❌ 股票归因分析失败: {e}")
            return []
    
    def _analyze_sector_attribution(self,
                                   portfolio_stocks: List[Dict[str, Any]],
                                   benchmark_data: Dict[str, Any],
                                   period_days: int) -> List[Dict[str, Any]]:
        """分析行业级别归因"""
        try:
            # 按行业分组
            sector_groups = {}
            
            for stock in portfolio_stocks:
                sector = stock.get('sector', '其他')
                if sector not in sector_groups:
                    sector_groups[sector] = []
                sector_groups[sector].append(stock)
            
            sector_attributions = []
            
            for sector, stocks in sector_groups.items():
                # 计算行业权重
                sector_weight = sum(stock.get('weight', 0) for stock in stocks)
                
                # 计算行业收益率（加权平均）
                sector_return = 0
                for stock in stocks:
                    stock_weight = stock.get('weight', 0)
                    stock_return = self._estimate_stock_return(stock, period_days)
                    sector_return += (stock_weight / sector_weight) * stock_return if sector_weight > 0 else 0
                
                # 基准行业权重（简化处理）
                benchmark_sector_weight = 1.0 / len(sector_groups)
                benchmark_sector_return = self.benchmark_return  # 简化处理
                
                # 配置效应
                allocation_effect = (sector_weight - benchmark_sector_weight) * benchmark_sector_return
                
                # 选股效应
                selection_effect = sector_weight * (sector_return - benchmark_sector_return)
                
                # 交互效应
                interaction_effect = (sector_weight - benchmark_sector_weight) * (sector_return - benchmark_sector_return)
                
                # 总效应
                total_effect = allocation_effect + selection_effect + interaction_effect
                
                sector_attribution = {
                    'sector': sector,
                    'portfolio_weight': sector_weight,
                    'benchmark_weight': benchmark_sector_weight,
                    'portfolio_return': sector_return,
                    'benchmark_return': benchmark_sector_return,
                    'allocation_effect': allocation_effect,
                    'selection_effect': selection_effect,
                    'interaction_effect': interaction_effect,
                    'total_effect': total_effect,
                    'stock_count': len(stocks)
                }
                
                sector_attributions.append(sector_attribution)
            
            # 按总效应排序
            sector_attributions.sort(key=lambda x: x['total_effect'], reverse=True)
            
            logger.info(f"🏭 行业归因分析完成: {len(sector_attributions)}个行业")
            
            return sector_attributions
            
        except Exception as e:
            logger.error(f"❌ 行业归因分析失败: {e}")
            return []
    
    def _analyze_style_attribution(self,
                                  portfolio_stocks: List[Dict[str, Any]],
                                  portfolio_return: float) -> Dict[str, Any]:
        """分析风格归因"""
        try:
            # 风格分类
            growth_stocks = []
            value_stocks = []
            large_cap_stocks = []
            small_cap_stocks = []
            
            for stock in portfolio_stocks:
                weight = stock.get('weight', 0)
                score = stock.get('score', 70)
                market_cap = stock.get('market_cap', 100)
                
                # 成长vs价值分类（基于评分）
                if score >= 80:
                    growth_stocks.append((stock, weight))
                else:
                    value_stocks.append((stock, weight))
                
                # 大盘vs小盘分类（基于市值）
                if market_cap >= 100:  # 100亿以上为大盘股
                    large_cap_stocks.append((stock, weight))
                else:
                    small_cap_stocks.append((stock, weight))
            
            # 计算风格权重和收益
            style_metrics = {}
            
            # 成长vs价值
            growth_weight = sum(weight for _, weight in growth_stocks)
            value_weight = sum(weight for _, weight in value_stocks)
            
            growth_return = self._calculate_style_return(growth_stocks)
            value_return = self._calculate_style_return(value_stocks)
            
            # 大盘vs小盘
            large_cap_weight = sum(weight for _, weight in large_cap_stocks)
            small_cap_weight = sum(weight for _, weight in small_cap_stocks)
            
            large_cap_return = self._calculate_style_return(large_cap_stocks)
            small_cap_return = self._calculate_style_return(small_cap_stocks)
            
            style_attribution = {
                'growth_vs_value': {
                    'growth_weight': growth_weight,
                    'value_weight': value_weight,
                    'growth_return': growth_return,
                    'value_return': value_return,
                    'growth_contribution': growth_weight * growth_return,
                    'value_contribution': value_weight * value_return,
                    'style_bias': growth_weight - value_weight
                },
                'size_factor': {
                    'large_cap_weight': large_cap_weight,
                    'small_cap_weight': small_cap_weight,
                    'large_cap_return': large_cap_return,
                    'small_cap_return': small_cap_return,
                    'large_cap_contribution': large_cap_weight * large_cap_return,
                    'small_cap_contribution': small_cap_weight * small_cap_return,
                    'size_bias': large_cap_weight - small_cap_weight
                }
            }
            
            logger.info("🎨 风格归因分析完成")
            
            return style_attribution
            
        except Exception as e:
            logger.error(f"❌ 风格归因分析失败: {e}")
            return {}
    
    def _analyze_factor_attribution(self,
                                   portfolio_stocks: List[Dict[str, Any]],
                                   portfolio_return: float) -> Dict[str, Any]:
        """分析因子归因"""
        try:
            # 因子暴露度计算
            factor_exposures = {
                'quality': 0,      # 质量因子
                'momentum': 0,     # 动量因子
                'value': 0,        # 价值因子
                'growth': 0,       # 成长因子
                'volatility': 0    # 波动率因子
            }
            
            total_weight = 0
            
            for stock in portfolio_stocks:
                weight = stock.get('weight', 0)
                total_weight += weight
                
                # 基于股票特征计算因子暴露
                score = stock.get('score', 70)
                technical_score = stock.get('technical_score', 50)
                fundamental_score = stock.get('fundamental_score', 50)
                
                # 质量因子（基于综合评分）
                quality_exposure = (score - 70) / 30  # 标准化到-1到1
                factor_exposures['quality'] += weight * quality_exposure
                
                # 动量因子（基于技术评分）
                momentum_exposure = (technical_score - 50) / 50
                factor_exposures['momentum'] += weight * momentum_exposure
                
                # 价值因子（基于基本面评分的反向）
                value_exposure = -(fundamental_score - 50) / 50
                factor_exposures['value'] += weight * value_exposure
                
                # 成长因子（基于基本面评分）
                growth_exposure = (fundamental_score - 50) / 50
                factor_exposures['growth'] += weight * growth_exposure
                
                # 波动率因子（简化处理）
                volatility_exposure = -0.1  # 假设低波动率偏好
                factor_exposures['volatility'] += weight * volatility_exposure
            
            # 归一化因子暴露度
            if total_weight > 0:
                for factor in factor_exposures:
                    factor_exposures[factor] /= total_weight
            
            # 估计因子收益率（简化处理）
            factor_returns = {
                'quality': 0.02,      # 质量因子年化收益2%
                'momentum': 0.03,     # 动量因子年化收益3%
                'value': 0.01,        # 价值因子年化收益1%
                'growth': 0.04,       # 成长因子年化收益4%
                'volatility': 0.015   # 低波动率因子年化收益1.5%
            }
            
            # 计算因子贡献
            factor_contributions = {}
            total_factor_contribution = 0
            
            for factor in factor_exposures:
                contribution = factor_exposures[factor] * factor_returns[factor]
                factor_contributions[factor] = contribution
                total_factor_contribution += contribution
            
            # 特异性收益（无法用因子解释的部分）
            idiosyncratic_return = portfolio_return - self.benchmark_return - total_factor_contribution
            
            factor_attribution = {
                'factor_exposures': factor_exposures,
                'factor_returns': factor_returns,
                'factor_contributions': factor_contributions,
                'total_factor_contribution': total_factor_contribution,
                'idiosyncratic_return': idiosyncratic_return,
                'explained_return': total_factor_contribution,
                'explanation_ratio': abs(total_factor_contribution) / abs(portfolio_return - self.benchmark_return) if portfolio_return != self.benchmark_return else 0
            }
            
            logger.info("🧮 因子归因分析完成")
            
            return factor_attribution
            
        except Exception as e:
            logger.error(f"❌ 因子归因分析失败: {e}")
            return {}
    
    def _estimate_stock_return(self, stock: Dict[str, Any], period_days: int) -> float:
        """估计股票收益率"""
        try:
            # 基于股票评分估计收益率
            score = stock.get('score', 70)
            base_return = 0.08  # 8%基础收益
            
            # 评分调整
            score_adjustment = (score - 70) * 0.002  # 每分增加0.2%收益
            
            # 随机波动（模拟市场不确定性）
            import random
            random.seed(hash(stock.get('symbol', '')) % 2**32)
            random_factor = random.uniform(-0.05, 0.05)  # ±5%随机波动
            
            estimated_return = base_return + score_adjustment + random_factor
            
            # 年化调整
            if period_days != 252:
                estimated_return = estimated_return * (period_days / 252)
            
            return estimated_return
            
        except Exception as e:
            logger.error(f"❌ 估计股票收益率失败: {e}")
            return 0.08
    
    def _calculate_style_return(self, style_stocks: List[Tuple[Dict[str, Any], float]]) -> float:
        """计算风格收益率"""
        try:
            if not style_stocks:
                return 0
            
            total_weight = sum(weight for _, weight in style_stocks)
            if total_weight == 0:
                return 0
            
            weighted_return = 0
            for stock, weight in style_stocks:
                stock_return = self._estimate_stock_return(stock, 252)
                weighted_return += (weight / total_weight) * stock_return
            
            return weighted_return
            
        except Exception as e:
            logger.error(f"❌ 计算风格收益率失败: {e}")
            return 0
    
    def _generate_attribution_summary(self,
                                     stock_attributions: List[Dict[str, Any]],
                                     sector_attributions: List[Dict[str, Any]],
                                     style_attribution: Dict[str, Any],
                                     factor_attribution: Dict[str, Any]) -> Dict[str, Any]:
        """生成归因摘要"""
        try:
            # 股票贡献摘要
            top_contributors = stock_attributions[:5] if stock_attributions else []
            bottom_contributors = stock_attributions[-3:] if len(stock_attributions) >= 3 else []
            
            total_stock_contribution = sum(attr['total_contribution'] for attr in stock_attributions)
            
            # 行业贡献摘要
            top_sector = sector_attributions[0] if sector_attributions else None
            total_sector_effect = sum(attr['total_effect'] for attr in sector_attributions)
            
            # 风格贡献摘要
            growth_contribution = style_attribution.get('growth_vs_value', {}).get('growth_contribution', 0)
            value_contribution = style_attribution.get('growth_vs_value', {}).get('value_contribution', 0)
            
            # 因子贡献摘要
            top_factor = max(factor_attribution.get('factor_contributions', {}).items(), 
                           key=lambda x: abs(x[1]), default=('无', 0))
            
            summary = {
                'stock_level': {
                    'top_contributors': [
                        {
                            'symbol': attr['symbol'],
                            'contribution': attr['total_contribution'],
                            'weight': attr['weight']
                        }
                        for attr in top_contributors
                    ],
                    'bottom_contributors': [
                        {
                            'symbol': attr['symbol'],
                            'contribution': attr['total_contribution'],
                            'weight': attr['weight']
                        }
                        for attr in bottom_contributors
                    ],
                    'total_contribution': total_stock_contribution
                },
                'sector_level': {
                    'best_sector': {
                        'sector': top_sector['sector'] if top_sector else '无',
                        'effect': top_sector['total_effect'] if top_sector else 0
                    },
                    'total_sector_effect': total_sector_effect
                },
                'style_level': {
                    'growth_vs_value_bias': growth_contribution - value_contribution,
                    'dominant_style': 'growth' if growth_contribution > value_contribution else 'value'
                },
                'factor_level': {
                    'dominant_factor': top_factor[0],
                    'dominant_factor_contribution': top_factor[1],
                    'total_factor_contribution': factor_attribution.get('total_factor_contribution', 0),
                    'idiosyncratic_return': factor_attribution.get('idiosyncratic_return', 0)
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 生成归因摘要失败: {e}")
            return {}
    
    def generate_attribution_report(self, attribution_result: Dict[str, Any]) -> str:
        """生成归因分析报告"""
        try:
            if not attribution_result:
                return "归因分析报告生成失败"
            
            report = []
            report.append("=" * 60)
            report.append("📊 投资组合绩效归因分析报告")
            report.append("=" * 60)
            
            # 基本信息
            portfolio_return = attribution_result.get('portfolio_return', 0)
            benchmark_return = attribution_result.get('benchmark_return', 0)
            excess_return = attribution_result.get('excess_return', 0)
            
            report.append(f"\n📈 绩效概览:")
            report.append(f"  组合收益率: {portfolio_return:.2%}")
            report.append(f"  基准收益率: {benchmark_return:.2%}")
            report.append(f"  超额收益率: {excess_return:.2%}")
            
            # 股票贡献
            stock_attributions = attribution_result.get('stock_attributions', [])
            if stock_attributions:
                report.append(f"\n🏆 主要贡献股票:")
                for i, attr in enumerate(stock_attributions[:5], 1):
                    report.append(f"  {i}. {attr['symbol']}: 贡献{attr['total_contribution']:.2%} (权重{attr['weight']:.2%})")
            
            # 行业贡献
            sector_attributions = attribution_result.get('sector_attributions', [])
            if sector_attributions:
                report.append(f"\n🏭 行业贡献:")
                for attr in sector_attributions[:3]:
                    report.append(f"  {attr['sector']}: 总效应{attr['total_effect']:.2%}")
            
            # 因子贡献
            factor_attribution = attribution_result.get('factor_attribution', {})
            if factor_attribution:
                factor_contributions = factor_attribution.get('factor_contributions', {})
                report.append(f"\n🧮 因子贡献:")
                for factor, contribution in sorted(factor_contributions.items(), 
                                                 key=lambda x: abs(x[1]), reverse=True):
                    report.append(f"  {factor}: {contribution:.2%}")
            
            report.append("\n" + "=" * 60)
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"❌ 生成归因报告失败: {e}")
            return "归因分析报告生成失败"
