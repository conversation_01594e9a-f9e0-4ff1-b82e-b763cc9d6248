"""
投资组合优化器
实现现代投资组合理论的优化算法
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum
import scipy.optimize as opt

logger = logging.getLogger(__name__)

class OptimizationObjective(Enum):
    """优化目标"""
    MAX_SHARPE = "MAX_SHARPE"           # 最大夏普比率
    MIN_VARIANCE = "MIN_VARIANCE"       # 最小方差
    MAX_RETURN = "MAX_RETURN"           # 最大收益
    RISK_PARITY = "RISK_PARITY"         # 风险平价
    MAX_UTILITY = "MAX_UTILITY"         # 最大效用

@dataclass
class OptimizationConstraint:
    """优化约束"""
    constraint_type: str    # 'weight', 'sector', 'turnover', 'tracking_error'
    constraint_value: float
    constraint_bound: str   # 'upper', 'lower', 'equal'
    symbols: List[str] = None

@dataclass
class OptimizationResult:
    """优化结果"""
    optimal_weights: Dict[str, float]
    expected_return: float
    expected_risk: float
    sharpe_ratio: float
    optimization_success: bool
    objective_value: float
    constraints_satisfied: bool
    optimization_message: str

class PortfolioOptimizer:
    """投资组合优化器"""
    
    def __init__(self):
        # 优化参数
        self.risk_free_rate = 0.03          # 无风险利率3%
        self.risk_aversion = 2.0            # 风险厌恶系数
        self.max_iterations = 1000          # 最大迭代次数
        self.tolerance = 1e-8               # 收敛容差
        
        # 默认约束
        self.default_constraints = {
            'max_weight': 0.15,             # 单股最大权重15%
            'min_weight': 0.01,             # 单股最小权重1%
            'max_sector_weight': 0.40,      # 单行业最大权重40%
            'max_turnover': 0.50,           # 最大换手率50%
            'max_tracking_error': 0.05      # 最大跟踪误差5%
        }
        
        logger.info("🎯 投资组合优化器初始化完成")
        logger.info(f"  - 无风险利率: {self.risk_free_rate:.1%}")
        logger.info(f"  - 风险厌恶系数: {self.risk_aversion}")
    
    def optimize_portfolio(self,
                          stocks_data: List[Dict[str, Any]],
                          objective: OptimizationObjective,
                          constraints: List[OptimizationConstraint] = None,
                          current_weights: Dict[str, float] = None) -> OptimizationResult:
        """
        优化投资组合
        
        Args:
            stocks_data: 股票数据列表
            objective: 优化目标
            constraints: 约束条件列表
            current_weights: 当前权重（用于换手率约束）
            
        Returns:
            优化结果
        """
        try:
            logger.info(f"🎯 开始投资组合优化: {objective.value}")
            
            if not stocks_data:
                logger.error("❌ 股票数据为空")
                return self._create_failed_result("股票数据为空")
            
            # 准备优化数据
            symbols = [stock['symbol'] for stock in stocks_data]
            expected_returns = self._extract_expected_returns(stocks_data)
            covariance_matrix = self._estimate_covariance_matrix(stocks_data)
            
            # 设置约束条件
            optimization_constraints = self._setup_constraints(
                symbols, constraints, current_weights
            )
            
            # 设置边界条件
            bounds = self._setup_bounds(symbols, constraints)
            
            # 初始权重
            initial_weights = self._get_initial_weights(symbols, current_weights)
            
            # 执行优化
            if objective == OptimizationObjective.MAX_SHARPE:
                result = self._optimize_max_sharpe(
                    expected_returns, covariance_matrix, optimization_constraints, bounds, initial_weights
                )
            elif objective == OptimizationObjective.MIN_VARIANCE:
                result = self._optimize_min_variance(
                    covariance_matrix, optimization_constraints, bounds, initial_weights
                )
            elif objective == OptimizationObjective.MAX_RETURN:
                result = self._optimize_max_return(
                    expected_returns, optimization_constraints, bounds, initial_weights
                )
            elif objective == OptimizationObjective.RISK_PARITY:
                result = self._optimize_risk_parity(
                    covariance_matrix, optimization_constraints, bounds, initial_weights
                )
            elif objective == OptimizationObjective.MAX_UTILITY:
                result = self._optimize_max_utility(
                    expected_returns, covariance_matrix, optimization_constraints, bounds, initial_weights
                )
            else:
                logger.error(f"❌ 不支持的优化目标: {objective}")
                return self._create_failed_result(f"不支持的优化目标: {objective}")
            
            # 处理优化结果
            if result.success:
                optimal_weights_dict = dict(zip(symbols, result.x))
                
                # 计算组合指标
                portfolio_return = np.dot(expected_returns, result.x)
                portfolio_risk = np.sqrt(np.dot(result.x, np.dot(covariance_matrix, result.x)))
                sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
                
                # 检查约束满足情况
                constraints_satisfied = self._check_constraints_satisfaction(
                    optimal_weights_dict, constraints, current_weights
                )
                
                optimization_result = OptimizationResult(
                    optimal_weights=optimal_weights_dict,
                    expected_return=portfolio_return,
                    expected_risk=portfolio_risk,
                    sharpe_ratio=sharpe_ratio,
                    optimization_success=True,
                    objective_value=result.fun,
                    constraints_satisfied=constraints_satisfied,
                    optimization_message=result.message
                )
                
                logger.info("✅ 投资组合优化成功")
                logger.info(f"  - 预期收益: {portfolio_return:.2%}")
                logger.info(f"  - 预期风险: {portfolio_risk:.2%}")
                logger.info(f"  - 夏普比率: {sharpe_ratio:.2f}")
                
                return optimization_result
            
            else:
                logger.error(f"❌ 优化失败: {result.message}")
                return self._create_failed_result(result.message)
                
        except Exception as e:
            logger.error(f"❌ 投资组合优化失败: {e}")
            return self._create_failed_result(str(e))
    
    def _extract_expected_returns(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """提取预期收益率"""
        try:
            expected_returns = []
            
            for stock in stocks_data:
                # 基于评分估计预期收益
                score = stock.get('total_score', 70)
                base_return = 0.08  # 8%基础收益
                score_adjustment = (score - 70) * 0.002  # 每分增加0.2%收益
                expected_return = base_return + score_adjustment
                
                expected_returns.append(expected_return)
            
            return np.array(expected_returns)
            
        except Exception as e:
            logger.error(f"❌ 提取预期收益率失败: {e}")
            return np.array([0.08] * len(stocks_data))
    
    def _estimate_covariance_matrix(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """估计协方差矩阵"""
        try:
            n_stocks = len(stocks_data)
            
            # 简化处理：基于股票特征估计协方差
            volatilities = []
            for stock in stocks_data:
                # 基于评分估计波动率
                score = stock.get('total_score', 70)
                volatility = 0.25 - (score - 70) * 0.001  # 评分越高波动率越低
                volatility = max(0.1, min(0.4, volatility))  # 限制在10%-40%
                volatilities.append(volatility)
            
            volatilities = np.array(volatilities)
            
            # 构建协方差矩阵
            # 对角线为方差，非对角线为协方差
            covariance_matrix = np.zeros((n_stocks, n_stocks))
            
            for i in range(n_stocks):
                for j in range(n_stocks):
                    if i == j:
                        # 方差
                        covariance_matrix[i, j] = volatilities[i] ** 2
                    else:
                        # 协方差（简化处理：基于行业相关性）
                        sector_i = stocks_data[i].get('sector', '其他')
                        sector_j = stocks_data[j].get('sector', '其他')
                        
                        if sector_i == sector_j:
                            correlation = 0.3  # 同行业相关性30%
                        else:
                            correlation = 0.1  # 不同行业相关性10%
                        
                        covariance_matrix[i, j] = correlation * volatilities[i] * volatilities[j]
            
            return covariance_matrix
            
        except Exception as e:
            logger.error(f"❌ 估计协方差矩阵失败: {e}")
            # 返回单位矩阵作为备选
            n_stocks = len(stocks_data)
            return np.eye(n_stocks) * 0.04  # 20%波动率的方差
    
    def _setup_constraints(self,
                          symbols: List[str],
                          constraints: List[OptimizationConstraint],
                          current_weights: Dict[str, float]) -> List[Dict[str, Any]]:
        """设置约束条件"""
        try:
            optimization_constraints = []
            
            # 权重和为1的约束
            optimization_constraints.append({
                'type': 'eq',
                'fun': lambda x: np.sum(x) - 1.0
            })
            
            # 处理自定义约束
            if constraints:
                for constraint in constraints:
                    if constraint.constraint_type == 'sector':
                        # 行业权重约束
                        sector_indices = [i for i, symbol in enumerate(symbols) 
                                        if symbol in (constraint.symbols or [])]
                        
                        if constraint.constraint_bound == 'upper':
                            optimization_constraints.append({
                                'type': 'ineq',
                                'fun': lambda x, indices=sector_indices: 
                                    constraint.constraint_value - np.sum(x[indices])
                            })
                        elif constraint.constraint_bound == 'lower':
                            optimization_constraints.append({
                                'type': 'ineq',
                                'fun': lambda x, indices=sector_indices: 
                                    np.sum(x[indices]) - constraint.constraint_value
                            })
                    
                    elif constraint.constraint_type == 'turnover' and current_weights:
                        # 换手率约束
                        current_weights_array = np.array([
                            current_weights.get(symbol, 0) for symbol in symbols
                        ])
                        
                        optimization_constraints.append({
                            'type': 'ineq',
                            'fun': lambda x, current=current_weights_array: 
                                constraint.constraint_value - np.sum(np.abs(x - current))
                        })
            
            return optimization_constraints
            
        except Exception as e:
            logger.error(f"❌ 设置约束条件失败: {e}")
            return [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0}]
    
    def _setup_bounds(self,
                     symbols: List[str],
                     constraints: List[OptimizationConstraint]) -> List[Tuple[float, float]]:
        """设置边界条件"""
        try:
            bounds = []
            
            # 默认边界
            min_weight = self.default_constraints['min_weight']
            max_weight = self.default_constraints['max_weight']
            
            # 处理权重约束
            if constraints:
                for constraint in constraints:
                    if constraint.constraint_type == 'weight':
                        if constraint.constraint_bound == 'upper':
                            max_weight = min(max_weight, constraint.constraint_value)
                        elif constraint.constraint_bound == 'lower':
                            min_weight = max(min_weight, constraint.constraint_value)
            
            # 为每只股票设置边界
            for symbol in symbols:
                bounds.append((min_weight, max_weight))
            
            return bounds
            
        except Exception as e:
            logger.error(f"❌ 设置边界条件失败: {e}")
            return [(0.01, 0.15)] * len(symbols)
    
    def _get_initial_weights(self,
                            symbols: List[str],
                            current_weights: Dict[str, float]) -> np.ndarray:
        """获取初始权重"""
        try:
            if current_weights:
                # 使用当前权重作为初始值
                initial_weights = np.array([
                    current_weights.get(symbol, 1.0/len(symbols)) for symbol in symbols
                ])
            else:
                # 等权重初始化
                initial_weights = np.ones(len(symbols)) / len(symbols)
            
            # 确保权重和为1
            initial_weights = initial_weights / np.sum(initial_weights)
            
            return initial_weights
            
        except Exception as e:
            logger.error(f"❌ 获取初始权重失败: {e}")
            return np.ones(len(symbols)) / len(symbols)
    
    def _optimize_max_sharpe(self,
                            expected_returns: np.ndarray,
                            covariance_matrix: np.ndarray,
                            constraints: List[Dict[str, Any]],
                            bounds: List[Tuple[float, float]],
                            initial_weights: np.ndarray) -> opt.OptimizeResult:
        """最大夏普比率优化"""
        try:
            def negative_sharpe(weights):
                portfolio_return = np.dot(expected_returns, weights)
                portfolio_risk = np.sqrt(np.dot(weights, np.dot(covariance_matrix, weights)))
                
                if portfolio_risk == 0:
                    return -np.inf
                
                sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk
                return -sharpe_ratio  # 最小化负夏普比率
            
            result = opt.minimize(
                negative_sharpe,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 最大夏普比率优化失败: {e}")
            raise
    
    def _optimize_min_variance(self,
                              covariance_matrix: np.ndarray,
                              constraints: List[Dict[str, Any]],
                              bounds: List[Tuple[float, float]],
                              initial_weights: np.ndarray) -> opt.OptimizeResult:
        """最小方差优化"""
        try:
            def portfolio_variance(weights):
                return np.dot(weights, np.dot(covariance_matrix, weights))
            
            result = opt.minimize(
                portfolio_variance,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 最小方差优化失败: {e}")
            raise
    
    def _optimize_max_return(self,
                            expected_returns: np.ndarray,
                            constraints: List[Dict[str, Any]],
                            bounds: List[Tuple[float, float]],
                            initial_weights: np.ndarray) -> opt.OptimizeResult:
        """最大收益优化"""
        try:
            def negative_return(weights):
                return -np.dot(expected_returns, weights)
            
            result = opt.minimize(
                negative_return,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 最大收益优化失败: {e}")
            raise
    
    def _optimize_risk_parity(self,
                             covariance_matrix: np.ndarray,
                             constraints: List[Dict[str, Any]],
                             bounds: List[Tuple[float, float]],
                             initial_weights: np.ndarray) -> opt.OptimizeResult:
        """风险平价优化"""
        try:
            def risk_parity_objective(weights):
                # 计算风险贡献
                portfolio_risk = np.sqrt(np.dot(weights, np.dot(covariance_matrix, weights)))
                
                if portfolio_risk == 0:
                    return np.inf
                
                marginal_risk = np.dot(covariance_matrix, weights) / portfolio_risk
                risk_contributions = weights * marginal_risk
                
                # 目标：所有股票的风险贡献相等
                target_risk_contribution = portfolio_risk / len(weights)
                
                # 最小化风险贡献的方差
                return np.sum((risk_contributions - target_risk_contribution) ** 2)
            
            result = opt.minimize(
                risk_parity_objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 风险平价优化失败: {e}")
            raise
    
    def _optimize_max_utility(self,
                             expected_returns: np.ndarray,
                             covariance_matrix: np.ndarray,
                             constraints: List[Dict[str, Any]],
                             bounds: List[Tuple[float, float]],
                             initial_weights: np.ndarray) -> opt.OptimizeResult:
        """最大效用优化"""
        try:
            def negative_utility(weights):
                portfolio_return = np.dot(expected_returns, weights)
                portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))
                
                # 效用函数：U = E(R) - 0.5 * A * Var(R)
                utility = portfolio_return - 0.5 * self.risk_aversion * portfolio_variance
                
                return -utility  # 最小化负效用
            
            result = opt.minimize(
                negative_utility,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 最大效用优化失败: {e}")
            raise
    
    def _check_constraints_satisfaction(self,
                                      optimal_weights: Dict[str, float],
                                      constraints: List[OptimizationConstraint],
                                      current_weights: Dict[str, float]) -> bool:
        """检查约束满足情况"""
        try:
            if not constraints:
                return True
            
            for constraint in constraints:
                if constraint.constraint_type == 'weight':
                    # 检查权重约束
                    for symbol, weight in optimal_weights.items():
                        if constraint.constraint_bound == 'upper' and weight > constraint.constraint_value:
                            return False
                        elif constraint.constraint_bound == 'lower' and weight < constraint.constraint_value:
                            return False
                
                elif constraint.constraint_type == 'sector':
                    # 检查行业约束
                    if constraint.symbols:
                        sector_weight = sum(optimal_weights.get(symbol, 0) for symbol in constraint.symbols)
                        
                        if constraint.constraint_bound == 'upper' and sector_weight > constraint.constraint_value:
                            return False
                        elif constraint.constraint_bound == 'lower' and sector_weight < constraint.constraint_value:
                            return False
                
                elif constraint.constraint_type == 'turnover' and current_weights:
                    # 检查换手率约束
                    turnover = sum(abs(optimal_weights.get(symbol, 0) - current_weights.get(symbol, 0)) 
                                 for symbol in set(list(optimal_weights.keys()) + list(current_weights.keys())))
                    
                    if turnover > constraint.constraint_value:
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查约束满足情况失败: {e}")
            return False
    
    def _create_failed_result(self, message: str) -> OptimizationResult:
        """创建失败结果"""
        return OptimizationResult(
            optimal_weights={},
            expected_return=0,
            expected_risk=0,
            sharpe_ratio=0,
            optimization_success=False,
            objective_value=0,
            constraints_satisfied=False,
            optimization_message=message
        )
    
    def generate_efficient_frontier(self,
                                   stocks_data: List[Dict[str, Any]],
                                   n_points: int = 20) -> Dict[str, Any]:
        """生成有效前沿"""
        try:
            logger.info(f"📈 生成有效前沿: {n_points}个点")
            
            expected_returns = self._extract_expected_returns(stocks_data)
            covariance_matrix = self._estimate_covariance_matrix(stocks_data)
            
            min_return = np.min(expected_returns)
            max_return = np.max(expected_returns)
            
            target_returns = np.linspace(min_return, max_return, n_points)
            
            efficient_portfolios = []
            
            for target_return in target_returns:
                try:
                    # 添加目标收益约束
                    constraints = [
                        {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},
                        {'type': 'eq', 'fun': lambda x, target=target_return: np.dot(expected_returns, x) - target}
                    ]
                    
                    bounds = [(0.01, 0.15)] * len(stocks_data)
                    initial_weights = np.ones(len(stocks_data)) / len(stocks_data)
                    
                    # 最小方差优化
                    result = opt.minimize(
                        lambda weights: np.dot(weights, np.dot(covariance_matrix, weights)),
                        initial_weights,
                        method='SLSQP',
                        bounds=bounds,
                        constraints=constraints,
                        options={'maxiter': self.max_iterations}
                    )
                    
                    if result.success:
                        portfolio_return = np.dot(expected_returns, result.x)
                        portfolio_risk = np.sqrt(np.dot(result.x, np.dot(covariance_matrix, result.x)))
                        sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
                        
                        efficient_portfolios.append({
                            'return': portfolio_return,
                            'risk': portfolio_risk,
                            'sharpe_ratio': sharpe_ratio,
                            'weights': dict(zip([stock['symbol'] for stock in stocks_data], result.x))
                        })
                
                except Exception as e:
                    logger.debug(f"❌ 有效前沿点计算失败: {target_return:.3f} - {e}")
                    continue
            
            logger.info(f"✅ 有效前沿生成完成: {len(efficient_portfolios)}个有效组合")
            
            return {
                'efficient_portfolios': efficient_portfolios,
                'n_points': len(efficient_portfolios),
                'risk_range': [min(p['risk'] for p in efficient_portfolios), 
                              max(p['risk'] for p in efficient_portfolios)] if efficient_portfolios else [0, 0],
                'return_range': [min(p['return'] for p in efficient_portfolios), 
                               max(p['return'] for p in efficient_portfolios)] if efficient_portfolios else [0, 0]
            }
            
        except Exception as e:
            logger.error(f"❌ 生成有效前沿失败: {e}")
            return {'efficient_portfolios': [], 'n_points': 0}
    
    def get_optimizer_settings(self) -> Dict[str, Any]:
        """获取优化器设置"""
        return {
            'risk_free_rate': self.risk_free_rate,
            'risk_aversion': self.risk_aversion,
            'max_iterations': self.max_iterations,
            'tolerance': self.tolerance,
            'default_constraints': self.default_constraints
        }
