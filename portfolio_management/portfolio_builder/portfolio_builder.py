"""
投资组合构建器
基于多因子选股结果构建投资组合
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, DailyMarket
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector

logger = logging.getLogger(__name__)

class PortfolioType(Enum):
    """组合类型"""
    GROWTH = "GROWTH"           # 成长型
    VALUE = "VALUE"             # 价值型
    BALANCED = "BALANCED"       # 平衡型
    MOMENTUM = "MOMENTUM"       # 动量型
    QUALITY = "QUALITY"         # 质量型

@dataclass
class PortfolioStock:
    """组合股票"""
    symbol: str
    name: str
    weight: float
    score: float
    sector: str
    market_cap: float
    expected_return: float
    risk_level: str
    selection_reason: List[str]

@dataclass
class Portfolio:
    """投资组合"""
    portfolio_id: str
    name: str
    portfolio_type: PortfolioType
    creation_date: date
    stocks: List[PortfolioStock]
    total_weight: float
    expected_return: float
    expected_risk: float
    sharpe_ratio: float
    max_drawdown: float
    sector_allocation: Dict[str, float]
    constraints: Dict[str, Any]

class PortfolioBuilder:
    """投资组合构建器"""
    
    def __init__(self):
        self.multi_factor_selector = MultiFactorSelector()
        
        # 组合构建参数
        self.max_stocks = 20                    # 最大持股数量
        self.min_stocks = 5                     # 最小持股数量
        self.max_single_weight = 0.10           # 单股最大权重10%
        self.min_single_weight = 0.02           # 单股最小权重2%
        self.max_sector_weight = 0.30           # 单行业最大权重30%
        self.min_score_threshold = 70.0         # 最小评分阈值
        
        # 风险控制参数
        self.max_portfolio_risk = 0.20          # 最大组合风险20%
        self.target_sharpe_ratio = 1.5          # 目标夏普比率
        self.correlation_threshold = 0.7        # 相关性阈值
        
        logger.info("🏗️ 投资组合构建器初始化完成")
        logger.info(f"  - 最大持股: {self.max_stocks}只")
        logger.info(f"  - 单股最大权重: {self.max_single_weight:.1%}")
        logger.info(f"  - 最小评分: {self.min_score_threshold}")
    
    def build_portfolio(self,
                       portfolio_type: PortfolioType,
                       target_stocks: int = 10,
                       custom_constraints: Dict[str, Any] = None) -> Optional[Portfolio]:
        """
        构建投资组合
        
        Args:
            portfolio_type: 组合类型
            target_stocks: 目标股票数量
            custom_constraints: 自定义约束条件
            
        Returns:
            构建的投资组合
        """
        try:
            logger.info(f"🏗️ 开始构建{portfolio_type.value}投资组合...")
            
            # 1. 获取候选股票池
            candidate_stocks = self._get_candidate_stocks(portfolio_type)
            
            if len(candidate_stocks) < self.min_stocks:
                logger.error(f"❌ 候选股票不足: {len(candidate_stocks)} < {self.min_stocks}")
                return None
            
            # 2. 股票筛选和评分
            selected_stocks = self._select_stocks(candidate_stocks, target_stocks, portfolio_type)
            
            if not selected_stocks:
                logger.error("❌ 股票筛选失败")
                return None
            
            # 3. 权重分配
            portfolio_stocks = self._allocate_weights(selected_stocks, portfolio_type)
            
            # 4. 风险评估
            risk_metrics = self._assess_portfolio_risk(portfolio_stocks)
            
            # 5. 构建组合对象
            portfolio = self._create_portfolio(
                portfolio_type, portfolio_stocks, risk_metrics, custom_constraints
            )
            
            logger.info(f"✅ 投资组合构建完成: {len(portfolio.stocks)}只股票")
            logger.info(f"  - 预期收益: {portfolio.expected_return:.2%}")
            logger.info(f"  - 预期风险: {portfolio.expected_risk:.2%}")
            logger.info(f"  - 夏普比率: {portfolio.sharpe_ratio:.2f}")
            
            return portfolio
            
        except Exception as e:
            logger.error(f"❌ 构建投资组合失败: {e}")
            return None
    
    def _get_candidate_stocks(self, portfolio_type: PortfolioType) -> List[str]:
        """获取候选股票池"""
        try:
            # 根据组合类型设置筛选条件
            if portfolio_type == PortfolioType.GROWTH:
                # 成长型：关注成长性指标
                min_score = 75.0
                focus_factors = ['revenue_growth', 'profit_growth', 'roe']
            elif portfolio_type == PortfolioType.VALUE:
                # 价值型：关注估值指标
                min_score = 70.0
                focus_factors = ['pe_ratio', 'pb_ratio', 'dividend_yield']
            elif portfolio_type == PortfolioType.MOMENTUM:
                # 动量型：关注技术指标
                min_score = 80.0
                focus_factors = ['price_momentum', 'volume_momentum', 'technical_score']
            elif portfolio_type == PortfolioType.QUALITY:
                # 质量型：关注质量指标
                min_score = 75.0
                focus_factors = ['roe', 'debt_ratio', 'profit_stability']
            else:
                # 平衡型：综合考虑
                min_score = 70.0
                focus_factors = []
            
            # 获取全市场股票评分
            all_stocks_scores = self.multi_factor_selector.batch_calculate_scores(
                symbols=None,  # None表示全市场
                min_score=min_score
            )
            
            # 筛选符合条件的股票
            candidate_stocks = []
            for symbol, score_info in all_stocks_scores.items():
                total_score = score_info.get('total_score', 0)
                if total_score >= min_score:
                    candidate_stocks.append(symbol)
            
            logger.info(f"📊 获取候选股票: {len(candidate_stocks)}只")
            
            return candidate_stocks
            
        except Exception as e:
            logger.error(f"❌ 获取候选股票失败: {e}")
            return []
    
    def _select_stocks(self,
                      candidate_stocks: List[str],
                      target_stocks: int,
                      portfolio_type: PortfolioType) -> List[Dict[str, Any]]:
        """选择股票"""
        try:
            # 获取详细评分信息
            stocks_info = []
            
            for symbol in candidate_stocks:
                try:
                    score_result = self.multi_factor_selector.calculate_comprehensive_score(symbol)
                    
                    if not score_result:
                        continue
                    
                    stock_info = {
                        'symbol': symbol,
                        'total_score': score_result.get('total_score', 0),
                        'technical_score': score_result.get('technical_score', 0),
                        'fundamental_score': score_result.get('fundamental_score', 0),
                        'market_score': score_result.get('market_score', 0),
                        'factor_scores': score_result.get('factor_scores', {}),
                        'sector': self._get_stock_sector(symbol),
                        'market_cap': self._get_market_cap(symbol)
                    }
                    
                    stocks_info.append(stock_info)
                    
                except Exception as e:
                    logger.debug(f"❌ 获取股票信息失败: {symbol} - {e}")
                    continue
            
            if not stocks_info:
                return []
            
            # 根据组合类型排序
            if portfolio_type == PortfolioType.GROWTH:
                # 成长型：按成长性排序
                stocks_info.sort(key=lambda x: x['fundamental_score'], reverse=True)
            elif portfolio_type == PortfolioType.VALUE:
                # 价值型：按价值评分排序
                stocks_info.sort(key=lambda x: x['fundamental_score'], reverse=True)
            elif portfolio_type == PortfolioType.MOMENTUM:
                # 动量型：按技术评分排序
                stocks_info.sort(key=lambda x: x['technical_score'], reverse=True)
            else:
                # 其他：按综合评分排序
                stocks_info.sort(key=lambda x: x['total_score'], reverse=True)
            
            # 行业分散化选择
            selected_stocks = self._diversified_selection(stocks_info, target_stocks)
            
            logger.info(f"📈 股票选择完成: {len(selected_stocks)}只")
            
            return selected_stocks
            
        except Exception as e:
            logger.error(f"❌ 股票选择失败: {e}")
            return []
    
    def _diversified_selection(self,
                             stocks_info: List[Dict[str, Any]],
                             target_stocks: int) -> List[Dict[str, Any]]:
        """分散化选择股票"""
        try:
            selected_stocks = []
            sector_counts = {}
            max_per_sector = max(1, target_stocks // 5)  # 每个行业最多选择的股票数
            
            for stock_info in stocks_info:
                if len(selected_stocks) >= target_stocks:
                    break
                
                sector = stock_info['sector']
                sector_count = sector_counts.get(sector, 0)
                
                # 行业分散化约束
                if sector_count >= max_per_sector:
                    continue
                
                selected_stocks.append(stock_info)
                sector_counts[sector] = sector_count + 1
            
            # 如果选择的股票不够，放宽行业约束
            if len(selected_stocks) < target_stocks:
                remaining_stocks = target_stocks - len(selected_stocks)
                selected_symbols = {stock['symbol'] for stock in selected_stocks}
                
                for stock_info in stocks_info:
                    if remaining_stocks <= 0:
                        break
                    
                    if stock_info['symbol'] not in selected_symbols:
                        selected_stocks.append(stock_info)
                        remaining_stocks -= 1
            
            return selected_stocks
            
        except Exception as e:
            logger.error(f"❌ 分散化选择失败: {e}")
            return stocks_info[:target_stocks]
    
    def _allocate_weights(self,
                         selected_stocks: List[Dict[str, Any]],
                         portfolio_type: PortfolioType) -> List[PortfolioStock]:
        """分配权重"""
        try:
            portfolio_stocks = []
            
            if portfolio_type == PortfolioType.BALANCED:
                # 平衡型：等权重分配
                weights = self._equal_weight_allocation(selected_stocks)
            else:
                # 其他类型：基于评分的权重分配
                weights = self._score_based_allocation(selected_stocks, portfolio_type)
            
            # 创建组合股票对象
            for i, stock_info in enumerate(selected_stocks):
                weight = weights[i]
                
                portfolio_stock = PortfolioStock(
                    symbol=stock_info['symbol'],
                    name=self._get_stock_name(stock_info['symbol']),
                    weight=weight,
                    score=stock_info['total_score'],
                    sector=stock_info['sector'],
                    market_cap=stock_info['market_cap'],
                    expected_return=self._estimate_expected_return(stock_info),
                    risk_level=self._assess_stock_risk_level(stock_info),
                    selection_reason=self._generate_selection_reason(stock_info, portfolio_type)
                )
                
                portfolio_stocks.append(portfolio_stock)
            
            # 权重归一化
            total_weight = sum(stock.weight for stock in portfolio_stocks)
            if total_weight > 0:
                for stock in portfolio_stocks:
                    stock.weight = stock.weight / total_weight
            
            logger.info(f"⚖️ 权重分配完成: 总权重{total_weight:.3f}")
            
            return portfolio_stocks
            
        except Exception as e:
            logger.error(f"❌ 权重分配失败: {e}")
            return []
    
    def _equal_weight_allocation(self, selected_stocks: List[Dict[str, Any]]) -> List[float]:
        """等权重分配"""
        n_stocks = len(selected_stocks)
        return [1.0 / n_stocks] * n_stocks
    
    def _score_based_allocation(self,
                               selected_stocks: List[Dict[str, Any]],
                               portfolio_type: PortfolioType) -> List[float]:
        """基于评分的权重分配"""
        try:
            # 根据组合类型选择评分权重
            if portfolio_type == PortfolioType.GROWTH:
                scores = [stock['fundamental_score'] for stock in selected_stocks]
            elif portfolio_type == PortfolioType.MOMENTUM:
                scores = [stock['technical_score'] for stock in selected_stocks]
            else:
                scores = [stock['total_score'] for stock in selected_stocks]
            
            # 评分归一化
            min_score = min(scores)
            max_score = max(scores)
            
            if max_score == min_score:
                return self._equal_weight_allocation(selected_stocks)
            
            # 线性变换到权重范围
            normalized_scores = []
            for score in scores:
                normalized_score = (score - min_score) / (max_score - min_score)
                # 映射到权重范围 [min_weight, max_weight]
                weight = self.min_single_weight + normalized_score * (self.max_single_weight - self.min_single_weight)
                normalized_scores.append(weight)
            
            return normalized_scores
            
        except Exception as e:
            logger.error(f"❌ 基于评分的权重分配失败: {e}")
            return self._equal_weight_allocation(selected_stocks)
    
    def _assess_portfolio_risk(self, portfolio_stocks: List[PortfolioStock]) -> Dict[str, float]:
        """评估组合风险"""
        try:
            # 简化的风险评估
            weights = np.array([stock.weight for stock in portfolio_stocks])
            expected_returns = np.array([stock.expected_return for stock in portfolio_stocks])
            
            # 组合预期收益
            portfolio_return = np.sum(weights * expected_returns)
            
            # 简化的风险计算（基于股票数量和权重分散度）
            n_stocks = len(portfolio_stocks)
            weight_concentration = np.sum(weights ** 2)  # Herfindahl指数
            
            # 基础风险水平
            base_risk = 0.15  # 15%基础风险
            
            # 分散化调整
            diversification_factor = 1.0 - (n_stocks - 1) * 0.02  # 每增加一只股票降低2%风险
            diversification_factor = max(0.5, diversification_factor)
            
            # 集中度调整
            concentration_factor = 1.0 + weight_concentration * 0.5
            
            portfolio_risk = base_risk * diversification_factor * concentration_factor
            
            # 夏普比率
            risk_free_rate = 0.03  # 3%无风险利率
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
            
            # 最大回撤估计
            max_drawdown = portfolio_risk * 1.5  # 简化估计
            
            return {
                'expected_return': portfolio_return,
                'expected_risk': portfolio_risk,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'weight_concentration': weight_concentration
            }
            
        except Exception as e:
            logger.error(f"❌ 评估组合风险失败: {e}")
            return {
                'expected_return': 0.1,
                'expected_risk': 0.15,
                'sharpe_ratio': 1.0,
                'max_drawdown': 0.2,
                'weight_concentration': 0.1
            }
    
    def _create_portfolio(self,
                         portfolio_type: PortfolioType,
                         portfolio_stocks: List[PortfolioStock],
                         risk_metrics: Dict[str, float],
                         custom_constraints: Dict[str, Any] = None) -> Portfolio:
        """创建组合对象"""
        try:
            # 计算行业配置
            sector_allocation = {}
            for stock in portfolio_stocks:
                sector = stock.sector
                sector_allocation[sector] = sector_allocation.get(sector, 0) + stock.weight
            
            # 生成组合ID
            portfolio_id = f"{portfolio_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 组合名称
            type_names = {
                PortfolioType.GROWTH: "成长型组合",
                PortfolioType.VALUE: "价值型组合",
                PortfolioType.BALANCED: "平衡型组合",
                PortfolioType.MOMENTUM: "动量型组合",
                PortfolioType.QUALITY: "质量型组合"
            }
            portfolio_name = type_names.get(portfolio_type, "投资组合")
            
            # 约束条件
            constraints = {
                'max_stocks': self.max_stocks,
                'max_single_weight': self.max_single_weight,
                'max_sector_weight': self.max_sector_weight,
                'min_score_threshold': self.min_score_threshold
            }
            
            if custom_constraints:
                constraints.update(custom_constraints)
            
            portfolio = Portfolio(
                portfolio_id=portfolio_id,
                name=portfolio_name,
                portfolio_type=portfolio_type,
                creation_date=datetime.now().date(),
                stocks=portfolio_stocks,
                total_weight=sum(stock.weight for stock in portfolio_stocks),
                expected_return=risk_metrics['expected_return'],
                expected_risk=risk_metrics['expected_risk'],
                sharpe_ratio=risk_metrics['sharpe_ratio'],
                max_drawdown=risk_metrics['max_drawdown'],
                sector_allocation=sector_allocation,
                constraints=constraints
            )
            
            return portfolio
            
        except Exception as e:
            logger.error(f"❌ 创建组合对象失败: {e}")
            raise
    
    def _get_stock_sector(self, symbol: str) -> str:
        """获取股票行业（简化处理）"""
        # 简化处理：根据股票代码前缀判断
        if symbol.startswith('000'):
            return '主板'
        elif symbol.startswith('002'):
            return '中小板'
        elif symbol.startswith('300'):
            return '创业板'
        elif symbol.startswith('600') or symbol.startswith('601'):
            return '沪市主板'
        else:
            return '其他'
    
    def _get_market_cap(self, symbol: str) -> float:
        """获取市值（简化处理）"""
        # 简化处理：返回模拟市值
        return 50.0 + hash(symbol) % 200  # 50-250亿市值
    
    def _get_stock_name(self, symbol: str) -> str:
        """获取股票名称（简化处理）"""
        return f"股票{symbol}"
    
    def _estimate_expected_return(self, stock_info: Dict[str, Any]) -> float:
        """估计预期收益"""
        # 基于评分估计预期收益
        total_score = stock_info.get('total_score', 70)
        base_return = 0.08  # 8%基础收益
        score_bonus = (total_score - 70) * 0.002  # 每分增加0.2%收益
        return base_return + score_bonus
    
    def _assess_stock_risk_level(self, stock_info: Dict[str, Any]) -> str:
        """评估股票风险等级"""
        total_score = stock_info.get('total_score', 70)
        
        if total_score >= 85:
            return '低风险'
        elif total_score >= 75:
            return '中低风险'
        elif total_score >= 65:
            return '中等风险'
        else:
            return '中高风险'
    
    def _generate_selection_reason(self,
                                  stock_info: Dict[str, Any],
                                  portfolio_type: PortfolioType) -> List[str]:
        """生成选择理由"""
        reasons = []
        
        total_score = stock_info.get('total_score', 0)
        technical_score = stock_info.get('technical_score', 0)
        fundamental_score = stock_info.get('fundamental_score', 0)
        
        reasons.append(f"综合评分优秀: {total_score:.1f}分")
        
        if portfolio_type == PortfolioType.GROWTH:
            reasons.append(f"成长性突出: 基本面{fundamental_score:.1f}分")
        elif portfolio_type == PortfolioType.MOMENTUM:
            reasons.append(f"技术面强势: 技术面{technical_score:.1f}分")
        elif portfolio_type == PortfolioType.VALUE:
            reasons.append(f"估值合理: 基本面{fundamental_score:.1f}分")
        
        reasons.append("符合组合构建标准")
        
        return reasons
    
    def get_portfolio_summary(self, portfolio: Portfolio) -> Dict[str, Any]:
        """获取组合摘要"""
        return {
            'portfolio_id': portfolio.portfolio_id,
            'name': portfolio.name,
            'type': portfolio.portfolio_type.value,
            'creation_date': portfolio.creation_date.isoformat(),
            'stock_count': len(portfolio.stocks),
            'total_weight': portfolio.total_weight,
            'expected_return': portfolio.expected_return,
            'expected_risk': portfolio.expected_risk,
            'sharpe_ratio': portfolio.sharpe_ratio,
            'max_drawdown': portfolio.max_drawdown,
            'sector_count': len(portfolio.sector_allocation),
            'top_holdings': [
                {
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'weight': stock.weight,
                    'score': stock.score
                }
                for stock in sorted(portfolio.stocks, key=lambda x: x.weight, reverse=True)[:5]
            ],
            'sector_allocation': portfolio.sector_allocation
        }
