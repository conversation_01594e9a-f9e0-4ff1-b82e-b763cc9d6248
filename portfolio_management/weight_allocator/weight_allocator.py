"""
权重分配器
实现多种权重分配策略
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class WeightMethod(Enum):
    """权重分配方法"""
    EQUAL_WEIGHT = "EQUAL_WEIGHT"           # 等权重
    SCORE_WEIGHT = "SCORE_WEIGHT"           # 评分权重
    RISK_PARITY = "RISK_PARITY"             # 风险平价
    MARKET_CAP = "MARKET_CAP"               # 市值权重
    MOMENTUM = "MOMENTUM"                   # 动量权重
    VOLATILITY_INVERSE = "VOLATILITY_INVERSE" # 波动率倒数权重

@dataclass
class WeightAllocation:
    """权重分配结果"""
    symbol: str
    weight: float
    method: WeightMethod
    score: float
    risk_contribution: float
    expected_return: float
    allocation_reason: str

class WeightAllocator:
    """权重分配器"""
    
    def __init__(self):
        # 权重约束
        self.max_single_weight = 0.15       # 单股最大权重15%
        self.min_single_weight = 0.01       # 单股最小权重1%
        self.max_sector_weight = 0.40       # 单行业最大权重40%
        
        # 风险参数
        self.target_risk = 0.15             # 目标风险15%
        self.risk_free_rate = 0.03          # 无风险利率3%
        
        logger.info("⚖️ 权重分配器初始化完成")
        logger.info(f"  - 单股权重范围: {self.min_single_weight:.1%} - {self.max_single_weight:.1%}")
        logger.info(f"  - 单行业最大权重: {self.max_sector_weight:.1%}")
    
    def allocate_weights(self,
                        stocks_data: List[Dict[str, Any]],
                        method: WeightMethod,
                        constraints: Dict[str, Any] = None) -> List[WeightAllocation]:
        """
        分配权重
        
        Args:
            stocks_data: 股票数据列表
            method: 权重分配方法
            constraints: 约束条件
            
        Returns:
            权重分配结果列表
        """
        try:
            logger.info(f"⚖️ 开始权重分配: {method.value} 方法")
            
            if not stocks_data:
                logger.error("❌ 股票数据为空")
                return []
            
            # 应用约束条件
            if constraints:
                self._apply_constraints(constraints)
            
            # 根据方法分配权重
            if method == WeightMethod.EQUAL_WEIGHT:
                weights = self._equal_weight_allocation(stocks_data)
            elif method == WeightMethod.SCORE_WEIGHT:
                weights = self._score_weight_allocation(stocks_data)
            elif method == WeightMethod.RISK_PARITY:
                weights = self._risk_parity_allocation(stocks_data)
            elif method == WeightMethod.MARKET_CAP:
                weights = self._market_cap_allocation(stocks_data)
            elif method == WeightMethod.MOMENTUM:
                weights = self._momentum_allocation(stocks_data)
            elif method == WeightMethod.VOLATILITY_INVERSE:
                weights = self._volatility_inverse_allocation(stocks_data)
            else:
                logger.error(f"❌ 不支持的权重分配方法: {method}")
                return []
            
            # 权重归一化和约束检查
            normalized_weights = self._normalize_and_constrain_weights(weights)
            
            # 创建权重分配结果
            allocations = self._create_weight_allocations(
                stocks_data, normalized_weights, method
            )
            
            logger.info(f"✅ 权重分配完成: {len(allocations)}只股票")
            
            return allocations
            
        except Exception as e:
            logger.error(f"❌ 权重分配失败: {e}")
            return []
    
    def _apply_constraints(self, constraints: Dict[str, Any]) -> None:
        """应用约束条件"""
        try:
            if 'max_single_weight' in constraints:
                self.max_single_weight = constraints['max_single_weight']
            
            if 'min_single_weight' in constraints:
                self.min_single_weight = constraints['min_single_weight']
            
            if 'max_sector_weight' in constraints:
                self.max_sector_weight = constraints['max_sector_weight']
            
            if 'target_risk' in constraints:
                self.target_risk = constraints['target_risk']
                
        except Exception as e:
            logger.error(f"❌ 应用约束条件失败: {e}")
    
    def _equal_weight_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """等权重分配"""
        try:
            n_stocks = len(stocks_data)
            weights = np.ones(n_stocks) / n_stocks
            
            logger.debug(f"📊 等权重分配: 每只股票{1/n_stocks:.3f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 等权重分配失败: {e}")
            return np.array([])
    
    def _score_weight_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """基于评分的权重分配"""
        try:
            scores = np.array([stock.get('total_score', 70) for stock in stocks_data])
            
            # 评分归一化
            min_score = np.min(scores)
            max_score = np.max(scores)
            
            if max_score == min_score:
                return self._equal_weight_allocation(stocks_data)
            
            # 线性变换
            normalized_scores = (scores - min_score) / (max_score - min_score)
            
            # 转换为权重（高评分高权重）
            weights = normalized_scores / np.sum(normalized_scores)
            
            logger.debug(f"📊 评分权重分配: 评分范围{min_score:.1f}-{max_score:.1f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 评分权重分配失败: {e}")
            return self._equal_weight_allocation(stocks_data)
    
    def _risk_parity_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """风险平价分配"""
        try:
            # 获取或估计股票风险
            risks = []
            for stock in stocks_data:
                risk = stock.get('volatility', None)
                if risk is None:
                    # 基于评分估计风险
                    score = stock.get('total_score', 70)
                    risk = 0.25 - (score - 70) * 0.001  # 评分越高风险越低
                    risk = max(0.1, min(0.4, risk))  # 限制在10%-40%范围
                
                risks.append(risk)
            
            risks = np.array(risks)
            
            # 风险平价：权重与风险成反比
            inv_risks = 1.0 / risks
            weights = inv_risks / np.sum(inv_risks)
            
            logger.debug(f"📊 风险平价分配: 风险范围{np.min(risks):.3f}-{np.max(risks):.3f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 风险平价分配失败: {e}")
            return self._equal_weight_allocation(stocks_data)
    
    def _market_cap_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """市值权重分配"""
        try:
            market_caps = np.array([stock.get('market_cap', 100) for stock in stocks_data])
            
            # 市值权重
            weights = market_caps / np.sum(market_caps)
            
            logger.debug(f"📊 市值权重分配: 市值范围{np.min(market_caps):.1f}-{np.max(market_caps):.1f}亿")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 市值权重分配失败: {e}")
            return self._equal_weight_allocation(stocks_data)
    
    def _momentum_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """动量权重分配"""
        try:
            # 基于技术评分或动量指标
            momentum_scores = []
            for stock in stocks_data:
                momentum = stock.get('momentum', None)
                if momentum is None:
                    # 使用技术评分作为动量代理
                    momentum = stock.get('technical_score', 50)
                
                momentum_scores.append(momentum)
            
            momentum_scores = np.array(momentum_scores)
            
            # 只对正动量股票分配权重
            positive_momentum = np.maximum(momentum_scores, 0)
            
            if np.sum(positive_momentum) == 0:
                return self._equal_weight_allocation(stocks_data)
            
            weights = positive_momentum / np.sum(positive_momentum)
            
            logger.debug(f"📊 动量权重分配: 动量范围{np.min(momentum_scores):.1f}-{np.max(momentum_scores):.1f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 动量权重分配失败: {e}")
            return self._equal_weight_allocation(stocks_data)
    
    def _volatility_inverse_allocation(self, stocks_data: List[Dict[str, Any]]) -> np.ndarray:
        """波动率倒数权重分配"""
        try:
            volatilities = []
            for stock in stocks_data:
                vol = stock.get('volatility', None)
                if vol is None:
                    # 基于评分估计波动率
                    score = stock.get('total_score', 70)
                    vol = 0.3 - (score - 70) * 0.002  # 评分越高波动率越低
                    vol = max(0.1, min(0.5, vol))
                
                volatilities.append(vol)
            
            volatilities = np.array(volatilities)
            
            # 波动率倒数权重
            inv_vol = 1.0 / volatilities
            weights = inv_vol / np.sum(inv_vol)
            
            logger.debug(f"📊 波动率倒数分配: 波动率范围{np.min(volatilities):.3f}-{np.max(volatilities):.3f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 波动率倒数分配失败: {e}")
            return self._equal_weight_allocation(stocks_data)
    
    def _normalize_and_constrain_weights(self, weights: np.ndarray) -> np.ndarray:
        """权重归一化和约束检查"""
        try:
            if len(weights) == 0:
                return weights
            
            # 应用单股权重约束
            weights = np.clip(weights, self.min_single_weight, self.max_single_weight)
            
            # 归一化
            weights = weights / np.sum(weights)
            
            # 检查是否满足约束
            max_weight = np.max(weights)
            min_weight = np.min(weights)
            
            logger.debug(f"📊 权重约束检查: 范围{min_weight:.3f}-{max_weight:.3f}")
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 权重归一化失败: {e}")
            return weights
    
    def _create_weight_allocations(self,
                                  stocks_data: List[Dict[str, Any]],
                                  weights: np.ndarray,
                                  method: WeightMethod) -> List[WeightAllocation]:
        """创建权重分配结果"""
        try:
            allocations = []
            
            for i, stock in enumerate(stocks_data):
                weight = weights[i]
                
                # 计算风险贡献
                stock_risk = stock.get('volatility', 0.2)
                risk_contribution = weight * stock_risk
                
                # 估计预期收益
                score = stock.get('total_score', 70)
                expected_return = 0.08 + (score - 70) * 0.001  # 基于评分估计收益
                
                # 生成分配原因
                allocation_reason = self._generate_allocation_reason(stock, weight, method)
                
                allocation = WeightAllocation(
                    symbol=stock['symbol'],
                    weight=weight,
                    method=method,
                    score=score,
                    risk_contribution=risk_contribution,
                    expected_return=expected_return,
                    allocation_reason=allocation_reason
                )
                
                allocations.append(allocation)
            
            return allocations
            
        except Exception as e:
            logger.error(f"❌ 创建权重分配结果失败: {e}")
            return []
    
    def _generate_allocation_reason(self,
                                   stock: Dict[str, Any],
                                   weight: float,
                                   method: WeightMethod) -> str:
        """生成分配原因"""
        try:
            symbol = stock['symbol']
            score = stock.get('total_score', 70)
            
            if method == WeightMethod.EQUAL_WEIGHT:
                return f"等权重分配: {weight:.2%}"
            elif method == WeightMethod.SCORE_WEIGHT:
                return f"基于评分{score:.1f}分分配: {weight:.2%}"
            elif method == WeightMethod.RISK_PARITY:
                risk = stock.get('volatility', 0.2)
                return f"风险平价(风险{risk:.2%}): {weight:.2%}"
            elif method == WeightMethod.MARKET_CAP:
                market_cap = stock.get('market_cap', 100)
                return f"市值权重({market_cap:.1f}亿): {weight:.2%}"
            elif method == WeightMethod.MOMENTUM:
                momentum = stock.get('technical_score', 50)
                return f"动量权重(技术{momentum:.1f}分): {weight:.2%}"
            elif method == WeightMethod.VOLATILITY_INVERSE:
                vol = stock.get('volatility', 0.2)
                return f"低波动率权重(波动{vol:.2%}): {weight:.2%}"
            else:
                return f"权重分配: {weight:.2%}"
                
        except Exception as e:
            logger.error(f"❌ 生成分配原因失败: {e}")
            return f"权重分配: {weight:.2%}"
    
    def optimize_weights(self,
                        stocks_data: List[Dict[str, Any]],
                        target_return: float = None,
                        target_risk: float = None) -> List[WeightAllocation]:
        """
        优化权重分配
        
        Args:
            stocks_data: 股票数据
            target_return: 目标收益率
            target_risk: 目标风险
            
        Returns:
            优化后的权重分配
        """
        try:
            logger.info("🎯 开始权重优化...")
            
            n_stocks = len(stocks_data)
            if n_stocks == 0:
                return []
            
            # 提取预期收益和风险
            expected_returns = np.array([
                stock.get('expected_return', 0.08 + (stock.get('total_score', 70) - 70) * 0.001)
                for stock in stocks_data
            ])
            
            risks = np.array([
                stock.get('volatility', 0.25 - (stock.get('total_score', 70) - 70) * 0.001)
                for stock in stocks_data
            ])
            
            # 简化的优化：最大夏普比率
            if target_return is None and target_risk is None:
                # 最大夏普比率组合
                excess_returns = expected_returns - self.risk_free_rate
                sharpe_ratios = excess_returns / risks
                
                # 基于夏普比率分配权重
                positive_sharpe = np.maximum(sharpe_ratios, 0)
                if np.sum(positive_sharpe) > 0:
                    weights = positive_sharpe / np.sum(positive_sharpe)
                else:
                    weights = np.ones(n_stocks) / n_stocks
            
            elif target_return is not None:
                # 目标收益率下的最小风险组合
                weights = self._min_risk_for_target_return(expected_returns, risks, target_return)
            
            elif target_risk is not None:
                # 目标风险下的最大收益组合
                weights = self._max_return_for_target_risk(expected_returns, risks, target_risk)
            
            else:
                # 均值-方差优化
                weights = self._mean_variance_optimization(expected_returns, risks)
            
            # 应用约束
            weights = self._normalize_and_constrain_weights(weights)
            
            # 创建优化结果
            allocations = self._create_weight_allocations(
                stocks_data, weights, WeightMethod.SCORE_WEIGHT  # 使用评分权重作为标识
            )
            
            # 更新分配原因
            for allocation in allocations:
                allocation.allocation_reason = f"优化权重: {allocation.weight:.2%}"
            
            logger.info(f"✅ 权重优化完成: {len(allocations)}只股票")
            
            return allocations
            
        except Exception as e:
            logger.error(f"❌ 权重优化失败: {e}")
            return self.allocate_weights(stocks_data, WeightMethod.EQUAL_WEIGHT)
    
    def _min_risk_for_target_return(self,
                                   expected_returns: np.ndarray,
                                   risks: np.ndarray,
                                   target_return: float) -> np.ndarray:
        """目标收益率下的最小风险组合"""
        try:
            n_stocks = len(expected_returns)
            
            # 简化处理：线性规划近似
            # 在满足目标收益的前提下，选择低风险股票
            
            # 计算每只股票的收益风险比
            return_risk_ratio = expected_returns / risks
            
            # 按收益风险比排序
            sorted_indices = np.argsort(return_risk_ratio)[::-1]
            
            weights = np.zeros(n_stocks)
            cumulative_return = 0
            cumulative_weight = 0
            
            # 贪心算法：优先选择高收益风险比的股票
            for idx in sorted_indices:
                if cumulative_return >= target_return * cumulative_weight:
                    break
                
                # 计算需要的权重
                remaining_weight = 1.0 - cumulative_weight
                max_weight = min(self.max_single_weight, remaining_weight)
                
                weights[idx] = max_weight
                cumulative_weight += max_weight
                cumulative_return += expected_returns[idx] * max_weight
                
                if cumulative_weight >= 1.0:
                    break
            
            # 归一化
            if cumulative_weight > 0:
                weights = weights / cumulative_weight
            else:
                weights = np.ones(n_stocks) / n_stocks
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 最小风险组合计算失败: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _max_return_for_target_risk(self,
                                   expected_returns: np.ndarray,
                                   risks: np.ndarray,
                                   target_risk: float) -> np.ndarray:
        """目标风险下的最大收益组合"""
        try:
            n_stocks = len(expected_returns)
            
            # 简化处理：选择高收益且风险不超过目标的股票
            eligible_stocks = risks <= target_risk
            
            if not np.any(eligible_stocks):
                # 如果没有符合风险要求的股票，选择风险最低的
                min_risk_idx = np.argmin(risks)
                weights = np.zeros(n_stocks)
                weights[min_risk_idx] = 1.0
                return weights
            
            # 在符合风险要求的股票中，按收益分配权重
            eligible_returns = expected_returns * eligible_stocks
            
            if np.sum(eligible_returns) > 0:
                weights = eligible_returns / np.sum(eligible_returns)
            else:
                weights = eligible_stocks.astype(float) / np.sum(eligible_stocks)
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 最大收益组合计算失败: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _mean_variance_optimization(self,
                                   expected_returns: np.ndarray,
                                   risks: np.ndarray) -> np.ndarray:
        """均值-方差优化"""
        try:
            # 简化的均值-方差优化：最大化效用函数
            # U = E(R) - 0.5 * A * Var(R)，其中A是风险厌恶系数
            
            risk_aversion = 2.0  # 风险厌恶系数
            
            # 计算效用
            utilities = expected_returns - 0.5 * risk_aversion * (risks ** 2)
            
            # 基于效用分配权重
            positive_utilities = np.maximum(utilities, 0)
            
            if np.sum(positive_utilities) > 0:
                weights = positive_utilities / np.sum(positive_utilities)
            else:
                weights = np.ones(len(expected_returns)) / len(expected_returns)
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 均值-方差优化失败: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def get_allocation_summary(self, allocations: List[WeightAllocation]) -> Dict[str, Any]:
        """获取分配摘要"""
        try:
            if not allocations:
                return {}
            
            weights = np.array([alloc.weight for alloc in allocations])
            scores = np.array([alloc.score for alloc in allocations])
            expected_returns = np.array([alloc.expected_return for alloc in allocations])
            risk_contributions = np.array([alloc.risk_contribution for alloc in allocations])
            
            # 组合指标
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_risk = np.sum(risk_contributions)
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
            
            # 权重统计
            max_weight = np.max(weights)
            min_weight = np.min(weights)
            weight_concentration = np.sum(weights ** 2)  # Herfindahl指数
            
            return {
                'stock_count': len(allocations),
                'total_weight': np.sum(weights),
                'portfolio_return': portfolio_return,
                'portfolio_risk': portfolio_risk,
                'sharpe_ratio': sharpe_ratio,
                'max_weight': max_weight,
                'min_weight': min_weight,
                'weight_concentration': weight_concentration,
                'avg_score': np.mean(scores),
                'method': allocations[0].method.value if allocations else 'UNKNOWN',
                'top_holdings': [
                    {
                        'symbol': alloc.symbol,
                        'weight': alloc.weight,
                        'score': alloc.score,
                        'expected_return': alloc.expected_return
                    }
                    for alloc in sorted(allocations, key=lambda x: x.weight, reverse=True)[:5]
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 获取分配摘要失败: {e}")
            return {}
