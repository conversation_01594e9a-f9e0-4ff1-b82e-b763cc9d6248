#!/usr/bin/env python3
"""
修复VeighNa系统问题
1. 清理旧的股票数据，只保留有完整数据的股票
2. 禁用实时数据采集，使用模拟数据
"""

import sqlite3
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_stock_data():
    """修复股票数据问题"""
    try:
        conn = sqlite3.connect('vnpy_trading.db')
        cursor = conn.cursor()
        
        # 1. 删除没有完整数据的股票
        logger.info("🔧 清理无效股票数据...")
        
        # 删除没有日线数据的股票基本信息
        cursor.execute("""
            DELETE FROM stock_basic_info 
            WHERE symbol NOT IN (
                SELECT DISTINCT symbol FROM stock_daily_data
            )
        """)
        deleted_basic = cursor.rowcount
        
        # 删除没有技术指标的股票日线数据
        cursor.execute("""
            DELETE FROM stock_daily_data 
            WHERE symbol NOT IN (
                SELECT DISTINCT symbol FROM stock_technical_indicators
            )
        """)
        deleted_daily = cursor.rowcount
        
        # 删除没有基本面数据的股票技术指标
        cursor.execute("""
            DELETE FROM stock_technical_indicators 
            WHERE symbol NOT IN (
                SELECT DISTINCT symbol FROM stock_fundamental_data
            )
        """)
        deleted_tech = cursor.rowcount
        
        logger.info(f"✅ 清理完成: 基本信息{deleted_basic}条, 日线数据{deleted_daily}条, 技术指标{deleted_tech}条")
        
        # 2. 检查剩余的完整股票数据
        cursor.execute("""
            SELECT s.symbol, s.name, 
                   COUNT(DISTINCT d.trade_date) as daily_count,
                   COUNT(DISTINCT t.trade_date) as tech_count,
                   COUNT(DISTINCT f.report_date) as fund_count
            FROM stock_basic_info s
            INNER JOIN stock_daily_data d ON s.symbol = d.symbol
            INNER JOIN stock_technical_indicators t ON s.symbol = t.symbol
            INNER JOIN stock_fundamental_data f ON s.symbol = f.symbol
            GROUP BY s.symbol, s.name
            ORDER BY s.symbol
        """)
        
        complete_stocks = cursor.fetchall()
        logger.info(f"📊 有完整数据的股票: {len(complete_stocks)} 只")
        
        for stock in complete_stocks[:10]:
            logger.info(f"  {stock[0]} {stock[1]}: 日线{stock[2]}天, 技术指标{stock[3]}个, 基本面{stock[4]}个")
        
        # 3. 清理选股结果和交易信号
        cursor.execute("DELETE FROM stock_selection_results")
        cursor.execute("DELETE FROM trading_signals")
        logger.info("✅ 清理旧的选股结果和交易信号")
        
        conn.commit()
        conn.close()
        
        return len(complete_stocks) > 0
        
    except Exception as e:
        logger.error(f"❌ 修复股票数据失败: {e}")
        return False

def disable_real_data_collection():
    """禁用实时数据采集，修改配置"""
    try:
        # 修改数据采集器，使其不进行网络请求
        logger.info("🔧 禁用实时数据采集...")
        
        # 创建一个简化的数据采集器配置
        config_content = '''
# 禁用实时数据采集配置
DISABLE_REAL_DATA_COLLECTION = True
USE_MOCK_DATA_ONLY = True
'''
        
        with open('data_collection_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info("✅ 已禁用实时数据采集")
        return True
        
    except Exception as e:
        logger.error(f"❌ 禁用实时数据采集失败: {e}")
        return False

def create_mock_selection_results():
    """创建模拟选股结果"""
    try:
        conn = sqlite3.connect('vnpy_trading.db')
        cursor = conn.cursor()
        
        # 获取有完整数据的股票
        cursor.execute("""
            SELECT s.symbol, s.name 
            FROM stock_basic_info s
            INNER JOIN stock_daily_data d ON s.symbol = d.symbol
            INNER JOIN stock_technical_indicators t ON s.symbol = t.symbol
            INNER JOIN stock_fundamental_data f ON s.symbol = f.symbol
            GROUP BY s.symbol, s.name
            ORDER BY s.symbol
            LIMIT 20
        """)
        
        stocks = cursor.fetchall()
        
        if not stocks:
            logger.warning("⚠️ 没有找到完整的股票数据")
            return False
        
        # 生成选股结果
        selection_id = f"selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        import random
        
        for i, (symbol, name) in enumerate(stocks):
            # 生成随机评分
            technical_score = random.uniform(60, 95)
            fundamental_score = random.uniform(60, 95)
            market_score = random.uniform(60, 95)
            total_score = (technical_score * 0.4 + fundamental_score * 0.4 + market_score * 0.2)
            
            # 确定推荐等级
            if total_score >= 85:
                recommendation = "强烈推荐"
                reason = "技术面强势，基本面优秀，市场表现活跃"
            elif total_score >= 75:
                recommendation = "推荐"
                reason = "综合表现良好，具有投资价值"
            elif total_score >= 65:
                recommendation = "观察"
                reason = "表现一般，建议继续观察"
            else:
                recommendation = "谨慎"
                reason = "存在一定风险，需谨慎投资"
            
            cursor.execute("""
                INSERT INTO stock_selection_results 
                (selection_id, symbol, selection_date, total_score, technical_score, 
                 fundamental_score, market_score, rank, recommendation, reason,
                 technical_weight, fundamental_weight, market_weight, is_selected) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                selection_id, symbol, datetime.now(), 
                round(total_score, 1), round(technical_score, 1),
                round(fundamental_score, 1), round(market_score, 1),
                i + 1, recommendation, reason,
                0.4, 0.4, 0.2, True
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ 创建模拟选股结果: {len(stocks)} 只股票")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建模拟选股结果失败: {e}")
        return False

def create_mock_trading_signals():
    """创建模拟交易信号"""
    try:
        conn = sqlite3.connect('vnpy_trading.db')
        cursor = conn.cursor()
        
        # 获取选股结果中的股票
        cursor.execute("""
            SELECT DISTINCT symbol FROM stock_selection_results 
            ORDER BY total_score DESC LIMIT 10
        """)
        
        symbols = [row[0] for row in cursor.fetchall()]
        
        if not symbols:
            logger.warning("⚠️ 没有找到选股结果")
            return False
        
        import random
        import uuid
        
        # 生成买入信号
        for symbol in symbols[:5]:
            signal_id = f"BUY_{symbol}_{int(datetime.now().timestamp())}_{uuid.uuid4().hex[:8]}"
            
            cursor.execute("""
                INSERT INTO trading_signals 
                (signal_id, symbol, signal_type, signal_time, price, score, confidence,
                 reason, source_indicator, source_strategy, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal_id, symbol, 'BUY', datetime.now(),
                random.uniform(10, 100), random.uniform(80, 95), random.uniform(0.7, 0.9),
                'MACD金叉形成，成交量放大，技术面转强', 'MACD', 'technical_analysis', 'active'
            ))
        
        # 生成卖出信号
        for symbol in symbols[5:8]:
            signal_id = f"SELL_{symbol}_{int(datetime.now().timestamp())}_{uuid.uuid4().hex[:8]}"
            
            cursor.execute("""
                INSERT INTO trading_signals 
                (signal_id, symbol, signal_type, signal_time, price, score, confidence,
                 reason, source_indicator, source_strategy, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal_id, symbol, 'SELL', datetime.now(),
                random.uniform(10, 100), random.uniform(75, 90), random.uniform(0.6, 0.8),
                'RSI超买回调，价格遇阻回落', 'RSI', 'technical_analysis', 'active'
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ 创建模拟交易信号: 买入{5}个, 卖出{3}个")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建模拟交易信号失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 VeighNa系统问题修复工具")
    print("=" * 50)
    
    # 1. 修复股票数据
    print("1. 修复股票数据...")
    if fix_stock_data():
        print("✅ 股票数据修复完成")
    else:
        print("❌ 股票数据修复失败")
        return
    
    # 2. 禁用实时数据采集
    print("2. 禁用实时数据采集...")
    if disable_real_data_collection():
        print("✅ 实时数据采集已禁用")
    else:
        print("❌ 禁用实时数据采集失败")
    
    # 3. 创建模拟选股结果
    print("3. 创建模拟选股结果...")
    if create_mock_selection_results():
        print("✅ 模拟选股结果创建完成")
    else:
        print("❌ 模拟选股结果创建失败")
    
    # 4. 创建模拟交易信号
    print("4. 创建模拟交易信号...")
    if create_mock_trading_signals():
        print("✅ 模拟交易信号创建完成")
    else:
        print("❌ 模拟交易信号创建失败")
    
    print("=" * 50)
    print("🎉 系统修复完成！")
    print("现在可以重新启动VeighNa系统:")
    print("python web_server.py")

if __name__ == "__main__":
    main()
