"""
ADATA认证管理器
管理API认证信息和会话
"""

import os
import json
import time
import logging
from typing import Optional, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class AuthManager:
    """认证管理器"""
    
    def __init__(self, config_file: str = "adata_auth.json"):
        self.config_file = Path(config_file)
        self.auth_info = {}
        self.session_info = {}
        self.last_auth_time = 0
        
        # 加载认证配置
        self._load_auth_config()
        
        logger.info("🔐 认证管理器初始化完成")
    
    def _load_auth_config(self) -> None:
        """加载认证配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.auth_info = config.get('auth_info', {})
                    self.session_info = config.get('session_info', {})
                    self.last_auth_time = config.get('last_auth_time', 0)
                
                logger.info("✅ 认证配置加载成功")
            else:
                logger.info("📋 认证配置文件不存在，使用默认配置")
                self._create_default_config()
                
        except Exception as e:
            logger.error(f"❌ 加载认证配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """创建默认认证配置"""
        self.auth_info = {
            'api_key': os.getenv('ADATA_API_KEY', ''),
            'secret_key': os.getenv('ADATA_SECRET_KEY', ''),
            'username': os.getenv('ADATA_USERNAME', ''),
            'password': os.getenv('ADATA_PASSWORD', ''),
            'token': '',
            'token_expires': 0
        }
        
        self.session_info = {
            'session_id': '',
            'session_expires': 0,
            'user_agent': 'QuantTradingSystem/2.0',
            'headers': {}
        }
        
        # 保存默认配置
        self._save_auth_config()
    
    def _save_auth_config(self) -> None:
        """保存认证配置"""
        try:
            config = {
                'auth_info': self.auth_info,
                'session_info': self.session_info,
                'last_auth_time': self.last_auth_time
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.debug("✅ 认证配置保存成功")
            
        except Exception as e:
            logger.error(f"❌ 保存认证配置失败: {e}")
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        current_time = time.time()
        
        # 检查token是否有效
        if self.auth_info.get('token'):
            token_expires = self.auth_info.get('token_expires', 0)
            if current_time < token_expires:
                return True
        
        # 检查会话是否有效
        if self.session_info.get('session_id'):
            session_expires = self.session_info.get('session_expires', 0)
            if current_time < session_expires:
                return True
        
        return False
    
    def authenticate(self) -> bool:
        """执行认证"""
        try:
            logger.info("🔐 开始认证...")
            
            # 如果已经认证且未过期，直接返回
            if self.is_authenticated():
                logger.info("✅ 已认证，无需重新认证")
                return True
            
            # 尝试不同的认证方式
            if self._authenticate_with_token():
                return True
            elif self._authenticate_with_credentials():
                return True
            else:
                logger.warning("⚠️ 所有认证方式都失败，使用匿名模式")
                return self._setup_anonymous_session()
                
        except Exception as e:
            logger.error(f"❌ 认证失败: {e}")
            return False
    
    def _authenticate_with_token(self) -> bool:
        """使用API Token认证"""
        api_key = self.auth_info.get('api_key')
        secret_key = self.auth_info.get('secret_key')
        
        if not api_key or not secret_key:
            logger.debug("🔑 API密钥未配置，跳过Token认证")
            return False
        
        try:
            # 这里实现具体的Token认证逻辑
            # 由于AKShare通常不需要认证，这里主要是预留接口
            
            logger.info("✅ Token认证成功")
            
            # 更新认证信息
            current_time = time.time()
            self.auth_info['token'] = 'mock_token'  # 实际应该是API返回的token
            self.auth_info['token_expires'] = current_time + 3600  # 1小时后过期
            self.last_auth_time = current_time
            
            self._save_auth_config()
            return True
            
        except Exception as e:
            logger.error(f"❌ Token认证失败: {e}")
            return False
    
    def _authenticate_with_credentials(self) -> bool:
        """使用用户名密码认证"""
        username = self.auth_info.get('username')
        password = self.auth_info.get('password')
        
        if not username or not password:
            logger.debug("👤 用户凭据未配置，跳过凭据认证")
            return False
        
        try:
            # 这里实现具体的用户名密码认证逻辑
            # 由于AKShare通常不需要认证，这里主要是预留接口
            
            logger.info("✅ 凭据认证成功")
            
            # 更新会话信息
            current_time = time.time()
            self.session_info['session_id'] = 'mock_session'  # 实际应该是API返回的session
            self.session_info['session_expires'] = current_time + 7200  # 2小时后过期
            self.last_auth_time = current_time
            
            self._save_auth_config()
            return True
            
        except Exception as e:
            logger.error(f"❌ 凭据认证失败: {e}")
            return False
    
    def _setup_anonymous_session(self) -> bool:
        """设置匿名会话"""
        try:
            logger.info("🕶️ 设置匿名会话...")
            
            # 设置匿名会话信息
            current_time = time.time()
            self.session_info = {
                'session_id': 'anonymous',
                'session_expires': current_time + 86400,  # 24小时
                'user_agent': 'QuantTradingSystem/2.0',
                'headers': {
                    'User-Agent': 'QuantTradingSystem/2.0',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }
            
            self.last_auth_time = current_time
            self._save_auth_config()
            
            logger.info("✅ 匿名会话设置成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置匿名会话失败: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = self.session_info.get('headers', {}).copy()
        
        # 添加认证token
        if self.auth_info.get('token'):
            headers['Authorization'] = f"Bearer {self.auth_info['token']}"
        
        # 添加API密钥
        if self.auth_info.get('api_key'):
            headers['X-API-Key'] = self.auth_info['api_key']
        
        return headers
    
    def refresh_authentication(self) -> bool:
        """刷新认证"""
        logger.info("🔄 刷新认证...")
        
        # 清除当前认证信息
        self.auth_info['token'] = ''
        self.auth_info['token_expires'] = 0
        self.session_info['session_id'] = ''
        self.session_info['session_expires'] = 0
        
        # 重新认证
        return self.authenticate()
    
    def logout(self) -> None:
        """登出"""
        logger.info("👋 登出...")
        
        try:
            # 清除认证信息
            self.auth_info['token'] = ''
            self.auth_info['token_expires'] = 0
            self.session_info['session_id'] = ''
            self.session_info['session_expires'] = 0
            self.last_auth_time = 0
            
            # 保存配置
            self._save_auth_config()
            
            logger.info("✅ 登出成功")
            
        except Exception as e:
            logger.error(f"❌ 登出失败: {e}")
    
    def update_credentials(self, 
                          api_key: Optional[str] = None,
                          secret_key: Optional[str] = None,
                          username: Optional[str] = None,
                          password: Optional[str] = None) -> None:
        """更新认证凭据"""
        logger.info("🔧 更新认证凭据...")
        
        if api_key is not None:
            self.auth_info['api_key'] = api_key
        
        if secret_key is not None:
            self.auth_info['secret_key'] = secret_key
        
        if username is not None:
            self.auth_info['username'] = username
        
        if password is not None:
            self.auth_info['password'] = password
        
        # 保存配置
        self._save_auth_config()
        
        logger.info("✅ 认证凭据更新完成")
    
    def get_auth_status(self) -> Dict[str, Any]:
        """获取认证状态"""
        current_time = time.time()
        
        return {
            'is_authenticated': self.is_authenticated(),
            'auth_method': 'token' if self.auth_info.get('token') else 'session' if self.session_info.get('session_id') else 'none',
            'token_expires': self.auth_info.get('token_expires', 0),
            'session_expires': self.session_info.get('session_expires', 0),
            'last_auth_time': self.last_auth_time,
            'time_since_auth': current_time - self.last_auth_time if self.last_auth_time > 0 else 0,
            'has_api_key': bool(self.auth_info.get('api_key')),
            'has_credentials': bool(self.auth_info.get('username') and self.auth_info.get('password'))
        }
