"""
API频率限制器
控制API请求频率，避免触发限流
"""

import time
import threading
from collections import deque
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """API频率限制器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        """
        初始化频率限制器
        
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口大小(秒)
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()  # 存储请求时间戳
        self.lock = threading.Lock()  # 线程锁
        
        logger.info(f"🚦 频率限制器初始化: {max_requests}请求/{time_window}秒")
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求"""
        with self.lock:
            current_time = time.time()
            
            # 清理过期的请求记录
            self._cleanup_old_requests(current_time)
            
            # 检查是否超过限制
            return len(self.requests) < self.max_requests
    
    def record_request(self) -> None:
        """记录一次请求"""
        with self.lock:
            current_time = time.time()
            self.requests.append(current_time)
            
            # 清理过期的请求记录
            self._cleanup_old_requests(current_time)
    
    def get_wait_time(self) -> float:
        """获取需要等待的时间"""
        with self.lock:
            if len(self.requests) == 0:
                return 0.0
            
            current_time = time.time()
            oldest_request = self.requests[0]
            
            # 计算最老请求的剩余时间
            elapsed = current_time - oldest_request
            remaining = self.time_window - elapsed
            
            return max(0.0, remaining)
    
    def _cleanup_old_requests(self, current_time: float) -> None:
        """清理过期的请求记录"""
        cutoff_time = current_time - self.time_window
        
        while self.requests and self.requests[0] < cutoff_time:
            self.requests.popleft()
    
    def get_current_usage(self) -> dict:
        """获取当前使用情况"""
        with self.lock:
            current_time = time.time()
            self._cleanup_old_requests(current_time)
            
            return {
                'current_requests': len(self.requests),
                'max_requests': self.max_requests,
                'time_window': self.time_window,
                'usage_ratio': len(self.requests) / self.max_requests,
                'can_request': len(self.requests) < self.max_requests,
                'wait_time': self.get_wait_time()
            }
    
    def reset(self) -> None:
        """重置限制器"""
        with self.lock:
            self.requests.clear()
            logger.info("🔄 频率限制器已重置")

class AdaptiveRateLimiter(RateLimiter):
    """自适应频率限制器"""
    
    def __init__(self, 
                 initial_max_requests: int = 100,
                 time_window: int = 60,
                 min_requests: int = 10,
                 max_requests: int = 200):
        """
        初始化自适应频率限制器
        
        Args:
            initial_max_requests: 初始最大请求数
            time_window: 时间窗口大小(秒)
            min_requests: 最小请求数限制
            max_requests: 最大请求数限制
        """
        super().__init__(initial_max_requests, time_window)
        
        self.min_requests = min_requests
        self.max_requests_limit = max_requests
        self.success_count = 0
        self.failure_count = 0
        self.last_adjustment = time.time()
        self.adjustment_interval = 300  # 5分钟调整一次
        
        logger.info(f"🎯 自适应频率限制器初始化: {initial_max_requests}-{max_requests}请求/{time_window}秒")
    
    def record_success(self) -> None:
        """记录成功请求"""
        self.record_request()
        self.success_count += 1
        self._maybe_adjust_limit()
    
    def record_failure(self) -> None:
        """记录失败请求"""
        self.failure_count += 1
        self._maybe_adjust_limit()
    
    def _maybe_adjust_limit(self) -> None:
        """根据成功率调整限制"""
        current_time = time.time()
        
        if current_time - self.last_adjustment < self.adjustment_interval:
            return
        
        total_requests = self.success_count + self.failure_count
        
        if total_requests < 10:  # 样本太少，不调整
            return
        
        success_rate = self.success_count / total_requests
        
        with self.lock:
            old_limit = self.max_requests
            
            if success_rate > 0.95:  # 成功率很高，可以增加限制
                self.max_requests = min(
                    self.max_requests_limit,
                    int(self.max_requests * 1.1)
                )
            elif success_rate < 0.8:  # 成功率较低，减少限制
                self.max_requests = max(
                    self.min_requests,
                    int(self.max_requests * 0.9)
                )
            
            if old_limit != self.max_requests:
                logger.info(f"📊 调整频率限制: {old_limit} -> {self.max_requests} (成功率: {success_rate:.2%})")
        
        # 重置计数器
        self.success_count = 0
        self.failure_count = 0
        self.last_adjustment = current_time

class TokenBucketRateLimiter:
    """令牌桶频率限制器"""
    
    def __init__(self, 
                 capacity: int = 100,
                 refill_rate: float = 1.0,
                 refill_interval: float = 1.0):
        """
        初始化令牌桶限制器
        
        Args:
            capacity: 桶容量
            refill_rate: 每次补充的令牌数
            refill_interval: 补充间隔(秒)
        """
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.refill_interval = refill_interval
        self.last_refill = time.time()
        self.lock = threading.Lock()
        
        logger.info(f"🪣 令牌桶限制器初始化: 容量{capacity}, 补充率{refill_rate}/{refill_interval}秒")
    
    def can_make_request(self, tokens_needed: int = 1) -> bool:
        """检查是否有足够的令牌"""
        with self.lock:
            self._refill_tokens()
            return self.tokens >= tokens_needed
    
    def consume_tokens(self, tokens_needed: int = 1) -> bool:
        """消费令牌"""
        with self.lock:
            self._refill_tokens()
            
            if self.tokens >= tokens_needed:
                self.tokens -= tokens_needed
                return True
            else:
                return False
    
    def _refill_tokens(self) -> None:
        """补充令牌"""
        current_time = time.time()
        elapsed = current_time - self.last_refill
        
        if elapsed >= self.refill_interval:
            refill_count = int(elapsed / self.refill_interval)
            tokens_to_add = refill_count * self.refill_rate
            
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = current_time
    
    def get_available_tokens(self) -> int:
        """获取可用令牌数"""
        with self.lock:
            self._refill_tokens()
            return int(self.tokens)
    
    def get_wait_time(self, tokens_needed: int = 1) -> float:
        """获取等待时间"""
        with self.lock:
            self._refill_tokens()
            
            if self.tokens >= tokens_needed:
                return 0.0
            
            tokens_deficit = tokens_needed - self.tokens
            intervals_needed = tokens_deficit / self.refill_rate
            
            return intervals_needed * self.refill_interval
