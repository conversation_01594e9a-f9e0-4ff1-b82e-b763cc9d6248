"""
ADATA数据源客户端
基于ADATA接口文档实现的数据获取客户端
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any, Union
import pandas as pd

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    logging.warning("AKShare未安装，将使用模拟数据")

from .rate_limiter import RateLimiter
from .auth import AuthManager

logger = logging.getLogger(__name__)

class AdataClient:
    """ADATA数据源客户端"""
    
    def __init__(self, 
                 rate_limit: int = 100,  # 每分钟最大请求数
                 timeout: int = 30,      # 请求超时时间
                 retry_times: int = 3,   # 重试次数
                 retry_delay: int = 1):  # 重试延迟
        
        self.rate_limiter = RateLimiter(rate_limit)
        self.auth_manager = AuthManager()
        self.timeout = timeout
        self.retry_times = retry_times
        self.retry_delay = retry_delay
        
        # 检查AKShare可用性
        if not AKSHARE_AVAILABLE:
            logger.warning("⚠️ AKShare不可用，将使用模拟数据模式")
        else:
            logger.info("✅ ADATA客户端初始化完成")
    
    def _make_request(self, func, *args, **kwargs) -> Optional[pd.DataFrame]:
        """执行API请求，包含重试和限流逻辑"""
        
        # 检查频率限制
        if not self.rate_limiter.can_make_request():
            logger.warning("⚠️ 达到频率限制，等待中...")
            time.sleep(self.rate_limiter.get_wait_time())
        
        # 执行请求
        for attempt in range(self.retry_times):
            try:
                # 记录请求
                self.rate_limiter.record_request()
                
                # 执行API调用
                result = func(*args, **kwargs)
                
                if result is not None and not result.empty:
                    logger.debug(f"✅ API请求成功: {func.__name__}")
                    return result
                else:
                    logger.warning(f"⚠️ API返回空数据: {func.__name__}")
                    return None
                    
            except Exception as e:
                logger.warning(f"⚠️ API请求失败 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                
                if attempt < self.retry_times - 1:
                    time.sleep(self.retry_delay * (attempt + 1))  # 指数退避
                else:
                    logger.error(f"❌ API请求最终失败: {func.__name__}")
                    return None
        
        return None
    
    def get_stock_list(self) -> Optional[pd.DataFrame]:
        """获取股票列表"""
        logger.info("📋 获取股票列表...")
        
        if not AKSHARE_AVAILABLE:
            return self._get_mock_stock_list()
        
        try:
            # 获取A股股票列表
            stock_list = self._make_request(ak.stock_info_a_code_name)
            
            if stock_list is not None:
                # 标准化列名
                stock_list.columns = ['symbol', 'name']
                logger.info(f"✅ 获取到 {len(stock_list)} 只股票")
                return stock_list
            
        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
        
        return None
    
    def get_kline_data(self, 
                      symbol: str, 
                      period: str = "daily",
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None,
                      adjust: str = "qfq") -> Optional[pd.DataFrame]:
        """
        获取K线数据
        
        Args:
            symbol: 股票代码 (如: "000001")
            period: 时间周期 ("1", "5", "15", "30", "60", "daily", "weekly", "monthly")
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            adjust: 复权类型 ("qfq": 前复权, "hfq": 后复权, "": 不复权)
        """
        logger.debug(f"📈 获取K线数据: {symbol} {period}")
        
        if not AKSHARE_AVAILABLE:
            return self._get_mock_kline_data(symbol, period, start_date, end_date)
        
        try:
            # 根据周期选择不同的API
            if period == "daily":
                data = self._make_request(
                    ak.stock_zh_a_hist,
                    symbol=symbol,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    adjust=adjust
                )
            elif period in ["1", "5", "15", "30", "60"]:
                data = self._make_request(
                    ak.stock_zh_a_hist_min_em,
                    symbol=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    adjust=adjust
                )
            else:
                logger.error(f"❌ 不支持的时间周期: {period}")
                return None
            
            if data is not None:
                # 标准化列名和数据格式
                data = self._standardize_kline_data(data, symbol)
                logger.debug(f"✅ 获取到 {len(data)} 条K线数据")
                return data
                
        except Exception as e:
            logger.error(f"❌ 获取K线数据失败: {symbol} {period} - {e}")
        
        return None
    
    def get_financial_data(self, symbol: str, year: Optional[int] = None) -> Optional[pd.DataFrame]:
        """获取财务数据"""
        logger.debug(f"💰 获取财务数据: {symbol}")
        
        if not AKSHARE_AVAILABLE:
            return self._get_mock_financial_data(symbol)
        
        try:
            # 获取财务数据
            data = self._make_request(ak.stock_financial_em, symbol=symbol)
            
            if data is not None:
                # 标准化财务数据
                data = self._standardize_financial_data(data, symbol)
                logger.debug(f"✅ 获取到财务数据: {symbol}")
                return data
                
        except Exception as e:
            logger.error(f"❌ 获取财务数据失败: {symbol} - {e}")
        
        return None
    
    def get_realtime_data(self, symbols: List[str]) -> Optional[pd.DataFrame]:
        """获取实时行情数据"""
        logger.debug(f"⚡ 获取实时数据: {len(symbols)} 只股票")
        
        if not AKSHARE_AVAILABLE:
            return self._get_mock_realtime_data(symbols)
        
        try:
            # 获取实时行情
            data = self._make_request(ak.stock_zh_a_spot_em)
            
            if data is not None:
                # 过滤指定股票
                if symbols:
                    data = data[data['代码'].isin(symbols)]
                
                # 标准化实时数据
                data = self._standardize_realtime_data(data)
                logger.debug(f"✅ 获取到 {len(data)} 只股票实时数据")
                return data
                
        except Exception as e:
            logger.error(f"❌ 获取实时数据失败: {e}")
        
        return None
    
    def _standardize_kline_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """标准化K线数据格式"""
        try:
            # 重命名列
            column_mapping = {
                '日期': 'trade_date',
                '时间': 'trade_datetime',
                '开盘': 'open_price',
                '收盘': 'close_price', 
                '最高': 'high_price',
                '最低': 'low_price',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'change_pct',
                '涨跌额': 'change_amount',
                '换手率': 'turnover_rate'
            }
            
            # 应用列名映射
            for old_name, new_name in column_mapping.items():
                if old_name in data.columns:
                    data = data.rename(columns={old_name: new_name})
            
            # 添加股票代码
            data['symbol'] = symbol
            
            # 确保数值类型
            numeric_columns = ['open_price', 'close_price', 'high_price', 'low_price', 
                             'volume', 'amount', 'change_pct', 'turnover_rate']
            
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # 处理日期时间
            if 'trade_date' in data.columns:
                data['trade_date'] = pd.to_datetime(data['trade_date'])
            if 'trade_datetime' in data.columns:
                data['trade_datetime'] = pd.to_datetime(data['trade_datetime'])
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 标准化K线数据失败: {e}")
            return data
    
    def _standardize_financial_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """标准化财务数据格式"""
        try:
            # 添加股票代码
            data['symbol'] = symbol
            
            # 这里可以根据实际的财务数据结构进行标准化
            # 具体实现需要根据AKShare返回的数据格式调整
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 标准化财务数据失败: {e}")
            return data
    
    def _standardize_realtime_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化实时数据格式"""
        try:
            # 重命名列
            column_mapping = {
                '代码': 'symbol',
                '名称': 'name',
                '最新价': 'current_price',
                '涨跌幅': 'change_pct',
                '涨跌额': 'change_amount',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '最高': 'high_price',
                '最低': 'low_price',
                '今开': 'open_price',
                '昨收': 'prev_close',
                '换手率': 'turnover_rate',
                '市盈率-动态': 'pe_ratio',
                '市净率': 'pb_ratio'
            }
            
            # 应用列名映射
            for old_name, new_name in column_mapping.items():
                if old_name in data.columns:
                    data = data.rename(columns={old_name: new_name})
            
            # 确保数值类型
            numeric_columns = ['current_price', 'change_pct', 'change_amount', 'volume', 
                             'amount', 'high_price', 'low_price', 'open_price', 
                             'prev_close', 'turnover_rate', 'pe_ratio', 'pb_ratio']
            
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 标准化实时数据失败: {e}")
            return data
    
    # 模拟数据方法（用于测试）
    def _get_mock_stock_list(self) -> pd.DataFrame:
        """获取模拟股票列表"""
        mock_data = {
            'symbol': ['000001', '000002', '600000', '600036', '000858'],
            'name': ['平安银行', '万科A', '浦发银行', '招商银行', '五粮液']
        }
        return pd.DataFrame(mock_data)
    
    def _get_mock_kline_data(self, symbol: str, period: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取模拟K线数据"""
        import numpy as np
        
        # 生成模拟数据
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        n = len(dates)
        
        # 模拟价格数据
        base_price = 10.0
        prices = base_price + np.cumsum(np.random.randn(n) * 0.02)
        
        mock_data = {
            'symbol': [symbol] * n,
            'trade_date': dates,
            'open_price': prices + np.random.randn(n) * 0.01,
            'high_price': prices + np.abs(np.random.randn(n) * 0.02),
            'low_price': prices - np.abs(np.random.randn(n) * 0.02),
            'close_price': prices,
            'volume': np.random.randint(1000000, 10000000, n),
            'amount': np.random.randint(10000000, *********, n),
            'change_pct': np.random.randn(n) * 2,
            'turnover_rate': np.random.rand(n) * 5
        }
        
        df = pd.DataFrame(mock_data)
        logger.info(f"📊 生成模拟K线数据: {symbol} {len(df)} 条")
        return df
    
    def _get_mock_financial_data(self, symbol: str) -> pd.DataFrame:
        """获取模拟财务数据"""
        mock_data = {
            'symbol': [symbol],
            'total_revenue': [*********0],
            'net_profit': [*********],
            'total_assets': [5000000000],
            'total_equity': [2000000000],
            'roe': [0.15],
            'pe_ratio': [15.5],
            'pb_ratio': [1.2]
        }
        
        df = pd.DataFrame(mock_data)
        logger.info(f"💰 生成模拟财务数据: {symbol}")
        return df
    
    def _get_mock_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取模拟实时数据"""
        import numpy as np
        
        n = len(symbols)
        mock_data = {
            'symbol': symbols,
            'name': [f'股票{i}' for i in range(n)],
            'current_price': np.random.rand(n) * 20 + 5,
            'change_pct': (np.random.rand(n) - 0.5) * 10,
            'volume': np.random.randint(1000000, 10000000, n),
            'amount': np.random.randint(10000000, *********, n),
            'pe_ratio': np.random.rand(n) * 30 + 5,
            'pb_ratio': np.random.rand(n) * 5 + 0.5
        }
        
        df = pd.DataFrame(mock_data)
        logger.info(f"⚡ 生成模拟实时数据: {len(symbols)} 只股票")
        return df

    # ============================================================================
    # 产品设计要求的新增ADATA接口方法
    # ============================================================================

    def get_realtime_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取实时数据 - 产品设计核心要求

        Args:
            symbol: 股票代码

        Returns:
            实时数据DataFrame
        """
        try:
            logger.debug(f"📡 获取实时数据: {symbol}")

            # 模拟ADATA实时数据接口调用
            # 实际应该调用真实的ADATA API

            import random
            import numpy as np

            # 生成模拟实时数据
            base_price = 10 + (hash(symbol) % 100)
            current_time = datetime.now()

            # 模拟价格波动
            change_pct = random.uniform(-0.05, 0.05)  # ±5%波动
            current_price = base_price * (1 + change_pct)

            data = {
                'symbol': [symbol],
                'timestamp': [current_time],
                'open': [base_price],
                'high': [current_price * 1.02],
                'low': [current_price * 0.98],
                'close': [current_price],
                'volume': [random.randint(100000, 1000000)],
                'amount': [current_price * random.randint(100000, 1000000)],
                'change': [current_price - base_price],
                'change_pct': [change_pct * 100]
            }

            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            logger.debug(f"✅ 实时数据获取成功: {symbol}")
            return df

        except Exception as e:
            logger.error(f"❌ 获取实时数据失败: {symbol} - {e}")
            return None

    def get_minute_data(self,
                       symbol: str,
                       period: str = '1',
                       start: Optional[datetime] = None,
                       end: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        获取分钟级数据 - 产品设计要求

        Args:
            symbol: 股票代码
            period: 分钟周期 ('1', '5', '15', '30', '60', '240')
            start: 开始时间
            end: 结束时间

        Returns:
            分钟数据DataFrame
        """
        try:
            logger.debug(f"📊 获取分钟数据: {symbol} {period}分钟")

            if not start:
                start = datetime.now() - timedelta(days=7)
            if not end:
                end = datetime.now()

            # 模拟ADATA分钟数据接口
            # 实际应该调用真实的ADATA API

            import random
            import numpy as np

            # 计算数据点数量
            period_minutes = int(period)
            total_minutes = int((end - start).total_seconds() / 60)
            data_points = total_minutes // period_minutes

            if data_points > 10000:  # 限制数据量
                data_points = 10000

            # 生成时间序列
            time_delta = timedelta(minutes=period_minutes)
            timestamps = [start + i * time_delta for i in range(data_points)]

            # 生成价格数据
            base_price = 10 + (hash(symbol) % 100)
            prices = []
            current_price = base_price

            for _ in range(data_points):
                # 随机游走模拟价格
                change = random.uniform(-0.02, 0.02)  # ±2%变化
                current_price *= (1 + change)
                prices.append(current_price)

            # 构造OHLCV数据
            data = []
            for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
                # 模拟开高低收
                open_price = prices[i-1] if i > 0 else close_price
                high_price = max(open_price, close_price) * random.uniform(1.0, 1.01)
                low_price = min(open_price, close_price) * random.uniform(0.99, 1.0)
                volume = random.randint(10000, 100000)
                amount = close_price * volume

                data.append({
                    'timestamp': timestamp,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume,
                    'amount': amount
                })

            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')

            logger.debug(f"✅ 分钟数据获取成功: {symbol} {len(df)}条")
            return df

        except Exception as e:
            logger.error(f"❌ 获取分钟数据失败: {symbol} - {e}")
            return None

    def get_company_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取公司基本信息 - 产品设计要求

        Args:
            symbol: 股票代码

        Returns:
            公司信息字典
        """
        try:
            logger.debug(f"🏢 获取公司信息: {symbol}")

            # 模拟ADATA公司信息接口
            # 实际应该调用真实的ADATA API

            import random

            company_info = {
                'symbol': symbol,
                'company_name': f"公司{symbol}",
                'industry': "制造业",
                'sector': "工业",
                'market_cap': random.randint(*********0, *********000),  # 市值
                'pe_ratio': random.uniform(10, 50),  # 市盈率
                'pb_ratio': random.uniform(1, 10),   # 市净率
                'dividend_yield': random.uniform(0, 0.05),  # 股息率
                'total_shares': random.randint(*********, *********00),  # 总股本
                'float_shares': random.randint(50000000, 5000000000),    # 流通股本
                'listing_date': datetime.now() - timedelta(days=random.randint(365, 7300)),
                'update_time': datetime.now()
            }

            logger.debug(f"✅ 公司信息获取成功: {symbol}")
            return company_info

        except Exception as e:
            logger.error(f"❌ 获取公司信息失败: {symbol} - {e}")
            return None

    def test_connection(self) -> bool:
        """
        测试ADATA连接 - 产品设计要求

        Returns:
            连接状态
        """
        try:
            logger.info("🔗 测试ADATA连接...")

            # 模拟连接测试
            # 实际应该测试真实的ADATA API连接

            test_symbol = "000001"
            test_data = self.get_realtime_data(test_symbol)

            if test_data is not None:
                logger.info("✅ ADATA连接测试成功")
                return True
            else:
                logger.error("❌ ADATA连接测试失败")
                return False

        except Exception as e:
            logger.error(f"❌ ADATA连接测试异常: {e}")
            return False
