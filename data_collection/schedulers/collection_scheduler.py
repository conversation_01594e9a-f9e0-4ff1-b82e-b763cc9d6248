"""
数据采集任务调度器
负责管理和调度各种数据采集任务
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import json

from data_collection.collectors.market_collector import MarketDataCollector
from system_management.config.config_manager import config_manager

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class CollectionTask:
    """采集任务数据类"""
    task_id: str
    name: str
    task_type: str
    schedule: str  # cron表达式或间隔时间
    target_symbols: List[str]
    parameters: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0
    success_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    enabled: bool = True

class CollectionScheduler:
    """数据采集任务调度器"""
    
    def __init__(self):
        self.market_collector = MarketDataCollector()
        self.tasks: Dict[str, CollectionTask] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.is_running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        
        # 初始化默认任务
        self._init_default_tasks()
        
        logger.info("⏰ 数据采集调度器初始化完成")
    
    def _init_default_tasks(self):
        """初始化默认采集任务"""
        try:
            # 股票列表更新任务（每天凌晨1点）
            self.add_task(
                task_id="stock_list_update",
                name="股票列表更新",
                task_type="stock_list",
                schedule="0 1 * * *",  # 每天凌晨1点
                target_symbols=[],
                parameters={}
            )
            
            # 日线数据采集任务（每天下午6点）
            self.add_task(
                task_id="daily_kline_collection",
                name="日线数据采集",
                task_type="kline",
                schedule="0 18 * * *",  # 每天下午6点
                target_symbols=[],  # 空表示所有活跃股票
                parameters={"periods": ["daily"]}
            )
            
            # 小时线数据采集任务（每小时）
            self.add_task(
                task_id="hourly_kline_collection",
                name="小时线数据采集",
                task_type="kline",
                schedule="0 * * * *",  # 每小时
                target_symbols=[],
                parameters={"periods": ["1hour", "4hour"]}
            )
            
            # 分钟线数据采集任务（每5分钟）
            self.add_task(
                task_id="minute_kline_collection",
                name="分钟线数据采集",
                task_type="kline",
                schedule="*/5 * * * *",  # 每5分钟
                target_symbols=[],
                parameters={"periods": ["1min", "5min", "15min"]}
            )
            
            # 实时数据采集任务（每30秒）
            self.add_task(
                task_id="realtime_data_collection",
                name="实时数据采集",
                task_type="realtime",
                schedule="*/30 * * * * *",  # 每30秒
                target_symbols=[],
                parameters={}
            )
            
            logger.info(f"✅ 初始化了 {len(self.tasks)} 个默认采集任务")
            
        except Exception as e:
            logger.error(f"❌ 初始化默认任务失败: {e}")
    
    def add_task(self,
                task_id: str,
                name: str,
                task_type: str,
                schedule: str,
                target_symbols: List[str],
                parameters: Dict[str, Any],
                enabled: bool = True) -> bool:
        """添加采集任务"""
        try:
            if task_id in self.tasks:
                logger.warning(f"⚠️ 任务已存在: {task_id}")
                return False
            
            task = CollectionTask(
                task_id=task_id,
                name=name,
                task_type=task_type,
                schedule=schedule,
                target_symbols=target_symbols,
                parameters=parameters,
                enabled=enabled
            )
            
            # 计算下次运行时间
            task.next_run = self._calculate_next_run(schedule)
            
            self.tasks[task_id] = task
            
            logger.info(f"✅ 添加采集任务: {name} ({task_id})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加采集任务失败: {task_id} - {e}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """移除采集任务"""
        try:
            if task_id not in self.tasks:
                logger.warning(f"⚠️ 任务不存在: {task_id}")
                return False
            
            # 如果任务正在运行，先取消
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            # 移除任务
            task_name = self.tasks[task_id].name
            del self.tasks[task_id]
            
            logger.info(f"✅ 移除采集任务: {task_name} ({task_id})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 移除采集任务失败: {task_id} - {e}")
            return False
    
    def enable_task(self, task_id: str) -> bool:
        """启用任务"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = True
            logger.info(f"✅ 启用任务: {task_id}")
            return True
        return False
    
    def disable_task(self, task_id: str) -> bool:
        """禁用任务"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = False
            logger.info(f"⏸️ 禁用任务: {task_id}")
            return True
        return False
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("⚠️ 调度器已在运行")
            return
        
        logger.info("🚀 启动数据采集调度器...")
        self.is_running = True
        
        # 启动调度循环
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        logger.info("✅ 数据采集调度器启动成功")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("⚠️ 调度器未在运行")
            return
        
        logger.info("🛑 停止数据采集调度器...")
        self.is_running = False
        
        # 取消调度任务
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            logger.info(f"⏹️ 取消运行中的任务: {task_id}")
            task.cancel()
        
        # 等待所有任务完成
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        self.running_tasks.clear()
        
        logger.info("✅ 数据采集调度器已停止")
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("🔄 调度器主循环开始...")
        
        try:
            while self.is_running:
                current_time = datetime.now()
                
                # 检查需要执行的任务
                for task_id, task in self.tasks.items():
                    if not task.enabled:
                        continue
                    
                    if task.next_run and current_time >= task.next_run:
                        # 检查任务是否已在运行
                        if task_id in self.running_tasks:
                            logger.debug(f"⏳ 任务正在运行，跳过: {task_id}")
                            continue
                        
                        # 启动任务
                        logger.info(f"▶️ 启动采集任务: {task.name}")
                        task_coroutine = self._execute_task(task)
                        self.running_tasks[task_id] = asyncio.create_task(task_coroutine)
                
                # 清理已完成的任务
                completed_tasks = []
                for task_id, running_task in self.running_tasks.items():
                    if running_task.done():
                        completed_tasks.append(task_id)
                
                for task_id in completed_tasks:
                    del self.running_tasks[task_id]
                
                # 等待一段时间再检查
                await asyncio.sleep(10)  # 每10秒检查一次
                
        except asyncio.CancelledError:
            logger.info("📋 调度器主循环被取消")
        except Exception as e:
            logger.error(f"❌ 调度器主循环异常: {e}")
    
    async def _execute_task(self, task: CollectionTask):
        """执行采集任务"""
        try:
            logger.info(f"🎯 执行采集任务: {task.name}")
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_run = datetime.now()
            task.run_count += 1
            
            # 获取目标股票列表
            target_symbols = await self._get_target_symbols(task)
            
            # 根据任务类型执行不同的采集逻辑
            success = False
            
            if task.task_type == "stock_list":
                success = await self.market_collector.collect_stock_list()
            
            elif task.task_type == "kline":
                periods = task.parameters.get("periods", ["daily"])
                result = await self.market_collector.collect_kline_data(
                    symbols=target_symbols,
                    periods=periods
                )
                success = result.get("total_success", 0) > 0
            
            elif task.task_type == "realtime":
                success = await self.market_collector.collect_realtime_data(target_symbols)
            
            else:
                logger.error(f"❌ 不支持的任务类型: {task.task_type}")
                success = False
            
            # 更新任务状态
            if success:
                task.status = TaskStatus.COMPLETED
                task.success_count += 1
                task.last_error = None
                logger.info(f"✅ 任务执行成功: {task.name}")
            else:
                task.status = TaskStatus.FAILED
                task.error_count += 1
                task.last_error = "任务执行失败"
                logger.error(f"❌ 任务执行失败: {task.name}")
            
            # 计算下次运行时间
            task.next_run = self._calculate_next_run(task.schedule)
            
        except Exception as e:
            logger.error(f"❌ 执行采集任务异常: {task.name} - {e}")
            
            # 更新任务状态
            task.status = TaskStatus.FAILED
            task.error_count += 1
            task.last_error = str(e)
            
            # 计算下次运行时间
            task.next_run = self._calculate_next_run(task.schedule)
    
    async def _get_target_symbols(self, task: CollectionTask) -> List[str]:
        """获取目标股票列表"""
        try:
            if task.target_symbols:
                return task.target_symbols
            
            # 如果没有指定股票，获取所有活跃股票
            from database_models import db_manager, StockInfo
            
            with db_manager.get_session() as session:
                stocks = session.query(StockInfo).filter(
                    StockInfo.is_active == True
                ).limit(100).all()  # 限制数量避免过多请求
                
                return [stock.symbol for stock in stocks]
                
        except Exception as e:
            logger.error(f"❌ 获取目标股票列表失败: {e}")
            return []
    
    def _calculate_next_run(self, schedule: str) -> Optional[datetime]:
        """计算下次运行时间"""
        try:
            current_time = datetime.now()
            
            # 简单的调度解析（实际项目中可以使用croniter库）
            if schedule.startswith("*/"):
                # 间隔调度，如 "*/5 * * * *" 表示每5分钟
                parts = schedule.split()
                if len(parts) >= 1:
                    interval_str = parts[0][2:]  # 去掉 "*/"
                    interval = int(interval_str)
                    
                    if len(parts) == 6:  # 包含秒
                        return current_time + timedelta(seconds=interval)
                    else:  # 分钟
                        return current_time + timedelta(minutes=interval)
            
            elif schedule.count(" ") == 4:  # 标准cron格式 "分 时 日 月 周"
                # 这里简化处理，实际应该使用专业的cron解析库
                parts = schedule.split()
                minute, hour = int(parts[0]), int(parts[1])
                
                next_run = current_time.replace(minute=minute, second=0, microsecond=0)
                if parts[1] != "*":
                    next_run = next_run.replace(hour=hour)
                
                # 如果时间已过，调整到下一天
                if next_run <= current_time:
                    next_run += timedelta(days=1)
                
                return next_run
            
            else:
                # 默认1小时后
                return current_time + timedelta(hours=1)
                
        except Exception as e:
            logger.error(f"❌ 计算下次运行时间失败: {schedule} - {e}")
            return datetime.now() + timedelta(hours=1)
    
    def get_task_status(self, task_id: Optional[str] = None) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            if task_id:
                if task_id not in self.tasks:
                    return {}
                
                task = self.tasks[task_id]
                return {
                    "task_id": task.task_id,
                    "name": task.name,
                    "task_type": task.task_type,
                    "status": task.status.value,
                    "enabled": task.enabled,
                    "last_run": task.last_run.isoformat() if task.last_run else None,
                    "next_run": task.next_run.isoformat() if task.next_run else None,
                    "run_count": task.run_count,
                    "success_count": task.success_count,
                    "error_count": task.error_count,
                    "success_rate": task.success_count / task.run_count if task.run_count > 0 else 0,
                    "last_error": task.last_error
                }
            else:
                # 返回所有任务状态
                return {
                    "scheduler_running": self.is_running,
                    "total_tasks": len(self.tasks),
                    "running_tasks": len(self.running_tasks),
                    "tasks": [
                        self.get_task_status(tid) for tid in self.tasks.keys()
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ 获取任务状态失败: {e}")
            return {}
    
    async def run_task_now(self, task_id: str) -> bool:
        """立即运行指定任务"""
        try:
            if task_id not in self.tasks:
                logger.error(f"❌ 任务不存在: {task_id}")
                return False
            
            if task_id in self.running_tasks:
                logger.warning(f"⚠️ 任务正在运行: {task_id}")
                return False
            
            task = self.tasks[task_id]
            logger.info(f"🚀 立即运行任务: {task.name}")
            
            # 启动任务
            task_coroutine = self._execute_task(task)
            self.running_tasks[task_id] = asyncio.create_task(task_coroutine)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 立即运行任务失败: {task_id} - {e}")
            return False
