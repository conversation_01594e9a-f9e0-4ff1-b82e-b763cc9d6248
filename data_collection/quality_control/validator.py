"""
数据质量验证器
负责验证采集数据的质量和完整性
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DataValidator:
    """数据质量验证器"""
    
    def __init__(self):
        # 验证规则配置
        self.validation_rules = {
            'max_missing_ratio': 0.1,      # 最大缺失数据比例
            'price_change_threshold': 0.2,  # 价格变动阈值
            'volume_change_threshold': 10.0, # 成交量变动阈值
            'min_price': 0.01,             # 最小价格
            'max_price': 10000.0,          # 最大价格
            'min_volume': 0,               # 最小成交量
            'max_volume': 1e12,            # 最大成交量
        }
        
        logger.info("🔍 数据质量验证器初始化完成")
    
    def validate_stock_list(self, data: pd.DataFrame) -> bool:
        """验证股票列表数据"""
        try:
            logger.debug("🔍 验证股票列表数据...")
            
            # 检查必要字段
            required_columns = ['symbol', 'name']
            if not all(col in data.columns for col in required_columns):
                logger.error(f"❌ 股票列表缺少必要字段: {required_columns}")
                return False
            
            # 检查数据是否为空
            if data.empty:
                logger.error("❌ 股票列表为空")
                return False
            
            # 检查股票代码格式
            invalid_symbols = data[~data['symbol'].str.match(r'^\d{6}$')]
            if not invalid_symbols.empty:
                logger.warning(f"⚠️ 发现无效股票代码: {len(invalid_symbols)}个")
            
            # 检查重复股票代码
            duplicates = data[data['symbol'].duplicated()]
            if not duplicates.empty:
                logger.warning(f"⚠️ 发现重复股票代码: {len(duplicates)}个")
            
            # 检查股票名称
            empty_names = data[data['name'].isna() | (data['name'] == '')]
            if not empty_names.empty:
                logger.warning(f"⚠️ 发现空股票名称: {len(empty_names)}个")
            
            logger.debug("✅ 股票列表数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 股票列表数据验证失败: {e}")
            return False
    
    def validate_kline_data(self, data: pd.DataFrame) -> bool:
        """验证K线数据"""
        try:
            logger.debug("🔍 验证K线数据...")
            
            # 检查必要字段
            required_columns = ['symbol', 'open_price', 'high_price', 'low_price', 'close_price']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"❌ K线数据缺少必要字段: {missing_columns}")
                return False
            
            # 检查数据是否为空
            if data.empty:
                logger.warning("⚠️ K线数据为空")
                return True  # 空数据不算错误
            
            # 验证价格数据
            if not self._validate_price_data(data):
                return False
            
            # 验证成交量数据
            if not self._validate_volume_data(data):
                return False
            
            # 验证时间数据
            if not self._validate_time_data(data):
                return False
            
            # 检查数据完整性
            if not self._check_data_completeness(data):
                return False
            
            # 检查异常值
            if not self._detect_outliers(data):
                return False
            
            logger.debug("✅ K线数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ K线数据验证失败: {e}")
            return False
    
    def validate_financial_data(self, data: pd.DataFrame) -> bool:
        """验证财务数据"""
        try:
            logger.debug("🔍 验证财务数据...")
            
            # 检查必要字段
            required_columns = ['symbol']
            if not all(col in data.columns for col in required_columns):
                logger.error(f"❌ 财务数据缺少必要字段: {required_columns}")
                return False
            
            # 检查数据是否为空
            if data.empty:
                logger.warning("⚠️ 财务数据为空")
                return True
            
            # 验证财务指标的合理性
            if not self._validate_financial_metrics(data):
                return False
            
            logger.debug("✅ 财务数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 财务数据验证失败: {e}")
            return False
    
    def validate_realtime_data(self, data: pd.DataFrame) -> bool:
        """验证实时数据"""
        try:
            logger.debug("🔍 验证实时数据...")
            
            # 检查必要字段
            required_columns = ['symbol', 'current_price']
            if not all(col in data.columns for col in required_columns):
                logger.error(f"❌ 实时数据缺少必要字段: {required_columns}")
                return False
            
            # 检查数据是否为空
            if data.empty:
                logger.warning("⚠️ 实时数据为空")
                return True
            
            # 验证价格数据
            if 'current_price' in data.columns:
                invalid_prices = data[
                    (data['current_price'] <= 0) | 
                    (data['current_price'] > self.validation_rules['max_price'])
                ]
                if not invalid_prices.empty:
                    logger.warning(f"⚠️ 发现无效价格: {len(invalid_prices)}个")
            
            logger.debug("✅ 实时数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 实时数据验证失败: {e}")
            return False
    
    def _validate_price_data(self, data: pd.DataFrame) -> bool:
        """验证价格数据"""
        try:
            price_columns = ['open_price', 'high_price', 'low_price', 'close_price']
            
            for col in price_columns:
                if col not in data.columns:
                    continue
                
                # 检查负价格
                negative_prices = data[data[col] < 0]
                if not negative_prices.empty:
                    logger.error(f"❌ 发现负价格: {col} {len(negative_prices)}个")
                    return False
                
                # 检查零价格
                zero_prices = data[data[col] == 0]
                if not zero_prices.empty:
                    logger.warning(f"⚠️ 发现零价格: {col} {len(zero_prices)}个")
                
                # 检查异常高价格
                high_prices = data[data[col] > self.validation_rules['max_price']]
                if not high_prices.empty:
                    logger.warning(f"⚠️ 发现异常高价格: {col} {len(high_prices)}个")
            
            # 检查价格逻辑关系
            if all(col in data.columns for col in price_columns):
                # 最高价应该 >= 开盘价、收盘价
                invalid_high = data[
                    (data['high_price'] < data['open_price']) |
                    (data['high_price'] < data['close_price'])
                ]
                if not invalid_high.empty:
                    logger.warning(f"⚠️ 最高价逻辑错误: {len(invalid_high)}个")
                
                # 最低价应该 <= 开盘价、收盘价
                invalid_low = data[
                    (data['low_price'] > data['open_price']) |
                    (data['low_price'] > data['close_price'])
                ]
                if not invalid_low.empty:
                    logger.warning(f"⚠️ 最低价逻辑错误: {len(invalid_low)}个")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 价格数据验证失败: {e}")
            return False
    
    def _validate_volume_data(self, data: pd.DataFrame) -> bool:
        """验证成交量数据"""
        try:
            volume_columns = ['volume', 'amount']
            
            for col in volume_columns:
                if col not in data.columns:
                    continue
                
                # 检查负成交量
                negative_volume = data[data[col] < 0]
                if not negative_volume.empty:
                    logger.error(f"❌ 发现负成交量: {col} {len(negative_volume)}个")
                    return False
                
                # 检查异常大成交量
                large_volume = data[data[col] > self.validation_rules['max_volume']]
                if not large_volume.empty:
                    logger.warning(f"⚠️ 发现异常大成交量: {col} {len(large_volume)}个")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 成交量数据验证失败: {e}")
            return False
    
    def _validate_time_data(self, data: pd.DataFrame) -> bool:
        """验证时间数据"""
        try:
            time_columns = ['trade_date', 'trade_datetime']
            
            for col in time_columns:
                if col not in data.columns:
                    continue
                
                # 检查时间格式
                try:
                    pd.to_datetime(data[col])
                except Exception as e:
                    logger.error(f"❌ 时间格式错误: {col} - {e}")
                    return False
                
                # 检查未来时间
                current_time = datetime.now()
                if col == 'trade_date':
                    future_dates = data[pd.to_datetime(data[col]) > current_time]
                else:
                    future_dates = data[pd.to_datetime(data[col]) > current_time]
                
                if not future_dates.empty:
                    logger.warning(f"⚠️ 发现未来时间: {col} {len(future_dates)}个")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 时间数据验证失败: {e}")
            return False
    
    def _check_data_completeness(self, data: pd.DataFrame) -> bool:
        """检查数据完整性"""
        try:
            total_rows = len(data)
            
            for col in data.columns:
                missing_count = data[col].isna().sum()
                missing_ratio = missing_count / total_rows
                
                if missing_ratio > self.validation_rules['max_missing_ratio']:
                    logger.warning(f"⚠️ 字段缺失率过高: {col} {missing_ratio:.2%}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据完整性检查失败: {e}")
            return False
    
    def _detect_outliers(self, data: pd.DataFrame) -> bool:
        """检测异常值"""
        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in ['volume', 'amount']:  # 成交量可能有很大变化，跳过
                    continue
                
                # 使用IQR方法检测异常值
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)]
                
                if not outliers.empty:
                    outlier_ratio = len(outliers) / len(data)
                    if outlier_ratio > 0.05:  # 异常值超过5%
                        logger.warning(f"⚠️ 异常值过多: {col} {len(outliers)}个 ({outlier_ratio:.2%})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 异常值检测失败: {e}")
            return False
    
    def _validate_financial_metrics(self, data: pd.DataFrame) -> bool:
        """验证财务指标"""
        try:
            # 检查ROE合理性
            if 'roe' in data.columns:
                invalid_roe = data[(data['roe'] < -1) | (data['roe'] > 1)]
                if not invalid_roe.empty:
                    logger.warning(f"⚠️ ROE值异常: {len(invalid_roe)}个")
            
            # 检查PE比率合理性
            if 'pe_ratio' in data.columns:
                invalid_pe = data[(data['pe_ratio'] < 0) | (data['pe_ratio'] > 1000)]
                if not invalid_pe.empty:
                    logger.warning(f"⚠️ PE比率异常: {len(invalid_pe)}个")
            
            # 检查PB比率合理性
            if 'pb_ratio' in data.columns:
                invalid_pb = data[(data['pb_ratio'] < 0) | (data['pb_ratio'] > 100)]
                if not invalid_pb.empty:
                    logger.warning(f"⚠️ PB比率异常: {len(invalid_pb)}个")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 财务指标验证失败: {e}")
            return False
    
    def get_data_quality_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成数据质量报告"""
        try:
            report = {
                'total_rows': len(data),
                'total_columns': len(data.columns),
                'missing_data': {},
                'data_types': {},
                'outliers': {},
                'quality_score': 0.0
            }
            
            # 缺失数据统计
            for col in data.columns:
                missing_count = data[col].isna().sum()
                missing_ratio = missing_count / len(data) if len(data) > 0 else 0
                report['missing_data'][col] = {
                    'count': int(missing_count),
                    'ratio': float(missing_ratio)
                }
            
            # 数据类型统计
            for col in data.columns:
                report['data_types'][col] = str(data[col].dtype)
            
            # 异常值统计
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)]
                outlier_ratio = len(outliers) / len(data) if len(data) > 0 else 0
                
                report['outliers'][col] = {
                    'count': int(len(outliers)),
                    'ratio': float(outlier_ratio)
                }
            
            # 计算质量评分
            quality_score = self._calculate_quality_score(report)
            report['quality_score'] = quality_score
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 生成数据质量报告失败: {e}")
            return {}
    
    def _calculate_quality_score(self, report: Dict[str, Any]) -> float:
        """计算数据质量评分"""
        try:
            score = 100.0
            
            # 根据缺失数据扣分
            for col, missing_info in report['missing_data'].items():
                missing_ratio = missing_info['ratio']
                if missing_ratio > 0.1:  # 缺失率超过10%
                    score -= missing_ratio * 20  # 最多扣20分
            
            # 根据异常值扣分
            for col, outlier_info in report['outliers'].items():
                outlier_ratio = outlier_info['ratio']
                if outlier_ratio > 0.05:  # 异常值超过5%
                    score -= outlier_ratio * 10  # 最多扣10分
            
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            logger.error(f"❌ 计算质量评分失败: {e}")
            return 0.0
