"""
市场数据采集器
负责采集多时间周期的K线数据
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import pandas as pd

from database_models import (
    db_manager, StockInfo, Minute1Market, Minute5Market, 
    Minute15Market, Hour1Market, Hour4Market, DailyMarket
)
from data_collection.adata_client.client import AdataClient
from data_collection.quality_control.validator import DataValidator

logger = logging.getLogger(__name__)

class MarketDataCollector:
    """市场数据采集器"""
    
    def __init__(self):
        self.adata_client = AdataClient()
        self.data_validator = DataValidator()
        
        # 时间周期映射
        self.period_mapping = {
            '1min': {'period': '1', 'model': Minute1Market, 'table': 'minute_1_market'},
            '5min': {'period': '5', 'model': Minute5Market, 'table': 'minute_5_market'},
            '15min': {'period': '15', 'model': Minute15Market, 'table': 'minute_15_market'},
            '1hour': {'period': '60', 'model': Hour1Market, 'table': 'hour_1_market'},
            '4hour': {'period': '240', 'model': Hour4Market, 'table': 'hour_4_market'},
            'daily': {'period': 'daily', 'model': DailyMarket, 'table': 'daily_market'}
        }
        
        logger.info("📈 市场数据采集器初始化完成")
    
    async def collect_stock_list(self) -> bool:
        """采集股票列表"""
        try:
            logger.info("📋 开始采集股票列表...")
            
            # 从ADATA获取股票列表
            stock_data = self.adata_client.get_stock_list()
            
            if stock_data is None or stock_data.empty:
                logger.error("❌ 获取股票列表失败")
                return False
            
            # 数据验证
            if not self.data_validator.validate_stock_list(stock_data):
                logger.error("❌ 股票列表数据验证失败")
                return False
            
            # 保存到数据库
            saved_count = await self._save_stock_list(stock_data)
            
            logger.info(f"✅ 股票列表采集完成，保存 {saved_count} 只股票")
            return True
            
        except Exception as e:
            logger.error(f"❌ 采集股票列表异常: {e}")
            return False
    
    async def collect_kline_data(self, 
                               symbols: List[str],
                               periods: List[str],
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> Dict[str, int]:
        """
        采集K线数据
        
        Args:
            symbols: 股票代码列表
            periods: 时间周期列表 ['1min', '5min', '15min', '1hour', '4hour', 'daily']
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            采集结果统计
        """
        try:
            logger.info(f"📊 开始采集K线数据: {len(symbols)}只股票, {len(periods)}个周期")
            
            results = {}
            
            for period in periods:
                if period not in self.period_mapping:
                    logger.warning(f"⚠️ 不支持的时间周期: {period}")
                    continue
                
                logger.info(f"📈 采集 {period} 数据...")
                period_results = await self._collect_period_data(
                    symbols, period, start_date, end_date
                )
                results[period] = period_results
            
            # 统计总结果
            total_success = sum(r.get('success', 0) for r in results.values())
            total_failed = sum(r.get('failed', 0) for r in results.values())
            
            logger.info(f"✅ K线数据采集完成: 成功{total_success}, 失败{total_failed}")
            
            return {
                'total_success': total_success,
                'total_failed': total_failed,
                'period_results': results
            }
            
        except Exception as e:
            logger.error(f"❌ 采集K线数据异常: {e}")
            return {'total_success': 0, 'total_failed': len(symbols) * len(periods)}
    
    async def _collect_period_data(self, 
                                 symbols: List[str],
                                 period: str,
                                 start_date: Optional[str],
                                 end_date: Optional[str]) -> Dict[str, int]:
        """采集指定周期的数据"""
        
        period_config = self.period_mapping[period]
        success_count = 0
        failed_count = 0
        
        # 批量处理，避免过多并发
        batch_size = 10
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            
            # 并发采集批次数据
            tasks = [
                self._collect_single_stock_data(symbol, period_config, start_date, end_date)
                for symbol in batch_symbols
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in batch_results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"❌ 采集失败: {result}")
                elif result:
                    success_count += 1
                else:
                    failed_count += 1
            
            # 避免请求过于频繁
            await asyncio.sleep(0.1)
        
        return {'success': success_count, 'failed': failed_count}
    
    async def _collect_single_stock_data(self,
                                       symbol: str,
                                       period_config: Dict[str, Any],
                                       start_date: Optional[str],
                                       end_date: Optional[str]) -> bool:
        """采集单只股票的数据"""
        try:
            # 获取K线数据
            kline_data = self.adata_client.get_kline_data(
                symbol=symbol,
                period=period_config['period'],
                start_date=start_date,
                end_date=end_date
            )
            
            if kline_data is None or kline_data.empty:
                logger.debug(f"⚠️ 无数据: {symbol} {period_config['period']}")
                return False
            
            # 数据验证
            if not self.data_validator.validate_kline_data(kline_data):
                logger.warning(f"⚠️ 数据验证失败: {symbol} {period_config['period']}")
                return False
            
            # 保存到数据库
            saved_count = await self._save_kline_data(kline_data, period_config)
            
            logger.debug(f"✅ 保存成功: {symbol} {period_config['period']} {saved_count}条")
            return True
            
        except Exception as e:
            logger.error(f"❌ 采集单股数据失败: {symbol} {period_config['period']} - {e}")
            return False
    
    async def _save_stock_list(self, stock_data: pd.DataFrame) -> int:
        """保存股票列表到数据库"""
        try:
            saved_count = 0
            
            with db_manager.get_session() as session:
                for _, row in stock_data.iterrows():
                    # 检查是否已存在
                    existing = session.query(StockInfo).filter(
                        StockInfo.symbol == row['symbol']
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.name = row['name']
                        existing.is_active = True
                    else:
                        # 创建新记录
                        stock_info = StockInfo(
                            symbol=row['symbol'],
                            name=row['name'],
                            is_active=True
                        )
                        session.add(stock_info)
                    
                    saved_count += 1
                
                session.commit()
            
            return saved_count
            
        except Exception as e:
            logger.error(f"❌ 保存股票列表失败: {e}")
            return 0
    
    async def _save_kline_data(self, kline_data: pd.DataFrame, period_config: Dict[str, Any]) -> int:
        """保存K线数据到数据库"""
        try:
            model_class = period_config['model']
            saved_count = 0
            
            with db_manager.get_session() as session:
                for _, row in kline_data.iterrows():
                    # 准备数据
                    data_dict = {
                        'symbol': row['symbol'],
                        'open_price': row.get('open_price'),
                        'high_price': row.get('high_price'),
                        'low_price': row.get('low_price'),
                        'close_price': row.get('close_price'),
                        'volume': row.get('volume'),
                        'amount': row.get('amount')
                    }
                    
                    # 处理时间字段
                    if 'trade_date' in row:
                        data_dict['trade_date'] = row['trade_date']
                    if 'trade_datetime' in row:
                        data_dict['trade_datetime'] = row['trade_datetime']
                    
                    # 添加其他字段
                    if 'turnover_rate' in row:
                        data_dict['turnover_rate'] = row['turnover_rate']
                    if 'pe_ratio' in row:
                        data_dict['pe_ratio'] = row['pe_ratio']
                    if 'pb_ratio' in row:
                        data_dict['pb_ratio'] = row['pb_ratio']
                    if 'market_cap' in row:
                        data_dict['market_cap'] = row['market_cap']
                    if 'circulating_cap' in row:
                        data_dict['circulating_cap'] = row['circulating_cap']
                    
                    # 检查是否已存在（避免重复）
                    query_filters = [model_class.symbol == row['symbol']]
                    
                    if hasattr(model_class, 'trade_date'):
                        query_filters.append(model_class.trade_date == row.get('trade_date'))
                    elif hasattr(model_class, 'trade_datetime'):
                        query_filters.append(model_class.trade_datetime == row.get('trade_datetime'))
                    
                    existing = session.query(model_class).filter(*query_filters).first()
                    
                    if not existing:
                        # 创建新记录
                        record = model_class(**data_dict)
                        session.add(record)
                        saved_count += 1
                
                session.commit()
            
            return saved_count
            
        except Exception as e:
            logger.error(f"❌ 保存K线数据失败: {e}")
            return 0
    
    async def collect_realtime_data(self, symbols: List[str]) -> bool:
        """采集实时数据"""
        try:
            logger.info(f"⚡ 开始采集实时数据: {len(symbols)}只股票")
            
            # 获取实时数据
            realtime_data = self.adata_client.get_realtime_data(symbols)
            
            if realtime_data is None or realtime_data.empty:
                logger.warning("⚠️ 获取实时数据失败")
                return False
            
            # 数据验证
            if not self.data_validator.validate_realtime_data(realtime_data):
                logger.warning("⚠️ 实时数据验证失败")
                return False
            
            # 这里可以将实时数据保存到缓存或实时数据表
            # 暂时只记录日志
            logger.info(f"✅ 实时数据采集完成: {len(realtime_data)}只股票")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 采集实时数据异常: {e}")
            return False
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """获取采集状态"""
        try:
            with db_manager.get_session() as session:
                # 统计各表的数据量
                status = {}
                
                for period, config in self.period_mapping.items():
                    count = session.query(config['model']).count()
                    status[period] = count
                
                # 股票数量
                stock_count = session.query(StockInfo).filter(StockInfo.is_active == True).count()
                status['active_stocks'] = stock_count
                
                return status
                
        except Exception as e:
            logger.error(f"❌ 获取采集状态失败: {e}")
            return {}
    
    async def cleanup_old_data(self, days: int = 365) -> Dict[str, int]:
        """清理旧数据"""
        try:
            logger.info(f"🧹 开始清理 {days} 天前的数据...")
            
            cutoff_date = datetime.now() - timedelta(days=days)
            cleanup_results = {}
            
            with db_manager.get_session() as session:
                for period, config in self.period_mapping.items():
                    model_class = config['model']
                    
                    # 根据模型的时间字段进行清理
                    if hasattr(model_class, 'trade_date'):
                        deleted_count = session.query(model_class).filter(
                            model_class.trade_date < cutoff_date.date()
                        ).delete()
                    elif hasattr(model_class, 'trade_datetime'):
                        deleted_count = session.query(model_class).filter(
                            model_class.trade_datetime < cutoff_date
                        ).delete()
                    else:
                        deleted_count = 0
                    
                    cleanup_results[period] = deleted_count
                
                session.commit()
            
            total_deleted = sum(cleanup_results.values())
            logger.info(f"✅ 数据清理完成，删除 {total_deleted} 条记录")
            
            return cleanup_results
            
        except Exception as e:
            logger.error(f"❌ 清理旧数据失败: {e}")
            return {}

    # ============================================================================
    # 产品设计要求的新增功能 - ADATA数据接口完整集成
    # ============================================================================

    async def collect_realtime_data(self, symbols: List[str]) -> Dict[str, Any]:
        """
        实时数据采集 - 产品设计核心要求
        支持多只股票并发实时数据采集
        """
        try:
            logger.info(f"📡 开始实时数据采集: {len(symbols)}只股票")

            realtime_data = {}

            # 并发采集实时数据
            tasks = [self._collect_single_realtime(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for symbol, result in zip(symbols, results):
                if isinstance(result, Exception):
                    logger.error(f"❌ {symbol} 实时数据采集失败: {result}")
                elif result:
                    realtime_data[symbol] = result

            logger.info(f"✅ 实时数据采集完成: {len(realtime_data)}只股票")
            return realtime_data

        except Exception as e:
            logger.error(f"❌ 实时数据采集异常: {e}")
            return {}

    async def _collect_single_realtime(self, symbol: str) -> Optional[Dict[str, Any]]:
        """采集单只股票实时数据"""
        try:
            # 调用ADATA接口获取实时数据
            data = self.adata_client.get_realtime_data(symbol)

            if data and not data.empty:
                latest = data.iloc[-1]
                return {
                    'symbol': symbol,
                    'current_price': float(latest.get('close', 0)),
                    'open_price': float(latest.get('open', 0)),
                    'high_price': float(latest.get('high', 0)),
                    'low_price': float(latest.get('low', 0)),
                    'volume': int(latest.get('volume', 0)),
                    'amount': float(latest.get('amount', 0)),
                    'change': float(latest.get('change', 0)),
                    'change_pct': float(latest.get('change_pct', 0)),
                    'timestamp': datetime.now()
                }

            return None

        except Exception as e:
            logger.error(f"❌ 采集单只股票实时数据失败: {symbol} - {e}")
            return None

    async def collect_multi_timeframe_data(self,
                                         symbols: List[str],
                                         timeframes: List[str],
                                         start_date: Optional[datetime] = None,
                                         end_date: Optional[datetime] = None) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        多时间周期数据采集 - 产品设计核心功能
        支持：1分钟、5分钟、15分钟、30分钟、1小时、4小时、日线数据
        """
        try:
            logger.info(f"📊 开始多时间周期数据采集: {len(symbols)}只股票, {len(timeframes)}个周期")

            if not start_date:
                start_date = datetime.now() - timedelta(days=30)
            if not end_date:
                end_date = datetime.now()

            multi_data = {}

            # 为每只股票采集多时间周期数据
            for symbol in symbols:
                symbol_data = {}

                for timeframe in timeframes:
                    try:
                        # 采集指定时间周期的数据
                        data = await self._collect_timeframe_data(
                            symbol, timeframe, start_date, end_date
                        )

                        if data is not None and not data.empty:
                            symbol_data[timeframe] = data
                            logger.debug(f"✅ {symbol} {timeframe} 数据采集完成: {len(data)}条")

                    except Exception as e:
                        logger.error(f"❌ {symbol} {timeframe} 数据采集失败: {e}")

                if symbol_data:
                    multi_data[symbol] = symbol_data

            logger.info(f"✅ 多时间周期数据采集完成: {len(multi_data)}只股票")
            return multi_data

        except Exception as e:
            logger.error(f"❌ 多时间周期数据采集异常: {e}")
            return {}

    async def _collect_timeframe_data(self,
                                    symbol: str,
                                    timeframe: str,
                                    start_date: datetime,
                                    end_date: datetime) -> Optional[pd.DataFrame]:
        """采集指定时间周期数据"""
        try:
            # 根据时间周期调用相应的ADATA接口
            if timeframe == '1m':
                data = self.adata_client.get_minute_data(symbol, period='1', start=start_date, end=end_date)
            elif timeframe == '5m':
                data = self.adata_client.get_minute_data(symbol, period='5', start=start_date, end=end_date)
            elif timeframe == '15m':
                data = self.adata_client.get_minute_data(symbol, period='15', start=start_date, end=end_date)
            elif timeframe == '30m':
                data = self.adata_client.get_minute_data(symbol, period='30', start=start_date, end=end_date)
            elif timeframe == '1h':
                data = self.adata_client.get_minute_data(symbol, period='60', start=start_date, end=end_date)
            elif timeframe == '4h':
                data = self.adata_client.get_minute_data(symbol, period='240', start=start_date, end=end_date)
            elif timeframe == '1d':
                data = self.adata_client.get_daily_data(symbol, start=start_date, end=end_date)
            else:
                logger.error(f"❌ 不支持的时间周期: {timeframe}")
                return None

            # 数据验证和清洗
            if data is not None and not data.empty:
                # 标准化列名
                data = self._standardize_columns(data)

                # 数据验证
                if self.data_validator.validate_market_data(data):
                    return data
                else:
                    logger.warning(f"⚠️ 数据验证失败: {symbol} {timeframe}")
                    return None

            return None

        except Exception as e:
            logger.error(f"❌ 采集时间周期数据失败: {symbol} {timeframe} - {e}")
            return None

    def _standardize_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化数据列名"""
        try:
            # 标准化列名映射
            column_mapping = {
                'Open': 'open_price',
                'High': 'high_price',
                'Low': 'low_price',
                'Close': 'close_price',
                'Volume': 'volume',
                'Amount': 'amount',
                'open': 'open_price',
                'high': 'high_price',
                'low': 'low_price',
                'close': 'close_price',
                'vol': 'volume',
                'amount': 'amount'
            }

            # 重命名列
            data = data.rename(columns=column_mapping)

            # 确保必要的列存在
            required_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    logger.warning(f"⚠️ 缺少必要列: {col}")
                    data[col] = 0.0

            # 数据类型转换
            numeric_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume', 'amount']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce').fillna(0)

            return data

        except Exception as e:
            logger.error(f"❌ 标准化数据列名失败: {e}")
            return data
