#!/usr/bin/env python3
"""
VeighNa量化交易系统完整业务流程引擎
实现数据采集→智能选股→信号跟踪→交易决策→绩效分析的完整闭环
"""

import asyncio
import logging
import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.system_config import trading_config, system_config
from database.database_schema import DatabaseManager
from data_collector.real_data_collector import RealDataCollector
from analysis.intelligent_stock_selector import IntelligentStockSelector
from signals.trading_signal_tracker import TradingSignalTracker

logger = logging.getLogger(__name__)

@dataclass
class BusinessFlowStatus:
    """业务流程状态"""
    data_collection_status: str = "stopped"
    stock_selection_status: str = "stopped"
    signal_tracking_status: str = "stopped"
    last_data_update: Optional[datetime] = None
    last_selection_time: Optional[datetime] = None
    last_signal_time: Optional[datetime] = None
    total_stocks_tracked: int = 0
    active_signals_count: int = 0
    system_health: str = "unknown"

class BusinessFlowEngine:
    """完整业务流程引擎"""
    
    def __init__(self):
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        self.db_manager.create_tables()
        
        # 初始化各个组件
        self.data_collector = RealDataCollector(self.db_manager)
        self.stock_selector = IntelligentStockSelector(self.db_manager)
        self.signal_tracker = TradingSignalTracker(self.db_manager)
        
        # 业务流程状态
        self.status = BusinessFlowStatus()
        self.is_running = False
        self.flow_thread = None
        
        # 调度器
        self.scheduler_thread = None
        
        logger.info("✅ 业务流程引擎初始化完成")
    
    def start_business_flow(self):
        """启动完整业务流程"""
        try:
            logger.info("🚀 启动VeighNa完整业务流程")
            self.is_running = True
            
            # 1. 启动数据采集
            self.start_data_collection()
            
            # 2. 启动定时任务调度
            self.start_scheduler()
            
            # 3. 启动业务流程监控
            self.start_flow_monitoring()
            
            logger.info("✅ 完整业务流程启动成功")
            
        except Exception as e:
            logger.error(f"❌ 业务流程启动失败: {e}")
            self.is_running = False
            raise
    
    def start_data_collection(self):
        """启动数据采集流程"""
        try:
            logger.info("📊 启动数据采集流程")
            
            # 在后台线程中运行数据采集
            def data_collection_worker():
                try:
                    # 首次全量数据采集
                    logger.info("🔄 执行首次全量数据采集")
                    self.data_collector.start_data_collection()
                    
                    self.status.data_collection_status = "running"
                    self.status.last_data_update = datetime.now()
                    
                    # 启动增量数据更新
                    self.start_incremental_updates()
                    
                except Exception as e:
                    logger.error(f"❌ 数据采集失败: {e}")
                    self.status.data_collection_status = "error"
            
            data_thread = threading.Thread(target=data_collection_worker, daemon=True)
            data_thread.start()
            
        except Exception as e:
            logger.error(f"启动数据采集失败: {e}")
            self.status.data_collection_status = "error"
    
    def start_incremental_updates(self):
        """启动增量数据更新"""
        def update_worker():
            while self.is_running:
                try:
                    # 获取需要更新的股票列表
                    tracked_stocks = self.get_tracked_stocks()
                    
                    if tracked_stocks:
                        logger.info(f"📈 更新 {len(tracked_stocks)} 只股票的实时数据")
                        
                        for stock in tracked_stocks[:20]:  # 限制并发数量
                            try:
                                # 采集单只股票数据
                                success = self.data_collector.collect_stock_data(stock)
                                if success:
                                    self.status.total_stocks_tracked += 1
                                
                                # 避免请求过于频繁
                                time.sleep(0.5)
                                
                            except Exception as e:
                                logger.error(f"更新{stock['symbol']}数据失败: {e}")
                                continue
                        
                        self.status.last_data_update = datetime.now()
                    
                    # 等待下次更新
                    time.sleep(60)  # 每分钟更新一次
                    
                except Exception as e:
                    logger.error(f"增量数据更新失败: {e}")
                    time.sleep(120)  # 出错后等待2分钟
        
        update_thread = threading.Thread(target=update_worker, daemon=True)
        update_thread.start()
    
    def start_scheduler(self):
        """启动定时任务调度"""
        try:
            logger.info("⏰ 启动定时任务调度")
            
            # 配置定时任务
            schedule.every(30).minutes.do(self.run_stock_selection)  # 每30分钟执行选股
            schedule.every(1).minutes.do(self.run_signal_tracking)   # 每分钟跟踪信号
            schedule.every(1).hours.do(self.run_portfolio_rebalance) # 每小时检查再平衡
            schedule.every().day.at("09:00").do(self.run_daily_analysis)  # 每日分析
            schedule.every().day.at("15:30").do(self.run_market_close_tasks)  # 收盘任务
            
            def scheduler_worker():
                while self.is_running:
                    try:
                        schedule.run_pending()
                        time.sleep(30)  # 每30秒检查一次
                    except Exception as e:
                        logger.error(f"调度器运行错误: {e}")
                        time.sleep(60)
            
            self.scheduler_thread = threading.Thread(target=scheduler_worker, daemon=True)
            self.scheduler_thread.start()
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
    
    def start_flow_monitoring(self):
        """启动业务流程监控"""
        def monitoring_worker():
            while self.is_running:
                try:
                    # 检查系统健康状态
                    self.check_system_health()
                    
                    # 更新统计信息
                    self.update_flow_statistics()
                    
                    # 检查异常情况
                    self.check_flow_anomalies()
                    
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    logger.error(f"流程监控错误: {e}")
                    time.sleep(120)
        
        monitor_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitor_thread.start()
    
    def run_stock_selection(self):
        """执行智能选股"""
        try:
            logger.info("🧠 执行智能选股")
            
            # 执行选股
            selected_stocks = self.stock_selector.select_stocks(
                max_count=trading_config.MAX_SELECTED_STOCKS,
                min_score=trading_config.MIN_STOCK_SCORE
            )
            
            if selected_stocks:
                logger.info(f"✅ 选股完成，共选出 {len(selected_stocks)} 只股票")
                self.status.stock_selection_status = "completed"
                self.status.last_selection_time = datetime.now()
                
                # 启动信号跟踪
                if not self.signal_tracker.is_running:
                    self.signal_tracker.start_tracking()
            else:
                logger.warning("⚠️ 本次选股未找到符合条件的股票")
                self.status.stock_selection_status = "no_results"
            
        except Exception as e:
            logger.error(f"❌ 智能选股失败: {e}")
            self.status.stock_selection_status = "error"
    
    def run_signal_tracking(self):
        """执行信号跟踪"""
        try:
            # 确保信号跟踪器正在运行
            if not self.signal_tracker.is_running:
                self.signal_tracker.start_tracking()
            
            # 获取活跃信号统计
            signal_stats = self.signal_tracker.get_signal_statistics()
            self.status.active_signals_count = signal_stats.get('active_signals', 0)
            self.status.last_signal_time = datetime.now()
            
        except Exception as e:
            logger.error(f"信号跟踪检查失败: {e}")
    
    def run_portfolio_rebalance(self):
        """执行投资组合再平衡检查"""
        try:
            logger.info("💼 检查投资组合再平衡")
            
            # 这里可以集成投资组合管理器的再平衡逻辑
            # portfolio_manager.check_rebalance_needed()
            
            logger.info("✅ 投资组合检查完成")
            
        except Exception as e:
            logger.error(f"投资组合再平衡检查失败: {e}")
    
    def run_daily_analysis(self):
        """执行每日分析"""
        try:
            logger.info("📊 执行每日市场分析")
            
            # 1. 市场整体分析
            self.analyze_market_overview()
            
            # 2. 个股表现分析
            self.analyze_stock_performance()
            
            # 3. 信号效果分析
            self.analyze_signal_effectiveness()
            
            logger.info("✅ 每日分析完成")
            
        except Exception as e:
            logger.error(f"每日分析失败: {e}")
    
    def run_market_close_tasks(self):
        """执行收盘后任务"""
        try:
            logger.info("🔔 执行收盘后任务")
            
            # 1. 数据完整性检查
            self.check_data_integrity()
            
            # 2. 生成日报
            self.generate_daily_report()
            
            # 3. 清理过期数据
            self.cleanup_expired_data()
            
            logger.info("✅ 收盘后任务完成")
            
        except Exception as e:
            logger.error(f"收盘后任务失败: {e}")
    
    def get_tracked_stocks(self) -> List[Dict]:
        """获取需要跟踪的股票列表"""
        try:
            # 从最新选股结果获取
            selection_results = self.stock_selector.get_latest_selection_results(limit=50)
            
            tracked_stocks = []
            for result in selection_results:
                tracked_stocks.append({
                    'symbol': result['symbol'],
                    'name': result['name'],
                    'exchange': result.get('exchange', 'SZ')
                })
            
            return tracked_stocks
            
        except Exception as e:
            logger.error(f"获取跟踪股票列表失败: {e}")
            return []
    
    def check_system_health(self):
        """检查系统健康状态"""
        try:
            health_score = 0
            total_checks = 4
            
            # 检查数据采集状态
            if self.status.data_collection_status == "running":
                health_score += 1
            
            # 检查选股状态
            if self.status.stock_selection_status in ["completed", "no_results"]:
                health_score += 1
            
            # 检查信号跟踪状态
            if self.signal_tracker.is_running:
                health_score += 1
            
            # 检查数据更新时间
            if (self.status.last_data_update and 
                datetime.now() - self.status.last_data_update < timedelta(minutes=10)):
                health_score += 1
            
            # 计算健康状态
            health_ratio = health_score / total_checks
            if health_ratio >= 0.8:
                self.status.system_health = "healthy"
            elif health_ratio >= 0.6:
                self.status.system_health = "warning"
            else:
                self.status.system_health = "critical"
            
        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")
            self.status.system_health = "error"
    
    def update_flow_statistics(self):
        """更新流程统计信息"""
        try:
            # 更新跟踪股票数量
            tracked_stocks = self.get_tracked_stocks()
            self.status.total_stocks_tracked = len(tracked_stocks)
            
            # 更新活跃信号数量
            if self.signal_tracker.is_running:
                signal_stats = self.signal_tracker.get_signal_statistics()
                self.status.active_signals_count = signal_stats.get('active_signals', 0)
            
        except Exception as e:
            logger.error(f"更新流程统计失败: {e}")
    
    def check_flow_anomalies(self):
        """检查流程异常"""
        try:
            anomalies = []
            
            # 检查数据更新延迟
            if (self.status.last_data_update and 
                datetime.now() - self.status.last_data_update > timedelta(minutes=15)):
                anomalies.append("数据更新延迟超过15分钟")
            
            # 检查选股频率
            if (self.status.last_selection_time and 
                datetime.now() - self.status.last_selection_time > timedelta(hours=2)):
                anomalies.append("选股间隔超过2小时")
            
            # 检查信号生成
            if self.status.active_signals_count == 0:
                anomalies.append("当前无活跃交易信号")
            
            # 记录异常
            for anomaly in anomalies:
                logger.warning(f"⚠️ 流程异常: {anomaly}")
            
        except Exception as e:
            logger.error(f"异常检查失败: {e}")
    
    def analyze_market_overview(self):
        """分析市场概况"""
        try:
            # 获取市场数据进行分析
            # 这里可以实现具体的市场分析逻辑
            logger.info("📈 市场概况分析完成")
        except Exception as e:
            logger.error(f"市场概况分析失败: {e}")
    
    def analyze_stock_performance(self):
        """分析个股表现"""
        try:
            # 分析跟踪股票的表现
            logger.info("📊 个股表现分析完成")
        except Exception as e:
            logger.error(f"个股表现分析失败: {e}")
    
    def analyze_signal_effectiveness(self):
        """分析信号有效性"""
        try:
            # 分析交易信号的有效性
            logger.info("🎯 信号有效性分析完成")
        except Exception as e:
            logger.error(f"信号有效性分析失败: {e}")
    
    def check_data_integrity(self):
        """检查数据完整性"""
        try:
            logger.info("🔍 数据完整性检查完成")
        except Exception as e:
            logger.error(f"数据完整性检查失败: {e}")
    
    def generate_daily_report(self):
        """生成日报"""
        try:
            logger.info("📋 日报生成完成")
        except Exception as e:
            logger.error(f"日报生成失败: {e}")
    
    def cleanup_expired_data(self):
        """清理过期数据"""
        try:
            logger.info("🧹 过期数据清理完成")
        except Exception as e:
            logger.error(f"过期数据清理失败: {e}")
    
    def stop_business_flow(self):
        """停止业务流程"""
        try:
            logger.info("⏹️ 停止业务流程")
            self.is_running = False
            
            # 停止各个组件
            if hasattr(self.signal_tracker, 'stop_tracking'):
                self.signal_tracker.stop_tracking()
            
            # 清理调度任务
            schedule.clear()
            
            logger.info("✅ 业务流程已停止")
            
        except Exception as e:
            logger.error(f"停止业务流程失败: {e}")
    
    def get_flow_status(self) -> Dict:
        """获取业务流程状态"""
        return {
            "data_collection_status": self.status.data_collection_status,
            "stock_selection_status": self.status.stock_selection_status,
            "signal_tracking_status": "running" if self.signal_tracker.is_running else "stopped",
            "last_data_update": self.status.last_data_update.isoformat() if self.status.last_data_update else None,
            "last_selection_time": self.status.last_selection_time.isoformat() if self.status.last_selection_time else None,
            "last_signal_time": self.status.last_signal_time.isoformat() if self.status.last_signal_time else None,
            "total_stocks_tracked": self.status.total_stocks_tracked,
            "active_signals_count": self.status.active_signals_count,
            "system_health": self.status.system_health,
            "is_running": self.is_running
        }

# 全局业务流程引擎实例
business_flow_engine = BusinessFlowEngine()

def main():
    """主函数 - 测试业务流程引擎"""
    try:
        print("🚀 启动VeighNa完整业务流程引擎测试")
        
        # 启动业务流程
        business_flow_engine.start_business_flow()
        
        # 运行一段时间
        time.sleep(300)  # 运行5分钟
        
        # 获取流程状态
        status = business_flow_engine.get_flow_status()
        
        print(f"\n📊 业务流程状态:")
        print(f"  数据采集: {status['data_collection_status']}")
        print(f"  智能选股: {status['stock_selection_status']}")
        print(f"  信号跟踪: {status['signal_tracking_status']}")
        print(f"  系统健康: {status['system_health']}")
        print(f"  跟踪股票: {status['total_stocks_tracked']} 只")
        print(f"  活跃信号: {status['active_signals_count']} 个")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    finally:
        business_flow_engine.stop_business_flow()

if __name__ == "__main__":
    main()
