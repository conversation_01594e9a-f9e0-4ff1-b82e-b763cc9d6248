#!/usr/bin/env python3
"""
初始化VeighNa系统 - 创建数据库表并生成模拟数据
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON>ta

def create_tables():
    """创建数据库表"""
    conn = sqlite3.connect('vnpy_trading.db')
    cursor = conn.cursor()
    
    # 股票基本信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_basic_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(10) NOT NULL UNIQUE,
            name VARCHAR(50) NOT NULL,
            exchange VARCHAR(10) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 股票日线数据表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_daily_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(10) NOT NULL,
            trade_date DATE NOT NULL,
            open_price DECIMAL(10,2) NOT NULL,
            high_price DECIMAL(10,2) NOT NULL,
            low_price DECIMAL(10,2) NOT NULL,
            close_price DECIMAL(10,2) NOT NULL,
            volume BIGINT NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            turnover_rate DECIMAL(8,4),
            pe_ratio DECIMAL(8,2),
            pb_ratio DECIMAL(8,2),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
    ''')
    
    # 技术指标表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_technical_indicators (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(10) NOT NULL,
            trade_date DATE NOT NULL,
            macd_dif DECIMAL(10,4),
            macd_dea DECIMAL(10,4),
            macd_histogram DECIMAL(10,4),
            sma_5 DECIMAL(10,2),
            sma_10 DECIMAL(10,2),
            sma_20 DECIMAL(10,2),
            sma_60 DECIMAL(10,2),
            ema_12 DECIMAL(10,2),
            ema_26 DECIMAL(10,2),
            rsi_14 DECIMAL(8,2),
            boll_upper DECIMAL(10,2),
            boll_middle DECIMAL(10,2),
            boll_lower DECIMAL(10,2),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
    ''')
    
    # 基本面数据表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_fundamental_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(10) NOT NULL,
            report_date DATE NOT NULL,
            pe_ratio DECIMAL(8,2),
            pb_ratio DECIMAL(8,2),
            ps_ratio DECIMAL(8,2),
            roe DECIMAL(8,2),
            gross_profit_margin DECIMAL(8,2),
            net_profit_margin DECIMAL(8,2),
            revenue_growth DECIMAL(8,2),
            profit_growth DECIMAL(8,2),
            eps_growth DECIMAL(8,2),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, report_date)
        )
    ''')
    
    # 选股结果表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_selection_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            selection_id VARCHAR(50) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            selection_date DATETIME NOT NULL,
            total_score DECIMAL(8,2) NOT NULL,
            technical_score DECIMAL(8,2) NOT NULL,
            fundamental_score DECIMAL(8,2) NOT NULL,
            market_score DECIMAL(8,2) NOT NULL,
            rank INTEGER NOT NULL,
            recommendation VARCHAR(20) NOT NULL,
            reason TEXT,
            technical_weight DECIMAL(4,2) NOT NULL,
            fundamental_weight DECIMAL(4,2) NOT NULL,
            market_weight DECIMAL(4,2) NOT NULL,
            is_selected BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 交易信号表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            signal_id VARCHAR(100) NOT NULL UNIQUE,
            symbol VARCHAR(10) NOT NULL,
            signal_type VARCHAR(10) NOT NULL,
            signal_time DATETIME NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            score DECIMAL(8,2) NOT NULL,
            confidence DECIMAL(4,2) NOT NULL,
            reason TEXT,
            source_indicator VARCHAR(50),
            source_strategy VARCHAR(50),
            status VARCHAR(20) DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ 数据库表创建完成")

def generate_mock_data():
    """生成模拟数据"""
    conn = sqlite3.connect('vnpy_trading.db')
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute("DELETE FROM stock_basic_info")
    cursor.execute("DELETE FROM stock_daily_data")
    cursor.execute("DELETE FROM stock_technical_indicators")
    cursor.execute("DELETE FROM stock_fundamental_data")
    cursor.execute("DELETE FROM stock_selection_results")
    cursor.execute("DELETE FROM trading_signals")
    
    # 模拟股票列表
    stocks = [
        ('000001', '平安银行', 'SZ'), ('000002', '万科A', 'SZ'), ('000858', '五粮液', 'SZ'),
        ('600000', '浦发银行', 'SH'), ('600036', '招商银行', 'SH'), ('600519', '贵州茅台', 'SH'),
        ('600887', '伊利股份', 'SH'), ('002415', '海康威视', 'SZ'), ('000725', '京东方A', 'SZ'),
        ('600276', '恒瑞医药', 'SH'), ('000063', '中兴通讯', 'SZ'), ('002594', '比亚迪', 'SZ'),
        ('600031', '三一重工', 'SH'), ('600009', '上海机场', 'SH'), ('000876', '新希望', 'SZ'),
        ('600104', '上汽集团', 'SH'), ('002304', '洋河股份', 'SZ'), ('600585', '海螺水泥', 'SH'),
        ('000338', '潍柴动力', 'SZ'), ('600048', '保利发展', 'SH'), ('000651', '格力电器', 'SZ'),
        ('600703', '三安光电', 'SH'), ('002142', '宁波银行', 'SZ'), ('600660', '福耀玻璃', 'SH'),
        ('000568', '泸州老窖', 'SZ'), ('600196', '复星医药', 'SH'), ('002027', '分众传媒', 'SZ'),
        ('600309', '万华化学', 'SH'), ('000895', '双汇发展', 'SZ'), ('600690', '海尔智家', 'SH')
    ]
    
    # 1. 插入股票基本信息
    for symbol, name, exchange in stocks:
        cursor.execute("""
            INSERT INTO stock_basic_info (symbol, name, exchange, is_active) 
            VALUES (?, ?, ?, ?)
        """, (symbol, name, exchange, True))
    
    # 2. 生成日线数据
    end_date = datetime.now()
    for symbol, name, exchange in stocks:
        base_price = random.uniform(8, 200)
        
        for i in range(60):
            trade_date = end_date - timedelta(days=i)
            if trade_date.weekday() >= 5:  # 跳过周末
                continue
            
            price_change = random.uniform(-0.05, 0.05)
            base_price = max(1.0, base_price * (1 + price_change))
            
            open_price = base_price * random.uniform(0.98, 1.02)
            close_price = base_price
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
            low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)
            
            volume = random.randint(1000000, 50000000)
            amount = volume * close_price
            turnover_rate = random.uniform(0.5, 8.0)
            pe_ratio = random.uniform(8, 50)
            pb_ratio = random.uniform(0.8, 8)
            
            cursor.execute("""
                INSERT INTO stock_daily_data 
                (symbol, trade_date, open_price, high_price, low_price, close_price, 
                 volume, amount, turnover_rate, pe_ratio, pb_ratio) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                symbol, trade_date.strftime('%Y-%m-%d'),
                round(open_price, 2), round(high_price, 2), round(low_price, 2), round(close_price, 2),
                volume, round(amount, 2), round(turnover_rate, 2), round(pe_ratio, 2), round(pb_ratio, 2)
            ))
    
    # 3. 生成技术指标
    for symbol, name, exchange in stocks:
        cursor.execute("SELECT MAX(trade_date) FROM stock_daily_data WHERE symbol = ?", (symbol,))
        latest_date = cursor.fetchone()[0]
        
        if latest_date:
            cursor.execute("""
                INSERT INTO stock_technical_indicators 
                (symbol, trade_date, macd_dif, macd_dea, macd_histogram, 
                 sma_5, sma_10, sma_20, sma_60, ema_12, ema_26, 
                 rsi_14, boll_upper, boll_middle, boll_lower) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                symbol, latest_date,
                round(random.uniform(-0.5, 0.5), 4),
                round(random.uniform(-0.3, 0.3), 4),
                round(random.uniform(-0.2, 0.2), 4),
                round(random.uniform(10, 50), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(20, 80), 2),
                round(random.uniform(15, 60), 2),
                round(random.uniform(10, 50), 2),
                round(random.uniform(5, 45), 2)
            ))
    
    # 4. 生成基本面数据
    for symbol, name, exchange in stocks:
        cursor.execute("""
            INSERT INTO stock_fundamental_data 
            (symbol, report_date, pe_ratio, pb_ratio, ps_ratio, 
             roe, gross_profit_margin, net_profit_margin, 
             revenue_growth, profit_growth, eps_growth) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol, datetime.now().strftime('%Y-%m-%d'),
            round(random.uniform(5, 50), 2),
            round(random.uniform(0.5, 8), 2),
            round(random.uniform(1, 10), 2),
            round(random.uniform(5, 25), 2),
            round(random.uniform(10, 60), 2),
            round(random.uniform(2, 30), 2),
            round(random.uniform(-20, 50), 2),
            round(random.uniform(-30, 80), 2),
            round(random.uniform(-50, 100), 2)
        ))
    
    conn.commit()
    conn.close()
    print(f"✅ 生成 {len(stocks)} 只股票的完整模拟数据")

def main():
    print("🚀 初始化VeighNa量化交易系统")
    print("=" * 50)
    
    print("1. 创建数据库表...")
    create_tables()
    
    print("2. 生成模拟数据...")
    generate_mock_data()
    
    print("=" * 50)
    print("✅ 系统初始化完成！")
    print("现在可以启动系统: python web_server.py")

if __name__ == "__main__":
    main()
