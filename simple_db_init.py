#!/usr/bin/env python3
"""
简化的数据库初始化脚本
"""

import os
import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_database():
    """创建数据库和基本表"""
    try:
        logger.info("🚀 开始创建数据库...")
        
        # 确保数据目录存在
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        os.makedirs(data_dir, exist_ok=True)
        
        # 数据库文件路径
        db_path = os.path.join(data_dir, 'quantitative_trading_system.db')
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建基本表
        create_tables(cursor)
        
        # 插入默认数据
        insert_default_data(cursor)
        
        conn.commit()
        conn.close()
        
        logger.info("🎉 数据库创建完成！")
        logger.info(f"数据库位置: {db_path}")
        
    except Exception as e:
        logger.error(f"❌ 数据库创建失败: {e}")
        raise

def create_tables(cursor):
    """创建数据库表"""
    logger.info("📋 创建数据库表...")
    
    # 股票基本信息表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS stock_info (
            id TEXT PRIMARY KEY,
            symbol TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            industry TEXT,
            sector TEXT,
            market TEXT,
            list_date DATE,
            delist_date DATE,
            market_cap INTEGER,
            total_shares INTEGER,
            float_shares INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 日线数据表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS daily_market (
            id TEXT PRIMARY KEY,
            symbol TEXT NOT NULL,
            trade_date DATE NOT NULL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            close_price REAL,
            volume INTEGER,
            amount REAL,
            turnover_rate REAL,
            pe_ratio REAL,
            pb_ratio REAL,
            market_cap REAL,
            circulating_cap REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
    """)
    
    # 因子配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS factor_config (
            id TEXT PRIMARY KEY,
            factor_id TEXT UNIQUE NOT NULL,
            factor_name TEXT NOT NULL,
            factor_category TEXT NOT NULL,
            factor_type TEXT NOT NULL,
            weight REAL NOT NULL DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            description TEXT,
            calculation_formula TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 因子参数表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS factor_parameters (
            id TEXT PRIMARY KEY,
            factor_id TEXT NOT NULL,
            param_name TEXT NOT NULL,
            param_type TEXT NOT NULL,
            param_value TEXT NOT NULL,
            default_value TEXT,
            min_value REAL,
            max_value REAL,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (factor_id) REFERENCES factor_config(factor_id)
        )
    """)
    
    # 股票评分表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS stock_scores (
            id TEXT PRIMARY KEY,
            symbol TEXT NOT NULL,
            calculation_date DATE NOT NULL,
            technical_score REAL,
            fundamental_score REAL,
            market_score REAL,
            total_score REAL NOT NULL,
            score_rank INTEGER,
            factor_scores TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, calculation_date)
        )
    """)
    
    # 交易信号表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS trading_signals (
            id TEXT PRIMARY KEY,
            symbol TEXT NOT NULL,
            signal_time DATETIME NOT NULL,
            signal_type TEXT NOT NULL,
            signal_strength TEXT NOT NULL,
            signal_score REAL NOT NULL,
            confidence REAL,
            trigger_price REAL,
            target_price REAL,
            stop_loss_price REAL,
            signal_reasons TEXT,
            technical_indicators TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 投资组合表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS portfolios (
            id TEXT PRIMARY KEY,
            portfolio_name TEXT NOT NULL,
            portfolio_type TEXT NOT NULL,
            initial_capital REAL NOT NULL,
            current_value REAL,
            cash_balance REAL,
            max_positions INTEGER DEFAULT 20,
            max_position_weight REAL DEFAULT 0.1,
            rebalance_frequency TEXT DEFAULT 'MONTHLY',
            total_return REAL,
            annual_return REAL,
            max_drawdown REAL,
            sharpe_ratio REAL,
            status TEXT DEFAULT 'ACTIVE',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 回测策略表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS backtest_strategies (
            id TEXT PRIMARY KEY,
            strategy_name TEXT NOT NULL,
            strategy_type TEXT NOT NULL,
            description TEXT,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            initial_capital REAL NOT NULL,
            strategy_params TEXT,
            max_position_size REAL,
            stop_loss_ratio REAL,
            take_profit_ratio REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 回测结果表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS backtest_results (
            id TEXT PRIMARY KEY,
            strategy_id TEXT NOT NULL,
            symbol TEXT,
            total_return REAL,
            annual_return REAL,
            max_drawdown REAL,
            sharpe_ratio REAL,
            sortino_ratio REAL,
            calmar_ratio REAL,
            volatility REAL,
            total_trades INTEGER,
            winning_trades INTEGER,
            losing_trades INTEGER,
            win_rate REAL,
            profit_loss_ratio REAL,
            max_consecutive_wins INTEGER,
            max_consecutive_losses INTEGER,
            avg_trade_return REAL,
            daily_returns TEXT,
            trade_details TEXT,
            performance_metrics TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (strategy_id) REFERENCES backtest_strategies(id)
        )
    """)
    
    # 系统配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS system_config (
            id TEXT PRIMARY KEY,
            config_key TEXT UNIQUE NOT NULL,
            config_value TEXT NOT NULL,
            config_type TEXT DEFAULT 'STRING',
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 系统日志表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS system_logs (
            id TEXT PRIMARY KEY,
            log_level TEXT NOT NULL,
            module_name TEXT NOT NULL,
            message TEXT NOT NULL,
            details TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    logger.info("✅ 数据库表创建完成")

def insert_default_data(cursor):
    """插入默认数据"""
    logger.info("🔧 插入默认数据...")
    
    # 检查是否已有数据
    cursor.execute("SELECT COUNT(*) FROM factor_config")
    if cursor.fetchone()[0] > 0:
        logger.info("默认数据已存在，跳过插入")
        return
    
    # 插入默认因子配置
    default_factors = [
        ('momentum_5d', '5日动量因子', 'technical', 'momentum', 0.05, '5日价格动量'),
        ('momentum_20d', '20日动量因子', 'technical', 'momentum', 0.10, '20日价格动量'),
        ('rsi_14', 'RSI相对强弱指标', 'technical', 'momentum', 0.08, '14日RSI指标'),
        ('macd', 'MACD指标', 'technical', 'momentum', 0.12, 'MACD金叉死叉'),
        ('ma_trend', '均线趋势', 'technical', 'trend', 0.15, '多均线趋势判断'),
        ('pe_ratio', 'PE估值因子', 'fundamental', 'value', 0.10, 'PE市盈率估值'),
        ('pb_ratio', 'PB估值因子', 'fundamental', 'value', 0.08, 'PB市净率估值'),
        ('roe', 'ROE盈利因子', 'fundamental', 'quality', 0.12, '净资产收益率'),
        ('revenue_growth', '营收增长因子', 'fundamental', 'growth', 0.10, '营收增长率'),
        ('volume_ratio', '成交量比率', 'market', 'liquidity', 0.10, '成交量相对比率')
    ]
    
    for factor in default_factors:
        cursor.execute("""
            INSERT INTO factor_config (
                id, factor_id, factor_name, factor_category, factor_type, weight, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (f"factor_{factor[0]}", factor[0], factor[1], factor[2], factor[3], factor[4], factor[5]))
    
    # 插入默认系统配置
    default_configs = [
        ('data_collection_interval', '300', 'INTEGER', '数据采集间隔(秒)'),
        ('max_stock_selection', '50', 'INTEGER', '最大选股数量'),
        ('default_portfolio_size', '20', 'INTEGER', '默认组合大小'),
        ('risk_free_rate', '0.03', 'FLOAT', '无风险利率'),
        ('max_position_weight', '0.1', 'FLOAT', '单股最大权重'),
        ('technical_score_weight', '0.5', 'FLOAT', '技术面评分权重'),
        ('fundamental_score_weight', '0.3', 'FLOAT', '基本面评分权重'),
        ('market_score_weight', '0.2', 'FLOAT', '市场表现评分权重')
    ]
    
    for config in default_configs:
        cursor.execute("""
            INSERT INTO system_config (
                id, config_key, config_value, config_type, description
            ) VALUES (?, ?, ?, ?, ?)
        """, (f"config_{config[0]}", config[0], config[1], config[2], config[3]))
    
    logger.info("✅ 默认数据插入完成")

if __name__ == "__main__":
    create_database()
