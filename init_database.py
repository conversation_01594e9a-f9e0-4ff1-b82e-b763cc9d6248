#!/usr/bin/env python3
"""
量化交易系统数据库初始化脚本
基于ADATA接口文档和产品设计要求
"""

import sys
import os
import sqlite3
from datetime import datetime, date
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("🚀 开始初始化数据库...")

        # 确保数据目录存在
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        os.makedirs(data_dir, exist_ok=True)

        # 数据库文件路径
        db_path = os.path.join(data_dir, 'quantitative_trading_system.db')

        # 执行SQL初始化脚本
        logger.info("📋 执行数据库初始化脚本...")
        execute_sql_script(db_path)

        # 验证表是否创建成功
        verify_tables()

        # 初始化默认数据
        init_default_data()

        logger.info("🎉 数据库初始化完成！")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

def execute_sql_script(db_path):
    """执行SQL初始化脚本"""
    try:
        # 读取SQL脚本
        sql_script_path = os.path.join(os.path.dirname(__file__), 'database_init.sql')

        if not os.path.exists(sql_script_path):
            logger.error(f"❌ SQL脚本文件不存在: {sql_script_path}")
            return

        with open(sql_script_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # 连接数据库并执行脚本
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 分割SQL语句并执行
        sql_statements = sql_script.split(';')

        for statement in sql_statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    # 跳过MySQL特定的语句
                    if any(keyword in statement.upper() for keyword in [
                        'CREATE DATABASE', 'USE ', 'ENGINE=INNODB',
                        'DEFAULT CHARSET=UTF8MB4', 'COMMENT=', 'ON UPDATE CURRENT_TIMESTAMP'
                    ]):
                        continue

                    # 替换MySQL语法为SQLite语法
                    statement = statement.replace('VARCHAR(36) PRIMARY KEY DEFAULT (UUID())', 'TEXT PRIMARY KEY')
                    statement = statement.replace('DECIMAL(', 'REAL')
                    statement = statement.replace('BIGINT', 'INTEGER')
                    statement = statement.replace('TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                    statement = statement.replace('ENUM(', 'TEXT CHECK (')
                    statement = statement.replace('JSON', 'TEXT')

                    cursor.execute(statement)

                except sqlite3.Error as e:
                    if "already exists" not in str(e):
                        logger.warning(f"⚠️ SQL执行警告: {e}")

        conn.commit()
        conn.close()

        logger.info("✅ 数据库脚本执行完成")

    except Exception as e:
        logger.error(f"❌ 执行SQL脚本失败: {e}")
        raise

def verify_tables():
    """验证表是否创建成功"""
    try:
        logger.info("🔍 验证数据库表...")

        # 数据库文件路径
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        db_path = os.path.join(data_dir, 'quantitative_trading_system.db')

        if not os.path.exists(db_path):
            logger.error(f"❌ 数据库文件不存在: {db_path}")
            return

        # 检查关键表是否存在
        key_tables = [
            'stock_info',
            'daily_market',
            'minute_1_market',
            'minute_5_market',
            'minute_15_market',
            'hour_1_market',
            'hour_4_market',
            'financial_data',
            'factor_config',
            'factor_parameters',
            'stock_scores',
            'trading_signals',
            'portfolios',
            'portfolio_holdings',
            'trading_orders',
            'backtest_strategies',
            'backtest_results',
            'system_config',
            'system_logs'
        ]

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        for table_name in key_tables:
            try:
                cursor.execute("""
                    SELECT COUNT(*) as count
                    FROM sqlite_master
                    WHERE type='table'
                    AND name = ?
                """, (table_name,))

                result = cursor.fetchone()

                if result and result[0] > 0:
                    logger.info(f"✅ 表 {table_name} 创建成功")
                else:
                    logger.error(f"❌ 表 {table_name} 创建失败")

            except Exception as e:
                logger.error(f"❌ 检查表 {table_name} 失败: {e}")

        conn.close()
        logger.info("✅ 数据库表验证完成")

    except Exception as e:
        logger.error(f"❌ 验证数据库表失败: {e}")
        raise

def init_default_data():
    """初始化默认数据"""
    try:
        logger.info("🔧 初始化默认数据...")

        # 数据库文件路径
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        db_path = os.path.join(data_dir, 'quantitative_trading_system.db')

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 初始化默认因子配置
        init_default_factors(cursor)

        # 初始化系统配置
        init_system_config(cursor)

        conn.commit()
        conn.close()

        logger.info("✅ 默认数据初始化完成")

    except Exception as e:
        logger.error(f"❌ 初始化默认数据失败: {e}")
        raise

def init_default_factors(cursor):
    """初始化默认因子配置"""
    logger.info("📊 初始化默认因子配置...")

    # 检查是否已经存在因子配置
    cursor.execute("SELECT COUNT(*) FROM factor_config")
    existing_count = cursor.fetchone()[0]
    if existing_count > 0:
        logger.info(f"因子配置已存在 {existing_count} 个，跳过初始化")
        return
    
    # 默认因子配置
    default_factors = [
        {
            'factor_id': 'momentum_5d',
            'factor_name': '5日动量因子',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.0500'),
            'description': '5日价格动量指标',
            'calculation_formula': '(close_price_today / close_price_5d_ago - 1) * 100'
        },
        {
            'factor_id': 'momentum_20d',
            'factor_name': '20日动量因子',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.1000'),
            'description': '20日价格动量指标',
            'calculation_formula': '(close_price_today / close_price_20d_ago - 1) * 100'
        },
        {
            'factor_id': 'rsi_14',
            'factor_name': 'RSI相对强弱指标',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.0800'),
            'description': '14日RSI相对强弱指标',
            'calculation_formula': 'RSI = 100 - (100 / (1 + RS))'
        },
        {
            'factor_id': 'macd',
            'factor_name': 'MACD指标',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.1200'),
            'description': 'MACD金叉死叉信号',
            'calculation_formula': 'MACD = EMA12 - EMA26'
        },
        {
            'factor_id': 'ma_trend',
            'factor_name': '均线趋势',
            'factor_category': 'technical',
            'factor_type': 'trend',
            'weight': Decimal('0.1500'),
            'description': '多均线趋势判断',
            'calculation_formula': 'MA5 > MA10 > MA20 > MA60'
        },
        {
            'factor_id': 'pe_ratio',
            'factor_name': 'PE估值因子',
            'factor_category': 'fundamental',
            'factor_type': 'value',
            'weight': Decimal('0.1000'),
            'description': 'PE市盈率估值指标',
            'calculation_formula': 'PE = 股价 / 每股收益'
        },
        {
            'factor_id': 'pb_ratio',
            'factor_name': 'PB估值因子',
            'factor_category': 'fundamental',
            'factor_type': 'value',
            'weight': Decimal('0.0800'),
            'description': 'PB市净率估值指标',
            'calculation_formula': 'PB = 股价 / 每股净资产'
        },
        {
            'factor_id': 'roe',
            'factor_name': 'ROE盈利因子',
            'factor_category': 'fundamental',
            'factor_type': 'quality',
            'weight': Decimal('0.1200'),
            'description': '净资产收益率',
            'calculation_formula': 'ROE = 净利润 / 股东权益'
        },
        {
            'factor_id': 'revenue_growth',
            'factor_name': '营收增长因子',
            'factor_category': 'fundamental',
            'factor_type': 'growth',
            'weight': Decimal('0.1000'),
            'description': '营收增长率',
            'calculation_formula': '(本期营收 - 上期营收) / 上期营收'
        },
        {
            'factor_id': 'volume_ratio',
            'factor_name': '成交量比率',
            'factor_category': 'market',
            'factor_type': 'liquidity',
            'weight': Decimal('0.1000'),
            'description': '成交量相对比率',
            'calculation_formula': '当日成交量 / 20日平均成交量'
        }
    ]
    
    # 插入因子配置
    for factor_data in default_factors:
        cursor.execute("""
            INSERT INTO factor_config (
                factor_id, factor_name, factor_category, factor_type,
                weight, description, calculation_formula
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            factor_data['factor_id'],
            factor_data['factor_name'],
            factor_data['factor_category'],
            factor_data['factor_type'],
            float(factor_data['weight']),
            factor_data['description'],
            factor_data['calculation_formula']
        ))
    
    # 添加因子参数示例
    factor_params = [
        {
            'factor_id': 'momentum_5d',
            'param_name': 'window',
            'param_type': 'int',
            'param_value': '5',
            'default_value': '5',
            'min_value': Decimal('1'),
            'max_value': Decimal('30'),
            'description': '动量计算窗口期'
        },
        {
            'factor_id': 'rsi_14',
            'param_name': 'period',
            'param_type': 'int',
            'param_value': '14',
            'default_value': '14',
            'min_value': Decimal('6'),
            'max_value': Decimal('30'),
            'description': 'RSI计算周期'
        },
        {
            'factor_id': 'macd',
            'param_name': 'fast_period',
            'param_type': 'int',
            'param_value': '12',
            'default_value': '12',
            'min_value': Decimal('5'),
            'max_value': Decimal('20'),
            'description': 'MACD快线周期'
        },
        {
            'factor_id': 'macd',
            'param_name': 'slow_period',
            'param_type': 'int',
            'param_value': '26',
            'default_value': '26',
            'min_value': Decimal('20'),
            'max_value': Decimal('40'),
            'description': 'MACD慢线周期'
        }
    ]
    
    for param_data in factor_params:
        cursor.execute("""
            INSERT INTO factor_parameters (
                factor_id, param_name, param_type, param_value,
                default_value, min_value, max_value, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            param_data['factor_id'],
            param_data['param_name'],
            param_data['param_type'],
            param_data['param_value'],
            param_data['default_value'],
            float(param_data['min_value']) if param_data['min_value'] else None,
            float(param_data['max_value']) if param_data['max_value'] else None,
            param_data['description']
        ))
    
    logger.info(f"✅ 初始化了 {len(default_factors)} 个默认因子配置")

def init_system_config(cursor):
    """初始化系统配置"""
    logger.info("⚙️ 初始化系统配置...")

    # 检查是否已经存在系统配置
    cursor.execute("SELECT COUNT(*) FROM system_config")
    existing_count = cursor.fetchone()[0]
    if existing_count > 0:
        logger.info(f"系统配置已存在 {existing_count} 个，跳过初始化")
        return
    
    # 默认系统配置
    default_configs = [
        {
            'config_key': 'data_collection_interval',
            'config_value': '300',
            'config_type': 'INTEGER',
            'description': '数据采集间隔(秒)'
        },
        {
            'config_key': 'max_stock_selection',
            'config_value': '50',
            'config_type': 'INTEGER',
            'description': '最大选股数量'
        },
        {
            'config_key': 'default_portfolio_size',
            'config_value': '20',
            'config_type': 'INTEGER',
            'description': '默认组合大小'
        },
        {
            'config_key': 'risk_free_rate',
            'config_value': '0.03',
            'config_type': 'FLOAT',
            'description': '无风险利率'
        },
        {
            'config_key': 'max_position_weight',
            'config_value': '0.1',
            'config_type': 'FLOAT',
            'description': '单股最大权重'
        },
        {
            'config_key': 'rebalance_threshold',
            'config_value': '0.05',
            'config_type': 'FLOAT',
            'description': '再平衡阈值'
        },
        {
            'config_key': 'enable_real_trading',
            'config_value': 'false',
            'config_type': 'BOOLEAN',
            'description': '是否启用实盘交易'
        },
        {
            'config_key': 'backtest_commission',
            'config_value': '0.0003',
            'config_type': 'FLOAT',
            'description': '回测手续费率'
        },
        {
            'config_key': 'technical_score_weight',
            'config_value': '0.5',
            'config_type': 'FLOAT',
            'description': '技术面评分权重'
        },
        {
            'config_key': 'fundamental_score_weight',
            'config_value': '0.3',
            'config_type': 'FLOAT',
            'description': '基本面评分权重'
        },
        {
            'config_key': 'market_score_weight',
            'config_value': '0.2',
            'config_type': 'FLOAT',
            'description': '市场表现评分权重'
        }
    ]
    
    # 插入系统配置
    for config_data in default_configs:
        cursor.execute("""
            INSERT INTO system_config (
                config_key, config_value, config_type, description
            ) VALUES (?, ?, ?, ?)
        """, (
            config_data['config_key'],
            config_data['config_value'],
            config_data['config_type'],
            config_data['description']
        ))
    
    logger.info(f"✅ 初始化了 {len(default_configs)} 个系统配置")

def reset_database():
    """重置数据库（删除所有表并重新创建）"""
    logger.warning("⚠️ 准备重置数据库，这将删除所有数据！")

    try:
        # 删除数据库文件
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        db_path = os.path.join(data_dir, 'quantitative_trading_system.db')

        if os.path.exists(db_path):
            logger.info("🗑️ 删除现有数据库文件...")
            os.remove(db_path)

        # 重新初始化
        init_database()

        logger.info("🎉 数据库重置完成！")

    except Exception as e:
        logger.error(f"❌ 数据库重置失败: {e}")
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='量化交易系统数据库初始化')
    parser.add_argument('--reset', action='store_true', help='重置数据库（删除所有数据）')
    
    args = parser.parse_args()
    
    if args.reset:
        confirm = input("确认要重置数据库吗？这将删除所有数据！(yes/no): ")
        if confirm.lower() == 'yes':
            reset_database()
        else:
            logger.info("取消重置操作")
    else:
        init_database()
