#!/usr/bin/env python3
"""
量化交易系统数据库初始化脚本
基于ADATA接口文档和产品设计要求
"""

import asyncio
import sys
import os
from datetime import datetime, date
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_models import (
    Base, db_manager, FactorConfig, FactorParameter, SystemConfig,
    StockInfo, DailyMarket, FinancialData
)
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("🚀 开始初始化数据库...")
        
        # 创建所有表
        logger.info("📋 创建数据库表...")
        db_manager.create_all_tables()
        logger.info("✅ 数据库表创建完成")
        
        # 验证表是否创建成功
        verify_tables()
        
        # 初始化默认数据
        init_default_data()
        
        logger.info("🎉 数据库初始化完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

def verify_tables():
    """验证表是否创建成功"""
    try:
        logger.info("🔍 验证数据库表...")
        
        # 检查关键表是否存在
        key_tables = [
            'stock_info',
            'daily_market',
            'minute_1_market',
            'minute_5_market', 
            'minute_15_market',
            'hour_1_market',
            'hour_4_market',
            'financial_data',
            'factor_config',
            'factor_parameters',
            'stock_scores',
            'trading_signals',
            'portfolios',
            'portfolio_holdings',
            'trading_orders',
            'backtest_strategies',
            'backtest_results',
            'system_config',
            'system_logs'
        ]
        
        with db_manager.get_session_context() as session:
            for table_name in key_tables:
                try:
                    # 检查表是否存在（兼容SQLite和PostgreSQL）
                    if db_manager.engine.url.drivername == 'sqlite':
                        sql = text("""
                            SELECT COUNT(*) as count
                            FROM sqlite_master
                            WHERE type='table'
                            AND name = :table_name
                        """)
                    else:
                        sql = text("""
                            SELECT COUNT(*) as count
                            FROM information_schema.tables
                            WHERE table_name = :table_name
                        """)

                    result = session.execute(sql, {"table_name": table_name}).fetchone()

                    if result and result.count > 0:
                        logger.info(f"✅ 表 {table_name} 创建成功")
                    else:
                        logger.error(f"❌ 表 {table_name} 创建失败")

                except Exception as e:
                    logger.error(f"❌ 检查表 {table_name} 失败: {e}")
                    
        logger.info("✅ 数据库表验证完成")
        
    except Exception as e:
        logger.error(f"❌ 验证数据库表失败: {e}")
        raise

def init_default_data():
    """初始化默认数据"""
    try:
        logger.info("🔧 初始化默认数据...")
        
        with db_manager.get_session_context() as session:
            # 初始化默认因子配置
            init_default_factors(session)

            # 初始化系统配置
            init_system_config(session)
            
        logger.info("✅ 默认数据初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 初始化默认数据失败: {e}")
        raise

def init_default_factors(session):
    """初始化默认因子配置"""
    logger.info("📊 初始化默认因子配置...")
    
    # 检查是否已经存在因子配置
    existing_count = session.query(FactorConfig).count()
    if existing_count > 0:
        logger.info(f"因子配置已存在 {existing_count} 个，跳过初始化")
        return
    
    # 默认因子配置
    default_factors = [
        {
            'factor_id': 'momentum_5d',
            'factor_name': '5日动量因子',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.0500'),
            'description': '5日价格动量指标',
            'calculation_formula': '(close_price_today / close_price_5d_ago - 1) * 100'
        },
        {
            'factor_id': 'momentum_20d',
            'factor_name': '20日动量因子',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.1000'),
            'description': '20日价格动量指标',
            'calculation_formula': '(close_price_today / close_price_20d_ago - 1) * 100'
        },
        {
            'factor_id': 'rsi_14',
            'factor_name': 'RSI相对强弱指标',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.0800'),
            'description': '14日RSI相对强弱指标',
            'calculation_formula': 'RSI = 100 - (100 / (1 + RS))'
        },
        {
            'factor_id': 'macd',
            'factor_name': 'MACD指标',
            'factor_category': 'technical',
            'factor_type': 'momentum',
            'weight': Decimal('0.1200'),
            'description': 'MACD金叉死叉信号',
            'calculation_formula': 'MACD = EMA12 - EMA26'
        },
        {
            'factor_id': 'ma_trend',
            'factor_name': '均线趋势',
            'factor_category': 'technical',
            'factor_type': 'trend',
            'weight': Decimal('0.1500'),
            'description': '多均线趋势判断',
            'calculation_formula': 'MA5 > MA10 > MA20 > MA60'
        },
        {
            'factor_id': 'pe_ratio',
            'factor_name': 'PE估值因子',
            'factor_category': 'fundamental',
            'factor_type': 'value',
            'weight': Decimal('0.1000'),
            'description': 'PE市盈率估值指标',
            'calculation_formula': 'PE = 股价 / 每股收益'
        },
        {
            'factor_id': 'pb_ratio',
            'factor_name': 'PB估值因子',
            'factor_category': 'fundamental',
            'factor_type': 'value',
            'weight': Decimal('0.0800'),
            'description': 'PB市净率估值指标',
            'calculation_formula': 'PB = 股价 / 每股净资产'
        },
        {
            'factor_id': 'roe',
            'factor_name': 'ROE盈利因子',
            'factor_category': 'fundamental',
            'factor_type': 'quality',
            'weight': Decimal('0.1200'),
            'description': '净资产收益率',
            'calculation_formula': 'ROE = 净利润 / 股东权益'
        },
        {
            'factor_id': 'revenue_growth',
            'factor_name': '营收增长因子',
            'factor_category': 'fundamental',
            'factor_type': 'growth',
            'weight': Decimal('0.1000'),
            'description': '营收增长率',
            'calculation_formula': '(本期营收 - 上期营收) / 上期营收'
        },
        {
            'factor_id': 'volume_ratio',
            'factor_name': '成交量比率',
            'factor_category': 'market',
            'factor_type': 'liquidity',
            'weight': Decimal('0.1000'),
            'description': '成交量相对比率',
            'calculation_formula': '当日成交量 / 20日平均成交量'
        }
    ]
    
    # 插入因子配置
    for factor_data in default_factors:
        factor = FactorConfig(**factor_data)
        session.add(factor)
    
    # 添加因子参数示例
    factor_params = [
        {
            'factor_id': 'momentum_5d',
            'param_name': 'window',
            'param_type': 'int',
            'param_value': '5',
            'default_value': '5',
            'min_value': Decimal('1'),
            'max_value': Decimal('30'),
            'description': '动量计算窗口期'
        },
        {
            'factor_id': 'rsi_14',
            'param_name': 'period',
            'param_type': 'int',
            'param_value': '14',
            'default_value': '14',
            'min_value': Decimal('6'),
            'max_value': Decimal('30'),
            'description': 'RSI计算周期'
        },
        {
            'factor_id': 'macd',
            'param_name': 'fast_period',
            'param_type': 'int',
            'param_value': '12',
            'default_value': '12',
            'min_value': Decimal('5'),
            'max_value': Decimal('20'),
            'description': 'MACD快线周期'
        },
        {
            'factor_id': 'macd',
            'param_name': 'slow_period',
            'param_type': 'int',
            'param_value': '26',
            'default_value': '26',
            'min_value': Decimal('20'),
            'max_value': Decimal('40'),
            'description': 'MACD慢线周期'
        }
    ]
    
    for param_data in factor_params:
        param = FactorParameter(**param_data)
        session.add(param)
    
    logger.info(f"✅ 初始化了 {len(default_factors)} 个默认因子配置")

def init_system_config(session):
    """初始化系统配置"""
    logger.info("⚙️ 初始化系统配置...")
    
    # 检查是否已经存在系统配置
    existing_count = session.query(SystemConfig).count()
    if existing_count > 0:
        logger.info(f"系统配置已存在 {existing_count} 个，跳过初始化")
        return
    
    # 默认系统配置
    default_configs = [
        {
            'config_key': 'data_collection_interval',
            'config_value': '300',
            'config_type': 'INTEGER',
            'description': '数据采集间隔(秒)'
        },
        {
            'config_key': 'max_stock_selection',
            'config_value': '50',
            'config_type': 'INTEGER',
            'description': '最大选股数量'
        },
        {
            'config_key': 'default_portfolio_size',
            'config_value': '20',
            'config_type': 'INTEGER',
            'description': '默认组合大小'
        },
        {
            'config_key': 'risk_free_rate',
            'config_value': '0.03',
            'config_type': 'FLOAT',
            'description': '无风险利率'
        },
        {
            'config_key': 'max_position_weight',
            'config_value': '0.1',
            'config_type': 'FLOAT',
            'description': '单股最大权重'
        },
        {
            'config_key': 'rebalance_threshold',
            'config_value': '0.05',
            'config_type': 'FLOAT',
            'description': '再平衡阈值'
        },
        {
            'config_key': 'enable_real_trading',
            'config_value': 'false',
            'config_type': 'BOOLEAN',
            'description': '是否启用实盘交易'
        },
        {
            'config_key': 'backtest_commission',
            'config_value': '0.0003',
            'config_type': 'FLOAT',
            'description': '回测手续费率'
        },
        {
            'config_key': 'technical_score_weight',
            'config_value': '0.5',
            'config_type': 'FLOAT',
            'description': '技术面评分权重'
        },
        {
            'config_key': 'fundamental_score_weight',
            'config_value': '0.3',
            'config_type': 'FLOAT',
            'description': '基本面评分权重'
        },
        {
            'config_key': 'market_score_weight',
            'config_value': '0.2',
            'config_type': 'FLOAT',
            'description': '市场表现评分权重'
        }
    ]
    
    # 插入系统配置
    for config_data in default_configs:
        config = SystemConfig(**config_data)
        session.add(config)
    
    logger.info(f"✅ 初始化了 {len(default_configs)} 个系统配置")

def reset_database():
    """重置数据库（删除所有表并重新创建）"""
    logger.warning("⚠️ 准备重置数据库，这将删除所有数据！")
    
    try:
        # 删除所有表
        logger.info("🗑️ 删除所有表...")
        db_manager.drop_all_tables()
        
        # 重新初始化
        init_database()
        
        logger.info("🎉 数据库重置完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库重置失败: {e}")
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='量化交易系统数据库初始化')
    parser.add_argument('--reset', action='store_true', help='重置数据库（删除所有数据）')
    
    args = parser.parse_args()
    
    if args.reset:
        confirm = input("确认要重置数据库吗？这将删除所有数据！(yes/no): ")
        if confirm.lower() == 'yes':
            reset_database()
        else:
            logger.info("取消重置操作")
    else:
        init_database()
