"""
基本面分析器
实现财务数据分析和基本面评分
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

from database_models import db_manager, FinancialData, DailyMarket

logger = logging.getLogger(__name__)

class FinancialAnalyzer:
    """基本面分析器"""
    
    def __init__(self):
        # 基本面评分权重配置
        self.scoring_weights = {
            'profitability': 0.4,    # 盈利能力 40%
            'valuation': 0.3,        # 估值水平 30%
            'growth': 0.2,           # 成长性 20%
            'financial_health': 0.1  # 财务健康度 10%
        }
        
        # 行业基准值（简化处理，实际应该从数据库获取）
        self.industry_benchmarks = {
            'roe': 0.15,        # ROE基准15%
            'pe_ratio': 20.0,   # PE基准20倍
            'pb_ratio': 2.0,    # PB基准2倍
            'revenue_growth': 0.1,  # 营收增长基准10%
            'profit_growth': 0.15,  # 利润增长基准15%
            'debt_ratio': 0.5,      # 资产负债率基准50%
            'current_ratio': 2.0    # 流动比率基准2.0
        }
        
        logger.info("💰 基本面分析器初始化完成")
    
    def get_financial_data(self, symbol: str, periods: int = 4) -> Optional[pd.DataFrame]:
        """
        获取股票财务数据
        
        Args:
            symbol: 股票代码
            periods: 获取最近几期数据
            
        Returns:
            财务数据DataFrame
        """
        try:
            with db_manager.get_session() as session:
                financial_data = session.query(FinancialData).filter(
                    FinancialData.symbol == symbol
                ).order_by(FinancialData.report_date.desc()).limit(periods).all()
                
                if not financial_data:
                    logger.debug(f"⚠️ 未找到财务数据: {symbol}")
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in financial_data:
                    data_dict = {
                        'symbol': record.symbol,
                        'report_date': record.report_date,
                        'report_type': record.report_type,
                        'total_revenue': record.total_revenue,
                        'net_profit': record.net_profit,
                        'total_assets': record.total_assets,
                        'total_equity': record.total_equity,
                        'roe': record.roe,
                        'roa': record.roa,
                        'gross_margin': record.gross_margin,
                        'net_margin': record.net_margin,
                        'debt_ratio': record.debt_ratio,
                        'current_ratio': record.current_ratio,
                        'revenue_growth': record.revenue_growth,
                        'profit_growth': record.profit_growth
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取财务数据失败: {symbol} - {e}")
            return None
    
    def get_market_data(self, symbol: str, days: int = 30) -> Optional[pd.DataFrame]:
        """
        获取市场数据（用于估值分析）
        
        Args:
            symbol: 股票代码
            days: 获取最近几天数据
            
        Returns:
            市场数据DataFrame
        """
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                market_data = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.desc()).all()
                
                if not market_data:
                    logger.debug(f"⚠️ 未找到市场数据: {symbol}")
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in market_data:
                    data_dict = {
                        'symbol': record.symbol,
                        'trade_date': record.trade_date,
                        'close_price': record.close_price,
                        'market_cap': record.market_cap,
                        'pe_ratio': record.pe_ratio,
                        'pb_ratio': record.pb_ratio,
                        'turnover_rate': record.turnover_rate
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def calculate_profitability_score(self, financial_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算盈利能力评分
        
        Args:
            financial_data: 财务数据
            
        Returns:
            盈利能力评分字典
        """
        try:
            if financial_data.empty:
                return {'profitability_score': 50.0}
            
            latest_data = financial_data.iloc[0]  # 最新一期数据
            
            scores = {}
            
            # 1. ROE评分 (权重: 0.4)
            roe = latest_data.get('roe', 0) or 0
            roe_score = min(100, max(0, (roe / self.industry_benchmarks['roe']) * 50))
            scores['roe_score'] = roe_score
            
            # 2. ROA评分 (权重: 0.3)
            roa = latest_data.get('roa', 0) or 0
            roa_score = min(100, max(0, (roa / (self.industry_benchmarks['roe'] * 0.5)) * 50))
            scores['roa_score'] = roa_score
            
            # 3. 净利率评分 (权重: 0.2)
            net_margin = latest_data.get('net_margin', 0) or 0
            net_margin_score = min(100, max(0, (net_margin / 0.1) * 50))  # 基准10%
            scores['net_margin_score'] = net_margin_score
            
            # 4. 毛利率评分 (权重: 0.1)
            gross_margin = latest_data.get('gross_margin', 0) or 0
            gross_margin_score = min(100, max(0, (gross_margin / 0.3) * 50))  # 基准30%
            scores['gross_margin_score'] = gross_margin_score
            
            # 综合盈利能力评分
            profitability_score = (
                roe_score * 0.4 +
                roa_score * 0.3 +
                net_margin_score * 0.2 +
                gross_margin_score * 0.1
            )
            
            scores['profitability_score'] = profitability_score
            
            return scores
            
        except Exception as e:
            logger.error(f"❌ 计算盈利能力评分失败: {e}")
            return {'profitability_score': 50.0}
    
    def calculate_valuation_score(self, market_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算估值水平评分
        
        Args:
            market_data: 市场数据
            
        Returns:
            估值评分字典
        """
        try:
            if market_data.empty:
                return {'valuation_score': 50.0}
            
            latest_data = market_data.iloc[0]  # 最新数据
            
            scores = {}
            
            # 1. PE估值评分 (权重: 0.6)
            pe_ratio = latest_data.get('pe_ratio', 0) or 0
            if pe_ratio > 0:
                # PE越低估值越有吸引力，但要避免过低（可能是业绩问题）
                if pe_ratio < 5:
                    pe_score = 30  # 过低可能有问题
                elif pe_ratio <= 15:
                    pe_score = 90  # 低估值
                elif pe_ratio <= 25:
                    pe_score = 70  # 合理估值
                elif pe_ratio <= 40:
                    pe_score = 40  # 高估值
                else:
                    pe_score = 10  # 严重高估
            else:
                pe_score = 20  # 负PE或无PE
            
            scores['pe_score'] = pe_score
            
            # 2. PB估值评分 (权重: 0.4)
            pb_ratio = latest_data.get('pb_ratio', 0) or 0
            if pb_ratio > 0:
                if pb_ratio < 0.8:
                    pb_score = 95  # 破净，可能低估
                elif pb_ratio <= 1.5:
                    pb_score = 85  # 低PB
                elif pb_ratio <= 3.0:
                    pb_score = 60  # 合理PB
                elif pb_ratio <= 5.0:
                    pb_score = 30  # 高PB
                else:
                    pb_score = 10  # 极高PB
            else:
                pb_score = 20
            
            scores['pb_score'] = pb_score
            
            # 综合估值评分
            valuation_score = pe_score * 0.6 + pb_score * 0.4
            scores['valuation_score'] = valuation_score
            
            return scores
            
        except Exception as e:
            logger.error(f"❌ 计算估值评分失败: {e}")
            return {'valuation_score': 50.0}
    
    def calculate_growth_score(self, financial_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算成长性评分
        
        Args:
            financial_data: 财务数据
            
        Returns:
            成长性评分字典
        """
        try:
            if financial_data.empty:
                return {'growth_score': 50.0}
            
            scores = {}
            
            # 获取最新数据
            latest_data = financial_data.iloc[0]
            
            # 1. 营收增长率评分 (权重: 0.5)
            revenue_growth = latest_data.get('revenue_growth', 0) or 0
            if revenue_growth >= 0.3:  # 30%以上
                revenue_growth_score = 95
            elif revenue_growth >= 0.2:  # 20-30%
                revenue_growth_score = 85
            elif revenue_growth >= 0.1:  # 10-20%
                revenue_growth_score = 70
            elif revenue_growth >= 0:    # 0-10%
                revenue_growth_score = 50
            elif revenue_growth >= -0.1: # -10-0%
                revenue_growth_score = 30
            else:  # -10%以下
                revenue_growth_score = 10
            
            scores['revenue_growth_score'] = revenue_growth_score
            
            # 2. 利润增长率评分 (权重: 0.5)
            profit_growth = latest_data.get('profit_growth', 0) or 0
            if profit_growth >= 0.5:  # 50%以上
                profit_growth_score = 95
            elif profit_growth >= 0.3:  # 30-50%
                profit_growth_score = 85
            elif profit_growth >= 0.15: # 15-30%
                profit_growth_score = 70
            elif profit_growth >= 0:    # 0-15%
                profit_growth_score = 50
            elif profit_growth >= -0.2: # -20-0%
                profit_growth_score = 30
            else:  # -20%以下
                profit_growth_score = 10
            
            scores['profit_growth_score'] = profit_growth_score
            
            # 综合成长性评分
            growth_score = (
                revenue_growth_score * 0.5 +
                profit_growth_score * 0.5
            )
            
            scores['growth_score'] = growth_score
            
            return scores
            
        except Exception as e:
            logger.error(f"❌ 计算成长性评分失败: {e}")
            return {'growth_score': 50.0}
    
    def calculate_financial_health_score(self, financial_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算财务健康度评分
        
        Args:
            financial_data: 财务数据
            
        Returns:
            财务健康度评分字典
        """
        try:
            if financial_data.empty:
                return {'financial_health_score': 50.0}
            
            latest_data = financial_data.iloc[0]
            
            scores = {}
            
            # 1. 资产负债率评分 (权重: 0.6)
            debt_ratio = latest_data.get('debt_ratio', 0) or 0
            if debt_ratio <= 0.3:      # 30%以下
                debt_ratio_score = 90
            elif debt_ratio <= 0.5:    # 30-50%
                debt_ratio_score = 70
            elif debt_ratio <= 0.7:    # 50-70%
                debt_ratio_score = 50
            elif debt_ratio <= 0.8:    # 70-80%
                debt_ratio_score = 30
            else:  # 80%以上
                debt_ratio_score = 10
            
            scores['debt_ratio_score'] = debt_ratio_score
            
            # 2. 流动比率评分 (权重: 0.4)
            current_ratio = latest_data.get('current_ratio', 0) or 0
            if current_ratio >= 2.5:      # 2.5以上
                current_ratio_score = 90
            elif current_ratio >= 2.0:    # 2.0-2.5
                current_ratio_score = 80
            elif current_ratio >= 1.5:    # 1.5-2.0
                current_ratio_score = 70
            elif current_ratio >= 1.0:    # 1.0-1.5
                current_ratio_score = 50
            elif current_ratio >= 0.8:    # 0.8-1.0
                current_ratio_score = 30
            else:  # 0.8以下
                current_ratio_score = 10
            
            scores['current_ratio_score'] = current_ratio_score
            
            # 综合财务健康度评分
            financial_health_score = (
                debt_ratio_score * 0.6 +
                current_ratio_score * 0.4
            )
            
            scores['financial_health_score'] = financial_health_score
            
            return scores
            
        except Exception as e:
            logger.error(f"❌ 计算财务健康度评分失败: {e}")
            return {'financial_health_score': 50.0}
    
    def calculate_fundamental_score(self, symbol: str) -> Dict[str, Any]:
        """
        计算综合基本面评分
        
        Args:
            symbol: 股票代码
            
        Returns:
            基本面评分结果字典
        """
        try:
            logger.debug(f"💰 计算基本面评分: {symbol}")
            
            # 获取财务数据和市场数据
            financial_data = self.get_financial_data(symbol)
            market_data = self.get_market_data(symbol)
            
            # 如果没有数据，返回中性评分
            if financial_data is None and market_data is None:
                logger.debug(f"⚠️ 无基本面数据: {symbol}")
                return {
                    'fundamental_score': 50.0,
                    'profitability_score': 50.0,
                    'valuation_score': 50.0,
                    'growth_score': 50.0,
                    'financial_health_score': 50.0
                }
            
            # 计算各项评分
            profitability_scores = self.calculate_profitability_score(financial_data or pd.DataFrame())
            valuation_scores = self.calculate_valuation_score(market_data or pd.DataFrame())
            growth_scores = self.calculate_growth_score(financial_data or pd.DataFrame())
            health_scores = self.calculate_financial_health_score(financial_data or pd.DataFrame())
            
            # 综合基本面评分
            fundamental_score = (
                profitability_scores['profitability_score'] * self.scoring_weights['profitability'] +
                valuation_scores['valuation_score'] * self.scoring_weights['valuation'] +
                growth_scores['growth_score'] * self.scoring_weights['growth'] +
                health_scores['financial_health_score'] * self.scoring_weights['financial_health']
            )
            
            # 合并所有评分
            result = {
                'fundamental_score': fundamental_score,
                **profitability_scores,
                **valuation_scores,
                **growth_scores,
                **health_scores
            }
            
            logger.debug(f"✅ 基本面评分完成: {symbol} = {fundamental_score:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 计算基本面评分失败: {symbol} - {e}")
            return {
                'fundamental_score': 50.0,
                'profitability_score': 50.0,
                'valuation_score': 50.0,
                'growth_score': 50.0,
                'financial_health_score': 50.0
            }
    
    def batch_calculate_fundamental_scores(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        批量计算基本面评分
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            各股票基本面评分字典
        """
        try:
            logger.info(f"💰 批量计算基本面评分: {len(symbols)} 只股票")
            
            results = {}
            
            for symbol in symbols:
                try:
                    score_result = self.calculate_fundamental_score(symbol)
                    results[symbol] = score_result
                except Exception as e:
                    logger.error(f"❌ 计算基本面评分失败: {symbol} - {e}")
                    results[symbol] = {'fundamental_score': 50.0}
            
            logger.info(f"✅ 批量基本面评分完成: {len(results)} 只股票")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量计算基本面评分失败: {e}")
            return {}
