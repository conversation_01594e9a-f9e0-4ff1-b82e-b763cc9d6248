"""
因子计算器
负责计算和管理各种量化因子
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

from database_models import db_manager, FactorConfig, FactorParameter, DailyMarket
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class FactorCalculator:
    """因子计算器"""
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        self.factor_configs = {}
        self.factor_weights = {}
        
        # 加载因子配置
        self._load_factor_configs()
        
        logger.info("🧮 因子计算器初始化完成")
    
    def _load_factor_configs(self):
        """加载因子配置"""
        try:
            with db_manager.get_session() as session:
                factors = session.query(FactorConfig).filter(
                    FactorConfig.is_active == True
                ).all()
                
                for factor in factors:
                    self.factor_configs[factor.factor_id] = {
                        'name': factor.factor_name,
                        'category': factor.factor_category,
                        'type': factor.factor_type,
                        'weight': float(factor.weight),
                        'description': factor.description,
                        'formula': factor.calculation_formula
                    }
                    
                    self.factor_weights[factor.factor_id] = float(factor.weight)
                
                logger.info(f"✅ 加载了 {len(self.factor_configs)} 个因子配置")
                
        except Exception as e:
            logger.error(f"❌ 加载因子配置失败: {e}")
            # 使用默认配置
            self._load_default_factor_configs()
    
    def _load_default_factor_configs(self):
        """加载默认因子配置"""
        self.factor_configs = {
            'momentum_5d': {
                'name': '5日动量因子',
                'category': 'technical',
                'type': 'momentum',
                'weight': 0.05,
                'description': '5日价格动量',
                'formula': '(close_price_today / close_price_5d_ago - 1) * 100'
            },
            'momentum_20d': {
                'name': '20日动量因子',
                'category': 'technical',
                'type': 'momentum',
                'weight': 0.10,
                'description': '20日价格动量',
                'formula': '(close_price_today / close_price_20d_ago - 1) * 100'
            },
            'rsi_14': {
                'name': 'RSI相对强弱指标',
                'category': 'technical',
                'type': 'momentum',
                'weight': 0.08,
                'description': '14日RSI指标',
                'formula': 'RSI = 100 - (100 / (1 + RS))'
            },
            'macd': {
                'name': 'MACD指标',
                'category': 'technical',
                'type': 'momentum',
                'weight': 0.12,
                'description': 'MACD金叉死叉',
                'formula': 'MACD = EMA12 - EMA26'
            },
            'ma_trend': {
                'name': '均线趋势',
                'category': 'technical',
                'type': 'trend',
                'weight': 0.15,
                'description': '多均线趋势判断',
                'formula': 'MA5 > MA10 > MA20 > MA60'
            },
            'pe_ratio': {
                'name': 'PE估值因子',
                'category': 'fundamental',
                'type': 'value',
                'weight': 0.10,
                'description': 'PE市盈率估值',
                'formula': 'PE = 股价 / 每股收益'
            },
            'pb_ratio': {
                'name': 'PB估值因子',
                'category': 'fundamental',
                'type': 'value',
                'weight': 0.08,
                'description': 'PB市净率估值',
                'formula': 'PB = 股价 / 每股净资产'
            },
            'roe': {
                'name': 'ROE盈利因子',
                'category': 'fundamental',
                'type': 'quality',
                'weight': 0.12,
                'description': '净资产收益率',
                'formula': 'ROE = 净利润 / 股东权益'
            },
            'revenue_growth': {
                'name': '营收增长因子',
                'category': 'fundamental',
                'type': 'growth',
                'weight': 0.10,
                'description': '营收增长率',
                'formula': '(本期营收 - 上期营收) / 上期营收'
            },
            'volume_ratio': {
                'name': '成交量比率',
                'category': 'market',
                'type': 'liquidity',
                'weight': 0.10,
                'description': '成交量相对比率',
                'formula': '当日成交量 / 20日平均成交量'
            }
        }
        
        self.factor_weights = {k: v['weight'] for k, v in self.factor_configs.items()}
    
    def get_stock_market_data(self, symbol: str, days: int = 60) -> Optional[pd.DataFrame]:
        """
        获取股票市场数据
        
        Args:
            symbol: 股票代码
            days: 获取天数
            
        Returns:
            市场数据DataFrame
        """
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                market_data = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if not market_data:
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in market_data:
                    data_dict = {
                        'symbol': record.symbol,
                        'trade_date': record.trade_date,
                        'open_price': record.open_price,
                        'high_price': record.high_price,
                        'low_price': record.low_price,
                        'close_price': record.close_price,
                        'volume': record.volume,
                        'amount': record.amount,
                        'turnover_rate': record.turnover_rate,
                        'pe_ratio': record.pe_ratio,
                        'pb_ratio': record.pb_ratio,
                        'market_cap': record.market_cap
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def calculate_momentum_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        计算动量因子
        
        Args:
            data: 市场数据
            
        Returns:
            动量因子字典
        """
        try:
            if data.empty or len(data) < 21:
                return {
                    'momentum_5d': 0.0,
                    'momentum_20d': 0.0
                }
            
            close_prices = data['close_price']
            
            # 5日动量
            momentum_5d = 0.0
            if len(close_prices) >= 6:
                momentum_5d = (close_prices.iloc[-1] / close_prices.iloc[-6] - 1) * 100
            
            # 20日动量
            momentum_20d = 0.0
            if len(close_prices) >= 21:
                momentum_20d = (close_prices.iloc[-1] / close_prices.iloc[-21] - 1) * 100
            
            return {
                'momentum_5d': momentum_5d,
                'momentum_20d': momentum_20d
            }
            
        except Exception as e:
            logger.error(f"❌ 计算动量因子失败: {e}")
            return {'momentum_5d': 0.0, 'momentum_20d': 0.0}
    
    def calculate_technical_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        计算技术因子
        
        Args:
            data: 市场数据
            
        Returns:
            技术因子字典
        """
        try:
            if data.empty:
                return {
                    'rsi_14': 50.0,
                    'macd': 0.0,
                    'ma_trend': 0.0,
                    'volume_ratio': 1.0
                }
            
            factors = {}
            
            # 计算技术指标
            indicators = self.technical_indicators.calculate_all_indicators(data)
            
            # RSI因子
            if 'rsi' in indicators and len(indicators['rsi']) > 0:
                factors['rsi_14'] = indicators['rsi'].iloc[-1]
            else:
                factors['rsi_14'] = 50.0
            
            # MACD因子
            if 'macd' in indicators and len(indicators['macd']) > 0:
                factors['macd'] = indicators['macd'].iloc[-1]
            else:
                factors['macd'] = 0.0
            
            # 均线趋势因子
            ma_trend_score = 0.0
            if all(f'ma_{p}' in indicators for p in [5, 10, 20, 60]):
                ma_5 = indicators['ma_5'].iloc[-1] if len(indicators['ma_5']) > 0 else 0
                ma_10 = indicators['ma_10'].iloc[-1] if len(indicators['ma_10']) > 0 else 0
                ma_20 = indicators['ma_20'].iloc[-1] if len(indicators['ma_20']) > 0 else 0
                ma_60 = indicators['ma_60'].iloc[-1] if len(indicators['ma_60']) > 0 else 0
                
                if ma_5 > ma_10 > ma_20 > ma_60:
                    ma_trend_score = 100.0  # 多头排列
                elif ma_5 < ma_10 < ma_20 < ma_60:
                    ma_trend_score = 0.0    # 空头排列
                else:
                    ma_trend_score = 50.0   # 中性
            
            factors['ma_trend'] = ma_trend_score
            
            # 成交量比率因子
            if 'volume_ratio' in indicators and len(indicators['volume_ratio']) > 0:
                factors['volume_ratio'] = indicators['volume_ratio'].iloc[-1]
            else:
                factors['volume_ratio'] = 1.0
            
            return factors
            
        except Exception as e:
            logger.error(f"❌ 计算技术因子失败: {e}")
            return {
                'rsi_14': 50.0,
                'macd': 0.0,
                'ma_trend': 50.0,
                'volume_ratio': 1.0
            }
    
    def calculate_fundamental_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        计算基本面因子
        
        Args:
            data: 市场数据（包含PE、PB等）
            
        Returns:
            基本面因子字典
        """
        try:
            if data.empty:
                return {
                    'pe_ratio': 20.0,
                    'pb_ratio': 2.0,
                    'roe': 0.15,
                    'revenue_growth': 0.1
                }
            
            latest_data = data.iloc[-1]
            
            factors = {}
            
            # PE因子（转换为评分）
            pe_ratio = latest_data.get('pe_ratio', 20.0) or 20.0
            if pe_ratio > 0:
                if pe_ratio <= 10:
                    pe_score = 90
                elif pe_ratio <= 20:
                    pe_score = 70
                elif pe_ratio <= 30:
                    pe_score = 50
                else:
                    pe_score = 30
            else:
                pe_score = 20
            
            factors['pe_ratio'] = pe_score
            
            # PB因子（转换为评分）
            pb_ratio = latest_data.get('pb_ratio', 2.0) or 2.0
            if pb_ratio > 0:
                if pb_ratio <= 1.0:
                    pb_score = 90
                elif pb_ratio <= 2.0:
                    pb_score = 70
                elif pb_ratio <= 3.0:
                    pb_score = 50
                else:
                    pb_score = 30
            else:
                pb_score = 20
            
            factors['pb_ratio'] = pb_score
            
            # ROE和营收增长因子（这里使用模拟值，实际应从财务数据获取）
            factors['roe'] = 60.0  # 模拟ROE评分
            factors['revenue_growth'] = 55.0  # 模拟营收增长评分
            
            return factors
            
        except Exception as e:
            logger.error(f"❌ 计算基本面因子失败: {e}")
            return {
                'pe_ratio': 50.0,
                'pb_ratio': 50.0,
                'roe': 50.0,
                'revenue_growth': 50.0
            }
    
    def calculate_market_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        计算市场表现因子
        
        Args:
            data: 市场数据
            
        Returns:
            市场因子字典
        """
        try:
            if data.empty:
                return {'volume_ratio': 50.0}
            
            factors = {}
            
            # 成交量比率因子
            if 'volume' in data.columns and len(data) >= 20:
                recent_volume = data['volume'].iloc[-1]
                avg_volume = data['volume'].iloc[-20:].mean()
                
                if avg_volume > 0:
                    volume_ratio = recent_volume / avg_volume
                    
                    # 转换为评分
                    if volume_ratio >= 2.0:
                        volume_score = 90  # 放量
                    elif volume_ratio >= 1.5:
                        volume_score = 75
                    elif volume_ratio >= 1.0:
                        volume_score = 60
                    elif volume_ratio >= 0.5:
                        volume_score = 40
                    else:
                        volume_score = 20  # 缩量
                else:
                    volume_score = 50
            else:
                volume_score = 50
            
            factors['volume_ratio'] = volume_score
            
            return factors
            
        except Exception as e:
            logger.error(f"❌ 计算市场因子失败: {e}")
            return {'volume_ratio': 50.0}
    
    def calculate_all_factors(self, symbol: str) -> Dict[str, float]:
        """
        计算股票的所有因子
        
        Args:
            symbol: 股票代码
            
        Returns:
            所有因子字典
        """
        try:
            logger.debug(f"🧮 计算所有因子: {symbol}")
            
            # 获取市场数据
            market_data = self.get_stock_market_data(symbol)
            
            if market_data is None or market_data.empty:
                logger.debug(f"⚠️ 无市场数据: {symbol}")
                return {factor_id: 50.0 for factor_id in self.factor_configs.keys()}
            
            all_factors = {}
            
            # 计算动量因子
            momentum_factors = self.calculate_momentum_factors(market_data)
            all_factors.update(momentum_factors)
            
            # 计算技术因子
            technical_factors = self.calculate_technical_factors(market_data)
            all_factors.update(technical_factors)
            
            # 计算基本面因子
            fundamental_factors = self.calculate_fundamental_factors(market_data)
            all_factors.update(fundamental_factors)
            
            # 计算市场因子
            market_factors = self.calculate_market_factors(market_data)
            all_factors.update(market_factors)
            
            logger.debug(f"✅ 因子计算完成: {symbol}, 共 {len(all_factors)} 个因子")
            
            return all_factors
            
        except Exception as e:
            logger.error(f"❌ 计算所有因子失败: {symbol} - {e}")
            return {factor_id: 50.0 for factor_id in self.factor_configs.keys()}
    
    def batch_calculate_factors(self, symbols: List[str]) -> Dict[str, Dict[str, float]]:
        """
        批量计算因子
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            各股票因子字典
        """
        try:
            logger.info(f"🧮 批量计算因子: {len(symbols)} 只股票")
            
            results = {}
            
            for symbol in symbols:
                try:
                    factors = self.calculate_all_factors(symbol)
                    results[symbol] = factors
                except Exception as e:
                    logger.error(f"❌ 计算因子失败: {symbol} - {e}")
                    results[symbol] = {factor_id: 50.0 for factor_id in self.factor_configs.keys()}
            
            logger.info(f"✅ 批量因子计算完成: {len(results)} 只股票")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量计算因子失败: {e}")
            return {}
    
    def get_factor_weights(self) -> Dict[str, float]:
        """获取因子权重"""
        return self.factor_weights.copy()
    
    def update_factor_weight(self, factor_id: str, weight: float) -> bool:
        """
        更新因子权重
        
        Args:
            factor_id: 因子ID
            weight: 新权重
            
        Returns:
            是否更新成功
        """
        try:
            if factor_id not in self.factor_configs:
                logger.error(f"❌ 因子不存在: {factor_id}")
                return False
            
            # 更新内存中的权重
            self.factor_weights[factor_id] = weight
            self.factor_configs[factor_id]['weight'] = weight
            
            # 更新数据库中的权重
            with db_manager.get_session() as session:
                factor = session.query(FactorConfig).filter(
                    FactorConfig.factor_id == factor_id
                ).first()
                
                if factor:
                    factor.weight = weight
                    session.commit()
                    logger.info(f"✅ 更新因子权重: {factor_id} = {weight}")
                    return True
                else:
                    logger.error(f"❌ 数据库中未找到因子: {factor_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 更新因子权重失败: {factor_id} - {e}")
            return False
