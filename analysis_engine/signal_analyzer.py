"""
历史信号成功率分析器 - 按照产品设计要求实现
分析历史交易信号的成功率，为策略优化提供数据支持
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from collections import defaultdict

from database_models import db_manager

logger = logging.getLogger(__name__)

class SignalType(Enum):
    """信号类型"""
    BUY = "BUY"
    SELL = "SELL"

class AnalysisPeriod(Enum):
    """分析周期"""
    DAYS_1 = 1
    DAYS_3 = 3
    DAYS_7 = 7
    DAYS_15 = 15
    DAYS_30 = 30

@dataclass
class SignalRecord:
    """信号记录"""
    signal_id: str
    symbol: str
    signal_type: SignalType
    signal_time: datetime
    signal_price: float
    signal_strength: float
    signal_score: float
    strategy_id: str
    
@dataclass
class SignalResult:
    """信号结果"""
    signal_id: str
    symbol: str
    entry_price: float
    exit_price: Optional[float]
    exit_time: Optional[datetime]
    holding_period: int  # 持仓天数
    return_rate: float   # 收益率
    is_successful: bool  # 是否成功
    max_profit: float    # 最大盈利
    max_loss: float      # 最大亏损

@dataclass
class SuccessRateAnalysis:
    """成功率分析结果"""
    total_signals: int
    successful_signals: int
    success_rate: float
    avg_return: float
    avg_holding_period: float
    max_return: float
    min_return: float
    profit_factor: float
    sharpe_ratio: float
    win_loss_ratio: float

class SignalAnalyzer:
    """历史信号成功率分析器"""
    
    def __init__(self):
        # 分析配置
        self.analysis_periods = [1, 3, 7, 15, 30]  # 分析周期（天）
        self.success_threshold = 0.02  # 成功阈值（2%收益）
        self.max_holding_period = 30   # 最大持仓期（天）
        
        # 缓存数据
        self.signal_records: List[SignalRecord] = []
        self.signal_results: Dict[str, SignalResult] = {}
        self.analysis_cache: Dict[str, Any] = {}
        
        logger.info("📊 历史信号成功率分析器初始化完成")
    
    def load_historical_signals(self, 
                              start_date: datetime, 
                              end_date: datetime,
                              symbol: Optional[str] = None) -> bool:
        """
        加载历史信号数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            symbol: 股票代码（可选）
            
        Returns:
            加载是否成功
        """
        try:
            logger.info(f"📥 加载历史信号数据: {start_date.date()} 到 {end_date.date()}")
            
            # 从数据库加载信号记录
            # 这里使用模拟数据，实际应该从数据库查询
            self.signal_records = self._generate_mock_signals(start_date, end_date, symbol)
            
            logger.info(f"✅ 加载历史信号完成: {len(self.signal_records)}条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载历史信号失败: {e}")
            return False
    
    def analyze_signal_success_rate(self, 
                                  signal_type: Optional[SignalType] = None,
                                  strategy_id: Optional[str] = None,
                                  analysis_period: int = 7) -> SuccessRateAnalysis:
        """
        分析信号成功率
        
        Args:
            signal_type: 信号类型
            strategy_id: 策略ID
            analysis_period: 分析周期（天）
            
        Returns:
            成功率分析结果
        """
        try:
            logger.info(f"📊 开始分析信号成功率: 周期{analysis_period}天")
            
            # 过滤信号记录
            filtered_signals = self._filter_signals(signal_type, strategy_id)
            
            if not filtered_signals:
                logger.warning("⚠️ 没有符合条件的信号记录")
                return self._create_empty_analysis()
            
            # 计算信号结果
            signal_results = []
            for signal in filtered_signals:
                result = self._calculate_signal_result(signal, analysis_period)
                if result:
                    signal_results.append(result)
            
            if not signal_results:
                logger.warning("⚠️ 没有有效的信号结果")
                return self._create_empty_analysis()
            
            # 分析成功率
            analysis = self._analyze_results(signal_results)
            
            logger.info(f"✅ 信号成功率分析完成:")
            logger.info(f"  - 总信号数: {analysis.total_signals}")
            logger.info(f"  - 成功率: {analysis.success_rate:.2%}")
            logger.info(f"  - 平均收益率: {analysis.avg_return:.2%}")
            logger.info(f"  - 平均持仓期: {analysis.avg_holding_period:.1f}天")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析信号成功率失败: {e}")
            return self._create_empty_analysis()
    
    def analyze_by_signal_strength(self, analysis_period: int = 7) -> Dict[str, SuccessRateAnalysis]:
        """
        按信号强度分析成功率
        
        Args:
            analysis_period: 分析周期（天）
            
        Returns:
            按强度分组的分析结果
        """
        try:
            logger.info("📊 按信号强度分析成功率")
            
            # 定义强度区间
            strength_ranges = [
                ("弱信号", 0.0, 0.6),
                ("一般信号", 0.6, 0.8),
                ("强信号", 0.8, 1.0)
            ]
            
            results = {}
            
            for range_name, min_strength, max_strength in strength_ranges:
                # 过滤指定强度范围的信号
                filtered_signals = [
                    signal for signal in self.signal_records
                    if min_strength <= signal.signal_strength < max_strength
                ]
                
                if filtered_signals:
                    # 计算信号结果
                    signal_results = []
                    for signal in filtered_signals:
                        result = self._calculate_signal_result(signal, analysis_period)
                        if result:
                            signal_results.append(result)
                    
                    # 分析结果
                    if signal_results:
                        analysis = self._analyze_results(signal_results)
                        results[range_name] = analysis
                        
                        logger.info(f"  {range_name}: 成功率{analysis.success_rate:.2%}, 平均收益{analysis.avg_return:.2%}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 按信号强度分析失败: {e}")
            return {}
    
    def analyze_by_time_period(self, analysis_period: int = 7) -> Dict[str, SuccessRateAnalysis]:
        """
        按时间段分析成功率
        
        Args:
            analysis_period: 分析周期（天）
            
        Returns:
            按时间段分组的分析结果
        """
        try:
            logger.info("📊 按时间段分析成功率")
            
            # 按月份分组
            monthly_signals = defaultdict(list)
            for signal in self.signal_records:
                month_key = signal.signal_time.strftime('%Y-%m')
                monthly_signals[month_key].append(signal)
            
            results = {}
            
            for month, signals in monthly_signals.items():
                if len(signals) >= 10:  # 至少10个信号才分析
                    # 计算信号结果
                    signal_results = []
                    for signal in signals:
                        result = self._calculate_signal_result(signal, analysis_period)
                        if result:
                            signal_results.append(result)
                    
                    # 分析结果
                    if signal_results:
                        analysis = self._analyze_results(signal_results)
                        results[month] = analysis
                        
                        logger.info(f"  {month}: 成功率{analysis.success_rate:.2%}, 信号数{len(signals)}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 按时间段分析失败: {e}")
            return {}
    
    def analyze_by_symbol(self, analysis_period: int = 7, min_signals: int = 5) -> Dict[str, SuccessRateAnalysis]:
        """
        按股票代码分析成功率
        
        Args:
            analysis_period: 分析周期（天）
            min_signals: 最小信号数量
            
        Returns:
            按股票分组的分析结果
        """
        try:
            logger.info("📊 按股票代码分析成功率")
            
            # 按股票代码分组
            symbol_signals = defaultdict(list)
            for signal in self.signal_records:
                symbol_signals[signal.symbol].append(signal)
            
            results = {}
            
            for symbol, signals in symbol_signals.items():
                if len(signals) >= min_signals:
                    # 计算信号结果
                    signal_results = []
                    for signal in signals:
                        result = self._calculate_signal_result(signal, analysis_period)
                        if result:
                            signal_results.append(result)
                    
                    # 分析结果
                    if signal_results:
                        analysis = self._analyze_results(signal_results)
                        results[symbol] = analysis
                        
                        logger.debug(f"  {symbol}: 成功率{analysis.success_rate:.2%}, 信号数{len(signals)}")
            
            # 按成功率排序
            sorted_results = dict(sorted(results.items(), key=lambda x: x[1].success_rate, reverse=True))
            
            logger.info(f"✅ 按股票分析完成: {len(sorted_results)}只股票")
            
            return sorted_results
            
        except Exception as e:
            logger.error(f"❌ 按股票分析失败: {e}")
            return {}
    
    def get_signal_performance_summary(self) -> Dict[str, Any]:
        """
        获取信号表现总结
        
        Returns:
            信号表现总结
        """
        try:
            if not self.signal_records:
                return {}
            
            # 基础统计
            total_signals = len(self.signal_records)
            buy_signals = len([s for s in self.signal_records if s.signal_type == SignalType.BUY])
            sell_signals = len([s for s in self.signal_records if s.signal_type == SignalType.SELL])
            
            # 按不同周期分析
            period_analysis = {}
            for period in self.analysis_periods:
                analysis = self.analyze_signal_success_rate(analysis_period=period)
                period_analysis[f"{period}天"] = {
                    'success_rate': analysis.success_rate,
                    'avg_return': analysis.avg_return,
                    'total_signals': analysis.total_signals
                }
            
            # 按强度分析
            strength_analysis = self.analyze_by_signal_strength()
            
            summary = {
                'basic_stats': {
                    'total_signals': total_signals,
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'date_range': {
                        'start': min(s.signal_time for s in self.signal_records).isoformat(),
                        'end': max(s.signal_time for s in self.signal_records).isoformat()
                    }
                },
                'period_analysis': period_analysis,
                'strength_analysis': {
                    name: {
                        'success_rate': analysis.success_rate,
                        'avg_return': analysis.avg_return,
                        'total_signals': analysis.total_signals
                    }
                    for name, analysis in strength_analysis.items()
                },
                'top_performing_symbols': self._get_top_performing_symbols(),
                'recommendations': self._generate_recommendations()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 获取信号表现总结失败: {e}")
            return {}
    
    def _generate_mock_signals(self, 
                             start_date: datetime, 
                             end_date: datetime,
                             symbol: Optional[str] = None) -> List[SignalRecord]:
        """生成模拟信号数据"""
        try:
            import random
            
            signals = []
            symbols = [symbol] if symbol else ["000001", "000002", "600519", "000858", "600036", "002415"]
            
            current_date = start_date
            while current_date <= end_date:
                # 每天随机生成0-3个信号
                daily_signal_count = random.randint(0, 3)
                
                for _ in range(daily_signal_count):
                    signal_symbol = random.choice(symbols)
                    signal_type = random.choice([SignalType.BUY, SignalType.SELL])
                    
                    signal = SignalRecord(
                        signal_id=f"SIG_{current_date.strftime('%Y%m%d')}_{len(signals)+1:04d}",
                        symbol=signal_symbol,
                        signal_type=signal_type,
                        signal_time=current_date + timedelta(
                            hours=random.randint(9, 15),
                            minutes=random.randint(0, 59)
                        ),
                        signal_price=random.uniform(10, 100),
                        signal_strength=random.uniform(0.5, 1.0),
                        signal_score=random.randint(60, 95),
                        strategy_id=f"strategy_{random.randint(1, 3)}"
                    )
                    
                    signals.append(signal)
                
                current_date += timedelta(days=1)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ 生成模拟信号失败: {e}")
            return []
    
    def _filter_signals(self, 
                       signal_type: Optional[SignalType] = None,
                       strategy_id: Optional[str] = None) -> List[SignalRecord]:
        """过滤信号记录"""
        try:
            filtered = self.signal_records
            
            if signal_type:
                filtered = [s for s in filtered if s.signal_type == signal_type]
            
            if strategy_id:
                filtered = [s for s in filtered if s.strategy_id == strategy_id]
            
            return filtered
            
        except Exception as e:
            logger.error(f"❌ 过滤信号记录失败: {e}")
            return []
    
    def _calculate_signal_result(self, signal: SignalRecord, analysis_period: int) -> Optional[SignalResult]:
        """计算单个信号的结果"""
        try:
            # 模拟价格数据和交易结果
            import random
            
            # 模拟持仓期（1到analysis_period天）
            holding_period = random.randint(1, min(analysis_period, self.max_holding_period))
            
            # 模拟退出价格
            if signal.signal_type == SignalType.BUY:
                # 买入信号：模拟卖出价格
                price_change = random.uniform(-0.15, 0.25)  # -15%到+25%
                exit_price = signal.signal_price * (1 + price_change)
                return_rate = price_change
            else:
                # 卖出信号：模拟买回价格
                price_change = random.uniform(-0.20, 0.10)  # -20%到+10%
                exit_price = signal.signal_price * (1 + price_change)
                return_rate = -price_change  # 卖出信号收益率相反
            
            # 判断是否成功
            is_successful = return_rate >= self.success_threshold
            
            # 模拟最大盈利和亏损
            max_profit = max(0, return_rate * 1.2)
            max_loss = min(0, return_rate * 1.1)
            
            result = SignalResult(
                signal_id=signal.signal_id,
                symbol=signal.symbol,
                entry_price=signal.signal_price,
                exit_price=exit_price,
                exit_time=signal.signal_time + timedelta(days=holding_period),
                holding_period=holding_period,
                return_rate=return_rate,
                is_successful=is_successful,
                max_profit=max_profit,
                max_loss=max_loss
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 计算信号结果失败: {signal.signal_id} - {e}")
            return None
    
    def _analyze_results(self, signal_results: List[SignalResult]) -> SuccessRateAnalysis:
        """分析信号结果"""
        try:
            if not signal_results:
                return self._create_empty_analysis()
            
            total_signals = len(signal_results)
            successful_signals = len([r for r in signal_results if r.is_successful])
            success_rate = successful_signals / total_signals
            
            returns = [r.return_rate for r in signal_results]
            avg_return = np.mean(returns)
            max_return = max(returns)
            min_return = min(returns)
            
            holding_periods = [r.holding_period for r in signal_results]
            avg_holding_period = np.mean(holding_periods)
            
            # 计算盈亏比
            winning_returns = [r for r in returns if r > 0]
            losing_returns = [r for r in returns if r < 0]
            
            if winning_returns and losing_returns:
                avg_win = np.mean(winning_returns)
                avg_loss = abs(np.mean(losing_returns))
                win_loss_ratio = avg_win / avg_loss
                
                total_profit = sum(winning_returns)
                total_loss = abs(sum(losing_returns))
                profit_factor = total_profit / total_loss if total_loss > 0 else 0
            else:
                win_loss_ratio = 0
                profit_factor = 0
            
            # 计算夏普比率（简化）
            if len(returns) > 1:
                return_std = np.std(returns)
                sharpe_ratio = avg_return / return_std if return_std > 0 else 0
            else:
                sharpe_ratio = 0
            
            analysis = SuccessRateAnalysis(
                total_signals=total_signals,
                successful_signals=successful_signals,
                success_rate=success_rate,
                avg_return=avg_return,
                avg_holding_period=avg_holding_period,
                max_return=max_return,
                min_return=min_return,
                profit_factor=profit_factor,
                sharpe_ratio=sharpe_ratio,
                win_loss_ratio=win_loss_ratio
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析信号结果失败: {e}")
            return self._create_empty_analysis()
    
    def _create_empty_analysis(self) -> SuccessRateAnalysis:
        """创建空的分析结果"""
        return SuccessRateAnalysis(
            total_signals=0,
            successful_signals=0,
            success_rate=0.0,
            avg_return=0.0,
            avg_holding_period=0.0,
            max_return=0.0,
            min_return=0.0,
            profit_factor=0.0,
            sharpe_ratio=0.0,
            win_loss_ratio=0.0
        )
    
    def _get_top_performing_symbols(self, top_n: int = 10) -> List[Dict[str, Any]]:
        """获取表现最佳的股票"""
        try:
            symbol_analysis = self.analyze_by_symbol()
            
            # 按成功率排序
            sorted_symbols = sorted(
                symbol_analysis.items(),
                key=lambda x: (x[1].success_rate, x[1].avg_return),
                reverse=True
            )
            
            top_symbols = []
            for symbol, analysis in sorted_symbols[:top_n]:
                top_symbols.append({
                    'symbol': symbol,
                    'success_rate': analysis.success_rate,
                    'avg_return': analysis.avg_return,
                    'total_signals': analysis.total_signals
                })
            
            return top_symbols
            
        except Exception as e:
            logger.error(f"❌ 获取表现最佳股票失败: {e}")
            return []
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        try:
            recommendations = []
            
            # 分析整体表现
            overall_analysis = self.analyze_signal_success_rate()
            
            if overall_analysis.success_rate < 0.5:
                recommendations.append("整体成功率偏低，建议优化信号生成策略")
            
            if overall_analysis.avg_return < 0.02:
                recommendations.append("平均收益率较低，建议提高信号质量阈值")
            
            # 分析强度表现
            strength_analysis = self.analyze_by_signal_strength()
            
            if "强信号" in strength_analysis and "弱信号" in strength_analysis:
                strong_success = strength_analysis["强信号"].success_rate
                weak_success = strength_analysis["弱信号"].success_rate
                
                if strong_success - weak_success > 0.2:
                    recommendations.append("强信号表现明显优于弱信号，建议提高信号强度阈值")
            
            # 分析持仓期
            if overall_analysis.avg_holding_period > 20:
                recommendations.append("平均持仓期较长，建议优化退出策略")
            
            if not recommendations:
                recommendations.append("信号表现良好，继续保持当前策略")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ 生成优化建议失败: {e}")
            return ["分析数据不足，无法生成建议"]
    
    def export_analysis_report(self, file_path: str) -> bool:
        """导出分析报告"""
        try:
            summary = self.get_signal_performance_summary()
            
            if not summary:
                logger.error("❌ 没有分析数据可导出")
                return False
            
            import json
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"✅ 分析报告已导出: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出分析报告失败: {e}")
            return False
