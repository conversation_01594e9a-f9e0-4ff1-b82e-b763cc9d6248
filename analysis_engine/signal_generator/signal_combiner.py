"""
信号合成器
将多维度分析结果合成为交易信号
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from enum import Enum

from database_models import db_manager, TradingSignal
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector

logger = logging.getLogger(__name__)

class SignalType(Enum):
    """信号类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class SignalStrength(Enum):
    """信号强度枚举"""
    WEAK = "WEAK"
    MEDIUM = "MEDIUM"
    STRONG = "STRONG"

class SignalCombiner:
    """信号合成器"""
    
    def __init__(self):
        self.multi_factor_selector = MultiFactorSelector()
        
        # 信号阈值配置
        self.signal_thresholds = {
            'strong_buy': 80.0,     # 强买入信号阈值
            'buy': 65.0,            # 买入信号阈值
            'hold_upper': 60.0,     # 持有上限
            'hold_lower': 40.0,     # 持有下限
            'sell': 35.0,           # 卖出信号阈值
            'strong_sell': 20.0     # 强卖出信号阈值
        }
        
        logger.info("🎯 信号合成器初始化完成")
    
    def generate_trading_signal(self, symbol: str) -> Dict[str, Any]:
        """
        生成交易信号
        
        Args:
            symbol: 股票代码
            
        Returns:
            交易信号字典
        """
        try:
            logger.debug(f"🎯 生成交易信号: {symbol}")
            
            # 获取综合评分
            score_result = self.multi_factor_selector.calculate_comprehensive_score(symbol)
            
            total_score = score_result.get('total_score', 50.0)
            technical_score = score_result.get('technical_score', 50.0)
            fundamental_score = score_result.get('fundamental_score', 50.0)
            market_score = score_result.get('market_score', 50.0)
            
            # 确定信号类型和强度
            signal_type, signal_strength = self._determine_signal_type_and_strength(
                total_score, technical_score, fundamental_score, market_score
            )
            
            # 计算置信度
            confidence = self._calculate_confidence(
                total_score, technical_score, fundamental_score, market_score
            )
            
            # 生成信号原因
            signal_reasons = self._generate_signal_reasons(
                signal_type, total_score, technical_score, fundamental_score, market_score
            )
            
            # 计算目标价格和止损价格
            price_targets = self._calculate_price_targets(symbol, signal_type, total_score)
            
            # 构建信号结果
            signal_result = {
                'symbol': symbol,
                'signal_time': datetime.now(),
                'signal_type': signal_type.value,
                'signal_strength': signal_strength.value,
                'signal_score': total_score,
                'confidence': confidence,
                'trigger_price': price_targets.get('current_price'),
                'target_price': price_targets.get('target_price'),
                'stop_loss_price': price_targets.get('stop_loss_price'),
                'signal_reasons': signal_reasons,
                'technical_indicators': {
                    'technical_score': technical_score,
                    'fundamental_score': fundamental_score,
                    'market_score': market_score,
                    **{k: v for k, v in score_result.items() 
                       if k not in ['symbol', 'total_score', 'technical_score', 
                                  'fundamental_score', 'market_score', 'calculation_date']}
                }
            }
            
            logger.debug(f"✅ 交易信号生成完成: {symbol} - {signal_type.value} ({signal_strength.value})")
            
            return signal_result
            
        except Exception as e:
            logger.error(f"❌ 生成交易信号失败: {symbol} - {e}")
            return {
                'symbol': symbol,
                'signal_time': datetime.now(),
                'signal_type': SignalType.HOLD.value,
                'signal_strength': SignalStrength.WEAK.value,
                'signal_score': 50.0,
                'confidence': 0.5,
                'signal_reasons': ['数据不足或计算错误']
            }
    
    def _determine_signal_type_and_strength(self, 
                                          total_score: float,
                                          technical_score: float,
                                          fundamental_score: float,
                                          market_score: float) -> Tuple[SignalType, SignalStrength]:
        """
        确定信号类型和强度
        
        Args:
            total_score: 综合评分
            technical_score: 技术面评分
            fundamental_score: 基本面评分
            market_score: 市场表现评分
            
        Returns:
            信号类型和强度的元组
        """
        try:
            # 基于综合评分确定基本信号类型
            if total_score >= self.signal_thresholds['strong_buy']:
                base_signal = SignalType.BUY
                base_strength = SignalStrength.STRONG
            elif total_score >= self.signal_thresholds['buy']:
                base_signal = SignalType.BUY
                base_strength = SignalStrength.MEDIUM
            elif total_score >= self.signal_thresholds['hold_upper']:
                base_signal = SignalType.HOLD
                base_strength = SignalStrength.MEDIUM
            elif total_score >= self.signal_thresholds['hold_lower']:
                base_signal = SignalType.HOLD
                base_strength = SignalStrength.WEAK
            elif total_score >= self.signal_thresholds['sell']:
                base_signal = SignalType.SELL
                base_strength = SignalStrength.MEDIUM
            else:
                base_signal = SignalType.SELL
                base_strength = SignalStrength.STRONG
            
            # 根据各维度评分的一致性调整信号强度
            scores = [technical_score, fundamental_score, market_score]
            score_std = np.std(scores)
            
            # 如果各维度评分差异较大，降低信号强度
            if score_std > 20:  # 标准差大于20
                if base_strength == SignalStrength.STRONG:
                    base_strength = SignalStrength.MEDIUM
                elif base_strength == SignalStrength.MEDIUM:
                    base_strength = SignalStrength.WEAK
            
            # 特殊情况处理
            # 如果技术面和市场表现都很差，即使基本面好也要谨慎
            if (technical_score < 40 and market_score < 40 and 
                base_signal == SignalType.BUY):
                base_signal = SignalType.HOLD
                base_strength = SignalStrength.WEAK
            
            # 如果基本面很差，即使技术面好也要谨慎
            if (fundamental_score < 30 and base_signal == SignalType.BUY):
                if base_strength == SignalStrength.STRONG:
                    base_strength = SignalStrength.MEDIUM
                elif base_strength == SignalStrength.MEDIUM:
                    base_strength = SignalStrength.WEAK
            
            return base_signal, base_strength
            
        except Exception as e:
            logger.error(f"❌ 确定信号类型和强度失败: {e}")
            return SignalType.HOLD, SignalStrength.WEAK
    
    def _calculate_confidence(self, 
                            total_score: float,
                            technical_score: float,
                            fundamental_score: float,
                            market_score: float) -> float:
        """
        计算信号置信度
        
        Args:
            total_score: 综合评分
            technical_score: 技术面评分
            fundamental_score: 基本面评分
            market_score: 市场表现评分
            
        Returns:
            置信度 (0-1)
        """
        try:
            # 基础置信度基于综合评分
            if total_score >= 80 or total_score <= 20:
                base_confidence = 0.9  # 极端评分，高置信度
            elif total_score >= 70 or total_score <= 30:
                base_confidence = 0.8  # 较极端评分
            elif total_score >= 60 or total_score <= 40:
                base_confidence = 0.7  # 中等评分
            else:
                base_confidence = 0.5  # 中性评分，低置信度
            
            # 根据各维度评分的一致性调整置信度
            scores = [technical_score, fundamental_score, market_score]
            score_std = np.std(scores)
            
            # 评分越一致，置信度越高
            consistency_factor = max(0.5, 1.0 - score_std / 50.0)
            
            # 最终置信度
            final_confidence = base_confidence * consistency_factor
            
            return max(0.1, min(1.0, final_confidence))
            
        except Exception as e:
            logger.error(f"❌ 计算置信度失败: {e}")
            return 0.5
    
    def _generate_signal_reasons(self, 
                               signal_type: SignalType,
                               total_score: float,
                               technical_score: float,
                               fundamental_score: float,
                               market_score: float) -> List[str]:
        """
        生成信号原因
        
        Args:
            signal_type: 信号类型
            total_score: 综合评分
            technical_score: 技术面评分
            fundamental_score: 基本面评分
            market_score: 市场表现评分
            
        Returns:
            信号原因列表
        """
        try:
            reasons = []
            
            # 综合评分原因
            reasons.append(f"综合评分: {total_score:.1f}分")
            
            # 各维度评分原因
            if technical_score >= 70:
                reasons.append(f"技术面强势 ({technical_score:.1f}分)")
            elif technical_score <= 30:
                reasons.append(f"技术面疲弱 ({technical_score:.1f}分)")
            
            if fundamental_score >= 70:
                reasons.append(f"基本面优秀 ({fundamental_score:.1f}分)")
            elif fundamental_score <= 30:
                reasons.append(f"基本面较差 ({fundamental_score:.1f}分)")
            
            if market_score >= 70:
                reasons.append(f"市场表现活跃 ({market_score:.1f}分)")
            elif market_score <= 30:
                reasons.append(f"市场表现低迷 ({market_score:.1f}分)")
            
            # 信号类型特定原因
            if signal_type == SignalType.BUY:
                if total_score >= 80:
                    reasons.append("多维度指标均显示强烈买入信号")
                else:
                    reasons.append("综合指标显示买入机会")
            elif signal_type == SignalType.SELL:
                if total_score <= 20:
                    reasons.append("多维度指标均显示强烈卖出信号")
                else:
                    reasons.append("综合指标显示卖出信号")
            else:
                reasons.append("综合指标显示持有观望")
            
            return reasons
            
        except Exception as e:
            logger.error(f"❌ 生成信号原因失败: {e}")
            return ["信号生成过程中出现错误"]
    
    def _calculate_price_targets(self, symbol: str, signal_type: SignalType, total_score: float) -> Dict[str, Optional[float]]:
        """
        计算价格目标
        
        Args:
            symbol: 股票代码
            signal_type: 信号类型
            total_score: 综合评分
            
        Returns:
            价格目标字典
        """
        try:
            # 获取当前价格
            market_data = self.multi_factor_selector.factor_calculator.get_stock_market_data(symbol, days=5)
            
            if market_data is None or market_data.empty:
                return {
                    'current_price': None,
                    'target_price': None,
                    'stop_loss_price': None
                }
            
            current_price = market_data['close_price'].iloc[-1]
            
            # 根据信号类型和评分计算目标价格
            if signal_type == SignalType.BUY:
                # 买入信号：根据评分设定上涨目标
                if total_score >= 80:
                    target_ratio = 0.15  # 15%上涨目标
                    stop_loss_ratio = 0.08  # 8%止损
                elif total_score >= 65:
                    target_ratio = 0.10  # 10%上涨目标
                    stop_loss_ratio = 0.06  # 6%止损
                else:
                    target_ratio = 0.05  # 5%上涨目标
                    stop_loss_ratio = 0.04  # 4%止损
                
                target_price = current_price * (1 + target_ratio)
                stop_loss_price = current_price * (1 - stop_loss_ratio)
                
            elif signal_type == SignalType.SELL:
                # 卖出信号：设定止损目标
                if total_score <= 20:
                    target_ratio = 0.15  # 15%下跌目标
                    stop_loss_ratio = 0.05  # 5%止损（反弹）
                else:
                    target_ratio = 0.08  # 8%下跌目标
                    stop_loss_ratio = 0.03  # 3%止损
                
                target_price = current_price * (1 - target_ratio)
                stop_loss_price = current_price * (1 + stop_loss_ratio)
                
            else:  # HOLD
                target_price = current_price
                stop_loss_price = current_price * 0.95  # 5%止损
            
            return {
                'current_price': current_price,
                'target_price': target_price,
                'stop_loss_price': stop_loss_price
            }
            
        except Exception as e:
            logger.error(f"❌ 计算价格目标失败: {symbol} - {e}")
            return {
                'current_price': None,
                'target_price': None,
                'stop_loss_price': None
            }
    
    def batch_generate_signals(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        批量生成交易信号
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            交易信号列表
        """
        try:
            logger.info(f"🎯 批量生成交易信号: {len(symbols)} 只股票")
            
            signals = []
            
            for symbol in symbols:
                try:
                    signal = self.generate_trading_signal(symbol)
                    signals.append(signal)
                except Exception as e:
                    logger.error(f"❌ 生成交易信号失败: {symbol} - {e}")
            
            logger.info(f"✅ 批量信号生成完成: {len(signals)} 个信号")
            
            # 保存信号到数据库
            self._save_trading_signals(signals)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ 批量生成交易信号失败: {e}")
            return []
    
    def _save_trading_signals(self, signals: List[Dict[str, Any]]):
        """
        保存交易信号到数据库
        
        Args:
            signals: 交易信号列表
        """
        try:
            logger.info("💾 保存交易信号到数据库...")
            
            with db_manager.get_session() as session:
                saved_count = 0
                
                for signal_data in signals:
                    try:
                        trading_signal = TradingSignal(
                            symbol=signal_data['symbol'],
                            signal_time=signal_data['signal_time'],
                            signal_type=signal_data['signal_type'],
                            signal_strength=signal_data['signal_strength'],
                            signal_score=signal_data['signal_score'],
                            confidence=signal_data.get('confidence'),
                            trigger_price=signal_data.get('trigger_price'),
                            target_price=signal_data.get('target_price'),
                            stop_loss_price=signal_data.get('stop_loss_price'),
                            signal_reasons=signal_data.get('signal_reasons'),
                            technical_indicators=signal_data.get('technical_indicators')
                        )
                        
                        session.add(trading_signal)
                        saved_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ 保存交易信号失败: {signal_data.get('symbol', 'unknown')} - {e}")
                
                session.commit()
                
                logger.info(f"✅ 保存交易信号完成: {saved_count} 条记录")
                
        except Exception as e:
            logger.error(f"❌ 保存交易信号到数据库失败: {e}")
    
    def get_latest_signals(self, signal_type: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最新交易信号
        
        Args:
            signal_type: 信号类型过滤
            limit: 返回数量限制
            
        Returns:
            最新交易信号列表
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(TradingSignal)
                
                if signal_type:
                    query = query.filter(TradingSignal.signal_type == signal_type)
                
                latest_signals = query.order_by(
                    TradingSignal.signal_time.desc()
                ).limit(limit).all()
                
                results = []
                for signal in latest_signals:
                    result = {
                        'symbol': signal.symbol,
                        'signal_time': signal.signal_time.isoformat(),
                        'signal_type': signal.signal_type,
                        'signal_strength': signal.signal_strength,
                        'signal_score': float(signal.signal_score),
                        'confidence': float(signal.confidence) if signal.confidence else None,
                        'trigger_price': float(signal.trigger_price) if signal.trigger_price else None,
                        'target_price': float(signal.target_price) if signal.target_price else None,
                        'stop_loss_price': float(signal.stop_loss_price) if signal.stop_loss_price else None,
                        'signal_reasons': signal.signal_reasons,
                        'technical_indicators': signal.technical_indicators
                    }
                    results.append(result)
                
                return results
                
        except Exception as e:
            logger.error(f"❌ 获取最新交易信号失败: {e}")
            return []
