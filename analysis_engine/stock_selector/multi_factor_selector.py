"""
多因子选股器
实现基于多维度评分的智能选股算法
技术面50% + 基本面30% + 市场表现20%
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date

from database_models import db_manager, StockInfo, StockScore
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators
from analysis_engine.fundamental_analyzer.financial_analyzer import FinancialAnalyzer
from analysis_engine.factor_manager.factor_calculator import FactorCalculator
from system_management.config.config_manager import config_manager

logger = logging.getLogger(__name__)

class MultiFactorSelector:
    """多因子选股器"""
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        self.financial_analyzer = FinancialAnalyzer()
        self.factor_calculator = FactorCalculator()
        
        # 评分权重配置（技术面50% + 基本面30% + 市场表现20%）
        self.scoring_weights = {
            'technical': config_manager.get('technical_score_weight', 0.5),
            'fundamental': config_manager.get('fundamental_score_weight', 0.3),
            'market': config_manager.get('market_score_weight', 0.2)
        }
        
        # 选股过滤条件
        self.selection_criteria = {
            'min_market_cap': config_manager.get('min_market_cap', 1000000000),  # 最小市值10亿
            'min_volume': config_manager.get('min_volume', 1000000),  # 最小成交量100万
            'exclude_st': config_manager.get('exclude_st', True),  # 排除ST股票
            'exclude_new_stock_days': config_manager.get('exclude_new_stock_days', 60),  # 排除新股天数
            'max_pe_ratio': config_manager.get('max_pe_ratio', 100),  # 最大PE比率
            'min_pe_ratio': config_manager.get('min_pe_ratio', 0),    # 最小PE比率
        }
        
        logger.info("🎯 多因子选股器初始化完成")
        logger.info(f"📊 评分权重: 技术面{self.scoring_weights['technical']:.0%}, "
                   f"基本面{self.scoring_weights['fundamental']:.0%}, "
                   f"市场表现{self.scoring_weights['market']:.0%}")
    
    def get_candidate_stocks(self) -> List[str]:
        """
        获取候选股票列表
        
        Returns:
            符合基本条件的股票代码列表
        """
        try:
            logger.info("📋 获取候选股票列表...")
            
            with db_manager.get_session() as session:
                # 基本查询条件
                query = session.query(StockInfo).filter(
                    StockInfo.is_active == True
                )
                
                # 排除ST股票
                if self.selection_criteria['exclude_st']:
                    query = query.filter(~StockInfo.name.contains('ST'))
                
                # 排除新股
                if self.selection_criteria['exclude_new_stock_days'] > 0:
                    cutoff_date = datetime.now().date() - pd.Timedelta(
                        days=self.selection_criteria['exclude_new_stock_days']
                    )
                    query = query.filter(
                        (StockInfo.list_date.is_(None)) | 
                        (StockInfo.list_date <= cutoff_date)
                    )
                
                # 市值过滤
                if self.selection_criteria['min_market_cap'] > 0:
                    query = query.filter(
                        (StockInfo.market_cap.is_(None)) |
                        (StockInfo.market_cap >= self.selection_criteria['min_market_cap'])
                    )
                
                stocks = query.limit(1000).all()  # 限制数量避免过多
                
                candidate_symbols = [stock.symbol for stock in stocks]
                
                logger.info(f"✅ 获取到 {len(candidate_symbols)} 只候选股票")
                
                return candidate_symbols
                
        except Exception as e:
            logger.error(f"❌ 获取候选股票失败: {e}")
            return []
    
    def calculate_technical_score(self, symbol: str) -> Dict[str, float]:
        """
        计算技术面评分
        
        Args:
            symbol: 股票代码
            
        Returns:
            技术面评分字典
        """
        try:
            # 获取市场数据
            market_data = self.factor_calculator.get_stock_market_data(symbol, days=60)
            
            if market_data is None or market_data.empty:
                return {'technical_score': 50.0}
            
            # 获取趋势信号评分
            trend_signals = self.technical_indicators.get_trend_signal(market_data)
            
            # 计算技术因子
            technical_factors = self.factor_calculator.calculate_technical_factors(market_data)
            momentum_factors = self.factor_calculator.calculate_momentum_factors(market_data)
            
            # 综合技术面评分
            technical_score = 0.0
            
            # 1. 趋势评分 (权重: 0.4)
            technical_score += trend_signals.get('trend_score', 50.0) * 0.4
            
            # 2. RSI评分 (权重: 0.2)
            rsi_value = technical_factors.get('rsi_14', 50.0)
            if 30 <= rsi_value <= 70:
                rsi_score = 80  # RSI在合理区间
            elif rsi_value < 30:
                rsi_score = 90  # 超卖，可能反弹
            elif rsi_value > 70:
                rsi_score = 30  # 超买，风险较高
            else:
                rsi_score = 50
            
            technical_score += rsi_score * 0.2
            
            # 3. 动量评分 (权重: 0.2)
            momentum_5d = momentum_factors.get('momentum_5d', 0.0)
            momentum_20d = momentum_factors.get('momentum_20d', 0.0)
            
            if momentum_5d > 0 and momentum_20d > 0:
                momentum_score = 85  # 双重正动量
            elif momentum_5d > 0 or momentum_20d > 0:
                momentum_score = 65  # 单一正动量
            elif momentum_5d < -10 or momentum_20d < -20:
                momentum_score = 20  # 强负动量
            else:
                momentum_score = 50  # 中性
            
            technical_score += momentum_score * 0.2
            
            # 4. 成交量评分 (权重: 0.2)
            volume_score = technical_factors.get('volume_ratio', 50.0)
            technical_score += volume_score * 0.2
            
            return {
                'technical_score': technical_score,
                'trend_score': trend_signals.get('trend_score', 50.0),
                'rsi_score': rsi_score,
                'momentum_score': momentum_score,
                'volume_score': volume_score
            }
            
        except Exception as e:
            logger.error(f"❌ 计算技术面评分失败: {symbol} - {e}")
            return {'technical_score': 50.0}
    
    def calculate_market_score(self, symbol: str) -> Dict[str, float]:
        """
        计算市场表现评分
        
        Args:
            symbol: 股票代码
            
        Returns:
            市场表现评分字典
        """
        try:
            # 获取市场数据
            market_data = self.factor_calculator.get_stock_market_data(symbol, days=30)
            
            if market_data is None or market_data.empty:
                return {'market_score': 50.0}
            
            market_score = 0.0
            
            # 1. 流动性评分 (权重: 0.5)
            if 'turnover_rate' in market_data.columns:
                avg_turnover = market_data['turnover_rate'].mean()
                if avg_turnover >= 5.0:
                    liquidity_score = 90  # 高流动性
                elif avg_turnover >= 2.0:
                    liquidity_score = 75  # 中等流动性
                elif avg_turnover >= 1.0:
                    liquidity_score = 60  # 一般流动性
                else:
                    liquidity_score = 30  # 低流动性
            else:
                liquidity_score = 50
            
            market_score += liquidity_score * 0.5
            
            # 2. 相对强度评分 (权重: 0.3)
            if len(market_data) >= 20:
                recent_return = (market_data['close_price'].iloc[-1] / 
                               market_data['close_price'].iloc[-20] - 1) * 100
                
                if recent_return >= 10:
                    strength_score = 90  # 强势
                elif recent_return >= 5:
                    strength_score = 75  # 较强
                elif recent_return >= 0:
                    strength_score = 60  # 平稳
                elif recent_return >= -10:
                    strength_score = 40  # 较弱
                else:
                    strength_score = 20  # 弱势
            else:
                strength_score = 50
            
            market_score += strength_score * 0.3
            
            # 3. 市场情绪评分 (权重: 0.2)
            # 基于成交量变化判断市场情绪
            if 'volume' in market_data.columns and len(market_data) >= 10:
                recent_volume = market_data['volume'].iloc[-5:].mean()
                historical_volume = market_data['volume'].iloc[-20:-5].mean()
                
                if historical_volume > 0:
                    volume_change = recent_volume / historical_volume
                    
                    if volume_change >= 1.5:
                        sentiment_score = 80  # 积极情绪
                    elif volume_change >= 1.2:
                        sentiment_score = 70  # 较积极
                    elif volume_change >= 0.8:
                        sentiment_score = 60  # 中性
                    else:
                        sentiment_score = 40  # 消极
                else:
                    sentiment_score = 50
            else:
                sentiment_score = 50
            
            market_score += sentiment_score * 0.2
            
            return {
                'market_score': market_score,
                'liquidity_score': liquidity_score,
                'strength_score': strength_score,
                'sentiment_score': sentiment_score
            }
            
        except Exception as e:
            logger.error(f"❌ 计算市场表现评分失败: {symbol} - {e}")
            return {'market_score': 50.0}
    
    def calculate_comprehensive_score(self, symbol: str) -> Dict[str, Any]:
        """
        计算综合评分
        
        Args:
            symbol: 股票代码
            
        Returns:
            综合评分结果字典
        """
        try:
            logger.debug(f"🎯 计算综合评分: {symbol}")
            
            # 计算各维度评分
            technical_result = self.calculate_technical_score(symbol)
            fundamental_result = self.financial_analyzer.calculate_fundamental_score(symbol)
            market_result = self.calculate_market_score(symbol)
            
            # 提取主要评分
            technical_score = technical_result.get('technical_score', 50.0)
            fundamental_score = fundamental_result.get('fundamental_score', 50.0)
            market_score = market_result.get('market_score', 50.0)
            
            # 计算综合评分
            total_score = (
                technical_score * self.scoring_weights['technical'] +
                fundamental_score * self.scoring_weights['fundamental'] +
                market_score * self.scoring_weights['market']
            )
            
            # 合并详细评分
            detailed_scores = {
                'symbol': symbol,
                'total_score': total_score,
                'technical_score': technical_score,
                'fundamental_score': fundamental_score,
                'market_score': market_score,
                'calculation_date': datetime.now().date(),
                **technical_result,
                **fundamental_result,
                **market_result
            }
            
            logger.debug(f"✅ 综合评分完成: {symbol} = {total_score:.2f}")
            
            return detailed_scores
            
        except Exception as e:
            logger.error(f"❌ 计算综合评分失败: {symbol} - {e}")
            return {
                'symbol': symbol,
                'total_score': 50.0,
                'technical_score': 50.0,
                'fundamental_score': 50.0,
                'market_score': 50.0,
                'calculation_date': datetime.now().date()
            }
    
    def select_stocks(self, max_stocks: int = None) -> List[Dict[str, Any]]:
        """
        执行智能选股
        
        Args:
            max_stocks: 最大选股数量
            
        Returns:
            选中股票列表，按评分降序排列
        """
        try:
            if max_stocks is None:
                max_stocks = config_manager.get('max_stock_selection', 50)
            
            logger.info(f"🎯 开始智能选股，目标数量: {max_stocks}")
            
            # 获取候选股票
            candidate_symbols = self.get_candidate_stocks()
            
            if not candidate_symbols:
                logger.warning("⚠️ 没有候选股票")
                return []
            
            # 批量计算评分
            logger.info(f"📊 计算 {len(candidate_symbols)} 只股票的评分...")
            
            stock_scores = []
            
            # 分批处理，避免内存占用过大
            batch_size = 50
            for i in range(0, len(candidate_symbols), batch_size):
                batch_symbols = candidate_symbols[i:i + batch_size]
                
                for symbol in batch_symbols:
                    try:
                        score_result = self.calculate_comprehensive_score(symbol)
                        stock_scores.append(score_result)
                    except Exception as e:
                        logger.error(f"❌ 计算股票评分失败: {symbol} - {e}")
                
                logger.info(f"📈 已完成 {min(i + batch_size, len(candidate_symbols))}/{len(candidate_symbols)} 只股票")
            
            # 按评分排序
            stock_scores.sort(key=lambda x: x['total_score'], reverse=True)
            
            # 选择前N只股票
            selected_stocks = stock_scores[:max_stocks]
            
            logger.info(f"✅ 智能选股完成，选中 {len(selected_stocks)} 只股票")
            
            # 保存评分结果到数据库
            self._save_stock_scores(stock_scores)
            
            return selected_stocks
            
        except Exception as e:
            logger.error(f"❌ 智能选股失败: {e}")
            return []
    
    def _save_stock_scores(self, stock_scores: List[Dict[str, Any]]):
        """
        保存股票评分到数据库
        
        Args:
            stock_scores: 股票评分列表
        """
        try:
            logger.info("💾 保存股票评分到数据库...")
            
            with db_manager.get_session() as session:
                saved_count = 0
                
                for score_data in stock_scores:
                    try:
                        # 检查是否已存在当日评分
                        existing = session.query(StockScore).filter(
                            StockScore.symbol == score_data['symbol'],
                            StockScore.calculation_date == score_data['calculation_date']
                        ).first()
                        
                        if existing:
                            # 更新现有记录
                            existing.technical_score = score_data['technical_score']
                            existing.fundamental_score = score_data['fundamental_score']
                            existing.market_score = score_data['market_score']
                            existing.total_score = score_data['total_score']
                            existing.factor_scores = {
                                k: v for k, v in score_data.items() 
                                if k not in ['symbol', 'calculation_date', 'technical_score', 
                                           'fundamental_score', 'market_score', 'total_score']
                            }
                        else:
                            # 创建新记录
                            stock_score = StockScore(
                                symbol=score_data['symbol'],
                                calculation_date=score_data['calculation_date'],
                                technical_score=score_data['technical_score'],
                                fundamental_score=score_data['fundamental_score'],
                                market_score=score_data['market_score'],
                                total_score=score_data['total_score'],
                                factor_scores={
                                    k: v for k, v in score_data.items() 
                                    if k not in ['symbol', 'calculation_date', 'technical_score', 
                                               'fundamental_score', 'market_score', 'total_score']
                                }
                            )
                            session.add(stock_score)
                        
                        saved_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ 保存股票评分失败: {score_data.get('symbol', 'unknown')} - {e}")
                
                session.commit()
                
                logger.info(f"✅ 保存股票评分完成: {saved_count} 条记录")
                
        except Exception as e:
            logger.error(f"❌ 保存股票评分到数据库失败: {e}")
    
    def get_top_stocks(self, limit: int = 20, date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        获取评分最高的股票
        
        Args:
            limit: 返回数量限制
            date: 指定日期，默认为最新日期
            
        Returns:
            评分最高的股票列表
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(StockScore)
                
                if date:
                    query = query.filter(StockScore.calculation_date == date)
                else:
                    # 获取最新日期的数据
                    latest_date = session.query(StockScore.calculation_date).order_by(
                        StockScore.calculation_date.desc()
                    ).first()
                    
                    if latest_date:
                        query = query.filter(StockScore.calculation_date == latest_date[0])
                
                top_stocks = query.order_by(
                    StockScore.total_score.desc()
                ).limit(limit).all()
                
                results = []
                for stock in top_stocks:
                    result = {
                        'symbol': stock.symbol,
                        'total_score': float(stock.total_score),
                        'technical_score': float(stock.technical_score) if stock.technical_score else None,
                        'fundamental_score': float(stock.fundamental_score) if stock.fundamental_score else None,
                        'market_score': float(stock.market_score) if stock.market_score else None,
                        'calculation_date': stock.calculation_date.isoformat(),
                        'factor_scores': stock.factor_scores
                    }
                    results.append(result)
                
                return results
                
        except Exception as e:
            logger.error(f"❌ 获取评分最高股票失败: {e}")
            return []
