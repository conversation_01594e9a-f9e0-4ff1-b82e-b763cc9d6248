"""
技术分析指标计算器
实现各种技术分析指标的计算
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技术分析指标计算器"""
    
    def __init__(self):
        logger.info("📈 技术分析指标计算器初始化完成")
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """
        计算RSI相对强弱指标
        
        Args:
            prices: 价格序列（通常是收盘价）
            period: 计算周期，默认14
            
        Returns:
            RSI指标序列
        """
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.fillna(50)  # 填充NaN为中性值50
            
        except Exception as e:
            logger.error(f"❌ 计算RSI失败: {e}")
            return pd.Series([50] * len(prices), index=prices.index)
    
    def calculate_macd(self, prices: pd.Series, 
                      fast_period: int = 12, 
                      slow_period: int = 26, 
                      signal_period: int = 9) -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        Args:
            prices: 价格序列
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            
        Returns:
            包含MACD、信号线、柱状图的字典
        """
        try:
            # 计算指数移动平均
            ema_fast = prices.ewm(span=fast_period).mean()
            ema_slow = prices.ewm(span=slow_period).mean()
            
            # MACD线
            macd_line = ema_fast - ema_slow
            
            # 信号线
            signal_line = macd_line.ewm(span=signal_period).mean()
            
            # MACD柱状图
            histogram = macd_line - signal_line
            
            return {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
            
        except Exception as e:
            logger.error(f"❌ 计算MACD失败: {e}")
            return {
                'macd': pd.Series([0] * len(prices), index=prices.index),
                'signal': pd.Series([0] * len(prices), index=prices.index),
                'histogram': pd.Series([0] * len(prices), index=prices.index)
            }
    
    def calculate_moving_averages(self, prices: pd.Series, 
                                periods: List[int] = [5, 10, 20, 60]) -> Dict[str, pd.Series]:
        """
        计算多周期移动平均线
        
        Args:
            prices: 价格序列
            periods: 移动平均周期列表
            
        Returns:
            各周期移动平均线字典
        """
        try:
            ma_dict = {}
            
            for period in periods:
                ma_dict[f'ma_{period}'] = prices.rolling(window=period).mean()
            
            return ma_dict
            
        except Exception as e:
            logger.error(f"❌ 计算移动平均线失败: {e}")
            return {f'ma_{p}': pd.Series([prices.mean()] * len(prices), index=prices.index) 
                   for p in periods}
    
    def calculate_momentum(self, prices: pd.Series, periods: List[int] = [5, 20]) -> Dict[str, pd.Series]:
        """
        计算动量指标
        
        Args:
            prices: 价格序列
            periods: 动量计算周期列表
            
        Returns:
            各周期动量指标字典
        """
        try:
            momentum_dict = {}
            
            for period in periods:
                momentum = (prices / prices.shift(period) - 1) * 100
                momentum_dict[f'momentum_{period}d'] = momentum.fillna(0)
            
            return momentum_dict
            
        except Exception as e:
            logger.error(f"❌ 计算动量指标失败: {e}")
            return {f'momentum_{p}d': pd.Series([0] * len(prices), index=prices.index) 
                   for p in periods}
    
    def calculate_volume_ratio(self, volumes: pd.Series, period: int = 20) -> pd.Series:
        """
        计算成交量比率
        
        Args:
            volumes: 成交量序列
            period: 平均成交量计算周期
            
        Returns:
            成交量比率序列
        """
        try:
            avg_volume = volumes.rolling(window=period).mean()
            volume_ratio = volumes / avg_volume
            
            return volume_ratio.fillna(1.0)
            
        except Exception as e:
            logger.error(f"❌ 计算成交量比率失败: {e}")
            return pd.Series([1.0] * len(volumes), index=volumes.index)
    
    def calculate_bollinger_bands(self, prices: pd.Series, 
                                 period: int = 20, 
                                 std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        计算布林带指标
        
        Args:
            prices: 价格序列
            period: 移动平均周期
            std_dev: 标准差倍数
            
        Returns:
            包含上轨、中轨、下轨的字典
        """
        try:
            # 中轨（移动平均线）
            middle_band = prices.rolling(window=period).mean()
            
            # 标准差
            std = prices.rolling(window=period).std()
            
            # 上轨和下轨
            upper_band = middle_band + (std * std_dev)
            lower_band = middle_band - (std * std_dev)
            
            return {
                'upper': upper_band,
                'middle': middle_band,
                'lower': lower_band
            }
            
        except Exception as e:
            logger.error(f"❌ 计算布林带失败: {e}")
            avg_price = prices.mean()
            return {
                'upper': pd.Series([avg_price * 1.1] * len(prices), index=prices.index),
                'middle': pd.Series([avg_price] * len(prices), index=prices.index),
                'lower': pd.Series([avg_price * 0.9] * len(prices), index=prices.index)
            }
    
    def calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                           k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        计算随机指标KDJ
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_period: K值计算周期
            d_period: D值平滑周期
            
        Returns:
            包含K值、D值的字典
        """
        try:
            # 计算最高价和最低价的滚动窗口
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            # 计算K值
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            k_percent = k_percent.fillna(50)
            
            # 计算D值（K值的移动平均）
            d_percent = k_percent.rolling(window=d_period).mean()
            d_percent = d_percent.fillna(50)
            
            return {
                'k': k_percent,
                'd': d_percent
            }
            
        except Exception as e:
            logger.error(f"❌ 计算随机指标失败: {e}")
            return {
                'k': pd.Series([50] * len(close), index=close.index),
                'd': pd.Series([50] * len(close), index=close.index)
            }
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series,
                     period: int = 14) -> pd.Series:
        """
        计算平均真实波幅ATR
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期
            
        Returns:
            ATR指标序列
        """
        try:
            # 计算真实波幅
            prev_close = close.shift(1)
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # 计算ATR（真实波幅的移动平均）
            atr = true_range.rolling(window=period).mean()
            
            return atr.fillna(true_range.mean())
            
        except Exception as e:
            logger.error(f"❌ 计算ATR失败: {e}")
            return pd.Series([1.0] * len(close), index=close.index)
    
    def calculate_williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series,
                           period: int = 14) -> pd.Series:
        """
        计算威廉指标%R
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期
            
        Returns:
            威廉指标序列
        """
        try:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            
            williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
            
            return williams_r.fillna(-50)
            
        except Exception as e:
            logger.error(f"❌ 计算威廉指标失败: {e}")
            return pd.Series([-50] * len(close), index=close.index)
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算所有技术指标
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            所有技术指标的字典
        """
        try:
            logger.info("📊 开始计算所有技术指标...")
            
            # 确保必要的列存在
            required_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                logger.error(f"❌ 缺少必要的数据列: {missing_columns}")
                return {}
            
            close = data['close_price']
            high = data['high_price']
            low = data['low_price']
            volume = data['volume']
            
            indicators = {}
            
            # 1. RSI指标
            indicators['rsi'] = self.calculate_rsi(close)
            
            # 2. MACD指标
            macd_data = self.calculate_macd(close)
            indicators.update(macd_data)
            
            # 3. 移动平均线
            ma_data = self.calculate_moving_averages(close)
            indicators.update(ma_data)
            
            # 4. 动量指标
            momentum_data = self.calculate_momentum(close)
            indicators.update(momentum_data)
            
            # 5. 成交量比率
            indicators['volume_ratio'] = self.calculate_volume_ratio(volume)
            
            # 6. 布林带
            bb_data = self.calculate_bollinger_bands(close)
            indicators['bb_upper'] = bb_data['upper']
            indicators['bb_middle'] = bb_data['middle']
            indicators['bb_lower'] = bb_data['lower']
            
            # 7. 随机指标
            stoch_data = self.calculate_stochastic(high, low, close)
            indicators['stoch_k'] = stoch_data['k']
            indicators['stoch_d'] = stoch_data['d']
            
            # 8. ATR
            indicators['atr'] = self.calculate_atr(high, low, close)
            
            # 9. 威廉指标
            indicators['williams_r'] = self.calculate_williams_r(high, low, close)
            
            logger.info(f"✅ 技术指标计算完成，共计算 {len(indicators)} 个指标")
            
            return indicators
            
        except Exception as e:
            logger.error(f"❌ 计算技术指标失败: {e}")
            return {}
    
    def get_trend_signal(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        获取趋势信号评分
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            趋势信号评分字典
        """
        try:
            indicators = self.calculate_all_indicators(data)
            
            if not indicators:
                return {'trend_score': 0.0}
            
            # 获取最新值
            latest_values = {key: series.iloc[-1] if len(series) > 0 else 0 
                           for key, series in indicators.items()}
            
            trend_score = 0.0
            
            # 1. 移动平均线趋势 (权重: 0.3)
            ma_score = 0.0
            if all(f'ma_{p}' in latest_values for p in [5, 10, 20, 60]):
                # 多头排列得分
                if (latest_values['ma_5'] > latest_values['ma_10'] > 
                    latest_values['ma_20'] > latest_values['ma_60']):
                    ma_score = 1.0
                elif (latest_values['ma_5'] < latest_values['ma_10'] < 
                      latest_values['ma_20'] < latest_values['ma_60']):
                    ma_score = -1.0
                else:
                    ma_score = 0.0
            
            trend_score += ma_score * 0.3
            
            # 2. MACD趋势 (权重: 0.25)
            macd_score = 0.0
            if 'macd' in latest_values and 'signal' in latest_values:
                if latest_values['macd'] > latest_values['signal']:
                    macd_score = 0.5
                if latest_values['macd'] > 0:
                    macd_score += 0.5
                if latest_values['macd'] < latest_values['signal']:
                    macd_score -= 0.5
                if latest_values['macd'] < 0:
                    macd_score -= 0.5
            
            trend_score += macd_score * 0.25
            
            # 3. RSI趋势 (权重: 0.2)
            rsi_score = 0.0
            if 'rsi' in latest_values:
                rsi_val = latest_values['rsi']
                if rsi_val > 70:
                    rsi_score = -0.5  # 超买
                elif rsi_val < 30:
                    rsi_score = 0.5   # 超卖
                elif 40 <= rsi_val <= 60:
                    rsi_score = 0.8   # 中性偏强
                else:
                    rsi_score = 0.0
            
            trend_score += rsi_score * 0.2
            
            # 4. 动量指标 (权重: 0.25)
            momentum_score = 0.0
            if 'momentum_5d' in latest_values and 'momentum_20d' in latest_values:
                mom_5d = latest_values['momentum_5d']
                mom_20d = latest_values['momentum_20d']
                
                if mom_5d > 0 and mom_20d > 0:
                    momentum_score = 1.0
                elif mom_5d < 0 and mom_20d < 0:
                    momentum_score = -1.0
                else:
                    momentum_score = 0.0
            
            trend_score += momentum_score * 0.25
            
            # 标准化到0-100分
            normalized_score = max(0, min(100, (trend_score + 1) * 50))
            
            return {
                'trend_score': normalized_score,
                'ma_score': ma_score,
                'macd_score': macd_score,
                'rsi_score': rsi_score,
                'momentum_score': momentum_score
            }
            
        except Exception as e:
            logger.error(f"❌ 获取趋势信号失败: {e}")
            return {'trend_score': 50.0}  # 返回中性分数
