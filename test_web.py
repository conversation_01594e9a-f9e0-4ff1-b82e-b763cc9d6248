#!/usr/bin/env python3
"""
测试Web应用
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI(title="量化交易系统测试")

@app.get("/", response_class=HTMLResponse)
async def root():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>量化交易系统</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                background: #1a1a1a; 
                color: white; 
                text-align: center; 
                padding: 50px; 
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: #2d2d2d;
                padding: 40px;
                border-radius: 10px;
            }
            .api-link {
                display: inline-block;
                margin: 10px;
                padding: 15px 25px;
                background: #4CAF50;
                color: white;
                text-decoration: none;
                border-radius: 5px;
            }
            .api-link:hover {
                background: #45a049;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 量化交易系统 V2.0</h1>
            <p>Web应用已成功启动！</p>
            <br>
            <a href="/api/status" class="api-link">📊 系统状态</a>
            <a href="/api/stocks" class="api-link">📈 股票数据</a>
            <a href="/docs" class="api-link">📚 API文档</a>
            <br><br>
            <p>🌐 Web服务器运行正常</p>
            <p>📍 访问地址: http://localhost:8000</p>
        </div>
    </body>
    </html>
    """

@app.get("/api/status")
async def get_status():
    return {
        "status": "running",
        "message": "量化交易系统运行正常",
        "version": "2.0.0",
        "modules": {
            "web_server": True,
            "api": True,
            "database": True
        }
    }

@app.get("/api/stocks")
async def get_stocks():
    return {
        "stocks": [
            {"symbol": "000001", "name": "平安银行", "price": 12.85, "change": 0.38},
            {"symbol": "600519", "name": "贵州茅台", "price": 1680.00, "change": 1.2},
            {"symbol": "000858", "name": "五粮液", "price": 128.50, "change": -0.5}
        ],
        "count": 3,
        "timestamp": "2025-07-28 00:30:00"
    }

if __name__ == "__main__":
    print("🌐 启动量化交易系统 Web 应用")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
