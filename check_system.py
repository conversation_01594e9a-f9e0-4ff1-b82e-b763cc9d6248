#!/usr/bin/env python3
"""
VeighNa系统状态检查脚本
"""

import requests
import json
import time

def check_system_comprehensive():
    print("🔍 VeighNa量化交易系统全面检查")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    # 1. 检查主页
    print("1. 检查主页访问...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ 主页正常 (HTML长度: {} 字符)".format(len(response.text)))
        else:
            print(f"   ❌ 主页错误: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 主页访问失败: {e}")
    
    # 2. 检查测试页面
    print("\n2. 检查测试页面...")
    try:
        response = requests.get(f"{base_url}/test", timeout=5)
        if response.status_code == 200:
            print("   ✅ 测试页面正常")
        else:
            print(f"   ❌ 测试页面错误: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 测试页面访问失败: {e}")
    
    # 3. 检查所有API
    print("\n3. 检查API接口...")
    apis = [
        ("系统状态", "/api/system/status"),
        ("实时数据", "/api/realtime-data"),
        ("智能选股", "/api/stock-selection"),
        ("交易信号", "/api/trading-signals"),
        ("投资组合", "/api/portfolio")
    ]
    
    for name, endpoint in apis:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ {name}: 正常")
                    
                    # 显示关键信息
                    if endpoint == "/api/system/status":
                        flow_status = data.get('data', {}).get('business_flow', {})
                        print(f"      - 系统健康: {flow_status.get('system_health', 'unknown')}")
                        print(f"      - 数据采集: {flow_status.get('data_collection_status', 'unknown')}")
                        print(f"      - 信号跟踪: {flow_status.get('signal_tracking_status', 'unknown')}")
                    
                    elif endpoint == "/api/realtime-data":
                        stocks = data.get('data', {}).get('stocks', [])
                        print(f"      - 股票数量: {len(stocks)}")
                        if stocks:
                            print(f"      - 示例股票: {stocks[0]['symbol']} {stocks[0]['name']} ¥{stocks[0]['price']}")
                    
                    elif endpoint == "/api/trading-signals":
                        buy_signals = data.get('data', {}).get('buy_signals', [])
                        sell_signals = data.get('data', {}).get('sell_signals', [])
                        print(f"      - 买入信号: {len(buy_signals)}")
                        print(f"      - 卖出信号: {len(sell_signals)}")
                        
                else:
                    print(f"   ⚠️ {name}: API返回失败")
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    # 4. 检查静态文件
    print("\n4. 检查静态文件...")
    static_files = [
        ("CSS样式", "/static/css/professional_ui.css"),
        ("JavaScript", "/static/js/professional_app.js")
    ]
    
    for name, path in static_files:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {name}: 正常 (大小: {len(response.text)} 字符)")
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    print("\n" + "=" * 60)
    print("📋 系统访问信息:")
    print(f"🌐 主界面: {base_url}")
    print(f"🧪 测试页面: {base_url}/test")
    print(f"📊 实时数据API: {base_url}/api/realtime-data")
    print(f"🧠 智能选股API: {base_url}/api/stock-selection")
    print(f"📈 交易信号API: {base_url}/api/trading-signals")
    print(f"💼 投资组合API: {base_url}/api/portfolio")
    print(f"⚙️ 系统状态API: {base_url}/api/system/status")
    print("=" * 60)
    
    print("\n💡 如果浏览器无法访问，请尝试:")
    print("1. 清除浏览器缓存和Cookie")
    print("2. 使用无痕/隐私模式")
    print("3. 尝试不同的浏览器")
    print("4. 检查防火墙设置")
    print("5. 直接访问测试页面: http://localhost:8080/test")

if __name__ == "__main__":
    check_system_comprehensive()
