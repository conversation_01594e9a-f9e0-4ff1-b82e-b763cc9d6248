#!/usr/bin/env python3
"""
VeighNa回测系统测试脚本
测试完整的VeighNa回测系统功能
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import date, datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from vnpy_backtesting import (
    BacktestingEngine, MultiTimeframeStrategy, ParameterOptimizer,
    PerformanceAnalyzer, RiskAnalyzer, ReportGenerator
)
from vnpy_backtesting.strategies.multi_factor_strategy import MultiFactorStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_backtesting_engine():
    """测试回测引擎"""
    logger.info("🚀 测试VeighNa回测引擎...")
    
    try:
        # 创建回测引擎
        engine = BacktestingEngine()
        
        # 设置回测参数
        engine.set_parameters(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 6, 30),
            initial_capital=1000000.0,
            commission_rate=0.0003,
            slippage=0.001
        )
        
        # 添加策略
        strategy_params = {
            'buy_score_threshold': 70,
            'sell_score_threshold': 30,
            'max_position_pct': 0.1,
            'rebalance_days': 20,
            'max_stocks': 5
        }
        
        engine.add_strategy(MultiFactorStrategy, strategy_params)
        
        # 加载数据
        test_symbols = ['000001', '000002', '600000', '000858', '002415']
        success = engine.load_data(test_symbols, timeframe="1d")
        
        if success:
            logger.info(f"✅ 数据加载成功: {len(test_symbols)}只股票")
            
            # 运行回测
            if engine.run_backtesting():
                logger.info("✅ 回测运行成功")
                
                # 获取结果
                results = engine.get_results()
                logger.info(f"📊 回测结果:")
                logger.info(f"  总收益率: {results.get('total_return', 0):.2%}")
                logger.info(f"  年化收益率: {results.get('annual_return', 0):.2%}")
                logger.info(f"  夏普比率: {results.get('sharpe_ratio', 0):.2f}")
                logger.info(f"  最大回撤: {results.get('max_drawdown', 0):.2%}")
                logger.info(f"  交易次数: {results.get('total_trades', 0)}")
                
                return results
            else:
                logger.error("❌ 回测运行失败")
                return None
        else:
            logger.error("❌ 数据加载失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ 回测引擎测试失败: {e}")
        return None

def test_performance_analyzer(backtest_results):
    """测试绩效分析器"""
    logger.info("📊 测试绩效分析器...")
    
    try:
        if not backtest_results:
            logger.warning("⚠️ 无回测结果，跳过绩效分析")
            return None
        
        analyzer = PerformanceAnalyzer()
        performance_analysis = analyzer.analyze_performance(backtest_results)
        
        if performance_analysis:
            logger.info("✅ 绩效分析完成")
            logger.info(f"📈 关键指标:")
            logger.info(f"  总收益率: {performance_analysis.get('total_return', 0):.2%}")
            logger.info(f"  年化收益率: {performance_analysis.get('annual_return', 0):.2%}")
            logger.info(f"  波动率: {performance_analysis.get('volatility', 0):.2%}")
            logger.info(f"  夏普比率: {performance_analysis.get('sharpe_ratio', 0):.2f}")
            logger.info(f"  最大回撤: {performance_analysis.get('max_drawdown', 0):.2%}")
            
            return performance_analysis
        else:
            logger.error("❌ 绩效分析失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ 绩效分析器测试失败: {e}")
        return None

def test_risk_analyzer(backtest_results):
    """测试风险分析器"""
    logger.info("⚠️ 测试风险分析器...")
    
    try:
        if not backtest_results:
            logger.warning("⚠️ 无回测结果，跳过风险分析")
            return None
        
        analyzer = RiskAnalyzer()
        risk_analysis = analyzer.analyze_risk(backtest_results)
        
        if risk_analysis:
            logger.info("✅ 风险分析完成")
            
            risk_rating = risk_analysis.get('risk_rating', {})
            logger.info(f"⚠️ 风险评级:")
            logger.info(f"  综合评分: {risk_rating.get('overall_score', 0):.1f}/100")
            logger.info(f"  风险等级: {risk_rating.get('risk_description', '未知')}")
            
            warnings = risk_analysis.get('risk_warnings', [])
            if warnings:
                logger.info(f"⚠️ 风险预警 ({len(warnings)}个):")
                for warning in warnings[:3]:
                    logger.warning(f"  • {warning.get('message', '')}")
            
            return risk_analysis
        else:
            logger.error("❌ 风险分析失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ 风险分析器测试失败: {e}")
        return None

def test_report_generator(backtest_results, performance_analysis, risk_analysis):
    """测试报告生成器"""
    logger.info("📄 测试报告生成器...")
    
    try:
        if not all([backtest_results, performance_analysis, risk_analysis]):
            logger.warning("⚠️ 缺少分析结果，跳过报告生成")
            return False
        
        generator = ReportGenerator()
        
        # 生成综合报告
        comprehensive_report = generator.generate_comprehensive_report(
            backtest_results=backtest_results,
            performance_analysis=performance_analysis,
            risk_analysis=risk_analysis,
            strategy_name="多因子选股策略测试"
        )
        
        if comprehensive_report:
            logger.info("✅ 综合报告生成成功")
            
            # 显示报告摘要
            executive_summary = comprehensive_report.get('executive_summary', {})
            logger.info(f"📋 报告摘要:")
            logger.info(f"  策略评级: {executive_summary.get('performance_grade', '未知')}")
            logger.info(f"  评价: {executive_summary.get('performance_comment', '无')}")
            
            # 保存报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"backtest_report_{timestamp}"
            
            if generator.save_report_to_file(comprehensive_report, filename):
                logger.info(f"✅ 报告已保存: {filename}")
                return True
            else:
                logger.error("❌ 报告保存失败")
                return False
        else:
            logger.error("❌ 报告生成失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 报告生成器测试失败: {e}")
        return False

def test_parameter_optimizer():
    """测试参数优化器"""
    logger.info("🔧 测试参数优化器...")
    
    try:
        optimizer = ParameterOptimizer()
        
        # 定义参数范围
        parameter_ranges = {
            'buy_score_threshold': [65, 70, 75, 80],
            'sell_score_threshold': [25, 30, 35],
            'max_position_pct': [0.08, 0.10, 0.12],
            'rebalance_days': [15, 20, 25]
        }
        
        # 回测引擎配置
        engine_config = {
            'start_date': date(2024, 1, 1),
            'end_date': date(2024, 3, 31),  # 缩短时间范围加快测试
            'initial_capital': 1000000.0,
            'commission_rate': 0.0003,
            'slippage': 0.001,
            'symbols': ['000001', '000002', '600000']
        }
        
        # 网格搜索优化（限制组合数量）
        logger.info("🔍 开始网格搜索优化（测试模式）...")
        
        # 简化参数范围进行测试
        test_parameter_ranges = {
            'buy_score_threshold': [70, 75],
            'sell_score_threshold': [30],
            'max_position_pct': [0.10],
            'rebalance_days': [20]
        }
        
        optimization_results = optimizer.grid_search_optimization(
            strategy_class=MultiFactorStrategy,
            parameter_ranges=test_parameter_ranges,
            engine_config=engine_config,
            max_workers=2
        )
        
        if optimization_results:
            logger.info(f"✅ 参数优化完成，测试了 {len(optimization_results)} 个参数组合")
            
            # 显示最佳结果
            best_results = optimizer.get_best_parameters(top_n=3)
            for i, result in enumerate(best_results, 1):
                logger.info(f"  第{i}名: 参数{result.parameters}, 目标值{result.target_value:.4f}")
            
            return True
        else:
            logger.error("❌ 参数优化失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 参数优化器测试失败: {e}")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🧪 开始VeighNa回测系统综合测试...")
    logger.info("=" * 80)
    
    test_results = []
    
    try:
        # 1. 测试回测引擎
        backtest_results = test_backtesting_engine()
        test_results.append(("回测引擎", backtest_results is not None))
        logger.info("=" * 80)
        
        # 2. 测试绩效分析器
        performance_analysis = test_performance_analyzer(backtest_results)
        test_results.append(("绩效分析器", performance_analysis is not None))
        logger.info("=" * 80)
        
        # 3. 测试风险分析器
        risk_analysis = test_risk_analyzer(backtest_results)
        test_results.append(("风险分析器", risk_analysis is not None))
        logger.info("=" * 80)
        
        # 4. 测试报告生成器
        report_success = test_report_generator(backtest_results, performance_analysis, risk_analysis)
        test_results.append(("报告生成器", report_success))
        logger.info("=" * 80)
        
        # 5. 测试参数优化器
        optimizer_success = test_parameter_optimizer()
        test_results.append(("参数优化器", optimizer_success))
        logger.info("=" * 80)
        
        # 测试结果总结
        logger.info("📊 测试结果总结:")
        success_count = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  - {test_name}: {status}")
            if result:
                success_count += 1
        
        logger.info(f"🎉 VeighNa回测系统测试完成: {success_count}/{len(test_results)} 项通过")
        
        if success_count == len(test_results):
            logger.info("🎊 所有测试均通过！VeighNa回测系统功能正常")
        else:
            logger.warning("⚠️ 部分测试失败，请检查相关功能")
        
        # 系统功能总结
        logger.info("\n🚀 VeighNa回测系统功能总结:")
        logger.info("  ✅ 专业回测引擎 - 支持多时间周期策略")
        logger.info("  ✅ 多因子选股策略 - 技术面+基本面综合评分")
        logger.info("  ✅ 绩效分析器 - 全面的绩效指标计算")
        logger.info("  ✅ 风险分析器 - 多维度风险评估")
        logger.info("  ✅ 参数优化器 - 网格搜索和遗传算法")
        logger.info("  ✅ 报告生成器 - 专业的回测分析报告")
        
    except Exception as e:
        logger.error(f"❌ 综合测试过程中发生异常: {e}")

async def main():
    """主函数"""
    print("🚀 VeighNa回测系统测试")
    print("=" * 80)
    print("基于VeighNa引擎的专业量化回测平台")
    print("支持多时间周期策略、参数优化、绩效分析、风险控制")
    print("=" * 80)
    
    await run_comprehensive_test()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
