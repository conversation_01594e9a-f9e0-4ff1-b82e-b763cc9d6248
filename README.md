# 🚀 VeighNa量化交易系统 v2.0

## 📋 项目简介

VeighNa量化交易系统v2.0是一个完整的量化交易解决方案，严格按照产品设计原图开发，提供从数据采集、智能选股、信号生成到投资组合管理的全流程功能。

## ✨ 核心功能

### 📊 实时数据引擎
- **实时行情采集**: 支持多市场股票数据实时采集
- **数据质量控制**: 自动数据清洗和异常检测
- **高性能存储**: SQLite数据库存储历史数据
- **数据API**: RESTful API提供数据服务

### 🔍 智能选股引擎
- **多因子模型**: 技术面、基本面、市场表现三维评分
- **动态权重**: 可配置的因子权重分配
- **实时评分**: 基于最新数据的股票评分排名
- **选股策略**: 支持多种选股策略和筛选条件

### 📈 交易信号系统
- **技术指标**: RSI、MACD、布林带等经典指标
- **信号生成**: 自动生成买入卖出信号
- **信号评分**: 基于历史表现的信号置信度
- **实时监控**: 24/7信号监控和推送

### 💼 投资组合管理
- **持仓管理**: 实时持仓监控和盈亏分析
- **风险控制**: 止损止盈、仓位控制、风险预警
- **绩效分析**: 夏普比率、最大回撤等绩效指标
- **再平衡**: 智能仓位再平衡建议

### 🌐 Web界面
- **实时仪表板**: 股票行情、交易信号、持仓明细
- **K线图表**: 多时间周期K线图表显示
- **交互操作**: 完整的Web界面交互功能
- **响应式设计**: 支持多设备访问

## 🛠️ 技术架构

### 后端技术栈
- **Python 3.8+**: 核心开发语言
- **Flask**: Web框架和API服务
- **SQLite**: 数据存储
- **NumPy/Pandas**: 数据处理和分析
- **Threading**: 多线程并发处理

### 前端技术栈
- **HTML5/CSS3**: 页面结构和样式
- **JavaScript ES6+**: 前端交互逻辑
- **ECharts**: 图表可视化
- **Font Awesome**: 图标库

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web前端界面    │    │   Flask API     │    │   核心数据引擎   │
│                │    │                │    │                │
│ • 实时仪表板     │◄──►│ • RESTful API  │◄──►│ • 数据采集       │
│ • K线图表       │    │ • 跨域支持      │    │ • 数据存储       │
│ • 交互操作       │    │ • 错误处理      │    │ • 数据质量控制   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  智能选股引擎    │    │  交易信号系统    │    │  投资组合管理    │
│                │    │                │    │                │
│ • 多因子评分     │    │ • 技术指标分析   │    │ • 持仓管理       │
│ • 股票排名       │    │ • 信号生成       │    │ • 风险控制       │
│ • 选股策略       │    │ • 置信度评估     │    │ • 绩效分析       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.8 或更高版本
- 8GB+ 内存推荐
- 现代浏览器支持

### 2. 安装依赖
```bash
# 安装Python依赖包
pip install flask flask-cors numpy pandas

# 或使用requirements.txt
pip install -r requirements.txt
```

### 3. 启动系统
```bash
# 方式1: 使用启动脚本（推荐）
python start_system.py

# 方式2: 直接启动Web服务器
python web_server.py

# 方式3: 安装依赖后启动
python start_system.py --install-deps
python start_system.py
```

### 4. 访问系统
- **Web界面**: http://localhost:8080
- **API文档**: http://localhost:8080/api

## 📖 使用指南

### 主界面功能
1. **实时行情监控**: 查看股票实时价格、涨跌幅、成交量等
2. **K线图表**: 支持1分钟到日K线多时间周期切换
3. **交易信号**: 实时显示买入卖出信号和置信度
4. **持仓明细**: 查看投资组合持仓和盈亏情况

### 智能选股
1. 点击左侧菜单"智能选股"
2. 调整技术面、基本面、市场表现权重
3. 点击"执行选股"获取推荐股票列表
4. 查看股票评分和推荐等级

### API接口
- `GET /api/realtime-data`: 获取实时数据
- `GET /api/stock-selection`: 获取选股结果
- `GET /api/trading-signals`: 获取交易信号
- `GET /api/portfolio`: 获取投资组合
- `GET /api/stock/<symbol>`: 获取股票详情

## 🔧 配置说明

### 投资组合配置
```python
# 在portfolio_manager.py中修改
PortfolioConfig(
    total_capital=1000000.0,      # 总资金
    max_position_weight=0.15,     # 单股最大权重15%
    max_positions=20,             # 最大持仓数量
    risk_tolerance="moderate",    # 风险偏好
    stop_loss_pct=-0.08,         # 止损线-8%
    take_profit_pct=0.20         # 止盈线+20%
)
```

### 选股权重配置
```python
# 在intelligent_stock_selector.py中修改
factor_weights = {
    'technical': 0.5,    # 技术面权重50%
    'fundamental': 0.3,  # 基本面权重30%
    'market': 0.2        # 市场表现权重20%
}
```

## 📊 系统监控

### 实时状态
- 数据引擎状态: 运行中/已停止
- 信号系统状态: 活跃/暂停
- 组合管理状态: 正常/异常
- Web服务状态: 在线/离线

### 性能指标
- CPU使用率: 通常<20%
- 内存使用: 约200-500MB
- 数据更新频率: 2秒/次
- 信号生成频率: 30秒/次

## 🐛 故障排除

### 常见问题
1. **端口占用**: 修改web_server.py中的端口号
2. **依赖缺失**: 运行`pip install -r requirements.txt`
3. **数据库错误**: 删除vnpy_trading_system.db重新启动
4. **浏览器兼容**: 使用Chrome、Firefox等现代浏览器

### 日志查看
```bash
# 查看系统日志
tail -f vnpy_system.log

# 查看错误日志
grep ERROR vnpy_system.log
```

## 🤝 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目主页: https://github.com/vnpy/vnpy
- 问题反馈: https://github.com/vnpy/vnpy/issues
- 邮箱: <EMAIL>
- QQ群: 262656087

## 🙏 致谢

感谢VeighNa开源社区的贡献者们，以及所有使用和支持本项目的用户！

---

**⚠️ 风险提示**: 本系统仅供学习和研究使用，不构成投资建议。量化交易存在风险，请谨慎使用。
