# 量化交易系统 V2.0 配置文件

# 系统基本配置
system:
  name: "量化交易系统 V2.0"
  version: "2.0.0"
  debug: false
  timezone: "Asia/Shanghai"

# 数据库配置
database:
  url: "sqlite:///quantitative_trading_system.db"
  pool_size: 20
  max_overflow: 30
  pool_pre_ping: true
  pool_recycle: 3600
  echo: false

# 数据采集配置
data_collection:
  # 采集间隔配置
  intervals:
    realtime: 5        # 实时数据采集间隔(秒)
    minute_1: 60       # 1分钟数据采集间隔(秒)
    minute_5: 300      # 5分钟数据采集间隔(秒)
    minute_15: 900     # 15分钟数据采集间隔(秒)
    hour_1: 3600       # 1小时数据采集间隔(秒)
    hour_4: 14400      # 4小时数据采集间隔(秒)
    daily: 86400       # 日线数据采集间隔(秒)
  
  # ADATA配置
  adata:
    enable: true
    rate_limit: 100    # 每分钟最大请求数
    timeout: 30        # 请求超时时间(秒)
    retry_times: 3     # 重试次数
    retry_delay: 1     # 重试延迟(秒)
  
  # 数据质量控制
  quality_control:
    enable: true
    max_missing_ratio: 0.1    # 最大缺失数据比例
    outlier_detection: true   # 是否启用异常值检测
    data_validation: true     # 是否启用数据验证

# 分析引擎配置
analysis_engine:
  # 智能选股配置
  stock_selection:
    max_stocks: 50           # 最大选股数量
    min_market_cap: 1000000000  # 最小市值(元)
    min_volume: 1000000      # 最小成交量
    exclude_st: true         # 是否排除ST股票
    exclude_new_stock_days: 60  # 排除新股天数
  
  # 评分权重配置
  scoring_weights:
    technical: 0.5      # 技术面权重 50%
    fundamental: 0.3    # 基本面权重 30%
    market: 0.2         # 市场表现权重 20%
  
  # 技术分析配置
  technical_analysis:
    indicators:
      - name: "RSI"
        period: 14
        weight: 0.15
      - name: "MACD"
        fast_period: 12
        slow_period: 26
        signal_period: 9
        weight: 0.20
      - name: "MA_TREND"
        periods: [5, 10, 20, 60]
        weight: 0.25
      - name: "MOMENTUM"
        periods: [5, 20]
        weight: 0.15
      - name: "VOLUME_RATIO"
        period: 20
        weight: 0.25
  
  # 基本面分析配置
  fundamental_analysis:
    metrics:
      - name: "PE_RATIO"
        weight: 0.25
        min_value: 0
        max_value: 100
      - name: "PB_RATIO"
        weight: 0.20
        min_value: 0
        max_value: 10
      - name: "ROE"
        weight: 0.30
        min_value: 0
        max_value: 1
      - name: "REVENUE_GROWTH"
        weight: 0.25
        min_value: -1
        max_value: 5

# VeighNa回测系统配置
vnpy_backtesting:
  # 回测基本配置
  basic:
    initial_capital: 1000000    # 初始资金
    commission: 0.0003          # 手续费率
    slippage: 0.001            # 滑点
    size_multiplier: 1         # 合约乘数
  
  # 策略配置
  strategy:
    name: "MultiTimeframeStrategy"
    class_name: "MultiTimeframeStrategy"
    parameters:
      fast_window: 10
      slow_window: 20
      signal_window: 5
  
  # 优化配置
  optimization:
    method: "genetic"          # 优化方法: genetic, grid
    population_size: 50        # 遗传算法种群大小
    generations: 100           # 遗传算法代数
    mutation_rate: 0.1         # 变异率
    crossover_rate: 0.8        # 交叉率

# 投资组合管理配置
portfolio_management:
  # 组合构建配置
  construction:
    default_size: 20           # 默认组合大小
    max_positions: 30          # 最大持仓数
    min_positions: 10          # 最小持仓数
    max_position_weight: 0.1   # 单股最大权重
    min_position_weight: 0.01  # 单股最小权重
  
  # 再平衡配置
  rebalancing:
    frequency: "monthly"       # 再平衡频率: daily, weekly, monthly
    threshold: 0.05           # 再平衡阈值
    method: "threshold"       # 再平衡方法: periodic, threshold
  
  # 风险控制配置
  risk_control:
    max_sector_weight: 0.3    # 单行业最大权重
    max_drawdown_limit: 0.2   # 最大回撤限制
    var_confidence: 0.95      # VaR置信度
    stress_test: true         # 是否启用压力测试

# 交易执行配置
trading_execution:
  # 模拟交易配置
  simulation:
    enable: true
    latency: 0.1              # 模拟延迟(秒)
    fill_ratio: 0.95          # 成交比例
    partial_fill: true        # 是否允许部分成交
  
  # 订单管理配置
  order_management:
    max_orders_per_second: 10  # 每秒最大订单数
    order_timeout: 300         # 订单超时时间(秒)
    auto_cancel: true          # 是否自动撤单
  
  # 滑点控制配置
  slippage_control:
    model: "linear"           # 滑点模型: linear, sqrt, log
    base_slippage: 0.001      # 基础滑点
    volume_impact: 0.0001     # 成交量冲击系数

# 可视化配置
visualization:
  # Web界面配置
  web:
    host: "0.0.0.0"
    port: 8000
    debug: false
    auto_reload: false
  
  # 图表配置
  charts:
    theme: "dark"             # 图表主题: light, dark
    default_period: "1d"      # 默认时间周期
    max_data_points: 1000     # 最大数据点数
  
  # 仪表板配置
  dashboard:
    refresh_interval: 30      # 刷新间隔(秒)
    max_stocks_display: 20    # 最大显示股票数
    enable_alerts: true       # 是否启用预警

# 系统管理配置
system_management:
  # 日志配置
  logging:
    level: "INFO"             # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
    max_file_size: 10485760   # 最大日志文件大小(字节) 10MB
    backup_count: 5           # 日志文件备份数量
    log_to_database: true     # 是否记录到数据库
    cleanup_days: 30          # 日志清理天数
  
  # 性能监控配置
  monitoring:
    enable: true
    interval: 60              # 监控间隔(秒)
    metrics:
      - "cpu_usage"
      - "memory_usage"
      - "disk_usage"
      - "database_connections"
      - "api_response_time"
  
  # 备份配置
  backup:
    enable: true
    interval: 86400           # 备份间隔(秒) 24小时
    keep_days: 7              # 备份保留天数
    compress: true            # 是否压缩备份
  
  # 用户管理配置
  user_management:
    enable: false             # 是否启用用户管理
    session_timeout: 3600     # 会话超时时间(秒)
    max_login_attempts: 5     # 最大登录尝试次数

# 缓存配置
cache:
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    max_connections: 20
  
  # 缓存策略
  strategy:
    stock_data_ttl: 300       # 股票数据缓存时间(秒)
    analysis_result_ttl: 600  # 分析结果缓存时间(秒)
    user_session_ttl: 3600    # 用户会话缓存时间(秒)

# API配置
api:
  # 基本配置
  title: "量化交易系统 API"
  description: "基于9层架构的智能量化交易系统API"
  version: "2.0.0"
  
  # 安全配置
  security:
    secret_key: "your-secret-key-here"
    algorithm: "HS256"
    access_token_expire_minutes: 30
  
  # 限流配置
  rate_limiting:
    enable: true
    requests_per_minute: 100
    burst_size: 20

# 第三方服务配置
external_services:
  # 邮件服务配置
  email:
    enable: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    from_email: ""
  
  # 消息推送配置
  notification:
    enable: false
    webhook_url: ""
    channels: ["email", "webhook"]
