"""
卖出策略
实现基于产品设计的卖出决策逻辑：
- 止盈15%
- 止损5%
- 技术信号转弱
- 趋势转弱确认
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass

from database_models import db_manager, DailyMarket
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

@dataclass
class SellSignal:
    """卖出信号"""
    symbol: str
    signal_time: datetime
    signal_type: str  # 'take_profit', 'stop_loss', 'technical_weak', 'trend_reverse'
    signal_strength: float  # 信号强度 0-100
    current_price: float
    entry_price: float
    profit_loss_pct: float
    signal_reasons: List[str]
    urgency: str  # 'low', 'medium', 'high', 'urgent'

class SellStrategy:
    """卖出策略"""
    
    def __init__(self):
        self.multi_factor_selector = MultiFactorSelector()
        self.technical_indicators = TechnicalIndicators()
        
        # 卖出策略参数
        self.take_profit_pct = 0.15          # 止盈15%
        self.stop_loss_pct = 0.05            # 止损5%
        self.trailing_stop_pct = 0.08        # 移动止损8%
        self.min_technical_score = 40        # 技术面最低分数
        self.trend_reverse_threshold = 35    # 趋势转弱阈值
        
        # 技术指标阈值
        self.rsi_overbought = 75             # RSI超买
        self.macd_divergence_threshold = 0.1 # MACD背离阈值
        self.volume_shrink_ratio = 0.6       # 成交量萎缩比率
        
        logger.info("📉 卖出策略初始化完成")
        logger.info(f"  - 止盈: {self.take_profit_pct:.1%}")
        logger.info(f"  - 止损: {self.stop_loss_pct:.1%}")
        logger.info(f"  - 移动止损: {self.trailing_stop_pct:.1%}")
        logger.info(f"  - 技术面最低分: {self.min_technical_score}")
    
    def generate_sell_signals(self, positions: List[Dict[str, Any]]) -> List[SellSignal]:
        """
        生成卖出信号
        
        Args:
            positions: 当前持仓列表，包含symbol, entry_price, entry_date等信息
            
        Returns:
            卖出信号列表
        """
        try:
            logger.info(f"📉 开始生成卖出信号: {len(positions)}个持仓")
            
            sell_signals = []
            
            for position in positions:
                try:
                    signals = self._analyze_sell_opportunity(position)
                    sell_signals.extend(signals)
                    
                except Exception as e:
                    symbol = position.get('symbol', 'unknown')
                    logger.error(f"❌ 分析卖出机会失败: {symbol} - {e}")
            
            # 按紧急程度和信号强度排序
            urgency_order = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
            sell_signals.sort(
                key=lambda x: (urgency_order.get(x.urgency, 0), x.signal_strength), 
                reverse=True
            )
            
            logger.info(f"✅ 卖出信号生成完成: {len(sell_signals)}个信号")
            
            return sell_signals
            
        except Exception as e:
            logger.error(f"❌ 生成卖出信号失败: {e}")
            return []
    
    def _analyze_sell_opportunity(self, position: Dict[str, Any]) -> List[SellSignal]:
        """
        分析单个持仓的卖出机会
        
        Args:
            position: 持仓信息
            
        Returns:
            卖出信号列表
        """
        try:
            symbol = position['symbol']
            entry_price = position['entry_price']
            entry_date = position.get('entry_date', datetime.now().date())
            
            # 获取当前市场数据
            market_data = self._get_recent_market_data(symbol, days=30)
            if market_data is None or market_data.empty:
                logger.debug(f"📉 无市场数据: {symbol}")
                return []
            
            current_price = market_data['close_price'].iloc[-1]
            profit_loss_pct = (current_price - entry_price) / entry_price
            
            signals = []
            
            # 1. 检查止盈信号
            take_profit_signal = self._check_take_profit(
                symbol, current_price, entry_price, profit_loss_pct
            )
            if take_profit_signal:
                signals.append(take_profit_signal)
            
            # 2. 检查止损信号
            stop_loss_signal = self._check_stop_loss(
                symbol, current_price, entry_price, profit_loss_pct, market_data
            )
            if stop_loss_signal:
                signals.append(stop_loss_signal)
            
            # 3. 检查综合评分卖出信号 - 产品设计要求
            comprehensive_sell_signal = self._check_comprehensive_sell_signal(
                symbol, current_price, entry_price, profit_loss_pct, market_data
            )
            if comprehensive_sell_signal:
                signals.append(comprehensive_sell_signal)

            # 4. 检查技术信号转弱
            technical_weak_signal = self._check_technical_weakness(
                symbol, current_price, entry_price, profit_loss_pct, market_data
            )
            if technical_weak_signal:
                signals.append(technical_weak_signal)

            # 5. 检查趋势转弱
            trend_reverse_signal = self._check_trend_reversal(
                symbol, current_price, entry_price, profit_loss_pct, market_data
            )
            if trend_reverse_signal:
                signals.append(trend_reverse_signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ 分析卖出机会失败: {e}")
            return []
    
    def _get_recent_market_data(self, symbol: str, days: int = 30) -> Optional[pd.DataFrame]:
        """获取最近的市场数据"""
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                records = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if not records:
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in records:
                    data_dict = {
                        'trade_date': record.trade_date,
                        'open_price': float(record.open_price or 0),
                        'high_price': float(record.high_price or 0),
                        'low_price': float(record.low_price or 0),
                        'close_price': float(record.close_price or 0),
                        'volume': float(record.volume or 0),
                        'amount': float(record.amount or 0)
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def _check_take_profit(self,
                          symbol: str,
                          current_price: float,
                          entry_price: float,
                          profit_loss_pct: float) -> Optional[SellSignal]:
        """检查止盈信号"""
        try:
            if profit_loss_pct >= self.take_profit_pct:
                return SellSignal(
                    symbol=symbol,
                    signal_time=datetime.now(),
                    signal_type='take_profit',
                    signal_strength=90.0,
                    current_price=current_price,
                    entry_price=entry_price,
                    profit_loss_pct=profit_loss_pct,
                    signal_reasons=[
                        f"达到止盈目标: {profit_loss_pct:.2%}",
                        f"目标收益: {self.take_profit_pct:.2%}",
                        "建议获利了结"
                    ],
                    urgency='high'
                )
            
            # 部分止盈检查（盈利超过10%）
            elif profit_loss_pct >= 0.10:
                return SellSignal(
                    symbol=symbol,
                    signal_time=datetime.now(),
                    signal_type='partial_take_profit',
                    signal_strength=60.0,
                    current_price=current_price,
                    entry_price=entry_price,
                    profit_loss_pct=profit_loss_pct,
                    signal_reasons=[
                        f"盈利丰厚: {profit_loss_pct:.2%}",
                        "可考虑部分获利了结",
                        "保留核心仓位"
                    ],
                    urgency='medium'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 检查止盈信号失败: {symbol} - {e}")
            return None
    
    def _check_stop_loss(self,
                        symbol: str,
                        current_price: float,
                        entry_price: float,
                        profit_loss_pct: float,
                        market_data: pd.DataFrame) -> Optional[SellSignal]:
        """检查止损信号"""
        try:
            # 固定止损
            if profit_loss_pct <= -self.stop_loss_pct:
                return SellSignal(
                    symbol=symbol,
                    signal_time=datetime.now(),
                    signal_type='stop_loss',
                    signal_strength=100.0,
                    current_price=current_price,
                    entry_price=entry_price,
                    profit_loss_pct=profit_loss_pct,
                    signal_reasons=[
                        f"触发止损: {profit_loss_pct:.2%}",
                        f"止损线: {-self.stop_loss_pct:.2%}",
                        "立即止损出场"
                    ],
                    urgency='urgent'
                )
            
            # 移动止损检查
            if len(market_data) >= 5:
                # 计算最近5日最高价
                recent_high = market_data['high_price'].iloc[-5:].max()
                trailing_stop_price = recent_high * (1 - self.trailing_stop_pct)
                
                if current_price <= trailing_stop_price and profit_loss_pct > 0:
                    return SellSignal(
                        symbol=symbol,
                        signal_time=datetime.now(),
                        signal_type='trailing_stop',
                        signal_strength=85.0,
                        current_price=current_price,
                        entry_price=entry_price,
                        profit_loss_pct=profit_loss_pct,
                        signal_reasons=[
                            f"触发移动止损: {current_price:.2f}",
                            f"移动止损价: {trailing_stop_price:.2f}",
                            "保护既得利润"
                        ],
                        urgency='high'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 检查止损信号失败: {symbol} - {e}")
            return None
    
    def _check_technical_weakness(self,
                                 symbol: str,
                                 current_price: float,
                                 entry_price: float,
                                 profit_loss_pct: float,
                                 market_data: pd.DataFrame) -> Optional[SellSignal]:
        """检查技术信号转弱"""
        try:
            # 计算技术指标
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if not indicators:
                return None
            
            weakness_score = 0
            reasons = []
            
            # 1. RSI超买检查
            if 'rsi' in indicators and len(indicators['rsi']) > 0:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value > self.rsi_overbought:
                    weakness_score += 30
                    reasons.append(f"RSI严重超买({rsi_value:.1f})")
                elif rsi_value > 70:
                    weakness_score += 15
                    reasons.append(f"RSI超买({rsi_value:.1f})")
            
            # 2. MACD死叉检查
            if all(k in indicators for k in ['macd', 'signal']) and len(indicators['macd']) > 1:
                macd_current = indicators['macd'].iloc[-1]
                signal_current = indicators['signal'].iloc[-1]
                macd_prev = indicators['macd'].iloc[-2]
                signal_prev = indicators['signal'].iloc[-2]
                
                # 检查死叉
                if (macd_prev > signal_prev and macd_current < signal_current):
                    weakness_score += 25
                    reasons.append("MACD死叉确认")
                elif macd_current < signal_current and macd_current < 0:
                    weakness_score += 15
                    reasons.append("MACD弱势")
            
            # 3. 成交量萎缩检查
            if len(market_data) >= 10:
                recent_volume = market_data['volume'].iloc[-3:].mean()
                avg_volume = market_data['volume'].iloc[-10:].mean()
                
                if avg_volume > 0:
                    volume_ratio = recent_volume / avg_volume
                    if volume_ratio < self.volume_shrink_ratio:
                        weakness_score += 20
                        reasons.append(f"成交量萎缩({volume_ratio:.1f})")
            
            # 4. 价格跌破关键均线
            if len(market_data) >= 20:
                ma20 = market_data['close_price'].rolling(window=20).mean().iloc[-1]
                if current_price < ma20:
                    weakness_score += 20
                    reasons.append("跌破20日均线")
            
            # 如果技术面转弱且有盈利，建议卖出
            if weakness_score >= 50 and profit_loss_pct > 0:
                return SellSignal(
                    symbol=symbol,
                    signal_time=datetime.now(),
                    signal_type='technical_weak',
                    signal_strength=weakness_score,
                    current_price=current_price,
                    entry_price=entry_price,
                    profit_loss_pct=profit_loss_pct,
                    signal_reasons=reasons,
                    urgency='medium'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 检查技术转弱失败: {symbol} - {e}")
            return None
    
    def _check_trend_reversal(self,
                             symbol: str,
                             current_price: float,
                             entry_price: float,
                             profit_loss_pct: float,
                             market_data: pd.DataFrame) -> Optional[SellSignal]:
        """检查趋势转弱"""
        try:
            # 获取综合评分
            score_result = self.multi_factor_selector.calculate_comprehensive_score(symbol)
            
            total_score = score_result.get('total_score', 50)
            technical_score = score_result.get('technical_score', 50)
            
            # 趋势转弱判断
            if technical_score < self.trend_reverse_threshold:
                urgency = 'high' if technical_score < 30 else 'medium'
                signal_strength = 100 - technical_score  # 分数越低，卖出信号越强
                
                return SellSignal(
                    symbol=symbol,
                    signal_time=datetime.now(),
                    signal_type='trend_reverse',
                    signal_strength=signal_strength,
                    current_price=current_price,
                    entry_price=entry_price,
                    profit_loss_pct=profit_loss_pct,
                    signal_reasons=[
                        f"技术面恶化: {technical_score:.1f}分",
                        f"综合评分下降: {total_score:.1f}分",
                        "趋势转弱确认"
                    ],
                    urgency=urgency
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 检查趋势转弱失败: {symbol} - {e}")
            return None
    
    def get_sell_strategy_status(self) -> Dict[str, Any]:
        """获取卖出策略状态"""
        return {
            'strategy_name': '多因子卖出策略',
            'take_profit_pct': self.take_profit_pct,
            'stop_loss_pct': self.stop_loss_pct,
            'trailing_stop_pct': self.trailing_stop_pct,
            'min_technical_score': self.min_technical_score,
            'trend_reverse_threshold': self.trend_reverse_threshold,
            'technical_thresholds': {
                'rsi_overbought': self.rsi_overbought,
                'macd_divergence_threshold': self.macd_divergence_threshold,
                'volume_shrink_ratio': self.volume_shrink_ratio
            }
        }

    # ============================================================================
    # 产品设计要求的新增方法
    # ============================================================================

    def _check_comprehensive_sell_signal(self,
                                       symbol: str,
                                       current_price: float,
                                       entry_price: float,
                                       profit_loss_pct: float,
                                       market_data: pd.DataFrame) -> Optional[SellSignal]:
        """
        检查综合评分卖出信号 - 按产品设计要求

        产品设计卖出逻辑：
        1. 综合评分≤75分触发卖出信号
        2. 技术指标组合转弱确认
        3. 成交量萎缩确认
        4. 综合评分≤65分触发强烈卖出信号
        """
        try:
            # 1. 计算当前因子评分
            from intelligent_stock_selection.stock_selector import StockSelector

            selector = StockSelector()
            current_factor_score = selector.calculate_stock_score(symbol)

            # 2. 检查技术指标组合转弱
            technical_weakness = self._check_technical_signal_weakness(market_data)

            # 3. 检查成交量萎缩
            volume_shrinkage = self._check_volume_shrinkage(market_data)

            # 4. 计算综合卖出评分
            comprehensive_score = self._calculate_comprehensive_sell_score(
                current_factor_score, technical_weakness, volume_shrinkage, market_data
            )

            # 5. 判断卖出信号
            if comprehensive_score > 75:  # 产品设计卖出阈值
                return None

            # 确定信号强度
            if comprehensive_score <= 65:
                signal_type = "STRONG_SELL"
                signal_strength = 0.9
                urgency = "HIGH"
            elif comprehensive_score <= 70:
                signal_type = "SELL"
                signal_strength = 0.7
                urgency = "MEDIUM"
            else:
                signal_type = "WEAK_SELL"
                signal_strength = 0.5
                urgency = "LOW"

            # 生成卖出信号
            sell_signal = SellSignal(
                symbol=symbol,
                signal_time=datetime.now(),
                signal_price=current_price,
                signal_strength=signal_strength,
                signal_type=signal_type,
                sell_reason=f"综合评分{comprehensive_score:.1f}分触发卖出",
                urgency=urgency,
                entry_price=entry_price,
                profit_loss_pct=profit_loss_pct,
                expected_price_target=current_price * 0.95,  # 预期5%下跌
                confidence=self._calculate_sell_confidence(technical_weakness, volume_shrinkage)
            )

            logger.info(f"🔵 生成卖出信号: {symbol} 评分{comprehensive_score} 类型{signal_type}")

            return sell_signal

        except Exception as e:
            logger.error(f"❌ 检查综合卖出信号失败: {symbol} - {e}")
            return None

    def _check_technical_signal_weakness(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查技术指标组合转弱"""
        try:
            from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

            indicators = TechnicalIndicators()
            all_indicators = indicators.calculate_all_indicators(market_data)

            result = {
                'weak': False,
                'score': 100,
                'details': {},
                'signals': []
            }

            score = 100

            # MA空头排列检查
            ma_signals = self._check_ma_bearish_alignment(market_data)
            if ma_signals['aligned']:
                score -= 25
                result['signals'].append("MA空头排列")
            result['details']['ma_alignment'] = ma_signals

            # MACD死叉检查
            if 'macd' in all_indicators and 'signal' in all_indicators:
                macd_value = all_indicators['macd'].iloc[-1]
                signal_value = all_indicators['signal'].iloc[-1]

                if macd_value < signal_value or macd_value < 0:
                    score -= 25
                    result['signals'].append("MACD死叉")

            result['score'] = max(0, score)
            result['weak'] = score <= 25

            return result

        except Exception as e:
            logger.error(f"❌ 技术指标转弱检查失败: {e}")
            return {'weak': False, 'score': 100, 'details': {}, 'signals': []}

    def _check_volume_shrinkage(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查成交量萎缩"""
        try:
            result = {
                'shrinking': False,
                'score': 100,
                'details': {},
                'reasons': []
            }

            if len(market_data) < 20:
                return result

            volumes = market_data['volume'].astype(float)
            current_volume = volumes.iloc[-1]
            avg_20d = volumes.rolling(20).mean().iloc[-1]

            score = 100

            # 当日成交量萎缩检查
            volume_ratio = current_volume / avg_20d if avg_20d > 0 else 1
            if volume_ratio < 0.7:
                score -= 40
                result['reasons'].append(f"当日成交量萎缩{volume_ratio:.1f}倍")

            result['score'] = max(0, score)
            result['shrinking'] = score <= 30

            return result

        except Exception as e:
            logger.error(f"❌ 成交量萎缩检查失败: {e}")
            return {'shrinking': False, 'score': 100, 'details': {}, 'reasons': []}

    def _calculate_comprehensive_sell_score(self,
                                          factor_score: float,
                                          technical_weakness: Dict[str, Any],
                                          volume_shrinkage: Dict[str, Any],
                                          market_data: pd.DataFrame) -> float:
        """计算综合卖出评分"""
        try:
            weights = {
                'factor': 0.40,
                'technical': 0.35,
                'volume': 0.15,
                'market': 0.10
            }

            factor_component = factor_score * weights['factor']
            technical_component = technical_weakness.get('score', 100) * weights['technical']
            volume_component = volume_shrinkage.get('score', 100) * weights['volume']
            market_component = 50 * weights['market']  # 简化市场环境评分

            comprehensive_score = (
                factor_component +
                technical_component +
                volume_component +
                market_component
            )

            return max(0, min(100, comprehensive_score))

        except Exception as e:
            logger.error(f"❌ 计算综合卖出评分失败: {e}")
            return 100

    def _check_ma_bearish_alignment(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查均线空头排列"""
        try:
            close_prices = market_data['close_price']

            ma5 = close_prices.rolling(5).mean().iloc[-1]
            ma10 = close_prices.rolling(10).mean().iloc[-1]
            ma20 = close_prices.rolling(20).mean().iloc[-1]
            ma60 = close_prices.rolling(60).mean().iloc[-1] if len(close_prices) >= 60 else ma20

            aligned = ma5 < ma10 < ma20 < ma60

            return {
                'aligned': aligned,
                'ma5': ma5,
                'ma10': ma10,
                'ma20': ma20,
                'ma60': ma60
            }

        except Exception as e:
            logger.error(f"❌ 均线空头排列检查失败: {e}")
            return {'aligned': False}

    def _calculate_sell_confidence(self,
                                 technical_weakness: Dict[str, Any],
                                 volume_shrinkage: Dict[str, Any]) -> float:
        """计算卖出信号置信度"""
        try:
            confidence = 0.5

            if technical_weakness.get('weak', False):
                confidence += 0.3

            if volume_shrinkage.get('shrinking', False):
                confidence += 0.2

            return min(1.0, confidence)

        except Exception as e:
            logger.error(f"❌ 计算卖出置信度失败: {e}")
            return 0.5
