"""
买入策略
实现基于产品设计的买入决策逻辑：
- 技术指标组合确认
- 多头排列确认
- 成交量确认
- 综合评分≥70分
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass

from database_models import db_manager, DailyMarket
from analysis_engine.stock_selector.multi_factor_selector import MultiFactorSelector
from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

@dataclass
class BuySignal:
    """买入信号"""
    symbol: str
    signal_time: datetime
    signal_strength: float  # 信号强度 0-100
    trigger_price: float
    target_price: float
    stop_loss_price: float
    signal_reasons: List[str]
    technical_score: float
    fundamental_score: float
    volume_confirmed: bool
    ma_alignment: bool

class BuyStrategy:
    """买入策略"""
    
    def __init__(self):
        self.multi_factor_selector = MultiFactorSelector()
        self.technical_indicators = TechnicalIndicators()
        
        # 买入策略参数
        self.min_total_score = 70.0          # 最小综合评分
        self.min_technical_score = 65.0      # 最小技术面评分
        self.min_volume_ratio = 1.2          # 最小成交量比率
        self.ma_periods = [5, 10, 20, 60]    # 均线周期
        self.rsi_oversold = 30               # RSI超卖阈值
        self.rsi_overbought = 70             # RSI超买阈值
        
        # 价格目标设置
        self.target_profit_pct = 0.15        # 目标盈利15%
        self.stop_loss_pct = 0.05            # 止损5%
        
        logger.info("📈 买入策略初始化完成")
        logger.info(f"  - 最小综合评分: {self.min_total_score}")
        logger.info(f"  - 最小技术评分: {self.min_technical_score}")
        logger.info(f"  - 目标盈利: {self.target_profit_pct:.1%}")
        logger.info(f"  - 止损: {self.stop_loss_pct:.1%}")
    
    def generate_buy_signals(self, symbols: List[str]) -> List[BuySignal]:
        """
        生成买入信号
        
        Args:
            symbols: 候选股票列表
            
        Returns:
            买入信号列表
        """
        try:
            logger.info(f"📈 开始生成买入信号: {len(symbols)}只股票")
            
            buy_signals = []
            
            for symbol in symbols:
                try:
                    signal = self._analyze_buy_opportunity(symbol)
                    if signal:
                        buy_signals.append(signal)
                        
                except Exception as e:
                    logger.error(f"❌ 分析买入机会失败: {symbol} - {e}")
            
            # 按信号强度排序
            buy_signals.sort(key=lambda x: x.signal_strength, reverse=True)
            
            logger.info(f"✅ 买入信号生成完成: {len(buy_signals)}个信号")
            
            return buy_signals
            
        except Exception as e:
            logger.error(f"❌ 生成买入信号失败: {e}")
            return []
    
    def _analyze_buy_opportunity(self, symbol: str) -> Optional[BuySignal]:
        """
        分析单只股票的买入机会
        
        Args:
            symbol: 股票代码
            
        Returns:
            买入信号或None
        """
        try:
            # 1. 获取综合评分
            score_result = self.multi_factor_selector.calculate_comprehensive_score(symbol)
            
            total_score = score_result.get('total_score', 0)
            technical_score = score_result.get('technical_score', 0)
            fundamental_score = score_result.get('fundamental_score', 0)
            
            # 2. 基础评分筛选 - 按产品设计要求调整
            # 产品设计要求：因子评分≥70分才考虑买入
            if total_score < 70:  # 产品设计硬性要求
                logger.debug(f"📉 综合评分不足70分: {symbol} = {total_score:.1f}")
                return None

            if technical_score < self.min_technical_score:
                logger.debug(f"📉 技术评分不足: {symbol} = {technical_score:.1f}")
                return None
            
            # 3. 获取市场数据进行技术分析
            market_data = self._get_recent_market_data(symbol, days=60)
            if market_data is None or market_data.empty:
                logger.debug(f"📉 无市场数据: {symbol}")
                return None
            
            # 4. 技术指标组合确认 - 按产品设计业务流程
            technical_signals = self._check_technical_signal_combination(market_data)
            if not technical_signals['valid']:
                logger.debug(f"📉 技术指标组合未确认: {symbol}")
                return None
            
            # 5. 多头排列确认
            ma_alignment = self._check_ma_alignment(market_data)
            if not ma_alignment['aligned']:
                logger.debug(f"📉 均线未多头排列: {symbol}")
                return None
            
            # 6. 成交量放大确认 - 产品设计要求
            volume_confirmation = self._check_volume_amplification(market_data)
            if not volume_confirmation['valid']:
                logger.debug(f"📉 成交量未放大确认: {symbol}")
                return None

            # 7. 综合评分计算 - 产品设计评分体系
            comprehensive_score = self._calculate_comprehensive_buy_score(
                total_score, technical_signals, volume_confirmation, market_data
            )

            # 8. 买入信号判断 - 产品设计阈值≥85分
            if comprehensive_score < 85:  # 强烈买入信号阈值
                logger.debug(f"📉 综合评分不足85分: {symbol} {comprehensive_score}")
                return None

            # 9. 计算价格目标
            current_price = market_data['close_price'].iloc[-1]
            target_price = current_price * (1 + self.target_profit_pct)
            stop_loss_price = current_price * (1 - self.stop_loss_pct)

            # 10. 计算信号强度
            signal_strength = min(comprehensive_score / 100.0, 1.0)  # 标准化到0-1
            
            # 9. 生成信号原因
            signal_reasons = self._generate_signal_reasons(
                total_score, technical_score, technical_confirmation,
                ma_alignment, volume_confirmation
            )
            
            # 10. 创建买入信号
            buy_signal = BuySignal(
                symbol=symbol,
                signal_time=datetime.now(),
                signal_strength=signal_strength,
                trigger_price=current_price,
                target_price=target_price,
                stop_loss_price=stop_loss_price,
                signal_reasons=signal_reasons,
                technical_score=technical_score,
                fundamental_score=fundamental_score,
                volume_confirmed=volume_confirmation['confirmed'],
                ma_alignment=ma_alignment['aligned']
            )
            
            logger.info(f"📈 生成买入信号: {symbol} 强度{signal_strength:.1f} @{current_price:.2f}")
            
            return buy_signal
            
        except Exception as e:
            logger.error(f"❌ 分析买入机会失败: {symbol} - {e}")
            return None
    
    def _get_recent_market_data(self, symbol: str, days: int = 60) -> Optional[pd.DataFrame]:
        """获取最近的市场数据"""
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                records = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if not records:
                    return None
                
                # 转换为DataFrame
                data_list = []
                for record in records:
                    data_dict = {
                        'trade_date': record.trade_date,
                        'open_price': float(record.open_price or 0),
                        'high_price': float(record.high_price or 0),
                        'low_price': float(record.low_price or 0),
                        'close_price': float(record.close_price or 0),
                        'volume': float(record.volume or 0),
                        'amount': float(record.amount or 0),
                        'turnover_rate': float(record.turnover_rate or 0)
                    }
                    data_list.append(data_dict)
                
                return pd.DataFrame(data_list)
                
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def _check_technical_confirmation(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查技术指标确认"""
        try:
            # 计算技术指标
            indicators = self.technical_indicators.calculate_all_indicators(market_data)
            
            if not indicators:
                return {'confirmed': False, 'reasons': ['技术指标计算失败']}
            
            confirmation_score = 0
            reasons = []
            
            # 1. RSI确认（不在超买区间）
            if 'rsi' in indicators and len(indicators['rsi']) > 0:
                rsi_value = indicators['rsi'].iloc[-1]
                if rsi_value < self.rsi_overbought:
                    confirmation_score += 25
                    if rsi_value < self.rsi_oversold:
                        confirmation_score += 10  # 超卖加分
                        reasons.append(f"RSI超卖反弹机会({rsi_value:.1f})")
                    else:
                        reasons.append(f"RSI合理区间({rsi_value:.1f})")
                else:
                    reasons.append(f"RSI超买风险({rsi_value:.1f})")
            
            # 2. MACD确认（金叉或上升趋势）
            if all(k in indicators for k in ['macd', 'signal']) and len(indicators['macd']) > 0:
                macd_value = indicators['macd'].iloc[-1]
                signal_value = indicators['signal'].iloc[-1]
                
                if macd_value > signal_value:
                    confirmation_score += 25
                    reasons.append("MACD金叉确认")
                elif macd_value > 0:
                    confirmation_score += 15
                    reasons.append("MACD零轴上方")
                else:
                    reasons.append("MACD弱势")
            
            # 3. 布林带确认（价格位置）
            if all(k in indicators for k in ['bb_upper', 'bb_middle', 'bb_lower']):
                current_price = market_data['close_price'].iloc[-1]
                bb_upper = indicators['bb_upper'].iloc[-1]
                bb_middle = indicators['bb_middle'].iloc[-1]
                bb_lower = indicators['bb_lower'].iloc[-1]
                
                if current_price < bb_middle:
                    confirmation_score += 20
                    if current_price < bb_lower:
                        confirmation_score += 10  # 超跌加分
                        reasons.append("布林带下轨支撑")
                    else:
                        reasons.append("布林带中轨下方")
                else:
                    reasons.append("布林带中轨上方")
            
            # 4. KDJ确认
            if all(k in indicators for k in ['stoch_k', 'stoch_d']):
                k_value = indicators['stoch_k'].iloc[-1]
                d_value = indicators['stoch_d'].iloc[-1]
                
                if k_value > d_value and k_value < 80:
                    confirmation_score += 15
                    reasons.append("KDJ金叉确认")
                elif k_value < 20:
                    confirmation_score += 10
                    reasons.append("KDJ超卖区域")
            
            confirmed = confirmation_score >= 60  # 需要60分以上确认
            
            return {
                'confirmed': confirmed,
                'score': confirmation_score,
                'reasons': reasons
            }
            
        except Exception as e:
            logger.error(f"❌ 技术指标确认失败: {e}")
            return {'confirmed': False, 'reasons': ['技术指标确认异常']}
    
    def _check_ma_alignment(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查均线多头排列"""
        try:
            if len(market_data) < max(self.ma_periods):
                return {'aligned': False, 'reason': '数据不足'}
            
            # 计算各周期均线
            mas = {}
            for period in self.ma_periods:
                ma_values = market_data['close_price'].rolling(window=period).mean()
                mas[f'ma_{period}'] = ma_values.iloc[-1]
            
            current_price = market_data['close_price'].iloc[-1]
            
            # 检查多头排列：价格 > MA5 > MA10 > MA20 > MA60
            ma_values = [current_price] + [mas[f'ma_{p}'] for p in self.ma_periods]
            
            aligned = all(ma_values[i] > ma_values[i+1] for i in range(len(ma_values)-1))
            
            # 计算排列强度
            if aligned:
                strength = 100
                reason = "完美多头排列"
            else:
                # 部分排列也给予一定分数
                partial_score = 0
                if current_price > mas['ma_5']:
                    partial_score += 25
                if mas['ma_5'] > mas['ma_10']:
                    partial_score += 25
                if mas['ma_10'] > mas['ma_20']:
                    partial_score += 25
                if mas['ma_20'] > mas['ma_60']:
                    partial_score += 25
                
                strength = partial_score
                if partial_score >= 75:
                    aligned = True  # 75分以上也认为是多头排列
                    reason = "强势多头排列"
                elif partial_score >= 50:
                    reason = "部分多头排列"
                else:
                    reason = "均线排列混乱"
            
            return {
                'aligned': aligned,
                'strength': strength,
                'reason': reason,
                'ma_values': mas
            }
            
        except Exception as e:
            logger.error(f"❌ 均线排列检查失败: {e}")
            return {'aligned': False, 'reason': '均线计算异常'}
    
    def _check_volume_confirmation(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查成交量确认"""
        try:
            if len(market_data) < 20:
                return {'confirmed': False, 'reason': '数据不足'}
            
            # 计算成交量比率
            recent_volume = market_data['volume'].iloc[-1]
            avg_volume_20 = market_data['volume'].iloc[-20:].mean()
            
            if avg_volume_20 == 0:
                return {'confirmed': False, 'reason': '成交量数据异常'}
            
            volume_ratio = recent_volume / avg_volume_20
            
            # 成交量确认标准
            if volume_ratio >= 2.0:
                confirmed = True
                strength = 100
                reason = f"放量突破({volume_ratio:.1f}倍)"
            elif volume_ratio >= self.min_volume_ratio:
                confirmed = True
                strength = 70
                reason = f"温和放量({volume_ratio:.1f}倍)"
            elif volume_ratio >= 0.8:
                confirmed = False
                strength = 40
                reason = f"成交量一般({volume_ratio:.1f}倍)"
            else:
                confirmed = False
                strength = 20
                reason = f"成交量萎缩({volume_ratio:.1f}倍)"
            
            return {
                'confirmed': confirmed,
                'volume_ratio': volume_ratio,
                'strength': strength,
                'reason': reason
            }
            
        except Exception as e:
            logger.error(f"❌ 成交量确认失败: {e}")
            return {'confirmed': False, 'reason': '成交量计算异常'}
    
    def _calculate_signal_strength(self,
                                 total_score: float,
                                 technical_score: float,
                                 technical_confirmation: Dict[str, Any],
                                 ma_alignment: Dict[str, Any],
                                 volume_confirmation: Dict[str, Any]) -> float:
        """计算信号强度"""
        try:
            # 基础评分权重
            base_strength = total_score * 0.4
            
            # 技术确认权重
            tech_strength = technical_confirmation.get('score', 0) * 0.3
            
            # 均线排列权重
            ma_strength = ma_alignment.get('strength', 0) * 0.2
            
            # 成交量确认权重
            volume_strength = volume_confirmation.get('strength', 0) * 0.1
            
            # 综合信号强度
            signal_strength = base_strength + tech_strength + ma_strength + volume_strength
            
            # 限制在0-100范围内
            return max(0, min(100, signal_strength))
            
        except Exception as e:
            logger.error(f"❌ 计算信号强度失败: {e}")
            return 0
    
    def _generate_signal_reasons(self,
                               total_score: float,
                               technical_score: float,
                               technical_confirmation: Dict[str, Any],
                               ma_alignment: Dict[str, Any],
                               volume_confirmation: Dict[str, Any]) -> List[str]:
        """生成信号原因"""
        reasons = []
        
        try:
            # 综合评分原因
            reasons.append(f"综合评分优秀: {total_score:.1f}分")
            reasons.append(f"技术面强势: {technical_score:.1f}分")
            
            # 技术指标原因
            tech_reasons = technical_confirmation.get('reasons', [])
            reasons.extend(tech_reasons[:2])  # 只取前2个
            
            # 均线排列原因
            ma_reason = ma_alignment.get('reason', '')
            if ma_reason:
                reasons.append(ma_reason)
            
            # 成交量原因
            volume_reason = volume_confirmation.get('reason', '')
            if volume_reason:
                reasons.append(volume_reason)
            
            return reasons
            
        except Exception as e:
            logger.error(f"❌ 生成信号原因失败: {e}")
            return ["买入信号生成"]
    
    def get_buy_strategy_status(self) -> Dict[str, Any]:
        """获取买入策略状态"""
        return {
            'strategy_name': '多因子买入策略',
            'min_total_score': self.min_total_score,
            'min_technical_score': self.min_technical_score,
            'min_volume_ratio': self.min_volume_ratio,
            'target_profit_pct': self.target_profit_pct,
            'stop_loss_pct': self.stop_loss_pct,
            'ma_periods': self.ma_periods,
            'rsi_thresholds': {
                'oversold': self.rsi_oversold,
                'overbought': self.rsi_overbought
            }
        }

    # ============================================================================
    # 产品设计要求的新增方法
    # ============================================================================

    def _check_technical_signal_combination(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        检查技术指标组合确认 - 按产品设计业务流程

        技术指标组合要求：
        1. MA多头排列 (5日>10日>20日>60日)
        2. MACD金叉 (MACD>Signal且DIF>0)
        3. RSI>30 (避免超卖区域)
        4. KDJ金叉 (K>D且K>20)

        Args:
            market_data: 市场数据

        Returns:
            技术信号确认结果
        """
        try:
            from analysis_engine.technical_analyzer.indicators import TechnicalIndicators

            # 计算技术指标
            indicators = TechnicalIndicators()
            all_indicators = indicators.calculate_all_indicators(market_data)

            result = {
                'valid': False,
                'score': 0,
                'details': {},
                'signals': []
            }

            score = 0
            max_score = 100

            # 1. MA多头排列检查 (25分)
            ma_signals = self._check_ma_bullish_alignment(market_data)
            if ma_signals['aligned']:
                score += 25
                result['signals'].append("MA多头排列")
            result['details']['ma_alignment'] = ma_signals

            # 2. MACD金叉检查 (25分)
            if 'macd' in all_indicators and 'signal' in all_indicators:
                macd_value = all_indicators['macd'].iloc[-1]
                signal_value = all_indicators['signal'].iloc[-1]

                if macd_value > signal_value and macd_value > 0:
                    score += 25
                    result['signals'].append("MACD金叉")

                result['details']['macd'] = {
                    'macd': macd_value,
                    'signal': signal_value,
                    'golden_cross': macd_value > signal_value and macd_value > 0
                }

            # 3. RSI检查 (25分)
            if 'rsi' in all_indicators:
                rsi_value = all_indicators['rsi'].iloc[-1]

                if 30 < rsi_value < 70:  # 避免超买超卖
                    score += 25
                    result['signals'].append("RSI适中")

                result['details']['rsi'] = {
                    'value': rsi_value,
                    'suitable': 30 < rsi_value < 70
                }

            # 4. KDJ金叉检查 (25分)
            if 'stoch_k' in all_indicators and 'stoch_d' in all_indicators:
                k_value = all_indicators['stoch_k'].iloc[-1]
                d_value = all_indicators['stoch_d'].iloc[-1]

                if k_value > d_value and k_value > 20:
                    score += 25
                    result['signals'].append("KDJ金叉")

                result['details']['kdj'] = {
                    'k': k_value,
                    'd': d_value,
                    'golden_cross': k_value > d_value and k_value > 20
                }

            result['score'] = score
            result['valid'] = score >= 75  # 至少3个指标确认

            logger.debug(f"技术指标组合评分: {score}/100, 确认信号: {result['signals']}")

            return result

        except Exception as e:
            logger.error(f"❌ 技术指标组合检查失败: {e}")
            return {'valid': False, 'score': 0, 'details': {}, 'signals': []}

    def _check_volume_amplification(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        检查成交量放大确认 - 产品设计要求

        成交量放大要求：
        1. 当日成交量 > 20日平均成交量 * 1.5
        2. 近3日平均成交量 > 20日平均成交量 * 1.2
        3. 成交量呈递增趋势

        Args:
            market_data: 市场数据

        Returns:
            成交量确认结果
        """
        try:
            result = {
                'valid': False,
                'score': 0,
                'details': {},
                'reasons': []
            }

            if len(market_data) < 20:
                return result

            volumes = market_data['volume'].astype(float)
            current_volume = volumes.iloc[-1]

            # 计算平均成交量
            avg_20d = volumes.rolling(20).mean().iloc[-1]
            avg_3d = volumes.tail(3).mean()

            score = 0

            # 1. 当日成交量放大检查 (40分)
            volume_ratio = current_volume / avg_20d if avg_20d > 0 else 0
            if volume_ratio >= 1.5:
                score += 40
                result['reasons'].append(f"当日成交量放大{volume_ratio:.1f}倍")

            # 2. 近3日平均成交量检查 (30分)
            avg_3d_ratio = avg_3d / avg_20d if avg_20d > 0 else 0
            if avg_3d_ratio >= 1.2:
                score += 30
                result['reasons'].append(f"近3日成交量放大{avg_3d_ratio:.1f}倍")

            # 3. 成交量趋势检查 (30分)
            recent_volumes = volumes.tail(5).tolist()
            if len(recent_volumes) >= 3:
                increasing_trend = all(
                    recent_volumes[i] <= recent_volumes[i+1]
                    for i in range(len(recent_volumes)-2)
                )
                if increasing_trend:
                    score += 30
                    result['reasons'].append("成交量呈递增趋势")

            result['score'] = score
            result['valid'] = score >= 70  # 至少2个条件满足
            result['details'] = {
                'current_volume': current_volume,
                'avg_20d': avg_20d,
                'avg_3d': avg_3d,
                'volume_ratio': volume_ratio,
                'avg_3d_ratio': avg_3d_ratio
            }

            logger.debug(f"成交量放大评分: {score}/100, 原因: {result['reasons']}")

            return result

        except Exception as e:
            logger.error(f"❌ 成交量放大检查失败: {e}")
            return {'valid': False, 'score': 0, 'details': {}, 'reasons': []}

    def _calculate_comprehensive_buy_score(self,
                                         factor_score: float,
                                         technical_signals: Dict[str, Any],
                                         volume_confirmation: Dict[str, Any],
                                         market_data: pd.DataFrame) -> float:
        """
        计算综合买入评分 - 产品设计评分体系

        评分权重：
        - 因子评分: 40% (基础分数)
        - 技术指标: 35% (技术面确认)
        - 成交量: 15% (资金确认)
        - 市场环境: 10% (环境加分)

        Args:
            factor_score: 因子评分
            technical_signals: 技术信号
            volume_confirmation: 成交量确认
            market_data: 市场数据

        Returns:
            综合评分 (0-100)
        """
        try:
            # 基础权重配置
            weights = {
                'factor': 0.40,      # 因子评分权重
                'technical': 0.35,   # 技术指标权重
                'volume': 0.15,      # 成交量权重
                'market': 0.10       # 市场环境权重
            }

            # 1. 因子评分 (0-100分)
            factor_component = factor_score * weights['factor']

            # 2. 技术指标评分 (0-100分)
            technical_component = technical_signals.get('score', 0) * weights['technical']

            # 3. 成交量评分 (0-100分)
            volume_component = volume_confirmation.get('score', 0) * weights['volume']

            # 4. 市场环境评分 (0-100分)
            market_score = self._assess_market_environment_score(market_data)
            market_component = market_score * weights['market']

            # 综合评分
            comprehensive_score = (
                factor_component +
                technical_component +
                volume_component +
                market_component
            )

            logger.debug(f"综合评分计算: 因子{factor_component:.1f} + 技术{technical_component:.1f} + 成交量{volume_component:.1f} + 市场{market_component:.1f} = {comprehensive_score:.1f}")

            return min(comprehensive_score, 100)  # 限制最高100分

        except Exception as e:
            logger.error(f"❌ 计算综合买入评分失败: {e}")
            return 0

    def _check_ma_bullish_alignment(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """检查均线多头排列"""
        try:
            close_prices = market_data['close_price']

            # 计算各周期均线
            ma5 = close_prices.rolling(5).mean().iloc[-1]
            ma10 = close_prices.rolling(10).mean().iloc[-1]
            ma20 = close_prices.rolling(20).mean().iloc[-1]
            ma60 = close_prices.rolling(60).mean().iloc[-1] if len(close_prices) >= 60 else ma20

            # 检查多头排列: 短期均线 > 长期均线
            aligned = ma5 > ma10 > ma20 > ma60

            return {
                'aligned': aligned,
                'ma5': ma5,
                'ma10': ma10,
                'ma20': ma20,
                'ma60': ma60
            }

        except Exception as e:
            logger.error(f"❌ 均线多头排列检查失败: {e}")
            return {'aligned': False}

    def _assess_market_environment_score(self, market_data: pd.DataFrame) -> float:
        """评估市场环境评分"""
        try:
            score = 50  # 基础分数

            # 1. 价格趋势评分 (±20分)
            close_prices = market_data['close_price']
            if len(close_prices) >= 10:
                recent_trend = (close_prices.iloc[-1] - close_prices.iloc[-10]) / close_prices.iloc[-10]
                if recent_trend > 0.05:  # 上涨超过5%
                    score += 20
                elif recent_trend < -0.05:  # 下跌超过5%
                    score -= 20

            # 2. 波动率评分 (±15分)
            if len(close_prices) >= 20:
                returns = close_prices.pct_change().dropna()
                volatility = returns.std()

                if 0.01 < volatility < 0.03:  # 适中波动率
                    score += 15
                elif volatility > 0.05:  # 高波动率
                    score -= 15

            # 3. 相对强度评分 (±15分)
            # 简化处理：基于价格相对位置
            if len(close_prices) >= 20:
                high_20 = market_data['high_price'].rolling(20).max().iloc[-1]
                low_20 = market_data['low_price'].rolling(20).min().iloc[-1]
                current_price = close_prices.iloc[-1]

                if high_20 > low_20:
                    relative_position = (current_price - low_20) / (high_20 - low_20)
                    if relative_position > 0.7:  # 接近高点
                        score += 15
                    elif relative_position < 0.3:  # 接近低点
                        score -= 15

            return max(0, min(100, score))

        except Exception as e:
            logger.error(f"❌ 市场环境评分失败: {e}")
            return 50
