"""
信号处理器
整合买入、卖出信号，协调风险管理和持仓管理
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass

from strategy_layer.trading_strategies.buy_strategy import BuyStrategy, BuySignal
from strategy_layer.trading_strategies.sell_strategy import SellStrategy, SellSignal
from strategy_layer.risk_management.risk_manager import RiskManager, RiskLevel
from strategy_layer.position_management.position_manager import PositionManager, PositionAdjustment

logger = logging.getLogger(__name__)

@dataclass
class TradingDecision:
    """交易决策"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    shares: int
    price: float
    decision_time: datetime
    confidence: float
    reasons: List[str]
    risk_approved: bool
    priority: int

class SignalProcessor:
    """信号处理器"""
    
    def __init__(self):
        # 初始化各个组件
        self.buy_strategy = BuyStrategy()
        self.sell_strategy = SellStrategy()
        self.risk_manager = RiskManager()
        self.position_manager = PositionManager()
        
        # 信号处理参数
        self.min_buy_confidence = 0.7       # 最小买入置信度
        self.min_sell_confidence = 0.6      # 最小卖出置信度
        self.max_daily_trades = 10          # 每日最大交易数量
        self.signal_cooldown_hours = 2      # 信号冷却时间（小时）
        
        # 处理状态
        self.daily_trade_count = 0
        self.last_signals: Dict[str, datetime] = {}
        self.pending_decisions: List[TradingDecision] = []
        
        logger.info("🎯 信号处理器初始化完成")
        logger.info(f"  - 最小买入置信度: {self.min_buy_confidence}")
        logger.info(f"  - 最小卖出置信度: {self.min_sell_confidence}")
        logger.info(f"  - 每日最大交易: {self.max_daily_trades}")
    
    def process_trading_signals(self,
                              candidate_symbols: List[str],
                              current_prices: Dict[str, float]) -> List[TradingDecision]:
        """
        处理交易信号
        
        Args:
            candidate_symbols: 候选股票列表
            current_prices: 当前价格字典
            
        Returns:
            交易决策列表
        """
        try:
            logger.info(f"🎯 开始处理交易信号: {len(candidate_symbols)}只候选股票")
            
            trading_decisions = []
            
            # 1. 生成卖出信号（优先处理）
            sell_decisions = self._process_sell_signals(current_prices)
            trading_decisions.extend(sell_decisions)
            
            # 2. 生成买入信号
            buy_decisions = self._process_buy_signals(candidate_symbols, current_prices)
            trading_decisions.extend(buy_decisions)
            
            # 3. 风险检查和优先级排序
            approved_decisions = self._apply_risk_management(trading_decisions, current_prices)
            
            # 4. 限制每日交易数量
            final_decisions = self._limit_daily_trades(approved_decisions)
            
            logger.info(f"✅ 信号处理完成: {len(final_decisions)}个交易决策")
            
            return final_decisions
            
        except Exception as e:
            logger.error(f"❌ 处理交易信号失败: {e}")
            return []
    
    def _process_sell_signals(self, current_prices: Dict[str, float]) -> List[TradingDecision]:
        """处理卖出信号"""
        try:
            # 获取当前持仓
            portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)
            current_positions = []
            
            for symbol, pos_info in portfolio_summary.get('positions', {}).items():
                if pos_info['shares'] > 0:
                    current_positions.append({
                        'symbol': symbol,
                        'shares': pos_info['shares'],
                        'entry_price': pos_info['avg_price'],
                        'current_price': pos_info['current_price'],
                        'market_value': pos_info['market_value']
                    })
            
            if not current_positions:
                return []
            
            # 生成卖出信号
            sell_signals = self.sell_strategy.generate_sell_signals(current_positions)
            
            sell_decisions = []
            
            for signal in sell_signals:
                # 检查信号冷却
                if self._is_signal_in_cooldown(signal.symbol):
                    continue
                
                # 计算置信度
                confidence = self._calculate_sell_confidence(signal)
                
                if confidence < self.min_sell_confidence:
                    continue
                
                # 确定卖出数量
                position_info = next((pos for pos in current_positions if pos['symbol'] == signal.symbol), None)
                if not position_info:
                    continue
                
                sell_shares = self._determine_sell_quantity(signal, position_info)
                
                decision = TradingDecision(
                    symbol=signal.symbol,
                    action='sell',
                    shares=sell_shares,
                    price=signal.current_price,
                    decision_time=datetime.now(),
                    confidence=confidence,
                    reasons=signal.signal_reasons,
                    risk_approved=False,  # 待风险检查
                    priority=self._get_sell_priority(signal)
                )
                
                sell_decisions.append(decision)
            
            logger.info(f"📉 生成卖出决策: {len(sell_decisions)}个")
            
            return sell_decisions
            
        except Exception as e:
            logger.error(f"❌ 处理卖出信号失败: {e}")
            return []
    
    def _process_buy_signals(self, candidate_symbols: List[str], current_prices: Dict[str, float]) -> List[TradingDecision]:
        """处理买入信号"""
        try:
            # 生成买入信号
            buy_signals = self.buy_strategy.generate_buy_signals(candidate_symbols)
            
            buy_decisions = []
            
            for signal in buy_signals:
                # 检查信号冷却
                if self._is_signal_in_cooldown(signal.symbol):
                    continue
                
                # 计算置信度
                confidence = self._calculate_buy_confidence(signal)
                
                if confidence < self.min_buy_confidence:
                    continue
                
                # 确定买入数量（初步计算，后续会经过风险管理调整）
                buy_shares = self._determine_buy_quantity(signal, current_prices)
                
                decision = TradingDecision(
                    symbol=signal.symbol,
                    action='buy',
                    shares=buy_shares,
                    price=signal.trigger_price,
                    decision_time=datetime.now(),
                    confidence=confidence,
                    reasons=signal.signal_reasons,
                    risk_approved=False,  # 待风险检查
                    priority=self._get_buy_priority(signal)
                )
                
                buy_decisions.append(decision)
            
            logger.info(f"📈 生成买入决策: {len(buy_decisions)}个")
            
            return buy_decisions
            
        except Exception as e:
            logger.error(f"❌ 处理买入信号失败: {e}")
            return []
    
    def _apply_risk_management(self, decisions: List[TradingDecision], current_prices: Dict[str, float]) -> List[TradingDecision]:
        """应用风险管理"""
        try:
            approved_decisions = []
            
            # 获取当前组合状态
            portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)
            current_positions = {
                symbol: {
                    'market_value': pos_info['market_value'],
                    'shares': pos_info['shares']
                }
                for symbol, pos_info in portfolio_summary.get('positions', {}).items()
            }
            
            total_portfolio_value = portfolio_summary.get('total_portfolio_value', 0)
            
            for decision in decisions:
                if decision.action == 'sell':
                    # 卖出决策一般都会通过风险检查
                    decision.risk_approved = True
                    approved_decisions.append(decision)
                    
                elif decision.action == 'buy':
                    # 买入决策需要风险检查
                    target_value = decision.shares * decision.price
                    
                    risk_result = self.risk_manager.check_position_risk(
                        symbol=decision.symbol,
                        target_position_value=target_value,
                        total_portfolio_value=total_portfolio_value,
                        current_positions=current_positions
                    )
                    
                    if risk_result['approved']:
                        # 调整买入数量
                        adjusted_value = risk_result['adjusted_value']
                        adjusted_shares = int(adjusted_value / decision.price / 100) * 100
                        
                        if adjusted_shares >= 100:  # 至少1手
                            decision.shares = adjusted_shares
                            decision.risk_approved = True
                            
                            # 添加风险调整原因
                            if adjusted_shares != int(target_value / decision.price / 100) * 100:
                                decision.reasons.append(f"风险管理调整仓位至{adjusted_shares}股")
                            
                            approved_decisions.append(decision)
                        else:
                            logger.debug(f"📉 买入数量不足1手: {decision.symbol}")
                    else:
                        logger.debug(f"📉 风险检查未通过: {decision.symbol}")
            
            logger.info(f"⚠️ 风险检查完成: {len(approved_decisions)}/{len(decisions)} 通过")
            
            return approved_decisions
            
        except Exception as e:
            logger.error(f"❌ 应用风险管理失败: {e}")
            return decisions
    
    def _limit_daily_trades(self, decisions: List[TradingDecision]) -> List[TradingDecision]:
        """限制每日交易数量"""
        try:
            # 按优先级排序
            decisions.sort(key=lambda x: x.priority, reverse=True)
            
            # 限制交易数量
            remaining_trades = self.max_daily_trades - self.daily_trade_count
            final_decisions = decisions[:remaining_trades]
            
            if len(final_decisions) < len(decisions):
                logger.info(f"📊 限制每日交易: {len(final_decisions)}/{len(decisions)} 个决策")
            
            return final_decisions
            
        except Exception as e:
            logger.error(f"❌ 限制每日交易失败: {e}")
            return decisions
    
    def _is_signal_in_cooldown(self, symbol: str) -> bool:
        """检查信号是否在冷却期"""
        try:
            if symbol not in self.last_signals:
                return False
            
            last_signal_time = self.last_signals[symbol]
            cooldown_end = last_signal_time + timedelta(hours=self.signal_cooldown_hours)
            
            return datetime.now() < cooldown_end
            
        except Exception as e:
            logger.error(f"❌ 检查信号冷却失败: {symbol} - {e}")
            return False
    
    def _calculate_buy_confidence(self, signal: BuySignal) -> float:
        """计算买入置信度"""
        try:
            # 基础置信度基于信号强度
            base_confidence = signal.signal_strength / 100.0
            
            # 技术面确认加成
            if signal.technical_score >= 80:
                base_confidence += 0.1
            elif signal.technical_score >= 70:
                base_confidence += 0.05
            
            # 成交量确认加成
            if signal.volume_confirmed:
                base_confidence += 0.1
            
            # 均线排列加成
            if signal.ma_alignment:
                base_confidence += 0.1
            
            return min(1.0, base_confidence)
            
        except Exception as e:
            logger.error(f"❌ 计算买入置信度失败: {e}")
            return 0.5
    
    def _calculate_sell_confidence(self, signal: SellSignal) -> float:
        """计算卖出置信度"""
        try:
            # 基础置信度基于信号强度
            base_confidence = signal.signal_strength / 100.0
            
            # 紧急程度加成
            urgency_bonus = {
                'urgent': 0.2,
                'high': 0.15,
                'medium': 0.1,
                'low': 0.05
            }
            
            base_confidence += urgency_bonus.get(signal.urgency, 0)
            
            # 止损信号高置信度
            if signal.signal_type in ['stop_loss', 'trailing_stop']:
                base_confidence += 0.2
            
            return min(1.0, base_confidence)
            
        except Exception as e:
            logger.error(f"❌ 计算卖出置信度失败: {e}")
            return 0.5
    
    def _determine_buy_quantity(self, signal: BuySignal, current_prices: Dict[str, float]) -> int:
        """确定买入数量"""
        try:
            # 获取组合状态
            portfolio_summary = self.position_manager.get_portfolio_summary(current_prices)
            total_value = portfolio_summary.get('total_portfolio_value', 0)
            
            # 基于信号强度确定仓位比例
            if signal.signal_strength >= 90:
                position_pct = 0.08  # 8%
            elif signal.signal_strength >= 80:
                position_pct = 0.06  # 6%
            elif signal.signal_strength >= 70:
                position_pct = 0.04  # 4%
            else:
                position_pct = 0.02  # 2%
            
            target_value = total_value * position_pct
            shares = int(target_value / signal.trigger_price / 100) * 100
            
            return max(100, shares)  # 至少1手
            
        except Exception as e:
            logger.error(f"❌ 确定买入数量失败: {e}")
            return 100
    
    def _determine_sell_quantity(self, signal: SellSignal, position_info: Dict[str, Any]) -> int:
        """确定卖出数量"""
        try:
            current_shares = position_info['shares']
            
            # 根据信号类型确定卖出比例
            if signal.signal_type in ['stop_loss', 'trailing_stop']:
                # 止损全部卖出
                return current_shares
            elif signal.signal_type == 'take_profit':
                # 止盈全部卖出
                return current_shares
            elif signal.signal_type == 'partial_take_profit':
                # 部分止盈，卖出50%
                return int(current_shares * 0.5 / 100) * 100
            elif signal.signal_type in ['technical_weak', 'trend_reverse']:
                # 技术转弱，根据紧急程度确定
                if signal.urgency == 'urgent':
                    return current_shares
                elif signal.urgency == 'high':
                    return int(current_shares * 0.8 / 100) * 100
                else:
                    return int(current_shares * 0.5 / 100) * 100
            else:
                return int(current_shares * 0.3 / 100) * 100
                
        except Exception as e:
            logger.error(f"❌ 确定卖出数量失败: {e}")
            return 100
    
    def _get_buy_priority(self, signal: BuySignal) -> int:
        """获取买入优先级"""
        try:
            # 基于信号强度确定优先级
            if signal.signal_strength >= 90:
                return 5  # 最高优先级
            elif signal.signal_strength >= 80:
                return 4
            elif signal.signal_strength >= 70:
                return 3
            else:
                return 2
                
        except Exception as e:
            logger.error(f"❌ 获取买入优先级失败: {e}")
            return 1
    
    def _get_sell_priority(self, signal: SellSignal) -> int:
        """获取卖出优先级"""
        try:
            # 卖出信号优先级更高
            urgency_priority = {
                'urgent': 10,
                'high': 8,
                'medium': 6,
                'low': 4
            }
            
            base_priority = urgency_priority.get(signal.urgency, 4)
            
            # 止损信号最高优先级
            if signal.signal_type in ['stop_loss', 'trailing_stop']:
                base_priority += 2
            
            return base_priority
            
        except Exception as e:
            logger.error(f"❌ 获取卖出优先级失败: {e}")
            return 5
    
    def execute_trading_decision(self, decision: TradingDecision) -> bool:
        """
        执行交易决策
        
        Args:
            decision: 交易决策
            
        Returns:
            是否执行成功
        """
        try:
            logger.info(f"🎯 执行交易决策: {decision.symbol} {decision.action} {decision.shares}股")
            
            # 更新持仓记录
            success = self.position_manager.update_position(
                symbol=decision.symbol,
                action=decision.action,
                shares=decision.shares,
                price=decision.price,
                commission=decision.shares * decision.price * 0.0003  # 0.03%手续费
            )
            
            if success:
                # 更新信号记录
                self.last_signals[decision.symbol] = decision.decision_time
                self.daily_trade_count += 1
                
                logger.info(f"✅ 交易执行成功: {decision.symbol}")
                return True
            else:
                logger.error(f"❌ 交易执行失败: {decision.symbol}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 执行交易决策失败: {decision.symbol} - {e}")
            return False
    
    def get_signal_processor_status(self) -> Dict[str, Any]:
        """获取信号处理器状态"""
        return {
            'daily_trade_count': self.daily_trade_count,
            'max_daily_trades': self.max_daily_trades,
            'remaining_trades': self.max_daily_trades - self.daily_trade_count,
            'active_cooldowns': len(self.last_signals),
            'pending_decisions': len(self.pending_decisions),
            'parameters': {
                'min_buy_confidence': self.min_buy_confidence,
                'min_sell_confidence': self.min_sell_confidence,
                'signal_cooldown_hours': self.signal_cooldown_hours
            }
        }
