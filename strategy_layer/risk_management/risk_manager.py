"""
风险管理器
实现基于产品设计的风险控制：
- 单股≤10%
- 总仓位≤80%
- 最大回撤≤15%
- 动态风险监控
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, DailyMarket

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class RiskAlert:
    """风险预警"""
    alert_type: str
    risk_level: RiskLevel
    symbol: Optional[str]
    message: str
    current_value: float
    threshold_value: float
    suggestion: str
    alert_time: datetime

class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        # 风险控制参数
        self.max_single_position_pct = 0.10      # 单股最大仓位10%
        self.max_total_position_pct = 0.80       # 总仓位最大80%
        self.max_drawdown_pct = 0.15             # 最大回撤15%
        self.max_sector_concentration = 0.30     # 单行业最大30%
        self.min_cash_reserve_pct = 0.20         # 最小现金储备20%
        
        # 风险监控参数
        self.volatility_threshold = 0.30         # 波动率阈值30%
        self.correlation_threshold = 0.70        # 相关性阈值70%
        self.var_confidence = 0.95               # VaR置信度95%
        self.stress_test_scenarios = 3           # 压力测试场景数
        
        # 风险状态
        self.current_alerts: List[RiskAlert] = []
        self.risk_metrics: Dict[str, float] = {}
        
        logger.info("⚠️ 风险管理器初始化完成")
        logger.info(f"  - 单股最大仓位: {self.max_single_position_pct:.1%}")
        logger.info(f"  - 总仓位上限: {self.max_total_position_pct:.1%}")
        logger.info(f"  - 最大回撤限制: {self.max_drawdown_pct:.1%}")
    
    def check_position_risk(self,
                           symbol: str,
                           target_position_value: float,
                           total_portfolio_value: float,
                           current_positions: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        检查仓位风险
        
        Args:
            symbol: 股票代码
            target_position_value: 目标仓位价值
            total_portfolio_value: 总组合价值
            current_positions: 当前持仓
            
        Returns:
            风险检查结果
        """
        try:
            logger.debug(f"⚠️ 检查仓位风险: {symbol}")
            
            risk_result = {
                'approved': True,
                'adjusted_value': target_position_value,
                'risk_alerts': [],
                'risk_level': RiskLevel.LOW
            }
            
            # 1. 单股仓位检查
            single_position_pct = target_position_value / total_portfolio_value
            max_allowed_single = self.max_single_position_pct
            
            if single_position_pct > max_allowed_single:
                # 调整到最大允许仓位
                adjusted_value = total_portfolio_value * max_allowed_single
                risk_result['adjusted_value'] = adjusted_value
                
                alert = RiskAlert(
                    alert_type='single_position_limit',
                    risk_level=RiskLevel.MEDIUM,
                    symbol=symbol,
                    message=f"单股仓位超限: {single_position_pct:.2%} > {max_allowed_single:.2%}",
                    current_value=single_position_pct,
                    threshold_value=max_allowed_single,
                    suggestion=f"调整仓位至{adjusted_value:,.0f}元",
                    alert_time=datetime.now()
                )
                risk_result['risk_alerts'].append(alert)
                risk_result['risk_level'] = RiskLevel.MEDIUM
            
            # 2. 总仓位检查
            current_total_position = sum(pos['market_value'] for pos in current_positions.values())
            new_total_position = current_total_position + risk_result['adjusted_value']
            total_position_pct = new_total_position / total_portfolio_value
            
            if total_position_pct > self.max_total_position_pct:
                # 计算可用资金
                available_funds = total_portfolio_value * self.max_total_position_pct - current_total_position
                
                if available_funds <= 0:
                    risk_result['approved'] = False
                    risk_result['adjusted_value'] = 0
                    
                    alert = RiskAlert(
                        alert_type='total_position_limit',
                        risk_level=RiskLevel.HIGH,
                        symbol=symbol,
                        message=f"总仓位已满: {total_position_pct:.2%} > {self.max_total_position_pct:.2%}",
                        current_value=total_position_pct,
                        threshold_value=self.max_total_position_pct,
                        suggestion="暂停新增仓位，考虑减仓",
                        alert_time=datetime.now()
                    )
                    risk_result['risk_alerts'].append(alert)
                    risk_result['risk_level'] = RiskLevel.HIGH
                else:
                    risk_result['adjusted_value'] = min(risk_result['adjusted_value'], available_funds)
                    
                    alert = RiskAlert(
                        alert_type='total_position_warning',
                        risk_level=RiskLevel.MEDIUM,
                        symbol=symbol,
                        message=f"总仓位接近上限，调整仓位至{available_funds:,.0f}元",
                        current_value=total_position_pct,
                        threshold_value=self.max_total_position_pct,
                        suggestion="控制仓位规模",
                        alert_time=datetime.now()
                    )
                    risk_result['risk_alerts'].append(alert)
            
            # 3. 行业集中度检查
            sector_risk = self._check_sector_concentration(symbol, risk_result['adjusted_value'], 
                                                         total_portfolio_value, current_positions)
            if sector_risk['alerts']:
                risk_result['risk_alerts'].extend(sector_risk['alerts'])
                if sector_risk['risk_level'].value > risk_result['risk_level'].value:
                    risk_result['risk_level'] = sector_risk['risk_level']
            
            return risk_result
            
        except Exception as e:
            logger.error(f"❌ 检查仓位风险失败: {symbol} - {e}")
            return {
                'approved': False,
                'adjusted_value': 0,
                'risk_alerts': [],
                'risk_level': RiskLevel.CRITICAL
            }
    
    def monitor_portfolio_risk(self,
                              current_positions: Dict[str, Dict[str, Any]],
                              portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        监控组合风险
        
        Args:
            current_positions: 当前持仓
            portfolio_history: 组合历史数据
            
        Returns:
            风险监控结果
        """
        try:
            logger.info("⚠️ 开始组合风险监控...")
            
            risk_monitoring = {
                'overall_risk_level': RiskLevel.LOW,
                'risk_alerts': [],
                'risk_metrics': {},
                'recommendations': []
            }
            
            # 1. 回撤风险监控
            drawdown_risk = self._monitor_drawdown_risk(portfolio_history)
            risk_monitoring['risk_metrics']['drawdown'] = drawdown_risk
            
            if drawdown_risk['current_drawdown'] > self.max_drawdown_pct:
                alert = RiskAlert(
                    alert_type='max_drawdown_exceeded',
                    risk_level=RiskLevel.CRITICAL,
                    symbol=None,
                    message=f"最大回撤超限: {drawdown_risk['current_drawdown']:.2%}",
                    current_value=drawdown_risk['current_drawdown'],
                    threshold_value=self.max_drawdown_pct,
                    suggestion="立即减仓，控制风险",
                    alert_time=datetime.now()
                )
                risk_monitoring['risk_alerts'].append(alert)
                risk_monitoring['overall_risk_level'] = RiskLevel.CRITICAL
            
            # 2. 波动率风险监控
            volatility_risk = self._monitor_volatility_risk(portfolio_history)
            risk_monitoring['risk_metrics']['volatility'] = volatility_risk
            
            if volatility_risk['current_volatility'] > self.volatility_threshold:
                alert = RiskAlert(
                    alert_type='high_volatility',
                    risk_level=RiskLevel.HIGH,
                    symbol=None,
                    message=f"组合波动率过高: {volatility_risk['current_volatility']:.2%}",
                    current_value=volatility_risk['current_volatility'],
                    threshold_value=self.volatility_threshold,
                    suggestion="降低仓位，分散投资",
                    alert_time=datetime.now()
                )
                risk_monitoring['risk_alerts'].append(alert)
                if risk_monitoring['overall_risk_level'].value < RiskLevel.HIGH.value:
                    risk_monitoring['overall_risk_level'] = RiskLevel.HIGH
            
            # 3. 集中度风险监控
            concentration_risk = self._monitor_concentration_risk(current_positions)
            risk_monitoring['risk_metrics']['concentration'] = concentration_risk
            
            if concentration_risk['alerts']:
                risk_monitoring['risk_alerts'].extend(concentration_risk['alerts'])
            
            # 4. VaR风险监控
            var_risk = self._calculate_var_risk(current_positions, portfolio_history)
            risk_monitoring['risk_metrics']['var'] = var_risk
            
            # 5. 生成风险建议
            recommendations = self._generate_risk_recommendations(risk_monitoring)
            risk_monitoring['recommendations'] = recommendations
            
            # 更新当前风险预警
            self.current_alerts = risk_monitoring['risk_alerts']
            self.risk_metrics = risk_monitoring['risk_metrics']
            
            logger.info(f"✅ 组合风险监控完成: {len(risk_monitoring['risk_alerts'])}个预警")
            
            return risk_monitoring
            
        except Exception as e:
            logger.error(f"❌ 组合风险监控失败: {e}")
            return {
                'overall_risk_level': RiskLevel.CRITICAL,
                'risk_alerts': [],
                'risk_metrics': {},
                'recommendations': ['系统异常，建议人工检查']
            }
    
    def _check_sector_concentration(self,
                                   symbol: str,
                                   position_value: float,
                                   total_portfolio_value: float,
                                   current_positions: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """检查行业集中度风险"""
        try:
            # 简化处理：假设从股票代码判断行业
            # 实际应该从数据库获取行业信息
            sector = self._get_stock_sector(symbol)
            
            # 计算当前行业仓位
            current_sector_value = 0
            for pos_symbol, pos_info in current_positions.items():
                if self._get_stock_sector(pos_symbol) == sector:
                    current_sector_value += pos_info.get('market_value', 0)
            
            new_sector_value = current_sector_value + position_value
            sector_concentration = new_sector_value / total_portfolio_value
            
            alerts = []
            risk_level = RiskLevel.LOW
            
            if sector_concentration > self.max_sector_concentration:
                alert = RiskAlert(
                    alert_type='sector_concentration',
                    risk_level=RiskLevel.MEDIUM,
                    symbol=symbol,
                    message=f"{sector}行业集中度过高: {sector_concentration:.2%}",
                    current_value=sector_concentration,
                    threshold_value=self.max_sector_concentration,
                    suggestion="分散行业配置",
                    alert_time=datetime.now()
                )
                alerts.append(alert)
                risk_level = RiskLevel.MEDIUM
            
            return {
                'alerts': alerts,
                'risk_level': risk_level,
                'sector_concentration': sector_concentration
            }
            
        except Exception as e:
            logger.error(f"❌ 检查行业集中度失败: {e}")
            return {'alerts': [], 'risk_level': RiskLevel.LOW}
    
    def _get_stock_sector(self, symbol: str) -> str:
        """获取股票行业（简化处理）"""
        # 简化处理：根据股票代码前缀判断
        if symbol.startswith('000'):
            return '主板'
        elif symbol.startswith('002'):
            return '中小板'
        elif symbol.startswith('300'):
            return '创业板'
        elif symbol.startswith('600') or symbol.startswith('601'):
            return '沪市主板'
        else:
            return '其他'
    
    def _monitor_drawdown_risk(self, portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """监控回撤风险"""
        try:
            if not portfolio_history:
                return {'current_drawdown': 0, 'max_drawdown': 0}
            
            # 提取净值序列
            nav_series = [record.get('nav', 1.0) for record in portfolio_history]
            
            # 计算回撤
            peak = nav_series[0]
            max_drawdown = 0
            current_drawdown = 0
            
            for nav in nav_series:
                if nav > peak:
                    peak = nav
                    current_drawdown = 0
                else:
                    current_drawdown = (peak - nav) / peak
                    max_drawdown = max(max_drawdown, current_drawdown)
            
            return {
                'current_drawdown': current_drawdown,
                'max_drawdown': max_drawdown,
                'peak_nav': peak,
                'current_nav': nav_series[-1] if nav_series else 1.0
            }
            
        except Exception as e:
            logger.error(f"❌ 监控回撤风险失败: {e}")
            return {'current_drawdown': 0, 'max_drawdown': 0}
    
    def _monitor_volatility_risk(self, portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """监控波动率风险"""
        try:
            if len(portfolio_history) < 20:
                return {'current_volatility': 0, 'volatility_trend': 'stable'}
            
            # 计算日收益率
            nav_series = [record.get('nav', 1.0) for record in portfolio_history[-30:]]
            daily_returns = []
            
            for i in range(1, len(nav_series)):
                daily_return = (nav_series[i] - nav_series[i-1]) / nav_series[i-1]
                daily_returns.append(daily_return)
            
            # 计算年化波动率
            if daily_returns:
                volatility = np.std(daily_returns) * np.sqrt(252)
                
                # 判断波动率趋势
                if len(daily_returns) >= 20:
                    recent_vol = np.std(daily_returns[-10:]) * np.sqrt(252)
                    historical_vol = np.std(daily_returns[-20:-10]) * np.sqrt(252)
                    
                    if recent_vol > historical_vol * 1.2:
                        trend = 'increasing'
                    elif recent_vol < historical_vol * 0.8:
                        trend = 'decreasing'
                    else:
                        trend = 'stable'
                else:
                    trend = 'stable'
            else:
                volatility = 0
                trend = 'stable'
            
            return {
                'current_volatility': volatility,
                'volatility_trend': trend,
                'daily_returns': daily_returns[-10:]  # 最近10天收益率
            }
            
        except Exception as e:
            logger.error(f"❌ 监控波动率风险失败: {e}")
            return {'current_volatility': 0, 'volatility_trend': 'stable'}
    
    def _monitor_concentration_risk(self, current_positions: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """监控集中度风险"""
        try:
            if not current_positions:
                return {'alerts': [], 'concentration_metrics': {}}
            
            # 计算总市值
            total_value = sum(pos['market_value'] for pos in current_positions.values())
            
            if total_value == 0:
                return {'alerts': [], 'concentration_metrics': {}}
            
            # 计算各持仓权重
            position_weights = []
            for symbol, pos_info in current_positions.items():
                weight = pos_info['market_value'] / total_value
                position_weights.append((symbol, weight))
            
            # 按权重排序
            position_weights.sort(key=lambda x: x[1], reverse=True)
            
            alerts = []
            
            # 检查前几大持仓集中度
            top3_concentration = sum(weight for _, weight in position_weights[:3])
            top5_concentration = sum(weight for _, weight in position_weights[:5])
            
            if top3_concentration > 0.6:  # 前3大持仓超过60%
                alert = RiskAlert(
                    alert_type='top_concentration',
                    risk_level=RiskLevel.MEDIUM,
                    symbol=None,
                    message=f"前3大持仓集中度过高: {top3_concentration:.2%}",
                    current_value=top3_concentration,
                    threshold_value=0.6,
                    suggestion="分散持仓，降低集中度",
                    alert_time=datetime.now()
                )
                alerts.append(alert)
            
            return {
                'alerts': alerts,
                'concentration_metrics': {
                    'top3_concentration': top3_concentration,
                    'top5_concentration': top5_concentration,
                    'position_count': len(current_positions),
                    'largest_position': position_weights[0] if position_weights else ('', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 监控集中度风险失败: {e}")
            return {'alerts': [], 'concentration_metrics': {}}
    
    def _calculate_var_risk(self,
                           current_positions: Dict[str, Dict[str, Any]],
                           portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算VaR风险"""
        try:
            if len(portfolio_history) < 30:
                return {'var_95': 0, 'var_99': 0, 'expected_shortfall': 0}
            
            # 计算日收益率
            nav_series = [record.get('nav', 1.0) for record in portfolio_history[-60:]]
            daily_returns = []
            
            for i in range(1, len(nav_series)):
                daily_return = (nav_series[i] - nav_series[i-1]) / nav_series[i-1]
                daily_returns.append(daily_return)
            
            if not daily_returns:
                return {'var_95': 0, 'var_99': 0, 'expected_shortfall': 0}
            
            # 计算VaR
            var_95 = np.percentile(daily_returns, 5)  # 95%置信度
            var_99 = np.percentile(daily_returns, 1)  # 99%置信度
            
            # 计算期望损失（CVaR）
            tail_returns = [r for r in daily_returns if r <= var_95]
            expected_shortfall = np.mean(tail_returns) if tail_returns else 0
            
            return {
                'var_95': var_95,
                'var_99': var_99,
                'expected_shortfall': expected_shortfall,
                'return_distribution': {
                    'mean': np.mean(daily_returns),
                    'std': np.std(daily_returns),
                    'skewness': self._calculate_skewness(daily_returns),
                    'kurtosis': self._calculate_kurtosis(daily_returns)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 计算VaR风险失败: {e}")
            return {'var_95': 0, 'var_99': 0, 'expected_shortfall': 0}
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        try:
            if len(data) < 3:
                return 0
            
            mean = np.mean(data)
            std = np.std(data)
            
            if std == 0:
                return 0
            
            skewness = np.mean([((x - mean) / std) ** 3 for x in data])
            return skewness
            
        except:
            return 0
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        try:
            if len(data) < 4:
                return 0
            
            mean = np.mean(data)
            std = np.std(data)
            
            if std == 0:
                return 0
            
            kurtosis = np.mean([((x - mean) / std) ** 4 for x in data]) - 3
            return kurtosis
            
        except:
            return 0
    
    def _generate_risk_recommendations(self, risk_monitoring: Dict[str, Any]) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        try:
            risk_level = risk_monitoring['overall_risk_level']
            alerts = risk_monitoring['risk_alerts']
            
            if risk_level == RiskLevel.CRITICAL:
                recommendations.extend([
                    "立即减仓至安全水平",
                    "暂停新增投资",
                    "重新评估投资策略",
                    "考虑对冲操作"
                ])
            elif risk_level == RiskLevel.HIGH:
                recommendations.extend([
                    "适当减仓降低风险",
                    "加强风险监控",
                    "分散投资组合",
                    "调整仓位结构"
                ])
            elif risk_level == RiskLevel.MEDIUM:
                recommendations.extend([
                    "密切关注风险指标",
                    "优化持仓结构",
                    "保持适当现金储备"
                ])
            else:
                recommendations.append("当前风险水平可控，继续执行策略")
            
            # 根据具体预警添加建议
            for alert in alerts:
                if alert.suggestion not in recommendations:
                    recommendations.append(alert.suggestion)
            
        except Exception as e:
            logger.error(f"❌ 生成风险建议失败: {e}")
            recommendations.append("建议人工检查风险状况")
        
        return recommendations
    
    def get_current_risk_status(self) -> Dict[str, Any]:
        """获取当前风险状态"""
        return {
            'current_alerts': [
                {
                    'alert_type': alert.alert_type,
                    'risk_level': alert.risk_level.value,
                    'symbol': alert.symbol,
                    'message': alert.message,
                    'suggestion': alert.suggestion,
                    'alert_time': alert.alert_time.isoformat()
                }
                for alert in self.current_alerts
            ],
            'risk_metrics': self.risk_metrics,
            'risk_parameters': {
                'max_single_position_pct': self.max_single_position_pct,
                'max_total_position_pct': self.max_total_position_pct,
                'max_drawdown_pct': self.max_drawdown_pct,
                'max_sector_concentration': self.max_sector_concentration,
                'volatility_threshold': self.volatility_threshold
            }
        }
