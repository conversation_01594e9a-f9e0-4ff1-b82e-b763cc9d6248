"""
持仓管理器
实现动态仓位调整和资金管理
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum

from database_models import db_manager, DailyMarket

logger = logging.getLogger(__name__)

class PositionAction(Enum):
    """持仓操作类型"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    REDUCE = "REDUCE"
    INCREASE = "INCREASE"

@dataclass
class PositionAdjustment:
    """持仓调整建议"""
    symbol: str
    action: PositionAction
    current_shares: int
    target_shares: int
    adjustment_shares: int
    current_weight: float
    target_weight: float
    reason: str
    priority: int  # 1-5, 5为最高优先级

class PositionManager:
    """持仓管理器"""
    
    def __init__(self):
        # 资金管理参数
        self.initial_capital = 1000000.0         # 初始资金100万
        self.cash_reserve_pct = 0.20             # 现金储备20%
        self.max_positions = 20                  # 最大持仓数量
        self.min_position_value = 10000          # 最小持仓价值1万
        self.rebalance_threshold = 0.05          # 再平衡阈值5%
        
        # 仓位管理策略
        self.position_sizing_method = 'equal_weight'  # 等权重
        self.volatility_adjustment = True        # 波动率调整
        self.momentum_adjustment = True          # 动量调整
        
        # 当前状态
        self.current_positions: Dict[str, Dict[str, Any]] = {}
        self.available_cash = self.initial_capital
        self.total_portfolio_value = self.initial_capital
        
        logger.info("💼 持仓管理器初始化完成")
        logger.info(f"  - 初始资金: ¥{self.initial_capital:,.0f}")
        logger.info(f"  - 现金储备: {self.cash_reserve_pct:.1%}")
        logger.info(f"  - 最大持仓数: {self.max_positions}")
    
    def calculate_position_sizes(self,
                               target_symbols: List[str],
                               current_prices: Dict[str, float],
                               signal_strengths: Dict[str, float] = None) -> Dict[str, Dict[str, Any]]:
        """
        计算目标仓位大小
        
        Args:
            target_symbols: 目标股票列表
            current_prices: 当前价格字典
            signal_strengths: 信号强度字典
            
        Returns:
            仓位配置字典
        """
        try:
            logger.info(f"💼 计算目标仓位: {len(target_symbols)}只股票")
            
            # 更新组合总价值
            self._update_portfolio_value(current_prices)
            
            # 计算可投资资金
            investable_cash = self.total_portfolio_value * (1 - self.cash_reserve_pct)
            
            position_sizes = {}
            
            if self.position_sizing_method == 'equal_weight':
                position_sizes = self._calculate_equal_weight_positions(
                    target_symbols, investable_cash, current_prices
                )
            elif self.position_sizing_method == 'signal_weighted':
                position_sizes = self._calculate_signal_weighted_positions(
                    target_symbols, investable_cash, current_prices, signal_strengths or {}
                )
            elif self.position_sizing_method == 'risk_parity':
                position_sizes = self._calculate_risk_parity_positions(
                    target_symbols, investable_cash, current_prices
                )
            
            # 应用波动率和动量调整
            if self.volatility_adjustment or self.momentum_adjustment:
                position_sizes = self._apply_adjustments(position_sizes, current_prices)
            
            logger.info(f"✅ 仓位计算完成: {len(position_sizes)}个仓位")
            
            return position_sizes
            
        except Exception as e:
            logger.error(f"❌ 计算仓位大小失败: {e}")
            return {}
    
    def generate_position_adjustments(self,
                                    target_positions: Dict[str, Dict[str, Any]],
                                    current_prices: Dict[str, float]) -> List[PositionAdjustment]:
        """
        生成持仓调整建议
        
        Args:
            target_positions: 目标持仓配置
            current_prices: 当前价格
            
        Returns:
            持仓调整建议列表
        """
        try:
            logger.info("💼 生成持仓调整建议...")
            
            adjustments = []
            
            # 获取所有相关股票
            all_symbols = set(self.current_positions.keys()) | set(target_positions.keys())
            
            for symbol in all_symbols:
                current_pos = self.current_positions.get(symbol, {})
                target_pos = target_positions.get(symbol, {})
                
                current_shares = current_pos.get('shares', 0)
                target_shares = target_pos.get('shares', 0)
                
                if current_shares == target_shares:
                    continue  # 无需调整
                
                current_price = current_prices.get(symbol, 0)
                if current_price == 0:
                    continue
                
                # 计算权重
                current_value = current_shares * current_price
                target_value = target_shares * current_price
                current_weight = current_value / self.total_portfolio_value if self.total_portfolio_value > 0 else 0
                target_weight = target_value / self.total_portfolio_value if self.total_portfolio_value > 0 else 0
                
                # 确定操作类型和优先级
                adjustment_shares = target_shares - current_shares
                
                if target_shares == 0:
                    action = PositionAction.SELL
                    reason = "清仓退出"
                    priority = 4
                elif current_shares == 0:
                    action = PositionAction.BUY
                    reason = "新建仓位"
                    priority = 3
                elif adjustment_shares > 0:
                    action = PositionAction.INCREASE
                    reason = "增加仓位"
                    priority = 2
                else:
                    action = PositionAction.REDUCE
                    reason = "减少仓位"
                    priority = 3
                
                # 检查是否需要调整（避免微小调整）
                weight_diff = abs(target_weight - current_weight)
                if weight_diff < self.rebalance_threshold and action not in [PositionAction.BUY, PositionAction.SELL]:
                    continue
                
                adjustment = PositionAdjustment(
                    symbol=symbol,
                    action=action,
                    current_shares=current_shares,
                    target_shares=target_shares,
                    adjustment_shares=adjustment_shares,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    reason=reason,
                    priority=priority
                )
                
                adjustments.append(adjustment)
            
            # 按优先级排序
            adjustments.sort(key=lambda x: x.priority, reverse=True)
            
            logger.info(f"✅ 生成了 {len(adjustments)} 个持仓调整建议")
            
            return adjustments
            
        except Exception as e:
            logger.error(f"❌ 生成持仓调整建议失败: {e}")
            return []
    
    def _update_portfolio_value(self, current_prices: Dict[str, float]) -> None:
        """更新组合总价值"""
        try:
            total_position_value = 0
            
            for symbol, position in self.current_positions.items():
                current_price = current_prices.get(symbol, position.get('avg_price', 0))
                shares = position.get('shares', 0)
                position_value = shares * current_price
                
                # 更新持仓信息
                position['current_price'] = current_price
                position['market_value'] = position_value
                position['unrealized_pnl'] = position_value - position.get('cost_basis', 0)
                
                total_position_value += position_value
            
            self.total_portfolio_value = self.available_cash + total_position_value
            
        except Exception as e:
            logger.error(f"❌ 更新组合价值失败: {e}")
    
    def _calculate_equal_weight_positions(self,
                                        target_symbols: List[str],
                                        investable_cash: float,
                                        current_prices: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """计算等权重仓位"""
        try:
            if not target_symbols:
                return {}
            
            # 等权重分配
            weight_per_stock = 1.0 / len(target_symbols)
            value_per_stock = investable_cash * weight_per_stock
            
            positions = {}
            
            for symbol in target_symbols:
                current_price = current_prices.get(symbol, 0)
                if current_price == 0:
                    continue
                
                # 计算股数（按手数取整）
                shares = int(value_per_stock / current_price / 100) * 100
                
                if shares >= 100:  # 至少1手
                    positions[symbol] = {
                        'shares': shares,
                        'target_value': shares * current_price,
                        'weight': (shares * current_price) / investable_cash,
                        'sizing_method': 'equal_weight'
                    }
            
            return positions
            
        except Exception as e:
            logger.error(f"❌ 计算等权重仓位失败: {e}")
            return {}
    
    def _calculate_signal_weighted_positions(self,
                                           target_symbols: List[str],
                                           investable_cash: float,
                                           current_prices: Dict[str, float],
                                           signal_strengths: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """计算信号加权仓位"""
        try:
            if not target_symbols:
                return {}
            
            # 计算信号权重
            total_signal_strength = sum(signal_strengths.get(symbol, 50) for symbol in target_symbols)
            
            if total_signal_strength == 0:
                return self._calculate_equal_weight_positions(target_symbols, investable_cash, current_prices)
            
            positions = {}
            
            for symbol in target_symbols:
                signal_strength = signal_strengths.get(symbol, 50)
                weight = signal_strength / total_signal_strength
                value = investable_cash * weight
                
                current_price = current_prices.get(symbol, 0)
                if current_price == 0:
                    continue
                
                # 计算股数
                shares = int(value / current_price / 100) * 100
                
                if shares >= 100:
                    positions[symbol] = {
                        'shares': shares,
                        'target_value': shares * current_price,
                        'weight': weight,
                        'signal_strength': signal_strength,
                        'sizing_method': 'signal_weighted'
                    }
            
            return positions
            
        except Exception as e:
            logger.error(f"❌ 计算信号加权仓位失败: {e}")
            return {}
    
    def _calculate_risk_parity_positions(self,
                                       target_symbols: List[str],
                                       investable_cash: float,
                                       current_prices: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """计算风险平价仓位"""
        try:
            # 简化处理：基于历史波动率的风险平价
            volatilities = {}
            
            for symbol in target_symbols:
                vol = self._calculate_stock_volatility(symbol)
                volatilities[symbol] = vol if vol > 0 else 0.2  # 默认20%波动率
            
            # 计算风险权重（波动率的倒数）
            risk_weights = {}
            total_inv_vol = sum(1 / vol for vol in volatilities.values())
            
            for symbol in target_symbols:
                risk_weights[symbol] = (1 / volatilities[symbol]) / total_inv_vol
            
            positions = {}
            
            for symbol in target_symbols:
                weight = risk_weights[symbol]
                value = investable_cash * weight
                
                current_price = current_prices.get(symbol, 0)
                if current_price == 0:
                    continue
                
                shares = int(value / current_price / 100) * 100
                
                if shares >= 100:
                    positions[symbol] = {
                        'shares': shares,
                        'target_value': shares * current_price,
                        'weight': weight,
                        'volatility': volatilities[symbol],
                        'sizing_method': 'risk_parity'
                    }
            
            return positions
            
        except Exception as e:
            logger.error(f"❌ 计算风险平价仓位失败: {e}")
            return {}
    
    def _calculate_stock_volatility(self, symbol: str, days: int = 30) -> float:
        """计算股票历史波动率"""
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                records = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if len(records) < 10:
                    return 0.2  # 默认波动率
                
                # 计算日收益率
                prices = [float(record.close_price) for record in records if record.close_price]
                daily_returns = []
                
                for i in range(1, len(prices)):
                    daily_return = (prices[i] - prices[i-1]) / prices[i-1]
                    daily_returns.append(daily_return)
                
                if not daily_returns:
                    return 0.2
                
                # 年化波动率
                volatility = np.std(daily_returns) * np.sqrt(252)
                return volatility
                
        except Exception as e:
            logger.error(f"❌ 计算股票波动率失败: {symbol} - {e}")
            return 0.2
    
    def _apply_adjustments(self,
                          positions: Dict[str, Dict[str, Any]],
                          current_prices: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """应用波动率和动量调整"""
        try:
            adjusted_positions = positions.copy()
            
            for symbol, position in adjusted_positions.items():
                adjustment_factor = 1.0
                
                # 波动率调整
                if self.volatility_adjustment:
                    volatility = self._calculate_stock_volatility(symbol)
                    # 高波动率股票减少仓位
                    if volatility > 0.3:
                        adjustment_factor *= 0.8
                    elif volatility < 0.15:
                        adjustment_factor *= 1.2
                
                # 动量调整
                if self.momentum_adjustment:
                    momentum = self._calculate_stock_momentum(symbol)
                    # 正动量增加仓位，负动量减少仓位
                    if momentum > 0.1:
                        adjustment_factor *= 1.1
                    elif momentum < -0.1:
                        adjustment_factor *= 0.9
                
                # 应用调整
                if adjustment_factor != 1.0:
                    original_shares = position['shares']
                    adjusted_shares = int(original_shares * adjustment_factor / 100) * 100
                    adjusted_shares = max(100, adjusted_shares)  # 至少1手
                    
                    current_price = current_prices.get(symbol, 0)
                    if current_price > 0:
                        position['shares'] = adjusted_shares
                        position['target_value'] = adjusted_shares * current_price
                        position['adjustment_factor'] = adjustment_factor
            
            return adjusted_positions
            
        except Exception as e:
            logger.error(f"❌ 应用仓位调整失败: {e}")
            return positions
    
    def _calculate_stock_momentum(self, symbol: str, days: int = 20) -> float:
        """计算股票动量"""
        try:
            with db_manager.get_session() as session:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)
                
                records = session.query(DailyMarket).filter(
                    DailyMarket.symbol == symbol,
                    DailyMarket.trade_date >= start_date,
                    DailyMarket.trade_date <= end_date
                ).order_by(DailyMarket.trade_date.asc()).all()
                
                if len(records) < 2:
                    return 0
                
                # 计算期间收益率
                start_price = float(records[0].close_price or 0)
                end_price = float(records[-1].close_price or 0)
                
                if start_price == 0:
                    return 0
                
                momentum = (end_price - start_price) / start_price
                return momentum
                
        except Exception as e:
            logger.error(f"❌ 计算股票动量失败: {symbol} - {e}")
            return 0
    
    def update_position(self,
                       symbol: str,
                       action: str,
                       shares: int,
                       price: float,
                       commission: float = 0) -> bool:
        """
        更新持仓记录
        
        Args:
            symbol: 股票代码
            action: 操作类型 ('buy', 'sell')
            shares: 股数
            price: 成交价格
            commission: 手续费
            
        Returns:
            是否更新成功
        """
        try:
            if symbol not in self.current_positions:
                self.current_positions[symbol] = {
                    'shares': 0,
                    'avg_price': 0,
                    'cost_basis': 0,
                    'realized_pnl': 0,
                    'unrealized_pnl': 0,
                    'first_buy_date': None,
                    'last_trade_date': None
                }
            
            position = self.current_positions[symbol]
            
            if action.lower() == 'buy':
                # 买入操作
                old_shares = position['shares']
                old_cost = position['cost_basis']
                
                new_shares = old_shares + shares
                new_cost = old_cost + shares * price + commission
                
                position['shares'] = new_shares
                position['cost_basis'] = new_cost
                position['avg_price'] = new_cost / new_shares if new_shares > 0 else 0
                
                if position['first_buy_date'] is None:
                    position['first_buy_date'] = datetime.now().date()
                
                # 更新可用现金
                self.available_cash -= (shares * price + commission)
                
            elif action.lower() == 'sell':
                # 卖出操作
                if position['shares'] < shares:
                    logger.error(f"❌ 持仓不足: {symbol} 持有{position['shares']}股，尝试卖出{shares}股")
                    return False
                
                # 计算实现盈亏
                avg_cost = position['avg_price']
                realized_pnl = shares * (price - avg_cost) - commission
                
                position['shares'] -= shares
                position['cost_basis'] -= shares * avg_cost
                position['realized_pnl'] += realized_pnl
                
                # 更新可用现金
                self.available_cash += (shares * price - commission)
                
                # 如果全部卖出，清除持仓记录
                if position['shares'] == 0:
                    del self.current_positions[symbol]
            
            # 更新最后交易日期
            if symbol in self.current_positions:
                self.current_positions[symbol]['last_trade_date'] = datetime.now().date()
            
            logger.info(f"✅ 持仓更新: {symbol} {action} {shares}股 @{price:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新持仓失败: {symbol} - {e}")
            return False
    
    def get_portfolio_summary(self, current_prices: Dict[str, float]) -> Dict[str, Any]:
        """获取组合摘要"""
        try:
            self._update_portfolio_value(current_prices)
            
            total_position_value = sum(pos['market_value'] for pos in self.current_positions.values())
            total_cost_basis = sum(pos['cost_basis'] for pos in self.current_positions.values())
            total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in self.current_positions.values())
            total_realized_pnl = sum(pos['realized_pnl'] for pos in self.current_positions.values())
            
            return {
                'total_portfolio_value': self.total_portfolio_value,
                'available_cash': self.available_cash,
                'total_position_value': total_position_value,
                'cash_ratio': self.available_cash / self.total_portfolio_value if self.total_portfolio_value > 0 else 0,
                'position_count': len(self.current_positions),
                'total_cost_basis': total_cost_basis,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_realized_pnl': total_realized_pnl,
                'total_pnl': total_unrealized_pnl + total_realized_pnl,
                'total_return': (self.total_portfolio_value - self.initial_capital) / self.initial_capital,
                'positions': {
                    symbol: {
                        'shares': pos['shares'],
                        'avg_price': pos['avg_price'],
                        'current_price': pos.get('current_price', 0),
                        'market_value': pos.get('market_value', 0),
                        'weight': pos.get('market_value', 0) / self.total_portfolio_value if self.total_portfolio_value > 0 else 0,
                        'unrealized_pnl': pos.get('unrealized_pnl', 0),
                        'unrealized_pnl_pct': pos.get('unrealized_pnl', 0) / pos['cost_basis'] if pos['cost_basis'] > 0 else 0
                    }
                    for symbol, pos in self.current_positions.items()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 获取组合摘要失败: {e}")
            return {}
