"""
量化交易系统数据库模型定义
基于ADATA接口文档和产品设计要求
"""

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Date,
    Text, JSON, DECIMAL, BigInteger, Enum, ForeignKey, Index,
    UniqueConstraint, func, create_engine
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime, date
from typing import Optional
import uuid
import os
from contextlib import contextmanager

Base = declarative_base()

# ============================================================================
# 1. 基础数据模型
# ============================================================================

class StockInfo(Base):
    """股票基本信息表"""
    __tablename__ = 'stock_info'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), unique=True, nullable=False, comment='股票代码')
    name = Column(String(50), nullable=False, comment='股票名称')
    industry = Column(String(50), comment='所属行业')
    sector = Column(String(50), comment='所属板块')
    market = Column(String(10), comment='交易市场(SH/SZ)')
    list_date = Column(Date, comment='上市日期')
    delist_date = Column(Date, comment='退市日期')
    market_cap = Column(BigInteger, comment='总市值')
    total_shares = Column(BigInteger, comment='总股本')
    float_shares = Column(BigInteger, comment='流通股本')
    is_active = Column(Boolean, default=True, comment='是否有效')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_stock_info_symbol', 'symbol'),
        Index('idx_stock_info_industry', 'industry'),
        Index('idx_stock_info_market', 'market'),
        Index('idx_stock_info_active', 'is_active'),
    )

# ============================================================================
# 2. 多时间周期行情数据模型
# ============================================================================

class BaseMarketData(Base):
    """行情数据基类"""
    __abstract__ = True
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), nullable=False, comment='股票代码')
    open_price = Column(DECIMAL(10, 3), comment='开盘价')
    high_price = Column(DECIMAL(10, 3), comment='最高价')
    low_price = Column(DECIMAL(10, 3), comment='最低价')
    close_price = Column(DECIMAL(10, 3), comment='收盘价')
    volume = Column(BigInteger, comment='成交量')
    amount = Column(DECIMAL(20, 2), comment='成交额')
    created_at = Column(DateTime, default=func.now())

class Minute1Market(BaseMarketData):
    """1分钟K线数据表"""
    __tablename__ = 'minute_1_market'
    
    trade_datetime = Column(DateTime, nullable=False, comment='交易时间')
    
    __table_args__ = (
        UniqueConstraint('symbol', 'trade_datetime', name='uk_symbol_datetime'),
        Index('idx_symbol_time', 'symbol', 'trade_datetime'),
        Index('idx_trade_datetime', 'trade_datetime'),
    )

class Minute5Market(BaseMarketData):
    """5分钟K线数据表"""
    __tablename__ = 'minute_5_market'

    trade_datetime = Column(DateTime, nullable=False, comment='交易时间')

    __table_args__ = (
        UniqueConstraint('symbol', 'trade_datetime', name='uk_symbol_datetime_5m'),
        Index('idx_symbol_time_5m', 'symbol', 'trade_datetime'),
        Index('idx_trade_datetime_5m', 'trade_datetime'),
    )

class Minute15Market(BaseMarketData):
    """15分钟K线数据表"""
    __tablename__ = 'minute_15_market'

    trade_datetime = Column(DateTime, nullable=False, comment='交易时间')

    __table_args__ = (
        UniqueConstraint('symbol', 'trade_datetime', name='uk_symbol_datetime_15m'),
        Index('idx_symbol_time_15m', 'symbol', 'trade_datetime'),
        Index('idx_trade_datetime_15m', 'trade_datetime'),
    )

class Hour1Market(BaseMarketData):
    """1小时K线数据表"""
    __tablename__ = 'hour_1_market'

    trade_datetime = Column(DateTime, nullable=False, comment='交易时间')

    __table_args__ = (
        UniqueConstraint('symbol', 'trade_datetime', name='uk_symbol_datetime_1h'),
        Index('idx_symbol_time_1h', 'symbol', 'trade_datetime'),
        Index('idx_trade_datetime_1h', 'trade_datetime'),
    )

class Hour4Market(BaseMarketData):
    """4小时K线数据表"""
    __tablename__ = 'hour_4_market'

    trade_datetime = Column(DateTime, nullable=False, comment='交易时间')

    __table_args__ = (
        UniqueConstraint('symbol', 'trade_datetime', name='uk_symbol_datetime_4h'),
        Index('idx_symbol_time_4h', 'symbol', 'trade_datetime'),
        Index('idx_trade_datetime_4h', 'trade_datetime'),
    )

class DailyMarket(BaseMarketData):
    """日线数据表"""
    __tablename__ = 'daily_market'
    
    trade_date = Column(Date, nullable=False, comment='交易日期')
    turnover_rate = Column(DECIMAL(8, 4), comment='换手率')
    pe_ratio = Column(DECIMAL(8, 4), comment='PE市盈率')
    pb_ratio = Column(DECIMAL(8, 4), comment='PB市净率')
    market_cap = Column(DECIMAL(20, 2), comment='总市值')
    circulating_cap = Column(DECIMAL(20, 2), comment='流通市值')
    
    __table_args__ = (
        UniqueConstraint('symbol', 'trade_date', name='uk_symbol_date'),
        Index('idx_symbol_date', 'symbol', 'trade_date'),
        Index('idx_trade_date', 'trade_date'),
    )

# ============================================================================
# 3. 基本面数据模型
# ============================================================================

class FinancialData(Base):
    """财务数据表"""
    __tablename__ = 'financial_data'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), nullable=False, comment='股票代码')
    report_date = Column(Date, nullable=False, comment='报告期')
    report_type = Column(String(10), nullable=False, comment='报告类型(Q1/Q2/Q3/Q4)')
    
    # 收入利润指标
    total_revenue = Column(DECIMAL(20, 2), comment='总营收')
    net_profit = Column(DECIMAL(20, 2), comment='净利润')
    gross_profit = Column(DECIMAL(20, 2), comment='毛利润')
    operating_profit = Column(DECIMAL(20, 2), comment='营业利润')
    
    # 资产负债指标
    total_assets = Column(DECIMAL(20, 2), comment='总资产')
    total_liabilities = Column(DECIMAL(20, 2), comment='总负债')
    total_equity = Column(DECIMAL(20, 2), comment='股东权益')
    current_assets = Column(DECIMAL(20, 2), comment='流动资产')
    current_liabilities = Column(DECIMAL(20, 2), comment='流动负债')
    
    # 财务比率
    roe = Column(DECIMAL(8, 4), comment='净资产收益率')
    roa = Column(DECIMAL(8, 4), comment='总资产收益率')
    gross_margin = Column(DECIMAL(8, 4), comment='毛利率')
    net_margin = Column(DECIMAL(8, 4), comment='净利率')
    debt_ratio = Column(DECIMAL(8, 4), comment='资产负债率')
    current_ratio = Column(DECIMAL(8, 4), comment='流动比率')
    
    # 增长率指标
    revenue_growth = Column(DECIMAL(8, 4), comment='营收增长率')
    profit_growth = Column(DECIMAL(8, 4), comment='利润增长率')
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        UniqueConstraint('symbol', 'report_date', 'report_type', name='uk_symbol_report_financial'),
        Index('idx_symbol_date_financial', 'symbol', 'report_date'),
        Index('idx_report_date_financial', 'report_date'),
    )

# ============================================================================
# 4. 因子数据模型
# ============================================================================

class FactorConfig(Base):
    """因子配置表"""
    __tablename__ = 'factor_config'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    factor_id = Column(String(50), unique=True, nullable=False, comment='因子ID')
    factor_name = Column(String(100), nullable=False, comment='因子名称')
    factor_category = Column(Enum('technical', 'fundamental', 'market'), nullable=False, comment='因子分类')
    factor_type = Column(String(50), nullable=False, comment='因子类型')
    weight = Column(DECIMAL(5, 4), nullable=False, default=0.0000, comment='权重')
    is_active = Column(Boolean, default=True, comment='是否启用')
    description = Column(Text, comment='因子描述')
    calculation_formula = Column(Text, comment='计算公式')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    parameters = relationship("FactorParameter", back_populates="factor", cascade="all, delete-orphan")
    
    __table_args__ = (
        Index('idx_factor_category', 'factor_category'),
        Index('idx_factor_active', 'is_active'),
    )

class FactorParameter(Base):
    """因子参数表"""
    __tablename__ = 'factor_parameters'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    factor_id = Column(String(50), ForeignKey('factor_config.factor_id', ondelete='CASCADE'), nullable=False, comment='因子ID')
    param_name = Column(String(50), nullable=False, comment='参数名称')
    param_type = Column(Enum('int', 'float', 'string', 'boolean'), nullable=False, comment='参数类型')
    param_value = Column(Text, nullable=False, comment='参数值')
    default_value = Column(Text, comment='默认值')
    min_value = Column(DECIMAL(20, 6), comment='最小值')
    max_value = Column(DECIMAL(20, 6), comment='最大值')
    description = Column(Text, comment='参数描述')
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    factor = relationship("FactorConfig", back_populates="parameters")
    
    __table_args__ = (
        Index('idx_factor_param', 'factor_id', 'param_name'),
    )

# ============================================================================
# 5. 交易信号模型
# ============================================================================

class StockScore(Base):
    """股票评分表"""
    __tablename__ = 'stock_scores'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), nullable=False, comment='股票代码')
    calculation_date = Column(Date, nullable=False, comment='计算日期')
    
    # 分项评分
    technical_score = Column(DECIMAL(8, 4), comment='技术面评分')
    fundamental_score = Column(DECIMAL(8, 4), comment='基本面评分')
    market_score = Column(DECIMAL(8, 4), comment='市场表现评分')
    
    # 综合评分
    total_score = Column(DECIMAL(8, 4), nullable=False, comment='综合评分')
    score_rank = Column(Integer, comment='评分排名')
    
    # 详细因子评分
    factor_scores = Column(JSON, comment='详细因子评分')
    
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        UniqueConstraint('symbol', 'calculation_date', name='uk_symbol_date_score'),
        Index('idx_symbol_score_stocks', 'symbol', 'total_score'),
        Index('idx_calculation_date_score', 'calculation_date'),
        Index('idx_total_score_stocks', 'total_score'),
    )

class TradingSignal(Base):
    """交易信号表"""
    __tablename__ = 'trading_signals'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), nullable=False, comment='股票代码')
    signal_time = Column(DateTime, nullable=False, comment='信号时间')
    signal_type = Column(Enum('BUY', 'SELL', 'HOLD'), nullable=False, comment='信号类型')
    signal_strength = Column(Enum('WEAK', 'MEDIUM', 'STRONG'), nullable=False, comment='信号强度')
    signal_score = Column(DECIMAL(8, 4), nullable=False, comment='信号评分')
    confidence = Column(DECIMAL(5, 4), comment='置信度')
    
    # 价格信息
    trigger_price = Column(DECIMAL(10, 3), comment='触发价格')
    target_price = Column(DECIMAL(10, 3), comment='目标价格')
    stop_loss_price = Column(DECIMAL(10, 3), comment='止损价格')
    
    # 信号详情
    signal_reasons = Column(JSON, comment='信号原因')
    technical_indicators = Column(JSON, comment='技术指标状态')
    
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_symbol_time_signal', 'symbol', 'signal_time'),
        Index('idx_signal_type_trading', 'signal_type'),
        Index('idx_signal_time_trading', 'signal_time'),
        Index('idx_signal_score_trading', 'signal_score'),
    )

# ============================================================================
# 6. 投资组合管理模型
# ============================================================================

class Portfolio(Base):
    """投资组合表"""
    __tablename__ = 'portfolios'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    portfolio_name = Column(String(100), nullable=False, comment='组合名称')
    portfolio_type = Column(Enum('QUANTITATIVE', 'MANUAL', 'HYBRID'), nullable=False, comment='组合类型')
    initial_capital = Column(DECIMAL(20, 2), nullable=False, comment='初始资金')
    current_value = Column(DECIMAL(20, 2), comment='当前市值')
    cash_balance = Column(DECIMAL(20, 2), comment='现金余额')

    # 配置参数
    max_positions = Column(Integer, default=20, comment='最大持仓数')
    max_position_weight = Column(DECIMAL(5, 4), default=0.1, comment='单股最大权重')
    rebalance_frequency = Column(Enum('DAILY', 'WEEKLY', 'MONTHLY'), default='MONTHLY')

    # 绩效指标
    total_return = Column(DECIMAL(8, 4), comment='总收益率')
    annual_return = Column(DECIMAL(8, 4), comment='年化收益率')
    max_drawdown = Column(DECIMAL(8, 4), comment='最大回撤')
    sharpe_ratio = Column(DECIMAL(8, 4), comment='夏普比率')

    status = Column(Enum('ACTIVE', 'INACTIVE', 'CLOSED'), default='ACTIVE')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关联关系
    holdings = relationship("PortfolioHolding", back_populates="portfolio", cascade="all, delete-orphan")
    orders = relationship("TradingOrder", back_populates="portfolio")

    __table_args__ = (
        Index('idx_portfolio_type', 'portfolio_type'),
        Index('idx_portfolio_status', 'status'),
    )

class PortfolioHolding(Base):
    """投资组合持仓表"""
    __tablename__ = 'portfolio_holdings'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    portfolio_id = Column(String(36), ForeignKey('portfolios.id', ondelete='CASCADE'), nullable=False, comment='组合ID')
    symbol = Column(String(10), nullable=False, comment='股票代码')
    quantity = Column(Integer, nullable=False, comment='持仓数量')
    avg_cost = Column(DECIMAL(10, 3), nullable=False, comment='平均成本')
    current_price = Column(DECIMAL(10, 3), comment='当前价格')
    market_value = Column(DECIMAL(20, 2), comment='市值')
    weight = Column(DECIMAL(5, 4), comment='权重')
    unrealized_pnl = Column(DECIMAL(20, 2), comment='浮动盈亏')

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关联关系
    portfolio = relationship("Portfolio", back_populates="holdings")

    __table_args__ = (
        UniqueConstraint('portfolio_id', 'symbol', name='uk_portfolio_symbol_holding'),
        Index('idx_portfolio_id_holding', 'portfolio_id'),
        Index('idx_symbol_holding', 'symbol'),
    )

class TradingOrder(Base):
    """交易订单表"""
    __tablename__ = 'trading_orders'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    portfolio_id = Column(String(36), ForeignKey('portfolios.id', ondelete='SET NULL'), comment='组合ID')
    symbol = Column(String(10), nullable=False, comment='股票代码')
    order_type = Column(Enum('BUY', 'SELL'), nullable=False, comment='订单类型')
    quantity = Column(Integer, nullable=False, comment='数量')
    price = Column(DECIMAL(10, 3), comment='价格')
    order_status = Column(Enum('PENDING', 'PARTIAL', 'FILLED', 'CANCELLED'), default='PENDING')

    # 执行信息
    filled_quantity = Column(Integer, default=0, comment='已成交数量')
    avg_fill_price = Column(DECIMAL(10, 3), comment='平均成交价')
    commission = Column(DECIMAL(10, 2), comment='手续费')

    # 时间信息
    order_time = Column(DateTime, default=func.now(), comment='下单时间')
    fill_time = Column(DateTime, comment='成交时间')

    # 订单详情
    order_reason = Column(Text, comment='下单原因')

    # 关联关系
    portfolio = relationship("Portfolio", back_populates="orders")

    __table_args__ = (
        Index('idx_portfolio_id_order', 'portfolio_id'),
        Index('idx_symbol_order', 'symbol'),
        Index('idx_order_status_trading', 'order_status'),
        Index('idx_order_time_trading', 'order_time'),
    )

# ============================================================================
# 7. VeighNa回测系统模型
# ============================================================================

class BacktestStrategy(Base):
    """回测策略表"""
    __tablename__ = 'backtest_strategies'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    strategy_name = Column(String(100), nullable=False, comment='策略名称')
    strategy_type = Column(String(50), nullable=False, comment='策略类型')
    description = Column(Text, comment='策略描述')

    # 回测配置
    start_date = Column(Date, nullable=False, comment='开始日期')
    end_date = Column(Date, nullable=False, comment='结束日期')
    initial_capital = Column(DECIMAL(20, 2), nullable=False, comment='初始资金')

    # 策略参数
    strategy_params = Column(JSON, comment='策略参数')

    # 风险控制参数
    max_position_size = Column(DECIMAL(5, 4), comment='最大仓位')
    stop_loss_ratio = Column(DECIMAL(5, 4), comment='止损比例')
    take_profit_ratio = Column(DECIMAL(5, 4), comment='止盈比例')

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关联关系
    results = relationship("BacktestResult", back_populates="strategy", cascade="all, delete-orphan")

    __table_args__ = (
        Index('idx_strategy_type', 'strategy_type'),
        Index('idx_strategy_name', 'strategy_name'),
    )

class BacktestResult(Base):
    """回测结果表"""
    __tablename__ = 'backtest_results'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    strategy_id = Column(String(36), ForeignKey('backtest_strategies.id', ondelete='CASCADE'), nullable=False, comment='策略ID')
    symbol = Column(String(10), comment='股票代码')

    # 绩效指标
    total_return = Column(DECIMAL(8, 4), comment='总收益率')
    annual_return = Column(DECIMAL(8, 4), comment='年化收益率')
    max_drawdown = Column(DECIMAL(8, 4), comment='最大回撤')
    sharpe_ratio = Column(DECIMAL(8, 4), comment='夏普比率')
    sortino_ratio = Column(DECIMAL(8, 4), comment='索提诺比率')
    calmar_ratio = Column(DECIMAL(8, 4), comment='卡尔马比率')
    volatility = Column(DECIMAL(8, 4), comment='波动率')

    # 交易统计
    total_trades = Column(Integer, comment='总交易次数')
    winning_trades = Column(Integer, comment='盈利交易次数')
    losing_trades = Column(Integer, comment='亏损交易次数')
    win_rate = Column(DECIMAL(5, 4), comment='胜率')
    profit_loss_ratio = Column(DECIMAL(8, 4), comment='盈亏比')

    # 其他指标
    max_consecutive_wins = Column(Integer, comment='最大连胜次数')
    max_consecutive_losses = Column(Integer, comment='最大连亏次数')
    avg_trade_return = Column(DECIMAL(8, 4), comment='平均交易收益率')

    # 详细数据
    daily_returns = Column(JSON, comment='每日收益率数据')
    trade_details = Column(JSON, comment='交易明细数据')
    performance_metrics = Column(JSON, comment='详细绩效指标')

    created_at = Column(DateTime, default=func.now())

    # 关联关系
    strategy = relationship("BacktestStrategy", back_populates="results")

    __table_args__ = (
        Index('idx_strategy_id_result', 'strategy_id'),
        Index('idx_symbol_result', 'symbol'),
        Index('idx_total_return_result', 'total_return'),
        Index('idx_sharpe_ratio_result', 'sharpe_ratio'),
    )

# ============================================================================
# 8. 系统管理模型
# ============================================================================

class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = 'system_config'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    config_key = Column(String(100), unique=True, nullable=False, comment='配置键')
    config_value = Column(Text, nullable=False, comment='配置值')
    config_type = Column(Enum('STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', 'JSON'), default='STRING')
    description = Column(Text, comment='配置描述')
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('idx_config_key', 'config_key'),
        Index('idx_config_active', 'is_active'),
    )

class SystemLog(Base):
    """系统日志表"""
    __tablename__ = 'system_logs'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    log_level = Column(Enum('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'), nullable=False)
    module_name = Column(String(50), nullable=False, comment='模块名称')
    message = Column(Text, nullable=False, comment='日志消息')
    details = Column(JSON, comment='详细信息')
    created_at = Column(DateTime, default=func.now())

    __table_args__ = (
        Index('idx_log_level', 'log_level'),
        Index('idx_module_name', 'module_name'),
        Index('idx_created_at', 'created_at'),
    )

# ============================================================================
# 数据库连接和会话管理
# ============================================================================

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import os

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, database_url: str = None):
        if database_url is None:
            # 从环境变量获取数据库连接字符串，默认使用SQLite
            database_url = os.getenv(
                'DATABASE_URL',
                'sqlite:///quantitative_trading_system.db'
            )

        # 根据数据库类型设置不同的引擎参数
        if database_url.startswith('sqlite'):
            self.engine = create_engine(
                database_url,
                pool_pre_ping=True,
                echo=False,
                connect_args={"check_same_thread": False}  # SQLite特殊配置
            )
        else:
            self.engine = create_engine(
                database_url,
                pool_size=20,
                max_overflow=30,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # 设置为True可以看到SQL语句
            )

        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )

    def create_all_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)

    def drop_all_tables(self):
        """删除所有表（谨慎使用）"""
        Base.metadata.drop_all(bind=self.engine)

    @contextmanager
    def get_session(self):
        """获取数据库会话"""
        session = self.SessionLocal()

    @contextmanager
    def get_session_context(self):
        """获取数据库会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_session_context() as session:
                from sqlalchemy import text
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False

    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with self.get_session_context() as session:
                from sqlalchemy import text
                if self.engine.url.drivername == 'sqlite':
                    result = session.execute(text("SELECT sqlite_version()")).fetchone()
                    return {
                        'type': 'SQLite',
                        'version': result[0] if result else 'Unknown',
                        'url': str(self.engine.url).replace(str(self.engine.url.password) if self.engine.url.password else '', '***')
                    }
                else:
                    return {
                        'type': self.engine.url.drivername,
                        'host': self.engine.url.host,
                        'port': self.engine.url.port,
                        'database': self.engine.url.database,
                        'url': str(self.engine.url).replace(str(self.engine.url.password) if self.engine.url.password else '', '***')
                    }
        except Exception as e:
            return {'error': str(e)}
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

# ============================================================================
# 9. 因子管理系统数据模型
# ============================================================================

class FactorConfigExtended(Base):
    """扩展因子配置表"""
    __tablename__ = 'factor_config_extended'

    factor_id = Column(String(50), primary_key=True, comment='因子ID')
    factor_name = Column(String(100), nullable=False, comment='因子名称')
    factor_name_cn = Column(String(100), nullable=False, comment='因子中文名称')
    factor_category = Column(Enum('TECHNICAL', 'FUNDAMENTAL', 'MARKET', 'CUSTOM', name='factor_category_enum'),
                           nullable=False, comment='因子分类')
    factor_type = Column(Enum('MOMENTUM', 'REVERSAL', 'VALUE', 'GROWTH', 'QUALITY', 'VOLATILITY', 'LIQUIDITY', 'SIZE', 'CUSTOM', name='factor_type_enum'),
                        nullable=False, comment='因子类型')
    description = Column(Text, comment='因子描述')
    calculation_method = Column(String(100), nullable=False, comment='计算方法')
    parameters = Column(JSON, comment='因子参数')
    weight = Column(DECIMAL(5,4), nullable=False, default=0.0000, comment='因子权重')
    is_enabled = Column(Boolean, default=True, comment='是否启用')
    min_value = Column(Float, comment='最小值')
    max_value = Column(Float, comment='最大值')
    normalization_method = Column(String(20), default='z_score', comment='标准化方法')
    update_frequency = Column(String(20), default='daily', comment='更新频率')
    data_requirements = Column(JSON, comment='数据需求')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('idx_factor_config_ext_category', 'factor_category'),
        Index('idx_factor_config_ext_type', 'factor_type'),
        Index('idx_factor_config_ext_enabled', 'is_enabled'),
    )

# FactorParameters 已在上面定义，这里删除重复定义

class FactorValues(Base):
    """因子值存储表"""
    __tablename__ = 'factor_values'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = Column(String(10), nullable=False, comment='股票代码')
    factor_id = Column(String(50), ForeignKey('factor_config.factor_id'), nullable=False, comment='因子ID')
    trade_date = Column(Date, nullable=False, comment='交易日期')
    factor_value = Column(Float, comment='因子值')
    normalized_value = Column(Float, comment='标准化后的值')
    percentile_rank = Column(Float, comment='百分位排名')
    z_score = Column(Float, comment='Z分数')
    created_at = Column(DateTime, default=func.now())

    # 关系
    factor = relationship("FactorConfig", backref="values")

    __table_args__ = (
        UniqueConstraint('symbol', 'factor_id', 'trade_date', name='uq_factor_values'),
        Index('idx_factor_values_symbol_date', 'symbol', 'trade_date'),
        Index('idx_factor_values_factor_date', 'factor_id', 'trade_date'),
        Index('idx_factor_values_date', 'trade_date'),
    )

class CustomFactorDefinitions(Base):
    """自定义因子定义表"""
    __tablename__ = 'custom_factor_definitions'

    factor_id = Column(String(50), primary_key=True, comment='因子ID')
    factor_name = Column(String(100), nullable=False, comment='因子名称')
    factor_name_cn = Column(String(100), nullable=False, comment='因子中文名称')
    description = Column(Text, comment='因子描述')
    formula = Column(Text, comment='因子公式')
    code = Column(Text, nullable=False, comment='Python代码')
    input_variables = Column(JSON, comment='输入变量')
    output_type = Column(String(20), default='float', comment='输出类型')
    validation_rules = Column(JSON, comment='验证规则')
    created_by = Column(String(50), comment='创建者')
    is_validated = Column(Boolean, default=False, comment='是否已验证')
    test_results = Column(JSON, comment='测试结果')
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index('idx_custom_factor_validated', 'is_validated'),
        Index('idx_custom_factor_created_by', 'created_by'),
    )

class FactorTestResults(Base):
    """因子测试结果表"""
    __tablename__ = 'factor_test_results'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    factor_id = Column(String(50), ForeignKey('factor_config.factor_id'), nullable=False, comment='因子ID')
    test_type = Column(String(20), nullable=False, comment='测试类型')
    test_period_start = Column(Date, nullable=False, comment='测试开始日期')
    test_period_end = Column(Date, nullable=False, comment='测试结束日期')
    sample_size = Column(Integer, comment='样本数量')

    # 统计特性
    mean_value = Column(Float, comment='均值')
    std_value = Column(Float, comment='标准差')
    skewness = Column(Float, comment='偏度')
    kurtosis = Column(Float, comment='峰度')

    # 有效性指标
    ic_mean = Column(Float, comment='IC均值')
    ic_std = Column(Float, comment='IC标准差')
    ic_ir = Column(Float, comment='信息比率')
    rank_ic = Column(Float, comment='排序IC')

    # 稳定性指标
    stability_score = Column(Float, comment='稳定性评分')
    decay_rate = Column(Float, comment='衰减率')

    # 单调性指标
    monotonicity_score = Column(Float, comment='单调性评分')

    # 分层回测结果
    layered_returns = Column(JSON, comment='分层收益')

    # 综合评分
    overall_score = Column(Float, comment='综合评分')
    test_conclusion = Column(Text, comment='测试结论')

    created_at = Column(DateTime, default=func.now())

    # 关系
    factor = relationship("FactorConfig", backref="test_results")

    __table_args__ = (
        Index('idx_factor_test_factor_id', 'factor_id'),
        Index('idx_factor_test_type', 'test_type'),
        Index('idx_factor_test_period', 'test_period_start', 'test_period_end'),
    )

class WeightAdjustmentHistory(Base):
    """权重调整历史表"""
    __tablename__ = 'weight_adjustment_history'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    adjustment_id = Column(String(100), nullable=False, comment='调整ID')
    factor_id = Column(String(50), ForeignKey('factor_config.factor_id'), nullable=False, comment='因子ID')
    old_weight = Column(DECIMAL(5,4), nullable=False, comment='原权重')
    new_weight = Column(DECIMAL(5,4), nullable=False, comment='新权重')
    adjustment_method = Column(Enum('MANUAL', 'PERFORMANCE_BASED', 'CORRELATION_BASED', 'VOLATILITY_BASED', 'EQUAL_WEIGHT', 'CATEGORY_BALANCED', name='adjustment_method_enum'),
                              nullable=False, comment='调整方法')
    adjustment_reason = Column(Text, comment='调整原因')
    performance_impact = Column(Float, comment='绩效影响')
    adjustment_time = Column(DateTime, default=func.now(), comment='调整时间')

    # 关系
    factor = relationship("FactorConfig", backref="weight_history")

    __table_args__ = (
        Index('idx_weight_adjustment_factor_id', 'factor_id'),
        Index('idx_weight_adjustment_time', 'adjustment_time'),
        Index('idx_weight_adjustment_method', 'adjustment_method'),
    )

# ============================================================================
# 10. 系统管理数据模型 (使用已定义的SystemConfig)
# ============================================================================

# SystemConfig 已在上面定义，这里删除重复定义

# SystemLogs 已在上面定义，这里删除重复定义

class PerformanceMetrics(Base):
    """性能监控指标表"""
    __tablename__ = 'performance_metrics'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    metric_name = Column(String(100), nullable=False, comment='指标名称')
    metric_value = Column(Float, nullable=False, comment='指标值')
    metric_unit = Column(String(20), comment='指标单位')
    host_name = Column(String(100), comment='主机名')
    process_name = Column(String(100), comment='进程名')
    timestamp = Column(DateTime, default=func.now(), comment='时间戳')

    __table_args__ = (
        Index('idx_performance_metrics_name', 'metric_name'),
        Index('idx_performance_metrics_timestamp', 'timestamp'),
        Index('idx_performance_metrics_host', 'host_name'),
    )

class UserSessions(Base):
    """用户会话表"""
    __tablename__ = 'user_sessions'

    session_id = Column(String(100), primary_key=True, comment='会话ID')
    user_id = Column(String(50), nullable=False, comment='用户ID')
    login_time = Column(DateTime, default=func.now(), comment='登录时间')
    last_activity = Column(DateTime, default=func.now(), comment='最后活动时间')
    ip_address = Column(String(45), comment='IP地址')
    user_agent = Column(Text, comment='用户代理')
    is_active = Column(Boolean, default=True, comment='是否活跃')
    logout_time = Column(DateTime, comment='登出时间')

    __table_args__ = (
        Index('idx_user_sessions_user_id', 'user_id'),
        Index('idx_user_sessions_login_time', 'login_time'),
        Index('idx_user_sessions_active', 'is_active'),
    )

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 依赖注入函数，用于FastAPI等框架
def get_db():
    """获取数据库会话的依赖注入函数"""
    with db_manager.get_session() as session:
        yield session
