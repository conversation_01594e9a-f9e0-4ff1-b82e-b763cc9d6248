/* VeighNa量化交易系统样式 - 严格按照产品原型设计 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    background-color: #1f1f1f;
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
    overflow: hidden;
}

/* 顶部标题栏 */
.header {
    height: 50px;
    background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
    border-bottom: 1px solid #3f3f3f;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left h1 {
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.connection-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
}

.connection-status.connected {
    background-color: #52c41a;
    color: white;
}

.connection-status.disconnected {
    background-color: #ff4d4f;
    color: white;
}

.system-time, .user-info {
    font-size: 11px;
    color: #8c8c8c;
}

.settings-btn {
    background: #1890ff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
}

.settings-btn:hover {
    background: #40a9ff;
}

/* 主导航栏 */
.main-nav {
    height: 40px;
    background: #262626;
    border-bottom: 1px solid #3f3f3f;
    display: flex;
    align-items: center;
    padding: 0 20px;
    position: fixed;
    top: 50px;
    left: 0;
    right: 0;
    z-index: 999;
}

.nav-btn {
    background: transparent;
    color: #8c8c8c;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 4px;
    margin-right: 4px;
    transition: all 0.2s;
}

.nav-btn:hover {
    background: #3f3f3f;
    color: #ffffff;
}

.nav-btn.active {
    background: #1890ff;
    color: white;
}

/* 主内容区域 */
.main-content {
    display: flex;
    height: calc(100vh - 120px);
    margin-top: 90px;
    margin-bottom: 30px;
}

/* 左侧功能导航面板 */
.sidebar {
    width: 250px;
    background: #262626;
    border-right: 1px solid #3f3f3f;
    overflow-y: auto;
}

.function-nav {
    padding: 10px;
}

.nav-section {
    margin-bottom: 20px;
}

.nav-section h3 {
    font-size: 13px;
    color: #1890ff;
    margin-bottom: 8px;
    padding: 8px 12px;
    background: #1f1f1f;
    border-radius: 4px;
}

.nav-section ul {
    list-style: none;
}

.nav-section li {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #8c8c8c;
    border-radius: 4px;
    margin-bottom: 2px;
    transition: all 0.2s;
}

.nav-section li:hover {
    background: #3f3f3f;
    color: #ffffff;
}

.nav-section li.active {
    background: #1890ff;
    color: white;
}

/* 主工作区域 */
.workspace {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 20px;
}

/* 实时行情监控 */
.realtime-monitor {
    background: #262626;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 15px;
}

.realtime-monitor h3 {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 15px;
    border-bottom: 1px solid #3f3f3f;
    padding-bottom: 8px;
}

.stock-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stock-item {
    display: grid;
    grid-template-columns: 60px 80px 60px 60px 80px;
    gap: 10px;
    padding: 8px;
    background: #1f1f1f;
    border-radius: 4px;
    font-size: 11px;
    align-items: center;
}

.stock-item.price-up {
    border-left: 3px solid #52c41a;
}

.stock-item.price-down {
    border-left: 3px solid #ff4d4f;
}

.stock-code {
    font-weight: bold;
    color: #1890ff;
}

.stock-name {
    color: #ffffff;
}

.stock-price {
    font-weight: bold;
    color: #ffffff;
}

.stock-change {
    font-weight: bold;
}

.stock-item.price-up .stock-change,
.stock-item.price-up .stock-change-pct {
    color: #52c41a;
}

.stock-item.price-down .stock-change,
.stock-item.price-down .stock-change-pct {
    color: #ff4d4f;
}

/* K线图表区域 */
.chart-area {
    background: #262626;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 15px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #3f3f3f;
    padding-bottom: 8px;
}

.chart-header h3 {
    font-size: 14px;
    color: #ffffff;
}

.timeframe-buttons {
    display: flex;
    gap: 4px;
}

.tf-btn {
    background: #3f3f3f;
    color: #8c8c8c;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.tf-btn:hover {
    background: #4f4f4f;
    color: #ffffff;
}

.tf-btn.active {
    background: #1890ff;
    color: white;
}

.chart-container {
    height: 300px;
    background: #1f1f1f;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    font-size: 14px;
}

.chart-signals {
    margin-top: 10px;
    text-align: center;
}

.signal-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 11px;
}

.buy-signal {
    color: #ff4d4f;
}

.sell-signal {
    color: #52c41a;
}

/* 交易信号面板 */
.signal-panel {
    background: #262626;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 15px;
    grid-column: 1 / -1;
}

.signal-panel h3 {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 15px;
    border-bottom: 1px solid #3f3f3f;
    padding-bottom: 8px;
}

.signal-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.signal-item {
    display: grid;
    grid-template-columns: 30px 80px 120px 80px 100px;
    gap: 15px;
    padding: 10px;
    background: #1f1f1f;
    border-radius: 4px;
    font-size: 11px;
    align-items: center;
}

.signal-item.buy {
    border-left: 3px solid #ff4d4f;
}

.signal-item.sell {
    border-left: 3px solid #52c41a;
}

.signal-icon {
    font-size: 14px;
}

.signal-type {
    font-weight: bold;
    color: #ffffff;
}

.signal-stock {
    color: #1890ff;
}

.signal-score {
    color: #faad14;
    font-weight: bold;
}

.signal-time {
    color: #8c8c8c;
}

/* 持仓明细 */
.position-panel {
    background: #262626;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 15px;
    grid-column: 1 / -1;
}

.position-panel h3 {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 15px;
    border-bottom: 1px solid #3f3f3f;
    padding-bottom: 8px;
}

.position-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.position-table th {
    background: #1f1f1f;
    color: #8c8c8c;
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #3f3f3f;
}

.position-table td {
    padding: 8px;
    border-bottom: 1px solid #3f3f3f;
    color: #ffffff;
}

.position-table tr.profit td:nth-child(6) {
    color: #52c41a;
    font-weight: bold;
}

.position-table tr.loss td:nth-child(6) {
    color: #ff4d4f;
    font-weight: bold;
}

/* 状态栏 */
.status-bar {
    height: 30px;
    background: #262626;
    border-top: 1px solid #3f3f3f;
    display: flex;
    align-items: center;
    padding: 0 20px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.status-item {
    margin-right: 30px;
    font-size: 11px;
    color: #8c8c8c;
}

.status-item .normal {
    color: #52c41a;
}

.status-item .running {
    color: #1890ff;
}

.status-item .asset {
    color: #faad14;
    font-weight: bold;
}

.status-item .profit {
    color: #52c41a;
    font-weight: bold;
}

.status-item .loss {
    color: #ff4d4f;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 200px;
    }
    
    .workspace {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .header-right {
        gap: 10px;
    }
    
    .header-right .user-info {
        display: none;
    }
}
