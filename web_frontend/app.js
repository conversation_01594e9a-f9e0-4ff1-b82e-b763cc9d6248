// VeighNa量化交易系统前端应用
const { createApp } = Vue;

createApp({
    data() {
        return {
            // 系统状态
            systemStatus: {
                status: 'running',
                connection_status: 'connected',
                data_status: 'normal',
                health_score: 100,
                modules: {}
            },
            
            // 当前时间
            currentTime: '',
            
            // 导航状态
            activeTab: 'system',
            activeView: 'data-collection',
            
            // 主导航标签
            mainTabs: [
                { key: 'system', label: '系统' },
                { key: 'data', label: '数据' },
                { key: 'strategy', label: '策略' },
                { key: 'trading', label: '交易' },
                { key: 'risk', label: '风控' },
                { key: 'portfolio', label: '组合' },
                { key: 'factor', label: '因子' },
                { key: 'log', label: '日志' },
                { key: 'help', label: '帮助' }
            ],
            
            // 时间周期
            timeframes: ['1m', '5m', '15m', '1h', '1d'],
            activeTimeframe: '1d',
            
            // 实时股票数据
            realtimeStocks: [],
            
            // 交易信号
            tradingSignals: [],
            
            // 投资组合持仓
            portfolioPositions: [],
            
            // 状态栏数据
            runningStrategies: 3,
            totalAssets: 1250000,
            todayPnl: 12500,
            
            // WebSocket连接
            websocket: null,
            
            // API基础URL
            apiBaseUrl: '/api'
        }
    },
    
    mounted() {
        this.initializeApp();
    },
    
    beforeUnmount() {
        if (this.websocket) {
            this.websocket.close();
        }
    },
    
    methods: {
        // 初始化应用
        async initializeApp() {
            console.log('🚀 初始化VeighNa量化交易系统前端...');
            
            // 启动时间更新
            this.updateTime();
            setInterval(this.updateTime, 1000);
            
            // 加载初始数据
            await this.loadInitialData();
            
            // 建立WebSocket连接
            this.connectWebSocket();
            
            // 启动定时数据更新
            setInterval(this.updateData, 5000);
            
            console.log('✅ 前端初始化完成');
        },
        
        // 更新当前时间
        updateTime() {
            const now = new Date();
            this.currentTime = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },
        
        // 加载初始数据
        async loadInitialData() {
            try {
                // 并行加载所有数据
                const [
                    systemStatus,
                    realtimeMarket,
                    tradingSignals,
                    portfolio
                ] = await Promise.all([
                    this.fetchSystemStatus(),
                    this.fetchRealtimeMarket(),
                    this.fetchTradingSignals(),
                    this.fetchPortfolio()
                ]);
                
                console.log('✅ 初始数据加载完成');
            } catch (error) {
                console.error('❌ 初始数据加载失败:', error);
            }
        },
        
        // 获取系统状态
        async fetchSystemStatus() {
            try {
                const response = await axios.get(`${this.apiBaseUrl}/system/status`);
                this.systemStatus = response.data;
                return response.data;
            } catch (error) {
                console.error('获取系统状态失败:', error);
                this.systemStatus.status = 'error';
                this.systemStatus.connection_status = 'disconnected';
            }
        },
        
        // 获取实时行情
        async fetchRealtimeMarket() {
            try {
                const response = await axios.get(`${this.apiBaseUrl}/market/realtime`);
                this.realtimeStocks = response.data.stocks || [];
                return response.data;
            } catch (error) {
                console.error('获取实时行情失败:', error);
            }
        },
        
        // 获取交易信号
        async fetchTradingSignals() {
            try {
                const response = await axios.get(`${this.apiBaseUrl}/signals/all`);
                const data = response.data;
                
                // 合并买入和卖出信号
                this.tradingSignals = [
                    ...(data.buy_signals || []),
                    ...(data.sell_signals || [])
                ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                
                return data;
            } catch (error) {
                console.error('获取交易信号失败:', error);
            }
        },
        
        // 获取投资组合
        async fetchPortfolio() {
            try {
                const response = await axios.get(`${this.apiBaseUrl}/portfolio/current`);
                const data = response.data;
                
                this.portfolioPositions = data.positions || [];
                this.totalAssets = data.total_assets || 0;
                this.todayPnl = data.performance?.today_pnl || 0;
                
                return data;
            } catch (error) {
                console.error('获取投资组合失败:', error);
            }
        },
        
        // 建立WebSocket连接
        connectWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                this.websocket = new WebSocket(wsUrl);
                
                this.websocket.onopen = () => {
                    console.log('✅ WebSocket连接已建立');
                    this.systemStatus.connection_status = 'connected';
                };
                
                this.websocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('WebSocket消息解析失败:', error);
                    }
                };
                
                this.websocket.onclose = () => {
                    console.log('⚠️ WebSocket连接已断开');
                    this.systemStatus.connection_status = 'disconnected';
                    
                    // 5秒后尝试重连
                    setTimeout(() => {
                        this.connectWebSocket();
                    }, 5000);
                };
                
                this.websocket.onerror = (error) => {
                    console.error('❌ WebSocket连接错误:', error);
                    this.systemStatus.connection_status = 'disconnected';
                };
                
            } catch (error) {
                console.error('❌ WebSocket连接失败:', error);
            }
        },
        
        // 处理WebSocket消息
        handleWebSocketMessage(data) {
            switch (data.type) {
                case 'market_update':
                    // 处理市场数据更新
                    console.log('📊 收到市场数据更新:', data.data);
                    break;
                    
                case 'signal_update':
                    // 处理交易信号更新
                    console.log('📈 收到交易信号更新:', data.data);
                    this.fetchTradingSignals();
                    break;
                    
                case 'portfolio_update':
                    // 处理组合更新
                    console.log('💼 收到组合更新:', data.data);
                    this.fetchPortfolio();
                    break;
                    
                default:
                    console.log('收到未知类型消息:', data);
            }
        },
        
        // 定时更新数据
        async updateData() {
            try {
                // 更新实时行情
                await this.fetchRealtimeMarket();
                
                // 每30秒更新一次其他数据
                if (Date.now() % 30000 < 5000) {
                    await Promise.all([
                        this.fetchSystemStatus(),
                        this.fetchTradingSignals(),
                        this.fetchPortfolio()
                    ]);
                }
            } catch (error) {
                console.error('定时数据更新失败:', error);
            }
        },
        
        // 切换主标签
        switchTab(tabKey) {
            this.activeTab = tabKey;
            console.log(`切换到标签: ${tabKey}`);
        },
        
        // 切换视图
        switchView(viewKey) {
            this.activeView = viewKey;
            console.log(`切换到视图: ${viewKey}`);
        },
        
        // 切换时间周期
        switchTimeframe(timeframe) {
            this.activeTimeframe = timeframe;
            console.log(`切换时间周期: ${timeframe}`);
            // 这里可以更新K线图
            this.updateKlineChart();
        },
        
        // 更新K线图
        updateKlineChart() {
            // 这里应该调用ECharts更新K线图
            console.log(`更新K线图: ${this.activeTimeframe}`);
        },
        
        // 格式化时间
        formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },
        
        // 格式化数字
        formatNumber(num, decimals = 2) {
            return num.toFixed(decimals);
        },
        
        // 格式化货币
        formatCurrency(amount) {
            return `¥${amount.toLocaleString()}`;
        }
    }
}).mount('#app');
