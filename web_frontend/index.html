<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeighNa量化交易系统 v2.0</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <h1>📊 VeighNa量化交易系统 v2.0</h1>
            </div>
            <div class="header-right">
                <span class="connection-status" :class="systemStatus.connection_status">
                    {{ systemStatus.connection_status === 'connected' ? '连接正常' : '连接异常' }}
                </span>
                <span class="system-time">{{ currentTime }}</span>
                <span class="user-info">用户: Admin</span>
                <button class="settings-btn">⚙️ 设置</button>
            </div>
        </header>

        <!-- 主导航栏 -->
        <nav class="main-nav">
            <button 
                v-for="tab in mainTabs" 
                :key="tab.key"
                :class="['nav-btn', { active: activeTab === tab.key }]"
                @click="switchTab(tab.key)"
            >
                {{ tab.label }}
            </button>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧功能导航面板 -->
            <aside class="sidebar">
                <div class="function-nav">
                    <div class="nav-section">
                        <h3>📊 数据管理</h3>
                        <ul>
                            <li @click="switchView('data-collection')" :class="{ active: activeView === 'data-collection' }">
                                📈 数据采集
                            </li>
                            <li @click="switchView('data-storage')" :class="{ active: activeView === 'data-storage' }">
                                💾 数据存储
                            </li>
                            <li @click="switchView('data-quality')" :class="{ active: activeView === 'data-quality' }">
                                🔍 数据质量
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>🔍 智能选股</h3>
                        <ul>
                            <li @click="switchView('factor-config')" :class="{ active: activeView === 'factor-config' }">
                                🔧 因子配置
                            </li>
                            <li @click="switchView('stock-selection')" :class="{ active: activeView === 'stock-selection' }">
                                🎯 选股策略
                            </li>
                            <li @click="switchView('candidate-pool')" :class="{ active: activeView === 'candidate-pool' }">
                                📋 候选池
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>🚀 策略回测</h3>
                        <ul>
                            <li @click="switchView('vnpy-engine')" :class="{ active: activeView === 'vnpy-engine' }">
                                ⚡ VeighNa引擎
                            </li>
                            <li @click="switchView('strategy-management')" :class="{ active: activeView === 'strategy-management' }">
                                📈 策略管理
                            </li>
                            <li @click="switchView('parameter-optimization')" :class="{ active: activeView === 'parameter-optimization' }">
                                🎛️ 参数优化
                            </li>
                            <li @click="switchView('performance-analysis')" :class="{ active: activeView === 'performance-analysis' }">
                                📊 绩效分析
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>💼 组合管理</h3>
                        <ul>
                            <li @click="switchView('portfolio-construction')" :class="{ active: activeView === 'portfolio-construction' }">
                                🏗️ 组合构建
                            </li>
                            <li @click="switchView('weight-allocation')" :class="{ active: activeView === 'weight-allocation' }">
                                ⚖️ 权重分配
                            </li>
                            <li @click="switchView('rebalancing')" :class="{ active: activeView === 'rebalancing' }">
                                🔄 再平衡
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>⚡ 交易执行</h3>
                        <ul>
                            <li @click="switchView('order-management')" :class="{ active: activeView === 'order-management' }">
                                📋 订单管理
                            </li>
                            <li @click="switchView('trade-report')" :class="{ active: activeView === 'trade-report' }">
                                📊 成交回报
                            </li>
                            <li @click="switchView('risk-control')" :class="{ active: activeView === 'risk-control' }">
                                🛡️ 风险控制
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <h3>⚙️ 系统管理</h3>
                        <ul>
                            <li @click="switchView('system-config')" :class="{ active: activeView === 'system-config' }">
                                🔧 参数配置
                            </li>
                            <li @click="switchView('log-viewer')" :class="{ active: activeView === 'log-viewer' }">
                                📝 日志查看
                            </li>
                            <li @click="switchView('performance-monitor')" :class="{ active: activeView === 'performance-monitor' }">
                                📊 性能监控
                            </li>
                        </ul>
                    </div>
                </div>
            </aside>

            <!-- 主工作区域 -->
            <section class="workspace">
                <!-- 实时行情监控 -->
                <div class="realtime-monitor">
                    <h3>实时行情监控</h3>
                    <div class="stock-list">
                        <div 
                            v-for="stock in realtimeStocks" 
                            :key="stock.symbol"
                            class="stock-item"
                            :class="{ 
                                'price-up': stock.change > 0, 
                                'price-down': stock.change < 0 
                            }"
                        >
                            <div class="stock-code">{{ stock.symbol }}</div>
                            <div class="stock-name">{{ stock.name }}</div>
                            <div class="stock-price">{{ stock.price.toFixed(2) }}</div>
                            <div class="stock-change">
                                {{ stock.change > 0 ? '+' : '' }}{{ stock.change.toFixed(2) }}
                            </div>
                            <div class="stock-change-pct">
                                {{ stock.change_pct > 0 ? '+' : '' }}{{ stock.change_pct.toFixed(2) }}%
                                {{ stock.change > 0 ? '↗' : stock.change < 0 ? '↘' : '→' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- K线图表区域 -->
                <div class="chart-area">
                    <div class="chart-header">
                        <h3>K线图表区域</h3>
                        <div class="timeframe-buttons">
                            <button 
                                v-for="tf in timeframes" 
                                :key="tf"
                                :class="['tf-btn', { active: activeTimeframe === tf }]"
                                @click="switchTimeframe(tf)"
                            >
                                {{ tf }}
                            </button>
                        </div>
                    </div>
                    <div id="kline-chart" class="chart-container"></div>
                    <div class="chart-signals">
                        <div class="signal-legend">
                            <span class="buy-signal">🔴 买入信号标注</span>
                            <span class="sell-signal">🟢 卖出信号标注</span>
                        </div>
                    </div>
                </div>

                <!-- 交易信号面板 -->
                <div class="signal-panel">
                    <h3>交易信号面板</h3>
                    <div class="signal-list">
                        <div 
                            v-for="signal in tradingSignals" 
                            :key="signal.signal_id"
                            class="signal-item"
                            :class="signal.signal_type.toLowerCase()"
                        >
                            <span class="signal-icon">
                                {{ signal.signal_type === 'BUY' ? '🔴' : '🟢' }}
                            </span>
                            <span class="signal-type">
                                {{ signal.signal_type === 'BUY' ? '买入信号' : '卖出信号' }}
                            </span>
                            <span class="signal-stock">{{ signal.symbol }} {{ signal.name }}</span>
                            <span class="signal-score">评分:{{ signal.score.toFixed(1) }}</span>
                            <span class="signal-time">{{ formatTime(signal.timestamp) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 持仓明细 -->
                <div class="position-panel">
                    <h3>持仓明细</h3>
                    <table class="position-table">
                        <thead>
                            <tr>
                                <th>代码</th>
                                <th>名称</th>
                                <th>数量</th>
                                <th>成本价</th>
                                <th>现价</th>
                                <th>盈亏</th>
                                <th>比例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr 
                                v-for="position in portfolioPositions" 
                                :key="position.symbol"
                                :class="{ 
                                    'profit': position.pnl > 0, 
                                    'loss': position.pnl < 0 
                                }"
                            >
                                <td>{{ position.symbol }}</td>
                                <td>{{ position.name }}</td>
                                <td>{{ position.quantity.toLocaleString() }}</td>
                                <td>{{ position.avg_price.toFixed(2) }}</td>
                                <td>{{ position.current_price.toFixed(2) }}</td>
                                <td>{{ position.pnl > 0 ? '+' : '' }}{{ position.pnl.toFixed(0) }}</td>
                                <td>{{ position.weight.toFixed(1) }}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <span class="status-item">
                数据连接: <span :class="systemStatus.data_status">{{ systemStatus.data_status === 'normal' ? '正常' : '异常' }}</span>
            </span>
            <span class="status-item">
                策略运行: <span class="running">{{ runningStrategies }}个</span>
            </span>
            <span class="status-item">
                总资产: <span class="asset">¥{{ totalAssets.toLocaleString() }}</span>
            </span>
            <span class="status-item">
                今日盈亏: <span :class="{ profit: todayPnl > 0, loss: todayPnl < 0 }">
                    {{ todayPnl > 0 ? '+' : '' }}¥{{ todayPnl.toLocaleString() }}
                </span>
            </span>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>
