#!/usr/bin/env python3
"""
投资组合管理系统测试脚本
测试完整的投资组合管理功能：组合构建、权重分配、再平衡、绩效归因、组合优化
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import date, datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from portfolio_management import (
    PortfolioBuilder, WeightAllocator, RebalancingEngine,
    AttributionAnalyzer, PortfolioOptimizer
)
from portfolio_management.portfolio_builder.portfolio_builder import PortfolioType
from portfolio_management.weight_allocator.weight_allocator import WeightMethod
from portfolio_management.rebalancing.rebalancing_engine import RebalanceFrequency
from portfolio_management.portfolio_optimizer.portfolio_optimizer import OptimizationObjective

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_portfolio_builder():
    """测试投资组合构建器"""
    logger.info("🏗️ 测试投资组合构建器...")
    
    try:
        builder = PortfolioBuilder()
        
        # 构建成长型组合
        growth_portfolio = builder.build_portfolio(
            portfolio_type=PortfolioType.GROWTH,
            target_stocks=8
        )
        
        if growth_portfolio:
            logger.info(f"✅ 成长型组合构建成功")
            logger.info(f"  - 股票数量: {len(growth_portfolio.stocks)}只")
            logger.info(f"  - 预期收益: {growth_portfolio.expected_return:.2%}")
            logger.info(f"  - 预期风险: {growth_portfolio.expected_risk:.2%}")
            logger.info(f"  - 夏普比率: {growth_portfolio.sharpe_ratio:.2f}")
            
            # 显示前5大持仓
            top_holdings = sorted(growth_portfolio.stocks, key=lambda x: x.weight, reverse=True)[:5]
            logger.info("  前5大持仓:")
            for i, stock in enumerate(top_holdings, 1):
                logger.info(f"    {i}. {stock.symbol}: {stock.weight:.2%} (评分{stock.score:.1f})")
            
            return True
        else:
            logger.error("❌ 组合构建失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 投资组合构建器测试失败: {e}")
        return False

def test_weight_allocator():
    """测试权重分配器"""
    logger.info("⚖️ 测试权重分配器...")
    
    try:
        allocator = WeightAllocator()
        
        # 模拟股票数据
        mock_stocks = [
            {'symbol': '000001', 'total_score': 85, 'market_cap': 150, 'volatility': 0.18},
            {'symbol': '000002', 'total_score': 78, 'market_cap': 120, 'volatility': 0.22},
            {'symbol': '600000', 'total_score': 82, 'market_cap': 200, 'volatility': 0.16},
            {'symbol': '000858', 'total_score': 75, 'market_cap': 80, 'volatility': 0.25},
            {'symbol': '002415', 'total_score': 88, 'market_cap': 90, 'volatility': 0.20}
        ]
        
        # 测试不同权重分配方法
        methods = [
            WeightMethod.EQUAL_WEIGHT,
            WeightMethod.SCORE_WEIGHT,
            WeightMethod.RISK_PARITY,
            WeightMethod.MARKET_CAP
        ]
        
        results = {}
        
        for method in methods:
            allocations = allocator.allocate_weights(mock_stocks, method)
            
            if allocations:
                results[method.value] = allocations
                logger.info(f"  {method.value}: {len(allocations)}只股票")
                
                # 显示权重分配
                for alloc in allocations[:3]:
                    logger.info(f"    {alloc.symbol}: {alloc.weight:.2%}")
        
        logger.info(f"✅ 权重分配器测试完成: {len(results)}种方法")
        
        # 测试权重优化
        optimized_allocations = allocator.optimize_weights(
            mock_stocks, target_return=0.12, target_risk=0.18
        )
        
        if optimized_allocations:
            logger.info(f"  权重优化: {len(optimized_allocations)}只股票")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 权重分配器测试失败: {e}")
        return False

def test_rebalancing_engine():
    """测试再平衡引擎"""
    logger.info("🔄 测试再平衡引擎...")
    
    try:
        engine = RebalancingEngine()
        
        # 模拟当前组合
        current_portfolio = {
            '000001': {'shares': 1000, 'avg_price': 11.20},
            '000002': {'shares': 800, 'avg_price': 15.80},
            '600000': {'shares': 300, 'avg_price': 32.50}
        }
        
        # 目标权重
        target_weights = {
            '000001': 0.35,
            '000002': 0.30,
            '600000': 0.25,
            '000858': 0.10  # 新增股票
        }
        
        # 当前价格
        current_prices = {
            '000001': 11.50,
            '000002': 16.20,
            '600000': 33.80,
            '000858': 18.90
        }
        
        # 检查再平衡需求
        rebalance_check = engine.check_rebalance_need(
            current_portfolio, target_weights, current_prices
        )
        
        logger.info(f"✅ 再平衡检查完成")
        logger.info(f"  - 需要再平衡: {'是' if rebalance_check['need_rebalance'] else '否'}")
        logger.info(f"  - 触发条件: {len(rebalance_check['triggers'])}个")
        logger.info(f"  - 组合偏离: {rebalance_check['portfolio_drift']:.2%}")
        
        # 执行再平衡
        if rebalance_check['need_rebalance']:
            total_value = 50000  # 假设组合总价值5万
            
            rebalance_result = engine.execute_rebalance(
                current_portfolio, target_weights, current_prices, 
                total_value, "权重偏离触发"
            )
            
            if rebalance_result:
                logger.info(f"  再平衡执行成功:")
                logger.info(f"    - 操作数量: {len(rebalance_result.actions)}个")
                logger.info(f"    - 总换手率: {rebalance_result.total_turnover:.2%}")
                logger.info(f"    - 交易成本: ¥{rebalance_result.transaction_cost:.0f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 再平衡引擎测试失败: {e}")
        return False

def test_attribution_analyzer():
    """测试绩效归因分析器"""
    logger.info("📊 测试绩效归因分析器...")
    
    try:
        analyzer = AttributionAnalyzer()
        
        # 模拟组合数据
        portfolio_data = {
            'total_return': 0.15,  # 15%收益
            'stocks': [
                {
                    'symbol': '000001',
                    'weight': 0.25,
                    'score': 85,
                    'technical_score': 80,
                    'fundamental_score': 90,
                    'sector': '金融',
                    'market_cap': 150
                },
                {
                    'symbol': '000002',
                    'weight': 0.20,
                    'score': 78,
                    'technical_score': 75,
                    'fundamental_score': 80,
                    'sector': '科技',
                    'market_cap': 120
                },
                {
                    'symbol': '600000',
                    'weight': 0.30,
                    'score': 82,
                    'technical_score': 85,
                    'fundamental_score': 78,
                    'sector': '金融',
                    'market_cap': 200
                },
                {
                    'symbol': '000858',
                    'weight': 0.15,
                    'score': 75,
                    'technical_score': 70,
                    'fundamental_score': 80,
                    'sector': '消费',
                    'market_cap': 80
                },
                {
                    'symbol': '002415',
                    'weight': 0.10,
                    'score': 88,
                    'technical_score': 90,
                    'fundamental_score': 85,
                    'sector': '科技',
                    'market_cap': 90
                }
            ]
        }
        
        # 执行归因分析
        attribution_result = analyzer.analyze_performance_attribution(
            portfolio_data, period_days=252
        )
        
        if attribution_result:
            logger.info(f"✅ 绩效归因分析完成")
            logger.info(f"  - 组合收益: {attribution_result['portfolio_return']:.2%}")
            logger.info(f"  - 超额收益: {attribution_result['excess_return']:.2%}")
            logger.info(f"  - 股票归因: {len(attribution_result['stock_attributions'])}只")
            logger.info(f"  - 行业归因: {len(attribution_result['sector_attributions'])}个")
            
            # 显示主要贡献股票
            stock_attributions = attribution_result['stock_attributions']
            if stock_attributions:
                logger.info("  主要贡献股票:")
                for i, attr in enumerate(stock_attributions[:3], 1):
                    logger.info(f"    {i}. {attr['symbol']}: 贡献{attr['total_contribution']:.2%}")
            
            # 生成归因报告
            report = analyzer.generate_attribution_report(attribution_result)
            logger.info("  归因报告已生成")
            
            return True
        else:
            logger.error("❌ 归因分析失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 绩效归因分析器测试失败: {e}")
        return False

def test_portfolio_optimizer():
    """测试投资组合优化器"""
    logger.info("🎯 测试投资组合优化器...")
    
    try:
        optimizer = PortfolioOptimizer()
        
        # 模拟股票数据
        stocks_data = [
            {'symbol': '000001', 'total_score': 85, 'sector': '金融', 'market_cap': 150},
            {'symbol': '000002', 'total_score': 78, 'sector': '科技', 'market_cap': 120},
            {'symbol': '600000', 'total_score': 82, 'sector': '金融', 'market_cap': 200},
            {'symbol': '000858', 'total_score': 75, 'sector': '消费', 'market_cap': 80},
            {'symbol': '002415', 'total_score': 88, 'sector': '科技', 'market_cap': 90}
        ]
        
        # 测试不同优化目标
        objectives = [
            OptimizationObjective.MAX_SHARPE,
            OptimizationObjective.MIN_VARIANCE,
            OptimizationObjective.RISK_PARITY
        ]
        
        optimization_results = {}
        
        for objective in objectives:
            result = optimizer.optimize_portfolio(
                stocks_data=stocks_data,
                objective=objective
            )
            
            if result.optimization_success:
                optimization_results[objective.value] = result
                logger.info(f"  {objective.value}: 优化成功")
                logger.info(f"    - 预期收益: {result.expected_return:.2%}")
                logger.info(f"    - 预期风险: {result.expected_risk:.2%}")
                logger.info(f"    - 夏普比率: {result.sharpe_ratio:.2f}")
                
                # 显示前3大权重
                sorted_weights = sorted(result.optimal_weights.items(), 
                                      key=lambda x: x[1], reverse=True)
                for symbol, weight in sorted_weights[:3]:
                    logger.info(f"      {symbol}: {weight:.2%}")
            else:
                logger.warning(f"  {objective.value}: 优化失败")
        
        logger.info(f"✅ 投资组合优化器测试完成: {len(optimization_results)}个成功")
        
        # 测试有效前沿
        efficient_frontier = optimizer.generate_efficient_frontier(stocks_data, n_points=10)
        
        if efficient_frontier['efficient_portfolios']:
            logger.info(f"  有效前沿: {efficient_frontier['n_points']}个有效组合")
            logger.info(f"  风险范围: {efficient_frontier['risk_range'][0]:.2%} - {efficient_frontier['risk_range'][1]:.2%}")
            logger.info(f"  收益范围: {efficient_frontier['return_range'][0]:.2%} - {efficient_frontier['return_range'][1]:.2%}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 投资组合优化器测试失败: {e}")
        return False

def test_integrated_workflow():
    """测试集成工作流"""
    logger.info("🔄 测试集成工作流...")
    
    try:
        # 1. 构建组合
        builder = PortfolioBuilder()
        portfolio = builder.build_portfolio(PortfolioType.BALANCED, target_stocks=6)
        
        if not portfolio:
            logger.error("❌ 组合构建失败")
            return False
        
        # 2. 权重优化
        optimizer = PortfolioOptimizer()
        stocks_data = [
            {
                'symbol': stock.symbol,
                'total_score': stock.score,
                'sector': stock.sector,
                'market_cap': stock.market_cap
            }
            for stock in portfolio.stocks
        ]
        
        optimization_result = optimizer.optimize_portfolio(
            stocks_data, OptimizationObjective.MAX_SHARPE
        )
        
        # 3. 绩效归因
        if optimization_result.optimization_success:
            portfolio_data = {
                'total_return': optimization_result.expected_return,
                'stocks': [
                    {
                        'symbol': symbol,
                        'weight': weight,
                        'score': next(s['total_score'] for s in stocks_data if s['symbol'] == symbol),
                        'technical_score': 75,  # 模拟值
                        'fundamental_score': 80,  # 模拟值
                        'sector': next(s['sector'] for s in stocks_data if s['symbol'] == symbol),
                        'market_cap': next(s['market_cap'] for s in stocks_data if s['symbol'] == symbol)
                    }
                    for symbol, weight in optimization_result.optimal_weights.items()
                ]
            }
            
            analyzer = AttributionAnalyzer()
            attribution_result = analyzer.analyze_performance_attribution(portfolio_data)
        
        logger.info(f"✅ 集成工作流测试完成")
        logger.info(f"  - 组合构建: {'成功' if portfolio else '失败'}")
        logger.info(f"  - 权重优化: {'成功' if optimization_result.optimization_success else '失败'}")
        logger.info(f"  - 绩效归因: {'成功' if attribution_result else '失败'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成工作流测试失败: {e}")
        return False

async def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🧪 开始投资组合管理系统综合测试...")
    logger.info("=" * 80)
    
    test_results = []
    
    try:
        # 1. 测试投资组合构建器
        builder_result = test_portfolio_builder()
        test_results.append(("投资组合构建器", builder_result))
        logger.info("=" * 80)
        
        # 2. 测试权重分配器
        allocator_result = test_weight_allocator()
        test_results.append(("权重分配器", allocator_result))
        logger.info("=" * 80)
        
        # 3. 测试再平衡引擎
        rebalancing_result = test_rebalancing_engine()
        test_results.append(("再平衡引擎", rebalancing_result))
        logger.info("=" * 80)
        
        # 4. 测试绩效归因分析器
        attribution_result = test_attribution_analyzer()
        test_results.append(("绩效归因分析器", attribution_result))
        logger.info("=" * 80)
        
        # 5. 测试投资组合优化器
        optimizer_result = test_portfolio_optimizer()
        test_results.append(("投资组合优化器", optimizer_result))
        logger.info("=" * 80)
        
        # 6. 测试集成工作流
        workflow_result = test_integrated_workflow()
        test_results.append(("集成工作流", workflow_result))
        logger.info("=" * 80)
        
        # 测试结果总结
        logger.info("📊 测试结果总结:")
        success_count = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  - {test_name}: {status}")
            if result:
                success_count += 1
        
        logger.info(f"🎉 投资组合管理系统测试完成: {success_count}/{len(test_results)} 项通过")
        
        if success_count == len(test_results):
            logger.info("🎊 所有测试均通过！投资组合管理系统功能正常")
        else:
            logger.warning("⚠️ 部分测试失败，请检查相关功能")
        
        # 系统功能总结
        logger.info("\n🚀 投资组合管理系统功能总结:")
        logger.info("  ✅ 投资组合构建器 - 多类型组合构建，风险评估")
        logger.info("  ✅ 权重分配器 - 多种权重分配策略，权重优化")
        logger.info("  ✅ 再平衡引擎 - 动态再平衡，多触发条件")
        logger.info("  ✅ 绩效归因分析器 - 股票/行业/因子归因")
        logger.info("  ✅ 投资组合优化器 - 现代投资组合理论，有效前沿")
        logger.info("  ✅ 集成工作流 - 完整的组合管理流程")
        
    except Exception as e:
        logger.error(f"❌ 综合测试过程中发生异常: {e}")

async def main():
    """主函数"""
    print("🚀 投资组合管理系统测试")
    print("=" * 80)
    print("专业的投资组合管理平台")
    print("包括组合构建、权重分配、再平衡、绩效归因、组合优化")
    print("=" * 80)
    
    await run_comprehensive_test()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
