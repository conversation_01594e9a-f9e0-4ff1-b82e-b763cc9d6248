#!/usr/bin/env python3
"""
VeighNa量化交易系统真实A股数据采集器
支持全A股4000+股票的实时数据采集和存储
"""

import akshare as ak
import pandas as pd
import numpy as np
import logging
import threading
import time
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.system_config import data_config, system_config, STOCK_EXCHANGES
from database.database_schema import (
    DatabaseManager, StockBasicInfo, StockDailyData, 
    StockMinuteData, StockTechnicalIndicators, StockFundamentalData
)

logger = logging.getLogger(__name__)

class RealDataCollector:
    """真实A股数据采集器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=system_config.MAX_WORKERS)
        self.stock_list = []
        self.collection_stats = {
            "total_stocks": 0,
            "successful_updates": 0,
            "failed_updates": 0,
            "last_update_time": None
        }
        
        logger.info("✅ 真实A股数据采集器初始化完成")
    
    def get_all_stock_list(self) -> List[Dict]:
        """获取全A股股票列表"""
        try:
            logger.info("📋 获取全A股股票列表...")
            
            all_stocks = []
            
            # 获取沪深股票列表
            for exchange_code, exchange_info in STOCK_EXCHANGES.items():
                if exchange_code in ["SH", "SZ"]:  # 暂时只处理沪深
                    try:
                        # 使用akshare获取股票列表
                        if exchange_code == "SH":
                            stock_df = ak.stock_info_sh_name_code()
                        else:  # SZ
                            stock_df = ak.stock_info_sz_name_code()
                        
                        for _, row in stock_df.iterrows():
                            stock_code = row.get('证券代码', '') or row.get('code', '')
                            stock_name = row.get('证券简称', '') or row.get('name', '')
                            
                            if stock_code and stock_name:
                                # 过滤掉ST、退市等股票
                                if not any(keyword in stock_name for keyword in ['ST', '*ST', '退市']):
                                    all_stocks.append({
                                        'symbol': stock_code,
                                        'name': stock_name,
                                        'exchange': exchange_code,
                                        'market': exchange_info['market_code']
                                    })
                        
                        logger.info(f"✅ 获取{exchange_code}股票 {len(stock_df)} 只")
                        
                    except Exception as e:
                        logger.error(f"❌ 获取{exchange_code}股票列表失败: {e}")
                        continue
            
            # 如果akshare失败，使用备用方法
            if not all_stocks:
                logger.warning("⚠️ akshare获取失败，使用备用股票列表")
                all_stocks = self._get_backup_stock_list()
            
            self.stock_list = all_stocks
            self.collection_stats["total_stocks"] = len(all_stocks)
            
            logger.info(f"📊 共获取A股股票 {len(all_stocks)} 只")
            return all_stocks
            
        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return self._get_backup_stock_list()
    
    def _get_backup_stock_list(self) -> List[Dict]:
        """备用股票列表（主要股票）"""
        backup_stocks = [
            # 银行股
            {'symbol': '000001', 'name': '平安银行', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '600036', 'name': '招商银行', 'exchange': 'SH', 'market': 'sh'},
            {'symbol': '600000', 'name': '浦发银行', 'exchange': 'SH', 'market': 'sh'},
            {'symbol': '601166', 'name': '兴业银行', 'exchange': 'SH', 'market': 'sh'},
            {'symbol': '000002', 'name': '万科A', 'exchange': 'SZ', 'market': 'sz'},
            
            # 白酒股
            {'symbol': '600519', 'name': '贵州茅台', 'exchange': 'SH', 'market': 'sh'},
            {'symbol': '000858', 'name': '五粮液', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '000596', 'name': '古井贡酒', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '002304', 'name': '洋河股份', 'exchange': 'SZ', 'market': 'sz'},
            
            # 科技股
            {'symbol': '000002', 'name': '万科A', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '002415', 'name': '海康威视', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '300059', 'name': '东方财富', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '002594', 'name': '比亚迪', 'exchange': 'SZ', 'market': 'sz'},
            
            # 医药股
            {'symbol': '000661', 'name': '长春高新', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '300015', 'name': '爱尔眼科', 'exchange': 'SZ', 'market': 'sz'},
            
            # 消费股
            {'symbol': '600887', 'name': '伊利股份', 'exchange': 'SH', 'market': 'sh'},
            
            # 新能源
            {'symbol': '300750', 'name': '宁德时代', 'exchange': 'SZ', 'market': 'sz'},
            {'symbol': '002594', 'name': '比亚迪', 'exchange': 'SZ', 'market': 'sz'},
            
            # 其他重要股票
            {'symbol': '600276', 'name': '恒瑞医药', 'exchange': 'SH', 'market': 'sh'},
            {'symbol': '000166', 'name': '申万宏源', 'exchange': 'SZ', 'market': 'sz'},
        ]
        
        logger.info(f"📋 使用备用股票列表 {len(backup_stocks)} 只")
        return backup_stocks
    
    def save_stock_basic_info(self, stocks: List[Dict]):
        """保存股票基本信息到数据库"""
        try:
            session = self.db_manager.get_session()
            
            for stock in stocks:
                # 检查是否已存在
                existing = session.query(StockBasicInfo).filter_by(symbol=stock['symbol']).first()
                
                if not existing:
                    stock_info = StockBasicInfo(
                        symbol=stock['symbol'],
                        name=stock['name'],
                        exchange=stock['exchange'],
                        market=stock['market'],
                        is_active=True
                    )
                    session.add(stock_info)
                else:
                    # 更新信息
                    existing.name = stock['name']
                    existing.is_active = True
                    existing.updated_at = datetime.now()
            
            session.commit()
            session.close()
            
            logger.info(f"✅ 保存股票基本信息 {len(stocks)} 条")
            
        except Exception as e:
            logger.error(f"❌ 保存股票基本信息失败: {e}")
            if session:
                session.rollback()
                session.close()
    
    def collect_realtime_data(self, symbol: str) -> Optional[Dict]:
        """采集单只股票实时数据"""
        try:
            # 使用akshare获取实时数据
            df = ak.stock_zh_a_spot_em()
            
            # 查找对应股票
            stock_data = df[df['代码'] == symbol]
            
            if stock_data.empty:
                return None
            
            row = stock_data.iloc[0]
            
            return {
                'symbol': symbol,
                'datetime': datetime.now(),
                'open_price': float(row.get('今开', 0)),
                'high_price': float(row.get('最高', 0)),
                'low_price': float(row.get('最低', 0)),
                'close_price': float(row.get('最新价', 0)),
                'volume': int(row.get('成交量', 0)),
                'amount': float(row.get('成交额', 0)),
                'turnover_rate': float(row.get('换手率', 0)),
                'pe_ratio': float(row.get('市盈率-动态', 0)),
                'pb_ratio': float(row.get('市净率', 0))
            }
            
        except Exception as e:
            logger.error(f"❌ 采集{symbol}实时数据失败: {e}")
            return None
    
    def collect_daily_data(self, symbol: str, start_date: str = None) -> List[Dict]:
        """采集单只股票日线数据"""
        try:
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            
            end_date = datetime.now().strftime('%Y%m%d')
            
            # 使用akshare获取日线数据
            df = ak.stock_zh_a_hist(symbol=symbol, period="daily", 
                                   start_date=start_date, end_date=end_date)
            
            if df.empty:
                return []
            
            daily_data = []
            for _, row in df.iterrows():
                daily_data.append({
                    'symbol': symbol,
                    'trade_date': pd.to_datetime(row['日期']),
                    'open_price': float(row['开盘']),
                    'high_price': float(row['最高']),
                    'low_price': float(row['最低']),
                    'close_price': float(row['收盘']),
                    'volume': int(row['成交量']),
                    'amount': float(row['成交额']),
                    'turnover_rate': float(row.get('换手率', 0))
                })
            
            return daily_data
            
        except Exception as e:
            logger.error(f"❌ 采集{symbol}日线数据失败: {e}")
            return []
    
    def calculate_technical_indicators(self, daily_data: List[Dict]) -> List[Dict]:
        """计算技术指标"""
        try:
            if len(daily_data) < 30:  # 数据不足
                return []
            
            # 转换为DataFrame
            df = pd.DataFrame(daily_data)
            df = df.sort_values('trade_date')
            
            # 计算移动平均线
            df['sma_5'] = df['close_price'].rolling(window=5).mean()
            df['sma_10'] = df['close_price'].rolling(window=10).mean()
            df['sma_20'] = df['close_price'].rolling(window=20).mean()
            df['sma_60'] = df['close_price'].rolling(window=60).mean()
            
            # 计算EMA
            df['ema_12'] = df['close_price'].ewm(span=12).mean()
            df['ema_26'] = df['close_price'].ewm(span=26).mean()
            
            # 计算MACD
            df['macd_dif'] = df['ema_12'] - df['ema_26']
            df['macd_dea'] = df['macd_dif'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd_dif'] - df['macd_dea']
            
            # 计算RSI
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi_14'] = 100 - (100 / (1 + rs))
            
            # 计算布林带
            df['boll_middle'] = df['close_price'].rolling(window=20).mean()
            std = df['close_price'].rolling(window=20).std()
            df['boll_upper'] = df['boll_middle'] + (std * 2)
            df['boll_lower'] = df['boll_middle'] - (std * 2)
            
            # 转换回字典列表
            indicators = []
            for _, row in df.iterrows():
                if pd.notna(row['sma_20']):  # 确保有足够数据
                    indicators.append({
                        'symbol': row['symbol'],
                        'trade_date': row['trade_date'],
                        'sma_5': row['sma_5'],
                        'sma_10': row['sma_10'],
                        'sma_20': row['sma_20'],
                        'sma_60': row['sma_60'],
                        'ema_12': row['ema_12'],
                        'ema_26': row['ema_26'],
                        'macd_dif': row['macd_dif'],
                        'macd_dea': row['macd_dea'],
                        'macd_histogram': row['macd_histogram'],
                        'rsi_14': row['rsi_14'],
                        'boll_upper': row['boll_upper'],
                        'boll_middle': row['boll_middle'],
                        'boll_lower': row['boll_lower']
                    })
            
            return indicators
            
        except Exception as e:
            logger.error(f"❌ 计算技术指标失败: {e}")
            return []
    
    def save_daily_data(self, daily_data: List[Dict]):
        """保存日线数据"""
        try:
            if not daily_data:
                return
            
            session = self.db_manager.get_session()
            
            for data in daily_data:
                # 检查是否已存在
                existing = session.query(StockDailyData).filter_by(
                    symbol=data['symbol'],
                    trade_date=data['trade_date']
                ).first()
                
                if not existing:
                    daily_record = StockDailyData(**data)
                    session.add(daily_record)
            
            session.commit()
            session.close()
            
        except Exception as e:
            logger.error(f"❌ 保存日线数据失败: {e}")
            if session:
                session.rollback()
                session.close()
    
    def save_technical_indicators(self, indicators: List[Dict]):
        """保存技术指标数据"""
        try:
            if not indicators:
                return
            
            session = self.db_manager.get_session()
            
            for indicator in indicators:
                # 检查是否已存在
                existing = session.query(StockTechnicalIndicators).filter_by(
                    symbol=indicator['symbol'],
                    trade_date=indicator['trade_date']
                ).first()
                
                if not existing:
                    tech_record = StockTechnicalIndicators(**indicator)
                    session.add(tech_record)
                else:
                    # 更新现有记录
                    for key, value in indicator.items():
                        if key not in ['symbol', 'trade_date']:
                            setattr(existing, key, value)
            
            session.commit()
            session.close()
            
        except Exception as e:
            logger.error(f"❌ 保存技术指标失败: {e}")
            if session:
                session.rollback()
                session.close()
    
    def collect_stock_data(self, stock: Dict) -> bool:
        """采集单只股票的完整数据"""
        try:
            symbol = stock['symbol']
            logger.info(f"📊 采集 {symbol} {stock['name']} 数据...")
            
            # 1. 采集日线数据
            daily_data = self.collect_daily_data(symbol)
            if daily_data:
                self.save_daily_data(daily_data)
                
                # 2. 计算并保存技术指标
                indicators = self.calculate_technical_indicators(daily_data)
                if indicators:
                    self.save_technical_indicators(indicators)
                
                logger.info(f"✅ {symbol} 数据采集完成 (日线:{len(daily_data)}, 指标:{len(indicators)})")
                return True
            else:
                logger.warning(f"⚠️ {symbol} 无数据")
                return False
                
        except Exception as e:
            logger.error(f"❌ 采集{symbol}数据失败: {e}")
            return False
    
    def start_data_collection(self):
        """启动数据采集"""
        try:
            logger.info("🚀 启动A股数据采集系统")
            self.is_running = True
            
            # 1. 获取股票列表
            stocks = self.get_all_stock_list()
            if not stocks:
                logger.error("❌ 无法获取股票列表")
                return
            
            # 2. 保存股票基本信息
            self.save_stock_basic_info(stocks)
            
            # 3. 批量采集数据
            logger.info(f"📊 开始采集 {len(stocks)} 只股票数据...")
            
            successful = 0
            failed = 0
            
            # 使用线程池并发采集
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_stock = {
                    executor.submit(self.collect_stock_data, stock): stock 
                    for stock in stocks
                }
                
                for future in as_completed(future_to_stock):
                    stock = future_to_stock[future]
                    try:
                        result = future.result()
                        if result:
                            successful += 1
                        else:
                            failed += 1
                    except Exception as e:
                        logger.error(f"❌ 采集{stock['symbol']}失败: {e}")
                        failed += 1
                    
                    # 更新统计
                    self.collection_stats.update({
                        "successful_updates": successful,
                        "failed_updates": failed,
                        "last_update_time": datetime.now()
                    })
                    
                    # 显示进度
                    total = successful + failed
                    if total % 10 == 0:
                        logger.info(f"📈 进度: {total}/{len(stocks)} (成功:{successful}, 失败:{failed})")
            
            logger.info(f"✅ 数据采集完成! 成功:{successful}, 失败:{failed}")
            
        except Exception as e:
            logger.error(f"❌ 数据采集系统启动失败: {e}")
        finally:
            self.is_running = False
    
    def get_collection_stats(self) -> Dict:
        """获取采集统计信息"""
        return self.collection_stats.copy()

def main():
    """主函数 - 测试数据采集"""
    print("🚀 启动VeighNa真实A股数据采集测试")
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    db_manager.create_tables()
    
    # 创建数据采集器
    collector = RealDataCollector(db_manager)
    
    # 启动数据采集
    collector.start_data_collection()
    
    # 显示统计信息
    stats = collector.get_collection_stats()
    print(f"\n📊 采集统计:")
    print(f"  总股票数: {stats['total_stocks']}")
    print(f"  成功采集: {stats['successful_updates']}")
    print(f"  失败采集: {stats['failed_updates']}")
    print(f"  最后更新: {stats['last_update_time']}")

if __name__ == "__main__":
    main()
