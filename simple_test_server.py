#!/usr/bin/env python3
"""
简单测试服务器 - 验证网络连接
"""

from flask import Flask, jsonify
import threading
import time

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>VeighNa测试服务器</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 50px;
                background: #1a1f2e;
                color: white;
            }
            .success { color: #00ff88; }
            .info { color: #00d4ff; }
        </style>
    </head>
    <body>
        <h1 class="success">🎉 VeighNa测试服务器运行正常！</h1>
        <p class="info">如果您能看到这个页面，说明网络连接正常。</p>
        <p>当前时间: <span id="time"></span></p>
        <p><a href="/api/test" style="color: #00d4ff;">测试API接口</a></p>
        <script>
            setInterval(() => {
                document.getElementById('time').textContent = new Date().toLocaleString();
            }, 1000);
        </script>
    </body>
    </html>
    '''

@app.route('/api/test')
def api_test():
    return jsonify({
        'status': 'success',
        'message': 'API接口正常工作',
        'timestamp': time.time(),
        'server': 'VeighNa Test Server'
    })

def start_test_server():
    print("🚀 启动VeighNa测试服务器")
    print("=" * 40)
    print("🌐 访问地址: http://localhost:9999")
    print("📊 API测试: http://localhost:9999/api/test")
    print("=" * 40)
    
    app.run(host='0.0.0.0', port=9999, debug=False)

if __name__ == "__main__":
    start_test_server()
