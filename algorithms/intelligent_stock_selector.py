#!/usr/bin/env python3
"""
智能选股算法
从5000+股票中通过多因子模型筛选出优质股票，实现真实的智能推荐功能
"""

import pandas as pd
import numpy as np
import sqlite3
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SelectionCriteria:
    """选股标准"""
    min_market_cap: float = 1000000000  # 最小市值10亿
    max_pe_ratio: float = 50  # 最大市盈率
    min_roe: float = 5  # 最小ROE
    min_volume: int = 1000000  # 最小成交量100万手
    min_score: float = 70  # 最小综合评分
    max_selections: int = 50  # 最大选股数量

@dataclass
class StockScore:
    """股票评分"""
    symbol: str
    name: str
    technical_score: float
    fundamental_score: float
    market_score: float
    momentum_score: float
    quality_score: float
    total_score: float
    rank: int
    recommendation: str
    reason: str

class IntelligentStockSelector:
    """智能选股器"""
    
    def __init__(self, db_path='vnpy_trading.db'):
        self.db_path = db_path
        self.selection_criteria = SelectionCriteria()
        self.scaler = StandardScaler()
        self.ml_model = RandomForestRegressor(n_estimators=100, random_state=42)
        
        # 因子权重配置
        self.factor_weights = {
            'technical': 0.25,    # 技术面权重
            'fundamental': 0.30,  # 基本面权重
            'market': 0.15,       # 市场表现权重
            'momentum': 0.15,     # 动量因子权重
            'quality': 0.15       # 质量因子权重
        }
        
        # 选股历史
        self.selection_history = []
        
        logger.info("✅ 智能选股器初始化完成")
    
    def run_intelligent_selection(self, criteria: Optional[SelectionCriteria] = None) -> List[StockScore]:
        """运行智能选股"""
        try:
            logger.info("🎯 开始智能选股...")
            
            if criteria:
                self.selection_criteria = criteria
            
            # 1. 获取全部股票数据
            all_stocks_data = self._fetch_all_stocks_data()
            logger.info(f"📊 获取到 {len(all_stocks_data)} 只股票数据")
            
            if len(all_stocks_data) < 100:
                logger.warning("⚠️ 股票数据不足，无法进行有效选股")
                return []
            
            # 2. 数据预处理和筛选
            filtered_stocks = self._filter_stocks(all_stocks_data)
            logger.info(f"📋 筛选后剩余 {len(filtered_stocks)} 只股票")
            
            # 3. 计算多因子评分
            scored_stocks = self._calculate_multi_factor_scores(filtered_stocks)
            logger.info(f"🔢 完成 {len(scored_stocks)} 只股票评分")
            
            # 4. 机器学习增强评分
            enhanced_stocks = self._enhance_with_ml(scored_stocks)
            logger.info(f"🤖 机器学习增强完成")
            
            # 5. 排序和选择
            selected_stocks = self._rank_and_select(enhanced_stocks)
            logger.info(f"🏆 最终选出 {len(selected_stocks)} 只优质股票")
            
            # 6. 保存选股结果
            self._save_selection_results(selected_stocks)
            
            return selected_stocks
            
        except Exception as e:
            logger.error(f"❌ 智能选股失败: {e}")
            return []
    
    def _fetch_all_stocks_data(self) -> List[Dict[str, Any]]:
        """获取全部股票数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取有完整数据的股票
            query = """
                SELECT s.symbol, s.name, s.exchange, s.market_cap,
                       d.close_price, d.open_price, d.high_price, d.low_price, 
                       d.volume, d.amount, d.turnover_rate, d.pe_ratio, d.pb_ratio,
                       t.macd_dif, t.macd_dea, t.macd_histogram, t.rsi_14, 
                       t.sma_5, t.sma_10, t.sma_20, t.sma_60, t.ema_12, t.ema_26,
                       t.boll_upper, t.boll_middle, t.boll_lower,
                       f.roe, f.gross_profit_margin, f.net_profit_margin,
                       f.revenue_growth, f.profit_growth, f.eps_growth, f.ps_ratio
                FROM stock_basic_info s
                INNER JOIN stock_daily_data d ON s.symbol = d.symbol
                INNER JOIN stock_technical_indicators t ON s.symbol = t.symbol
                INNER JOIN stock_fundamental_data f ON s.symbol = f.symbol
                WHERE d.trade_date = (SELECT MAX(trade_date) FROM stock_daily_data WHERE symbol = s.symbol)
                  AND t.trade_date = (SELECT MAX(trade_date) FROM stock_technical_indicators WHERE symbol = s.symbol)
                  AND s.is_active = 1
                ORDER BY s.symbol
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # 转换为字典格式
            columns = [
                'symbol', 'name', 'exchange', 'market_cap',
                'close_price', 'open_price', 'high_price', 'low_price',
                'volume', 'amount', 'turnover_rate', 'pe_ratio', 'pb_ratio',
                'macd_dif', 'macd_dea', 'macd_histogram', 'rsi_14',
                'sma_5', 'sma_10', 'sma_20', 'sma_60', 'ema_12', 'ema_26',
                'boll_upper', 'boll_middle', 'boll_lower',
                'roe', 'gross_profit_margin', 'net_profit_margin',
                'revenue_growth', 'profit_growth', 'eps_growth', 'ps_ratio'
            ]
            
            stocks_data = []
            for row in results:
                stock_dict = dict(zip(columns, row))
                
                # 数据类型转换和空值处理
                for key, value in stock_dict.items():
                    if key in ['symbol', 'name', 'exchange']:
                        continue
                    stock_dict[key] = float(value) if value is not None else 0.0
                
                stocks_data.append(stock_dict)
            
            conn.close()
            return stocks_data
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return []
    
    def _filter_stocks(self, stocks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选股票"""
        try:
            filtered_stocks = []
            
            for stock in stocks_data:
                # 基本筛选条件
                if (stock['market_cap'] >= self.selection_criteria.min_market_cap and
                    0 < stock['pe_ratio'] <= self.selection_criteria.max_pe_ratio and
                    stock['roe'] >= self.selection_criteria.min_roe and
                    stock['volume'] >= self.selection_criteria.min_volume and
                    stock['close_price'] > 2.0 and  # 价格大于2元
                    stock['turnover_rate'] > 0.1):  # 有一定流动性
                    
                    # 排除异常数据
                    if (stock['pb_ratio'] > 0 and stock['pb_ratio'] < 20 and
                        stock['gross_profit_margin'] > 0 and
                        abs(stock['revenue_growth']) < 200):  # 排除异常增长率
                        
                        filtered_stocks.append(stock)
            
            return filtered_stocks
            
        except Exception as e:
            logger.error(f"筛选股票失败: {e}")
            return stocks_data
    
    def _calculate_multi_factor_scores(self, stocks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """计算多因子评分"""
        try:
            scored_stocks = []
            
            for stock in stocks_data:
                # 计算各个因子评分
                technical_score = self._calculate_technical_factor(stock)
                fundamental_score = self._calculate_fundamental_factor(stock)
                market_score = self._calculate_market_factor(stock)
                momentum_score = self._calculate_momentum_factor(stock)
                quality_score = self._calculate_quality_factor(stock)
                
                # 计算综合评分
                total_score = (
                    technical_score * self.factor_weights['technical'] +
                    fundamental_score * self.factor_weights['fundamental'] +
                    market_score * self.factor_weights['market'] +
                    momentum_score * self.factor_weights['momentum'] +
                    quality_score * self.factor_weights['quality']
                )
                
                # 添加评分信息
                stock['technical_score'] = round(technical_score, 2)
                stock['fundamental_score'] = round(fundamental_score, 2)
                stock['market_score'] = round(market_score, 2)
                stock['momentum_score'] = round(momentum_score, 2)
                stock['quality_score'] = round(quality_score, 2)
                stock['total_score'] = round(total_score, 2)
                
                scored_stocks.append(stock)
            
            return scored_stocks
            
        except Exception as e:
            logger.error(f"计算多因子评分失败: {e}")
            return stocks_data
    
    def _calculate_technical_factor(self, stock: Dict[str, Any]) -> float:
        """计算技术因子评分"""
        try:
            score = 0
            
            # MACD指标 (25分)
            macd_dif = stock.get('macd_dif', 0)
            macd_dea = stock.get('macd_dea', 0)
            
            if macd_dif > macd_dea and macd_dif > 0:
                score += 25  # MACD金叉且在零轴上方
            elif macd_dif > macd_dea:
                score += 15  # MACD金叉
            elif macd_dif > 0:
                score += 10  # DIF在零轴上方
            
            # RSI指标 (20分)
            rsi = stock.get('rsi_14', 50)
            if 30 <= rsi <= 70:
                score += 20  # RSI在正常区间
            elif 20 <= rsi < 30:
                score += 25  # RSI超卖区域
            elif rsi < 20:
                score += 15  # RSI严重超卖
            
            # 均线系统 (25分)
            close_price = stock.get('close_price', 0)
            sma_5 = stock.get('sma_5', 0)
            sma_10 = stock.get('sma_10', 0)
            sma_20 = stock.get('sma_20', 0)
            
            if close_price > sma_5 > sma_10 > sma_20:
                score += 25  # 多头排列
            elif close_price > sma_20:
                score += 15  # 价格在20日均线上方
            elif close_price > sma_10:
                score += 10  # 价格在10日均线上方
            
            # 布林带位置 (15分)
            boll_upper = stock.get('boll_upper', 0)
            boll_lower = stock.get('boll_lower', 0)
            boll_middle = stock.get('boll_middle', 0)
            
            if boll_lower < close_price < boll_middle:
                score += 15  # 在布林带下轨和中轨之间
            elif boll_middle < close_price < boll_upper:
                score += 10  # 在布林带中轨和上轨之间
            
            # 成交量确认 (15分)
            volume = stock.get('volume', 0)
            turnover_rate = stock.get('turnover_rate', 0)
            
            if volume > 5000000 and turnover_rate > 2:
                score += 15  # 大成交量确认
            elif volume > 2000000 and turnover_rate > 1:
                score += 10  # 中等成交量
            elif volume > 1000000:
                score += 5   # 基本成交量
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算技术因子失败: {e}")
            return 50

    def _calculate_momentum_factor(self, stock: Dict[str, Any]) -> float:
        """计算动量因子评分"""
        try:
            score = 50  # 基础分

            # 价格动量 (40分)
            close_price = stock.get('close_price', 0)
            open_price = stock.get('open_price', 0)
            high_price = stock.get('high_price', 0)
            low_price = stock.get('low_price', 0)

            # 当日涨跌幅
            if open_price > 0:
                daily_return = (close_price - open_price) / open_price
                if daily_return > 0.05:  # 涨幅大于5%
                    score += 20
                elif daily_return > 0.02:  # 涨幅大于2%
                    score += 15
                elif daily_return > 0:  # 上涨
                    score += 10
                elif daily_return > -0.02:  # 小幅下跌
                    score += 5

            # 价格位置 (20分)
            if high_price > low_price:
                price_position = (close_price - low_price) / (high_price - low_price)
                if price_position > 0.8:  # 收盘价接近最高价
                    score += 20
                elif price_position > 0.6:
                    score += 15
                elif price_position > 0.4:
                    score += 10

            # 均线趋势 (40分)
            sma_5 = stock.get('sma_5', 0)
            sma_10 = stock.get('sma_10', 0)
            sma_20 = stock.get('sma_20', 0)

            trend_score = 0
            if sma_5 > sma_10:
                trend_score += 15  # 短期趋势向上
            if sma_10 > sma_20:
                trend_score += 15  # 中期趋势向上
            if close_price > sma_5:
                trend_score += 10  # 价格在短期均线上方

            score += trend_score

            return min(score, 100)

        except Exception as e:
            logger.error(f"计算动量因子失败: {e}")
            return 50

    def _calculate_quality_factor(self, stock: Dict[str, Any]) -> float:
        """计算质量因子评分"""
        try:
            score = 0

            # 财务质量 (50分)
            roe = stock.get('roe', 0)
            gross_profit_margin = stock.get('gross_profit_margin', 0)
            net_profit_margin = stock.get('net_profit_margin', 0)

            # ROE稳定性 (20分)
            if roe >= 15 and roe <= 30:  # ROE在合理区间
                score += 20
            elif roe >= 10:
                score += 15
            elif roe >= 5:
                score += 10

            # 盈利质量 (15分)
            if net_profit_margin >= 10:
                score += 15  # 高净利率
            elif net_profit_margin >= 5:
                score += 10  # 中等净利率
            elif net_profit_margin >= 2:
                score += 5   # 基本净利率

            # 毛利率稳定性 (15分)
            if gross_profit_margin >= 30:
                score += 15  # 高毛利率
            elif gross_profit_margin >= 20:
                score += 10  # 中等毛利率
            elif gross_profit_margin >= 10:
                score += 5   # 基本毛利率

            # 估值合理性 (30分)
            pe_ratio = stock.get('pe_ratio', 0)
            pb_ratio = stock.get('pb_ratio', 0)

            # PE合理性 (15分)
            if 8 <= pe_ratio <= 20:
                score += 15  # 合理PE
            elif 20 < pe_ratio <= 30:
                score += 10  # 略高PE
            elif 5 <= pe_ratio < 8:
                score += 12  # 低PE

            # PB合理性 (15分)
            if 1 <= pb_ratio <= 3:
                score += 15  # 合理PB
            elif 0.5 <= pb_ratio < 1:
                score += 12  # 低PB
            elif 3 < pb_ratio <= 5:
                score += 8   # 略高PB

            # 成长稳定性 (20分)
            revenue_growth = stock.get('revenue_growth', 0)
            profit_growth = stock.get('profit_growth', 0)

            # 营收增长稳定性 (10分)
            if 5 <= revenue_growth <= 25:
                score += 10  # 稳定增长
            elif revenue_growth > 25:
                score += 7   # 高增长但可能不稳定
            elif revenue_growth >= 0:
                score += 5   # 正增长

            # 利润增长稳定性 (10分)
            if 10 <= profit_growth <= 30:
                score += 10  # 稳定增长
            elif profit_growth > 30:
                score += 7   # 高增长但可能不稳定
            elif profit_growth >= 0:
                score += 5   # 正增长

            return min(score, 100)

        except Exception as e:
            logger.error(f"计算质量因子失败: {e}")
            return 50

    def _enhance_with_ml(self, stocks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用机器学习增强评分"""
        try:
            if len(stocks_data) < 50:
                logger.warning("数据量不足，跳过机器学习增强")
                return stocks_data

            # 准备特征数据
            features = []
            targets = []

            for stock in stocks_data:
                feature_vector = [
                    stock.get('pe_ratio', 0),
                    stock.get('pb_ratio', 0),
                    stock.get('roe', 0),
                    stock.get('gross_profit_margin', 0),
                    stock.get('revenue_growth', 0),
                    stock.get('rsi_14', 50),
                    stock.get('macd_dif', 0),
                    stock.get('turnover_rate', 0),
                    stock.get('volume', 0) / 1000000,  # 转换为百万手
                    stock.get('market_cap', 0) / 1000000000  # 转换为十亿元
                ]

                features.append(feature_vector)
                targets.append(stock.get('total_score', 50))

            # 数据标准化
            features_array = np.array(features)
            features_scaled = self.scaler.fit_transform(features_array)

            # 训练模型（使用当前数据作为训练集）
            X_train, X_test, y_train, y_test = train_test_split(
                features_scaled, targets, test_size=0.2, random_state=42
            )

            self.ml_model.fit(X_train, y_train)

            # 预测增强评分
            ml_predictions = self.ml_model.predict(features_scaled)

            # 融合原始评分和ML预测评分
            for i, stock in enumerate(stocks_data):
                original_score = stock.get('total_score', 50)
                ml_score = ml_predictions[i]

                # 加权融合 (原始评分70% + ML评分30%)
                enhanced_score = original_score * 0.7 + ml_score * 0.3
                stock['total_score'] = round(enhanced_score, 2)
                stock['ml_enhanced'] = True

            logger.info("🤖 机器学习评分增强完成")
            return stocks_data

        except Exception as e:
            logger.error(f"机器学习增强失败: {e}")
            return stocks_data

    def _rank_and_select(self, stocks_data: List[Dict[str, Any]]) -> List[StockScore]:
        """排序和选择股票"""
        try:
            # 按总分排序
            sorted_stocks = sorted(
                stocks_data,
                key=lambda x: x.get('total_score', 0),
                reverse=True
            )

            # 筛选符合最低评分要求的股票
            qualified_stocks = [
                stock for stock in sorted_stocks
                if stock.get('total_score', 0) >= self.selection_criteria.min_score
            ]

            # 限制选股数量
            selected_stocks = qualified_stocks[:self.selection_criteria.max_selections]

            # 转换为StockScore对象
            stock_scores = []
            for i, stock in enumerate(selected_stocks):
                # 生成推荐理由
                reason = self._generate_recommendation_reason(stock)

                # 确定推荐等级
                total_score = stock.get('total_score', 0)
                if total_score >= 90:
                    recommendation = '强烈推荐'
                elif total_score >= 80:
                    recommendation = '推荐'
                elif total_score >= 70:
                    recommendation = '观察'
                else:
                    recommendation = '谨慎'

                stock_score = StockScore(
                    symbol=stock['symbol'],
                    name=stock['name'],
                    technical_score=stock.get('technical_score', 0),
                    fundamental_score=stock.get('fundamental_score', 0),
                    market_score=stock.get('market_score', 0),
                    momentum_score=stock.get('momentum_score', 0),
                    quality_score=stock.get('quality_score', 0),
                    total_score=total_score,
                    rank=i + 1,
                    recommendation=recommendation,
                    reason=reason
                )

                stock_scores.append(stock_score)

            return stock_scores

        except Exception as e:
            logger.error(f"排序和选择失败: {e}")
            return []

    def _generate_recommendation_reason(self, stock: Dict[str, Any]) -> str:
        """生成推荐理由"""
        try:
            reasons = []

            # 技术面理由
            if stock.get('technical_score', 0) >= 80:
                if stock.get('macd_dif', 0) > stock.get('macd_dea', 0):
                    reasons.append("MACD金叉形成")
                if stock.get('rsi_14', 50) < 30:
                    reasons.append("RSI超卖反弹")
                if stock.get('close_price', 0) > stock.get('sma_20', 0):
                    reasons.append("价格突破20日均线")

            # 基本面理由
            if stock.get('fundamental_score', 0) >= 80:
                if stock.get('roe', 0) >= 15:
                    reasons.append(f"ROE达{stock.get('roe', 0):.1f}%")
                if stock.get('revenue_growth', 0) >= 15:
                    reasons.append(f"营收增长{stock.get('revenue_growth', 0):.1f}%")
                if stock.get('pe_ratio', 0) <= 20:
                    reasons.append("估值合理")

            # 质量理由
            if stock.get('quality_score', 0) >= 80:
                reasons.append("财务质量优秀")

            # 动量理由
            if stock.get('momentum_score', 0) >= 80:
                reasons.append("价格动量强劲")

            if not reasons:
                reasons.append("综合指标表现良好")

            return "，".join(reasons[:3])  # 最多显示3个理由

        except Exception as e:
            logger.error(f"生成推荐理由失败: {e}")
            return "综合评分较高"

    def _save_selection_results(self, selected_stocks: List[StockScore]):
        """保存选股结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清空旧的选股结果
            cursor.execute("DELETE FROM stock_selection_results")

            # 保存新的选股结果
            selection_id = f"intelligent_selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            for stock_score in selected_stocks:
                cursor.execute("""
                    INSERT INTO stock_selection_results
                    (selection_id, symbol, selection_date, total_score, technical_score,
                     fundamental_score, market_score, rank, recommendation, reason,
                     technical_weight, fundamental_weight, market_weight, is_selected)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    selection_id, stock_score.symbol, datetime.now(),
                    stock_score.total_score, stock_score.technical_score,
                    stock_score.fundamental_score, stock_score.market_score,
                    stock_score.rank, stock_score.recommendation, stock_score.reason,
                    self.factor_weights['technical'],
                    self.factor_weights['fundamental'],
                    self.factor_weights['market'],
                    True
                ))

            conn.commit()
            conn.close()

            # 添加到历史记录
            self.selection_history.append({
                'selection_id': selection_id,
                'selection_time': datetime.now(),
                'selected_count': len(selected_stocks),
                'avg_score': sum(s.total_score for s in selected_stocks) / len(selected_stocks) if selected_stocks else 0
            })

            logger.info(f"✅ 选股结果已保存: {selection_id}")

        except Exception as e:
            logger.error(f"保存选股结果失败: {e}")

    def get_selection_history(self) -> List[Dict[str, Any]]:
        """获取选股历史"""
        return self.selection_history.copy()

    def update_selection_criteria(self, criteria: SelectionCriteria):
        """更新选股标准"""
        self.selection_criteria = criteria
        logger.info("✅ 选股标准已更新")

# 全局智能选股器实例
intelligent_selector = IntelligentStockSelector()

def main():
    """测试主函数"""
    print("🚀 智能选股算法测试")
    print("=" * 60)

    try:
        # 设置选股标准
        criteria = SelectionCriteria(
            min_market_cap=2000000000,  # 最小市值20亿
            max_pe_ratio=40,
            min_roe=8,
            min_volume=2000000,
            min_score=75,
            max_selections=30
        )

        # 运行智能选股
        start_time = time.time()
        selected_stocks = intelligent_selector.run_intelligent_selection(criteria)
        end_time = time.time()

        print(f"✅ 智能选股完成")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
        print(f"📊 选出股票: {len(selected_stocks)}只")

        if selected_stocks:
            print(f"📈 平均评分: {sum(s.total_score for s in selected_stocks) / len(selected_stocks):.2f}")

            print("\n🏆 前10只推荐股票:")
            for i, stock in enumerate(selected_stocks[:10]):
                print(f"  {i+1:2d}. {stock.symbol} {stock.name:8s} - "
                      f"总分:{stock.total_score:5.1f} {stock.recommendation:4s} - {stock.reason}")

            # 显示各因子平均得分
            avg_technical = sum(s.technical_score for s in selected_stocks) / len(selected_stocks)
            avg_fundamental = sum(s.fundamental_score for s in selected_stocks) / len(selected_stocks)
            avg_market = sum(s.market_score for s in selected_stocks) / len(selected_stocks)
            avg_momentum = sum(s.momentum_score for s in selected_stocks) / len(selected_stocks)
            avg_quality = sum(s.quality_score for s in selected_stocks) / len(selected_stocks)

            print(f"\n📊 各因子平均得分:")
            print(f"  技术面: {avg_technical:.1f}")
            print(f"  基本面: {avg_fundamental:.1f}")
            print(f"  市场面: {avg_market:.1f}")
            print(f"  动量面: {avg_momentum:.1f}")
            print(f"  质量面: {avg_quality:.1f}")

        else:
            print("❌ 未找到符合条件的股票")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
    
    def _calculate_fundamental_factor(self, stock: Dict[str, Any]) -> float:
        """计算基本面因子评分"""
        try:
            score = 0
            
            # 估值指标 (40分)
            pe_ratio = stock.get('pe_ratio', 0)
            pb_ratio = stock.get('pb_ratio', 0)
            ps_ratio = stock.get('ps_ratio', 0)
            
            # PE评分 (15分)
            if 0 < pe_ratio <= 15:
                score += 15  # 低估值
            elif 15 < pe_ratio <= 25:
                score += 10  # 合理估值
            elif 25 < pe_ratio <= 35:
                score += 5   # 略高估值
            
            # PB评分 (15分)
            if 0 < pb_ratio <= 2:
                score += 15  # 低市净率
            elif 2 < pb_ratio <= 3:
                score += 10  # 合理市净率
            elif 3 < pb_ratio <= 5:
                score += 5   # 略高市净率
            
            # PS评分 (10分)
            if 0 < ps_ratio <= 3:
                score += 10  # 低市销率
            elif 3 < ps_ratio <= 6:
                score += 5   # 合理市销率
            
            # 盈利能力 (35分)
            roe = stock.get('roe', 0)
            gross_profit_margin = stock.get('gross_profit_margin', 0)
            net_profit_margin = stock.get('net_profit_margin', 0)
            
            # ROE评分 (15分)
            if roe >= 20:
                score += 15  # 优秀ROE
            elif roe >= 15:
                score += 12  # 良好ROE
            elif roe >= 10:
                score += 8   # 一般ROE
            elif roe >= 5:
                score += 4   # 较低ROE
            
            # 毛利率评分 (10分)
            if gross_profit_margin >= 40:
                score += 10  # 高毛利率
            elif gross_profit_margin >= 25:
                score += 7   # 中等毛利率
            elif gross_profit_margin >= 15:
                score += 4   # 一般毛利率
            
            # 净利率评分 (10分)
            if net_profit_margin >= 15:
                score += 10  # 高净利率
            elif net_profit_margin >= 8:
                score += 7   # 中等净利率
            elif net_profit_margin >= 3:
                score += 4   # 一般净利率
            
            # 成长性 (25分)
            revenue_growth = stock.get('revenue_growth', 0)
            profit_growth = stock.get('profit_growth', 0)
            eps_growth = stock.get('eps_growth', 0)
            
            # 营收增长 (10分)
            if revenue_growth >= 20:
                score += 10  # 高增长
            elif revenue_growth >= 10:
                score += 7   # 中等增长
            elif revenue_growth >= 0:
                score += 4   # 正增长
            
            # 利润增长 (10分)
            if profit_growth >= 25:
                score += 10  # 高增长
            elif profit_growth >= 15:
                score += 7   # 中等增长
            elif profit_growth >= 0:
                score += 4   # 正增长
            
            # EPS增长 (5分)
            if eps_growth >= 20:
                score += 5   # 高增长
            elif eps_growth >= 10:
                score += 3   # 中等增长
            elif eps_growth >= 0:
                score += 1   # 正增长
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算基本面因子失败: {e}")
            return 50
    
    def _calculate_market_factor(self, stock: Dict[str, Any]) -> float:
        """计算市场因子评分"""
        try:
            score = 50  # 基础分
            
            # 市值规模 (30分)
            market_cap = stock.get('market_cap', 0)
            if market_cap >= 100000000000:  # 大于1000亿
                score += 20  # 大盘股
            elif market_cap >= 50000000000:  # 大于500亿
                score += 25  # 中大盘股
            elif market_cap >= 20000000000:  # 大于200亿
                score += 30  # 中盘股，最优
            elif market_cap >= 5000000000:   # 大于50亿
                score += 20  # 中小盘股
            else:
                score += 10  # 小盘股
            
            # 流动性 (20分)
            volume = stock.get('volume', 0)
            turnover_rate = stock.get('turnover_rate', 0)
            amount = stock.get('amount', 0)
            
            if volume > 10000000 and turnover_rate > 3:
                score += 20  # 高流动性
            elif volume > 5000000 and turnover_rate > 2:
                score += 15  # 中高流动性
            elif volume > 2000000 and turnover_rate > 1:
                score += 10  # 中等流动性
            elif volume > 1000000:
                score += 5   # 基本流动性
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"计算市场因子失败: {e}")
            return 50
