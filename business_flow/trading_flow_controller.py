"""
量化交易业务流程控制器
按照产品设计业务流程图实现完整的交易闭环控制
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import time
import queue

from intelligent_stock_selection.stock_selector import StockSelector
from strategy_layer.trading_strategies.buy_strategy import BuyStrategy
from strategy_layer.trading_strategies.sell_strategy import SellStrategy
from trading_execution.trading_engine.simulation_engine import SimulationTradingEngine
from trading_execution.order_manager.order_manager import OrderManager
from portfolio_management.portfolio_manager import PortfolioManager
from database_models import db_manager

logger = logging.getLogger(__name__)

class FlowStage(Enum):
    """业务流程阶段"""
    STOCK_SELECTION = "STOCK_SELECTION"     # 智能选股
    KLINE_ANALYSIS = "KLINE_ANALYSIS"       # K线分析
    BUY_DECISION = "BUY_DECISION"           # 买入决策
    POSITION_MONITOR = "POSITION_MONITOR"   # 持仓监控
    SELL_DECISION = "SELL_DECISION"         # 卖出决策
    INVESTMENT_RECORD = "INVESTMENT_RECORD" # 投资记录

@dataclass
class FlowContext:
    """流程上下文"""
    flow_id: str
    current_stage: FlowStage
    symbol: str
    data: Dict[str, Any]
    start_time: datetime
    last_update: datetime
    
class TradingFlowController:
    """量化交易业务流程控制器"""
    
    def __init__(self):
        # 核心组件
        self.stock_selector = StockSelector()
        self.buy_strategy = BuyStrategy()
        self.sell_strategy = SellStrategy()
        self.trading_engine = SimulationTradingEngine()
        self.order_manager = OrderManager(self.trading_engine)
        self.portfolio_manager = PortfolioManager()
        
        # 流程管理
        self.active_flows: Dict[str, FlowContext] = {}
        self.flow_queue = queue.Queue()
        
        # 配置参数
        self.config = {
            'selection_interval': 300,      # 选股间隔（秒）
            'monitor_interval': 60,         # 监控间隔（秒）
            'max_concurrent_flows': 100,    # 最大并发流程数
            'auto_execution': True,         # 自动执行交易
            'risk_check_enabled': True      # 风险检查开关
        }
        
        # 统计信息
        self.statistics = {
            'total_flows': 0,
            'completed_flows': 0,
            'active_flows': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_profit': 0.0
        }
        
        # 事件回调
        self.stage_callbacks: Dict[FlowStage, List[Callable]] = {
            stage: [] for stage in FlowStage
        }
        
        # 控制线程
        self.is_running = False
        self.main_thread = None
        self.monitor_thread = None
        
        # 启动交易引擎
        self.trading_engine.start()
        self.order_manager.start_monitoring()
        
        logger.info("🎯 量化交易业务流程控制器初始化完成")
        logger.info(f"  - 选股间隔: {self.config['selection_interval']}秒")
        logger.info(f"  - 监控间隔: {self.config['monitor_interval']}秒")
        logger.info(f"  - 最大并发: {self.config['max_concurrent_flows']}")
    
    def start(self):
        """启动业务流程"""
        try:
            if self.is_running:
                logger.warning("⚠️ 业务流程已在运行")
                return
            
            self.is_running = True
            
            # 启动主流程线程
            self.main_thread = threading.Thread(target=self._main_flow_loop, daemon=True)
            self.main_thread.start()
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info("🚀 量化交易业务流程已启动")
            
        except Exception as e:
            logger.error(f"❌ 启动业务流程失败: {e}")
    
    def stop(self):
        """停止业务流程"""
        try:
            self.is_running = False
            
            # 等待线程结束
            if self.main_thread and self.main_thread.is_alive():
                self.main_thread.join(timeout=10)
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)
            
            # 停止交易引擎
            self.trading_engine.stop()
            self.order_manager.stop_monitoring()
            
            logger.info("⏹️ 量化交易业务流程已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止业务流程失败: {e}")
    
    def _main_flow_loop(self):
        """主流程循环"""
        logger.info("🔄 主流程循环开始")
        
        last_selection_time = datetime.min
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 1. 定期执行智能选股
                if (current_time - last_selection_time).total_seconds() >= self.config['selection_interval']:
                    self._execute_stock_selection()
                    last_selection_time = current_time
                
                # 2. 处理流程队列
                self._process_flow_queue()
                
                # 3. 更新活跃流程
                self._update_active_flows()
                
                time.sleep(10)  # 主循环间隔
                
            except Exception as e:
                logger.error(f"❌ 主流程循环异常: {e}")
                time.sleep(30)
        
        logger.info("⏹️ 主流程循环结束")
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info("👁️ 监控循环开始")
        
        while self.is_running:
            try:
                # 监控持仓
                self._monitor_positions()
                
                # 更新统计信息
                self._update_statistics()
                
                # 风险检查
                if self.config['risk_check_enabled']:
                    self._risk_check()
                
                time.sleep(self.config['monitor_interval'])
                
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
                time.sleep(60)
        
        logger.info("⏹️ 监控循环结束")
    
    def _execute_stock_selection(self):
        """执行智能选股"""
        try:
            logger.info("🎯 开始智能选股...")
            
            # 获取候选股票池
            candidate_stocks = self.stock_selector.select_stocks()
            
            logger.info(f"📊 智能选股完成: {len(candidate_stocks)}只候选股票")
            
            # 为每只候选股票创建流程
            for stock_info in candidate_stocks:
                if len(self.active_flows) >= self.config['max_concurrent_flows']:
                    logger.warning(f"⚠️ 达到最大并发流程数: {self.config['max_concurrent_flows']}")
                    break
                
                self._create_flow(stock_info['symbol'], stock_info)
            
        except Exception as e:
            logger.error(f"❌ 智能选股失败: {e}")
    
    def _create_flow(self, symbol: str, stock_info: Dict[str, Any]):
        """创建交易流程"""
        try:
            flow_id = f"FLOW_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            flow_context = FlowContext(
                flow_id=flow_id,
                current_stage=FlowStage.STOCK_SELECTION,
                symbol=symbol,
                data={
                    'stock_info': stock_info,
                    'selection_time': datetime.now(),
                    'selection_score': stock_info.get('total_score', 0)
                },
                start_time=datetime.now(),
                last_update=datetime.now()
            )
            
            self.active_flows[flow_id] = flow_context
            self.flow_queue.put(flow_id)
            
            self.statistics['total_flows'] += 1
            self.statistics['active_flows'] += 1
            
            logger.info(f"📝 创建交易流程: {flow_id} - {symbol}")
            
        except Exception as e:
            logger.error(f"❌ 创建交易流程失败: {symbol} - {e}")
    
    def _process_flow_queue(self):
        """处理流程队列"""
        try:
            while not self.flow_queue.empty():
                try:
                    flow_id = self.flow_queue.get_nowait()
                    self._process_single_flow(flow_id)
                except queue.Empty:
                    break
                
        except Exception as e:
            logger.error(f"❌ 处理流程队列失败: {e}")
    
    def _process_single_flow(self, flow_id: str):
        """处理单个流程"""
        try:
            if flow_id not in self.active_flows:
                return
            
            flow_context = self.active_flows[flow_id]
            
            # 根据当前阶段执行相应逻辑
            if flow_context.current_stage == FlowStage.STOCK_SELECTION:
                self._stage_kline_analysis(flow_context)
                
            elif flow_context.current_stage == FlowStage.KLINE_ANALYSIS:
                self._stage_buy_decision(flow_context)
                
            elif flow_context.current_stage == FlowStage.BUY_DECISION:
                self._stage_position_monitor(flow_context)
                
            elif flow_context.current_stage == FlowStage.POSITION_MONITOR:
                self._stage_sell_decision(flow_context)
                
            elif flow_context.current_stage == FlowStage.SELL_DECISION:
                self._stage_investment_record(flow_context)
            
            # 更新时间
            flow_context.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 处理单个流程失败: {flow_id} - {e}")
    
    def _stage_kline_analysis(self, flow_context: FlowContext):
        """阶段1: K线分析"""
        try:
            symbol = flow_context.symbol
            
            logger.info(f"📈 K线分析阶段: {symbol}")
            
            # 获取市场数据进行技术分析
            # 这里应该调用技术分析模块
            market_data = self._get_market_data(symbol)
            
            if market_data is not None:
                # 计算技术指标
                technical_analysis = self._perform_technical_analysis(market_data)
                
                flow_context.data['market_data'] = market_data
                flow_context.data['technical_analysis'] = technical_analysis
                flow_context.current_stage = FlowStage.KLINE_ANALYSIS
                
                # 触发回调
                self._trigger_stage_callbacks(FlowStage.KLINE_ANALYSIS, flow_context)
                
                # 继续下一阶段
                self.flow_queue.put(flow_context.flow_id)
            else:
                logger.warning(f"⚠️ 无法获取市场数据: {symbol}")
                self._complete_flow(flow_context, success=False, reason="无市场数据")
            
        except Exception as e:
            logger.error(f"❌ K线分析阶段失败: {flow_context.symbol} - {e}")
            self._complete_flow(flow_context, success=False, reason=f"K线分析异常: {e}")
    
    def _stage_buy_decision(self, flow_context: FlowContext):
        """阶段2: 买入决策"""
        try:
            symbol = flow_context.symbol
            
            logger.info(f"🔴 买入决策阶段: {symbol}")
            
            # 使用买入策略生成买入信号
            market_data = flow_context.data.get('market_data')
            stock_info = flow_context.data.get('stock_info', {})
            
            # 构造策略输入数据
            strategy_input = {
                'symbol': symbol,
                'market_data': market_data,
                'factor_score': stock_info.get('total_score', 0),
                'technical_analysis': flow_context.data.get('technical_analysis', {})
            }
            
            # 生成买入信号
            buy_signals = self.buy_strategy.generate_buy_signals([symbol])
            
            if buy_signals:
                buy_signal = buy_signals[0]
                
                # 检查买入信号强度
                if buy_signal.signal_strength >= 0.7:  # 信号强度阈值
                    
                    # 执行买入决策
                    if self.config['auto_execution']:
                        success = self._execute_buy_order(flow_context, buy_signal)
                        
                        if success:
                            flow_context.data['buy_signal'] = buy_signal
                            flow_context.data['buy_time'] = datetime.now()
                            flow_context.current_stage = FlowStage.BUY_DECISION
                            
                            # 触发回调
                            self._trigger_stage_callbacks(FlowStage.BUY_DECISION, flow_context)
                            
                            # 继续监控阶段
                            self.flow_queue.put(flow_context.flow_id)
                        else:
                            self._complete_flow(flow_context, success=False, reason="买入订单失败")
                    else:
                        # 手动确认模式
                        flow_context.data['pending_buy_signal'] = buy_signal
                        logger.info(f"📋 等待手动确认买入: {symbol}")
                else:
                    logger.info(f"📉 买入信号强度不足: {symbol} 强度{buy_signal.signal_strength:.2f}")
                    self._complete_flow(flow_context, success=False, reason="信号强度不足")
            else:
                logger.info(f"📉 无买入信号: {symbol}")
                self._complete_flow(flow_context, success=False, reason="无买入信号")
            
        except Exception as e:
            logger.error(f"❌ 买入决策阶段失败: {flow_context.symbol} - {e}")
            self._complete_flow(flow_context, success=False, reason=f"买入决策异常: {e}")
    
    def _stage_position_monitor(self, flow_context: FlowContext):
        """阶段3: 持仓监控"""
        try:
            symbol = flow_context.symbol
            
            logger.debug(f"👁️ 持仓监控阶段: {symbol}")
            
            # 获取当前持仓
            position = self.trading_engine.get_position(symbol)
            
            if position.quantity > 0:
                # 更新持仓信息
                current_price = self._get_current_price(symbol)
                if current_price > 0:
                    unrealized_pnl = (current_price - position.avg_price) * position.quantity
                    pnl_pct = unrealized_pnl / (position.avg_price * position.quantity)
                    
                    flow_context.data['current_price'] = current_price
                    flow_context.data['unrealized_pnl'] = unrealized_pnl
                    flow_context.data['pnl_pct'] = pnl_pct
                    
                    # 检查是否需要卖出
                    if self._should_trigger_sell_check(flow_context):
                        flow_context.current_stage = FlowStage.POSITION_MONITOR
                        self.flow_queue.put(flow_context.flow_id)
                    else:
                        # 继续监控，延迟重新加入队列
                        threading.Timer(60, lambda: self.flow_queue.put(flow_context.flow_id)).start()
                else:
                    logger.warning(f"⚠️ 无法获取当前价格: {symbol}")
            else:
                logger.warning(f"⚠️ 持仓为空: {symbol}")
                self._complete_flow(flow_context, success=False, reason="持仓为空")
            
        except Exception as e:
            logger.error(f"❌ 持仓监控阶段失败: {flow_context.symbol} - {e}")
    
    def _stage_sell_decision(self, flow_context: FlowContext):
        """阶段4: 卖出决策"""
        try:
            symbol = flow_context.symbol
            
            logger.info(f"🟢 卖出决策阶段: {symbol}")
            
            # 获取持仓信息
            position = self.trading_engine.get_position(symbol)
            
            if position.quantity > 0:
                # 构造卖出策略输入
                position_info = {
                    'symbol': symbol,
                    'entry_price': position.avg_price,
                    'quantity': position.quantity,
                    'entry_date': flow_context.data.get('buy_time', datetime.now())
                }
                
                # 生成卖出信号
                sell_signals = self.sell_strategy.generate_sell_signals([position_info])
                
                if sell_signals:
                    sell_signal = sell_signals[0]
                    
                    # 执行卖出决策
                    if self.config['auto_execution']:
                        success = self._execute_sell_order(flow_context, sell_signal)
                        
                        if success:
                            flow_context.data['sell_signal'] = sell_signal
                            flow_context.data['sell_time'] = datetime.now()
                            flow_context.current_stage = FlowStage.SELL_DECISION
                            
                            # 触发回调
                            self._trigger_stage_callbacks(FlowStage.SELL_DECISION, flow_context)
                            
                            # 进入记录阶段
                            self.flow_queue.put(flow_context.flow_id)
                        else:
                            logger.error(f"❌ 卖出订单失败: {symbol}")
                    else:
                        # 手动确认模式
                        flow_context.data['pending_sell_signal'] = sell_signal
                        logger.info(f"📋 等待手动确认卖出: {symbol}")
                else:
                    # 继续监控
                    flow_context.current_stage = FlowStage.POSITION_MONITOR
                    threading.Timer(60, lambda: self.flow_queue.put(flow_context.flow_id)).start()
            else:
                logger.warning(f"⚠️ 持仓已清空: {symbol}")
                self._complete_flow(flow_context, success=True, reason="持仓已清空")
            
        except Exception as e:
            logger.error(f"❌ 卖出决策阶段失败: {flow_context.symbol} - {e}")
    
    def _stage_investment_record(self, flow_context: FlowContext):
        """阶段5: 投资记录"""
        try:
            symbol = flow_context.symbol
            
            logger.info(f"📊 投资记录阶段: {symbol}")
            
            # 记录完整的投资过程
            investment_record = self._create_investment_record(flow_context)
            
            # 保存到数据库
            self._save_investment_record(investment_record)
            
            # 更新统计信息
            self._update_trade_statistics(flow_context)
            
            # 触发回调
            self._trigger_stage_callbacks(FlowStage.INVESTMENT_RECORD, flow_context)
            
            # 完成流程
            self._complete_flow(flow_context, success=True, reason="投资记录完成")
            
        except Exception as e:
            logger.error(f"❌ 投资记录阶段失败: {flow_context.symbol} - {e}")
            self._complete_flow(flow_context, success=False, reason=f"记录异常: {e}")
    
    def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取市场数据"""
        try:
            # 这里应该调用数据采集模块获取真实市场数据
            # 简化处理：返回模拟数据
            return {
                'symbol': symbol,
                'current_price': 100.0,
                'volume': 1000000,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取市场数据失败: {symbol} - {e}")
            return None
    
    def _perform_technical_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行技术分析"""
        try:
            # 这里应该调用技术分析模块
            # 简化处理：返回模拟分析结果
            return {
                'ma_trend': 'bullish',
                'macd_signal': 'buy',
                'rsi_value': 65.0,
                'volume_trend': 'increasing'
            }
            
        except Exception as e:
            logger.error(f"❌ 技术分析失败: {e}")
            return {}
    
    def _execute_buy_order(self, flow_context: FlowContext, buy_signal) -> bool:
        """执行买入订单"""
        try:
            symbol = flow_context.symbol
            
            # 计算买入数量
            account_info = self.trading_engine.get_account_info()
            available_cash = account_info.get('available_cash', 0)
            
            # 简化处理：使用固定比例
            position_size = min(available_cash * 0.1, 100000)  # 10%仓位或10万上限
            quantity = int(position_size / buy_signal.signal_price)
            
            if quantity > 0:
                from trading_execution.order_manager.order_manager import OrderRequest
                from trading_execution.trading_engine.simulation_engine import OrderSide, OrderType
                
                order_request = OrderRequest(
                    symbol=symbol,
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=quantity,
                    strategy_id=flow_context.flow_id
                )
                
                result = self.order_manager.submit_order(order_request)
                
                if result['success']:
                    flow_context.data['buy_order_id'] = result['order_id']
                    logger.info(f"✅ 买入订单提交成功: {symbol} {quantity}股")
                    return True
                else:
                    logger.error(f"❌ 买入订单提交失败: {symbol} - {result['reason']}")
                    return False
            else:
                logger.warning(f"⚠️ 买入数量为0: {symbol}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 执行买入订单失败: {flow_context.symbol} - {e}")
            return False
    
    def _execute_sell_order(self, flow_context: FlowContext, sell_signal) -> bool:
        """执行卖出订单"""
        try:
            symbol = flow_context.symbol
            position = self.trading_engine.get_position(symbol)
            
            if position.quantity > 0:
                from trading_execution.order_manager.order_manager import OrderRequest
                from trading_execution.trading_engine.simulation_engine import OrderSide, OrderType
                
                order_request = OrderRequest(
                    symbol=symbol,
                    side=OrderSide.SELL,
                    order_type=OrderType.MARKET,
                    quantity=position.quantity,
                    strategy_id=flow_context.flow_id
                )
                
                result = self.order_manager.submit_order(order_request)
                
                if result['success']:
                    flow_context.data['sell_order_id'] = result['order_id']
                    logger.info(f"✅ 卖出订单提交成功: {symbol} {position.quantity}股")
                    return True
                else:
                    logger.error(f"❌ 卖出订单提交失败: {symbol} - {result['reason']}")
                    return False
            else:
                logger.warning(f"⚠️ 无持仓可卖: {symbol}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 执行卖出订单失败: {flow_context.symbol} - {e}")
            return False
    
    def _should_trigger_sell_check(self, flow_context: FlowContext) -> bool:
        """判断是否应该触发卖出检查"""
        try:
            # 检查持仓时间
            buy_time = flow_context.data.get('buy_time', datetime.now())
            holding_days = (datetime.now() - buy_time).days
            
            # 检查盈亏比例
            pnl_pct = flow_context.data.get('pnl_pct', 0)
            
            # 触发条件：
            # 1. 持仓超过1天
            # 2. 盈利超过10%或亏损超过5%
            # 3. 每小时检查一次
            
            if holding_days >= 1:
                return True
            
            if pnl_pct >= 0.1 or pnl_pct <= -0.05:
                return True
            
            last_check = flow_context.data.get('last_sell_check', datetime.min)
            if (datetime.now() - last_check).total_seconds() >= 3600:  # 1小时
                flow_context.data['last_sell_check'] = datetime.now()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 判断卖出检查失败: {e}")
            return False
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        try:
            # 简化处理：返回模拟价格
            import random
            return 100.0 + random.uniform(-5, 5)
            
        except Exception as e:
            logger.error(f"❌ 获取当前价格失败: {symbol} - {e}")
            return 0.0
    
    def _create_investment_record(self, flow_context: FlowContext) -> Dict[str, Any]:
        """创建投资记录"""
        try:
            buy_signal = flow_context.data.get('buy_signal')
            sell_signal = flow_context.data.get('sell_signal')
            
            record = {
                'flow_id': flow_context.flow_id,
                'symbol': flow_context.symbol,
                'start_time': flow_context.start_time,
                'end_time': datetime.now(),
                'buy_time': flow_context.data.get('buy_time'),
                'sell_time': flow_context.data.get('sell_time'),
                'buy_price': buy_signal.signal_price if buy_signal else 0,
                'sell_price': sell_signal.signal_price if sell_signal else 0,
                'quantity': 0,  # 从订单获取
                'profit_loss': 0,  # 计算盈亏
                'profit_loss_pct': 0,
                'holding_days': 0,
                'buy_reason': buy_signal.reasons if buy_signal else [],
                'sell_reason': sell_signal.sell_reason if sell_signal else "",
                'strategy_performance': {}
            }
            
            return record
            
        except Exception as e:
            logger.error(f"❌ 创建投资记录失败: {e}")
            return {}
    
    def _save_investment_record(self, record: Dict[str, Any]):
        """保存投资记录"""
        try:
            # 这里应该保存到数据库
            logger.info(f"💾 保存投资记录: {record.get('symbol', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"❌ 保存投资记录失败: {e}")
    
    def _update_trade_statistics(self, flow_context: FlowContext):
        """更新交易统计"""
        try:
            pnl = flow_context.data.get('unrealized_pnl', 0)
            
            if pnl > 0:
                self.statistics['successful_trades'] += 1
            else:
                self.statistics['failed_trades'] += 1
            
            self.statistics['total_profit'] += pnl
            
        except Exception as e:
            logger.error(f"❌ 更新交易统计失败: {e}")
    
    def _complete_flow(self, flow_context: FlowContext, success: bool, reason: str):
        """完成流程"""
        try:
            flow_id = flow_context.flow_id
            
            # 从活跃流程中移除
            if flow_id in self.active_flows:
                del self.active_flows[flow_id]
            
            # 更新统计
            self.statistics['active_flows'] -= 1
            self.statistics['completed_flows'] += 1
            
            status = "成功" if success else "失败"
            logger.info(f"🏁 流程完成: {flow_id} - {status} - {reason}")
            
        except Exception as e:
            logger.error(f"❌ 完成流程失败: {flow_context.flow_id} - {e}")
    
    def _update_active_flows(self):
        """更新活跃流程"""
        try:
            # 清理超时流程
            timeout_flows = []
            current_time = datetime.now()
            
            for flow_id, flow_context in self.active_flows.items():
                # 检查流程超时（24小时）
                if (current_time - flow_context.start_time).total_seconds() > 86400:
                    timeout_flows.append(flow_id)
            
            for flow_id in timeout_flows:
                flow_context = self.active_flows[flow_id]
                self._complete_flow(flow_context, success=False, reason="流程超时")
            
        except Exception as e:
            logger.error(f"❌ 更新活跃流程失败: {e}")
    
    def _monitor_positions(self):
        """监控持仓"""
        try:
            positions = self.trading_engine.get_all_positions()
            
            for symbol, position in positions.items():
                if position.quantity > 0:
                    # 检查是否有对应的活跃流程
                    has_active_flow = any(
                        flow.symbol == symbol and flow.current_stage in [
                            FlowStage.POSITION_MONITOR, FlowStage.SELL_DECISION
                        ]
                        for flow in self.active_flows.values()
                    )
                    
                    if not has_active_flow:
                        # 创建监控流程
                        logger.info(f"🔍 为孤立持仓创建监控流程: {symbol}")
                        # 这里可以创建一个专门的持仓监控流程
            
        except Exception as e:
            logger.error(f"❌ 监控持仓失败: {e}")
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            self.statistics['active_flows'] = len(self.active_flows)
            
        except Exception as e:
            logger.error(f"❌ 更新统计信息失败: {e}")
    
    def _risk_check(self):
        """风险检查"""
        try:
            account_info = self.trading_engine.get_account_info()
            total_return = account_info.get('total_return', 0)
            
            # 检查总回撤
            if total_return < -0.15:  # 15%回撤限制
                logger.warning(f"⚠️ 总回撤超限: {total_return:.2%}")
                # 可以触发风险控制措施
            
        except Exception as e:
            logger.error(f"❌ 风险检查失败: {e}")
    
    def _trigger_stage_callbacks(self, stage: FlowStage, flow_context: FlowContext):
        """触发阶段回调"""
        try:
            for callback in self.stage_callbacks.get(stage, []):
                try:
                    callback(flow_context)
                except Exception as e:
                    logger.error(f"❌ 阶段回调执行失败: {stage} - {e}")
                    
        except Exception as e:
            logger.error(f"❌ 触发阶段回调失败: {stage} - {e}")
    
    def add_stage_callback(self, stage: FlowStage, callback: Callable):
        """添加阶段回调"""
        self.stage_callbacks[stage].append(callback)
    
    def get_flow_status(self) -> Dict[str, Any]:
        """获取流程状态"""
        try:
            return {
                'is_running': self.is_running,
                'statistics': self.statistics.copy(),
                'active_flows': len(self.active_flows),
                'config': self.config.copy(),
                'flow_details': {
                    flow_id: {
                        'symbol': flow.symbol,
                        'stage': flow.current_stage.value,
                        'start_time': flow.start_time.isoformat(),
                        'last_update': flow.last_update.isoformat()
                    }
                    for flow_id, flow in self.active_flows.items()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 获取流程状态失败: {e}")
            return {}
