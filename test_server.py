#!/usr/bin/env python3
"""
简单测试服务器是否正常运行
"""

import requests
import json

def test_server():
    base_url = "http://localhost:8080"
    
    print("🧪 测试VeighNa Web服务器")
    print("=" * 50)
    
    # 测试主页
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ 主页访问: {response.status_code}")
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
    
    # 测试API接口
    apis = [
        "/api/realtime-data",
        "/api/stock-selection", 
        "/api/trading-signals",
        "/api/portfolio",
        "/api/system/status"
    ]
    
    for api in apis:
        try:
            response = requests.get(f"{base_url}{api}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {api}: 成功 (数据长度: {len(str(data))})")
            else:
                print(f"⚠️ {api}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {api}: {e}")
    
    print("=" * 50)

if __name__ == "__main__":
    test_server()
