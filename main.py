#!/usr/bin/env python3
"""
量化交易系统 V2.0 主启动脚本
按照产品设计要求启动完整的量化交易系统
"""

import sys
import os
import logging
import argparse
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
try:
    from business_flow.trading_flow_controller import TradingFlowController
    from frontend_ui.main_window import MainWindow
    from system_management.config_manager import ConfigManager
    from system_management.log_manager import LogManager
    from system_management.performance_monitor import PerformanceMonitor
    from database_models import db_manager
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖模块已正确安装")
    sys.exit(1)

def setup_logging(log_level: str = "INFO"):
    """设置日志系统"""
    try:
        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # 配置根日志器
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(f"{log_dir}/system_{datetime.now().strftime('%Y%m%d')}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )

        logger = logging.getLogger(__name__)
        logger.info("📝 日志系统初始化完成")

        return True

    except Exception as e:
        print(f"❌ 日志系统初始化失败: {e}")
        return False
    
def check_system_requirements():
    """检查系统要求"""
    try:
        logger = logging.getLogger(__name__)
        logger.info("🔍 检查系统要求...")

        # 检查Python版本
        if sys.version_info < (3, 7):
            logger.error("❌ Python版本过低，需要3.7+")
            return False

        # 检查必要的包
        required_packages = [
            'PyQt5', 'pandas', 'numpy', 'sqlalchemy',
            'pyqtgraph', 'requests', 'schedule'
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            logger.error(f"❌ 缺少必要包: {', '.join(missing_packages)}")
            logger.info("请运行: pip install " + " ".join(missing_packages))
            return False

        # 检查数据库连接
        try:
            with db_manager.get_session() as session:
                session.execute("SELECT 1").fetchone()
            logger.info("✅ 数据库连接正常")
        except Exception as e:
            logger.warning(f"⚠️ 数据库连接失败: {e}")
            logger.info("系统将使用模拟数据运行")

        logger.info("✅ 系统要求检查通过")
        return True

    except Exception as e:
        print(f"❌ 系统要求检查失败: {e}")
        return False
    
def initialize_system_components():
    """初始化系统组件"""
    try:
        logger = logging.getLogger(__name__)
        logger.info("🚀 初始化系统组件...")

        components = {}

        # 1. 配置管理器
        logger.info("  📋 初始化配置管理器...")
        try:
            components['config_manager'] = ConfigManager()
        except Exception as e:
            logger.warning(f"⚠️ 配置管理器初始化失败: {e}")
            components['config_manager'] = None

        # 2. 日志管理器
        logger.info("  📝 初始化日志管理器...")
        try:
            components['log_manager'] = LogManager()
        except Exception as e:
            logger.warning(f"⚠️ 日志管理器初始化失败: {e}")
            components['log_manager'] = None

        # 3. 性能监控器
        logger.info("  📊 初始化性能监控器...")
        try:
            components['performance_monitor'] = PerformanceMonitor()
            components['performance_monitor'].start_monitoring()
        except Exception as e:
            logger.warning(f"⚠️ 性能监控器初始化失败: {e}")
            components['performance_monitor'] = None

        # 4. 业务流程控制器
        logger.info("  🎯 初始化业务流程控制器...")
        try:
            components['flow_controller'] = TradingFlowController()
        except Exception as e:
            logger.warning(f"⚠️ 业务流程控制器初始化失败: {e}")
            components['flow_controller'] = None

        logger.info("✅ 系统组件初始化完成")
        return components

    except Exception as e:
        logger.error(f"❌ 系统组件初始化失败: {e}")
        return None
    
def start_backend_services(components: Dict[str, Any]):
    """启动后台服务"""
    try:
        logger = logging.getLogger(__name__)
        logger.info("🔧 启动后台服务...")

        # 启动业务流程控制器
        flow_controller = components.get('flow_controller')
        if flow_controller:
            flow_controller.start()
            logger.info("  ✅ 业务流程控制器已启动")

        logger.info("✅ 后台服务启动完成")
        return True

    except Exception as e:
        logger.error(f"❌ 启动后台服务失败: {e}")
        return False

def start_gui_application(components: Dict[str, Any]):
    """启动GUI应用"""
    try:
        logger = logging.getLogger(__name__)
        logger.info("🖥️ 启动GUI应用...")

        from PyQt5.QtWidgets import QApplication

        # 创建QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("量化交易系统")
        app.setApplicationVersion("2.0.0")

        # 创建主窗口
        main_window = MainWindow()

        # 传递系统组件到主窗口
        if hasattr(main_window, 'set_system_components'):
            main_window.set_system_components(components)

        # 显示主窗口
        main_window.show()

        logger.info("✅ GUI应用启动完成")

        # 运行应用
        return app.exec_()

    except Exception as e:
        logger.error(f"❌ 启动GUI应用失败: {e}")
        return 1

def start_console_mode(components: Dict[str, Any]):
    """启动控制台模式"""
    try:
        logger = logging.getLogger(__name__)
        logger.info("💻 启动控制台模式...")

        flow_controller = components.get('flow_controller')
        performance_monitor = components.get('performance_monitor')

        print("\n" + "="*80)
        print("🎯 量化交易系统 - 控制台模式")
        print("="*80)
        print("系统正在运行中...")
        print("按 Ctrl+C 退出系统")
        print("="*80)

        try:
            while True:
                # 显示系统状态
                if flow_controller:
                    status = flow_controller.get_flow_status()
                    print(f"\r⏰ {datetime.now().strftime('%H:%M:%S')} | "
                          f"活跃流程: {status.get('active_flows', 0)} | "
                          f"总流程: {status.get('statistics', {}).get('total_flows', 0)}",
                          end='', flush=True)

                import time
                time.sleep(5)

        except KeyboardInterrupt:
            print("\n\n🛑 收到退出信号，正在关闭系统...")

            # 停止服务
            if flow_controller:
                flow_controller.stop()

            if performance_monitor:
                performance_monitor.stop_monitoring()

            print("✅ 系统已安全关闭")
            return 0

    except Exception as e:
        logger.error(f"❌ 控制台模式运行失败: {e}")
        return 1

def run_system_test():
    """运行系统测试"""
    try:
        print("🧪 运行系统集成测试...")

        from tests.integration_test_suite import run_quick_test

        success = run_quick_test()

        if success:
            print("✅ 系统测试通过")
            return 0
        else:
            print("❌ 系统测试失败")
            return 1

    except Exception as e:
        print(f"❌ 系统测试异常: {e}")
        return 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='量化交易系统')
    parser.add_argument('--mode', choices=['gui', 'console', 'test'],
                       default='gui', help='运行模式')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 显示启动信息
    print("\n" + "="*80)
    print("🚀 量化交易系统 v2.0.0")
    print("="*80)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"运行模式: {args.mode.upper()}")
    print(f"日志级别: {args.log_level}")
    print("="*80)

    # 设置日志
    if not setup_logging(args.log_level):
        return 1

    # 运行测试模式
    if args.mode == 'test':
        return run_system_test()

    # 检查系统要求
    if not check_system_requirements():
        return 1

    # 初始化系统组件
    components = initialize_system_components()
    if not components:
        return 1

    # 启动后台服务
    if not start_backend_services(components):
        return 1

    try:
        # 根据模式启动相应服务
        if args.mode == 'gui':
            return start_gui_application(components)
        elif args.mode == 'console':
            return start_console_mode(components)
        else:
            print(f"❌ 未知运行模式: {args.mode}")
            return 1

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"❌ 系统运行异常: {e}")
        return 1

    finally:
        # 清理资源
        try:
            if components:
                flow_controller = components.get('flow_controller')
                if flow_controller:
                    flow_controller.stop()

                performance_monitor = components.get('performance_monitor')
                if performance_monitor:
                    performance_monitor.stop_monitoring()

        except Exception as e:
            print(f"⚠️ 资源清理异常: {e}")

if __name__ == "__main__":
    sys.exit(main())
