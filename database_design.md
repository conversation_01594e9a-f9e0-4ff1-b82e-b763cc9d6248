# 🗄️ 量化交易系统数据库设计文档

## 📋 设计概述

基于ADATA接口文档和产品设计要求，重新设计数据库结构，支持9个核心层次的系统架构。

### 🎯 设计原则

1. **数据完整性**: 确保所有ADATA接口数据完整存储
2. **查询性能**: 优化索引设计，支持高频查询
3. **扩展性**: 支持未来功能扩展和数据增长
4. **一致性**: 保证数据一致性和事务完整性
5. **分区策略**: 按时间分区存储历史数据

### 🏗️ 数据库架构

#### 核心数据表分类

1. **基础数据表** - 股票基本信息、行业分类等
2. **行情数据表** - 多时间周期K线数据
3. **基本面数据表** - 财务数据、估值数据
4. **因子数据表** - 技术因子、基本面因子
5. **交易信号表** - 买卖信号、评分数据
6. **组合管理表** - 投资组合、持仓明细
7. **回测数据表** - 策略配置、回测结果
8. **系统管理表** - 配置信息、日志记录

## 📊 详细表结构设计

### 1. 基础数据表

#### 股票基本信息表 (stock_info)
```sql
CREATE TABLE stock_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(10) NOT NULL UNIQUE COMMENT '股票代码',
    name VARCHAR(50) NOT NULL COMMENT '股票名称',
    industry VARCHAR(50) COMMENT '所属行业',
    sector VARCHAR(50) COMMENT '所属板块',
    market VARCHAR(10) COMMENT '交易市场(SH/SZ)',
    list_date DATE COMMENT '上市日期',
    delist_date DATE COMMENT '退市日期',
    market_cap BIGINT COMMENT '总市值',
    total_shares BIGINT COMMENT '总股本',
    float_shares BIGINT COMMENT '流通股本',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_stock_info_symbol (symbol),
    INDEX idx_stock_info_industry (industry),
    INDEX idx_stock_info_market (market),
    INDEX idx_stock_info_active (is_active)
);
```

### 2. 多时间周期行情数据表

#### 1分钟K线数据表 (minute_1_market)
```sql
CREATE TABLE minute_1_market (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    trade_datetime TIMESTAMP NOT NULL COMMENT '交易时间',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    close_price DECIMAL(10,3) COMMENT '收盘价',
    volume BIGINT COMMENT '成交量',
    amount DECIMAL(20,2) COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_datetime (symbol, trade_datetime),
    INDEX idx_symbol_time (symbol, trade_datetime),
    INDEX idx_trade_datetime (trade_datetime)
) PARTITION BY RANGE (YEAR(trade_datetime)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 其他时间周期表 (minute_5_market, minute_15_market, hour_1_market, hour_4_market, daily_market)
```sql
-- 5分钟、15分钟、1小时、4小时、日线数据表结构相同，只是表名不同
-- 分区策略根据数据量调整
```

### 3. 基本面数据表

#### 财务数据表 (financial_data)
```sql
CREATE TABLE financial_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    report_date DATE NOT NULL COMMENT '报告期',
    report_type VARCHAR(10) NOT NULL COMMENT '报告类型(Q1/Q2/Q3/Q4)',
    
    -- 收入利润指标
    total_revenue DECIMAL(20,2) COMMENT '总营收',
    net_profit DECIMAL(20,2) COMMENT '净利润',
    gross_profit DECIMAL(20,2) COMMENT '毛利润',
    operating_profit DECIMAL(20,2) COMMENT '营业利润',
    
    -- 资产负债指标
    total_assets DECIMAL(20,2) COMMENT '总资产',
    total_liabilities DECIMAL(20,2) COMMENT '总负债',
    total_equity DECIMAL(20,2) COMMENT '股东权益',
    current_assets DECIMAL(20,2) COMMENT '流动资产',
    current_liabilities DECIMAL(20,2) COMMENT '流动负债',
    
    -- 财务比率
    roe DECIMAL(8,4) COMMENT '净资产收益率',
    roa DECIMAL(8,4) COMMENT '总资产收益率',
    gross_margin DECIMAL(8,4) COMMENT '毛利率',
    net_margin DECIMAL(8,4) COMMENT '净利率',
    debt_ratio DECIMAL(8,4) COMMENT '资产负债率',
    current_ratio DECIMAL(8,4) COMMENT '流动比率',
    
    -- 增长率指标
    revenue_growth DECIMAL(8,4) COMMENT '营收增长率',
    profit_growth DECIMAL(8,4) COMMENT '利润增长率',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_report (symbol, report_date, report_type),
    INDEX idx_symbol_date (symbol, report_date),
    INDEX idx_report_date (report_date)
);
```

### 4. 因子数据表

#### 因子配置表 (factor_config)
```sql
CREATE TABLE factor_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    factor_id VARCHAR(50) NOT NULL UNIQUE COMMENT '因子ID',
    factor_name VARCHAR(100) NOT NULL COMMENT '因子名称',
    factor_category ENUM('technical', 'fundamental', 'market') NOT NULL COMMENT '因子分类',
    factor_type VARCHAR(50) NOT NULL COMMENT '因子类型',
    weight DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '权重',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '因子描述',
    calculation_formula TEXT COMMENT '计算公式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_factor_category (factor_category),
    INDEX idx_factor_active (is_active)
);
```

#### 因子参数表 (factor_parameters)
```sql
CREATE TABLE factor_parameters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    factor_id VARCHAR(50) NOT NULL COMMENT '因子ID',
    param_name VARCHAR(50) NOT NULL COMMENT '参数名称',
    param_type ENUM('int', 'float', 'string', 'boolean') NOT NULL COMMENT '参数类型',
    param_value TEXT NOT NULL COMMENT '参数值',
    default_value TEXT COMMENT '默认值',
    min_value DECIMAL(20,6) COMMENT '最小值',
    max_value DECIMAL(20,6) COMMENT '最大值',
    description TEXT COMMENT '参数描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (factor_id) REFERENCES factor_config(factor_id) ON DELETE CASCADE,
    INDEX idx_factor_param (factor_id, param_name)
);
```

### 5. 交易信号表

#### 股票评分表 (stock_scores)
```sql
CREATE TABLE stock_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    calculation_date DATE NOT NULL COMMENT '计算日期',
    
    -- 分项评分
    technical_score DECIMAL(8,4) COMMENT '技术面评分',
    fundamental_score DECIMAL(8,4) COMMENT '基本面评分',
    market_score DECIMAL(8,4) COMMENT '市场表现评分',
    
    -- 综合评分
    total_score DECIMAL(8,4) NOT NULL COMMENT '综合评分',
    score_rank INTEGER COMMENT '评分排名',
    
    -- 详细因子评分
    factor_scores JSON COMMENT '详细因子评分',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_symbol_date (symbol, calculation_date),
    INDEX idx_symbol_score (symbol, total_score),
    INDEX idx_calculation_date (calculation_date),
    INDEX idx_total_score (total_score DESC)
);
```

#### 交易信号表 (trading_signals)
```sql
CREATE TABLE trading_signals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(10) NOT NULL COMMENT '股票代码',
    signal_time TIMESTAMP NOT NULL COMMENT '信号时间',
    signal_type ENUM('BUY', 'SELL', 'HOLD') NOT NULL COMMENT '信号类型',
    signal_strength ENUM('WEAK', 'MEDIUM', 'STRONG') NOT NULL COMMENT '信号强度',
    signal_score DECIMAL(8,4) NOT NULL COMMENT '信号评分',
    confidence DECIMAL(5,4) COMMENT '置信度',
    
    -- 价格信息
    trigger_price DECIMAL(10,3) COMMENT '触发价格',
    target_price DECIMAL(10,3) COMMENT '目标价格',
    stop_loss_price DECIMAL(10,3) COMMENT '止损价格',
    
    -- 信号详情
    signal_reasons JSON COMMENT '信号原因',
    technical_indicators JSON COMMENT '技术指标状态',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_symbol_time (symbol, signal_time),
    INDEX idx_signal_type (signal_type),
    INDEX idx_signal_time (signal_time),
    INDEX idx_signal_score (signal_score DESC)
);
```

### 6. 投资组合管理表

#### 投资组合表 (portfolios)
```sql
CREATE TABLE portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_name VARCHAR(100) NOT NULL COMMENT '组合名称',
    portfolio_type ENUM('QUANTITATIVE', 'MANUAL', 'HYBRID') NOT NULL COMMENT '组合类型',
    initial_capital DECIMAL(20,2) NOT NULL COMMENT '初始资金',
    current_value DECIMAL(20,2) COMMENT '当前市值',
    cash_balance DECIMAL(20,2) COMMENT '现金余额',
    
    -- 配置参数
    max_positions INTEGER DEFAULT 20 COMMENT '最大持仓数',
    max_position_weight DECIMAL(5,4) DEFAULT 0.1 COMMENT '单股最大权重',
    rebalance_frequency ENUM('DAILY', 'WEEKLY', 'MONTHLY') DEFAULT 'MONTHLY',
    
    -- 绩效指标
    total_return DECIMAL(8,4) COMMENT '总收益率',
    annual_return DECIMAL(8,4) COMMENT '年化收益率',
    max_drawdown DECIMAL(8,4) COMMENT '最大回撤',
    sharpe_ratio DECIMAL(8,4) COMMENT '夏普比率',
    
    status ENUM('ACTIVE', 'INACTIVE', 'CLOSED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_portfolio_type (portfolio_type),
    INDEX idx_portfolio_status (status)
);
```

### 7. VeighNa回测系统表

#### 回测策略表 (backtest_strategies)
```sql
CREATE TABLE backtest_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_name VARCHAR(100) NOT NULL COMMENT '策略名称',
    strategy_type VARCHAR(50) NOT NULL COMMENT '策略类型',
    description TEXT COMMENT '策略描述',
    
    -- 回测配置
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    initial_capital DECIMAL(20,2) NOT NULL COMMENT '初始资金',
    
    -- 策略参数
    strategy_params JSON COMMENT '策略参数',
    
    -- 风险控制参数
    max_position_size DECIMAL(5,4) COMMENT '最大仓位',
    stop_loss_ratio DECIMAL(5,4) COMMENT '止损比例',
    take_profit_ratio DECIMAL(5,4) COMMENT '止盈比例',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_strategy_type (strategy_type),
    INDEX idx_strategy_name (strategy_name)
);
```

### 8. 系统管理表

#### 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', 'JSON') DEFAULT 'STRING',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_config_active (is_active)
);
```

## 📈 索引优化策略

### 主要索引设计原则

1. **时间序列索引**: 所有时间相关表都建立时间索引
2. **复合索引**: 常用查询组合建立复合索引
3. **分区索引**: 大表采用分区索引提高性能
4. **覆盖索引**: 高频查询建立覆盖索引

### 分区策略

1. **行情数据**: 按年分区，支持历史数据查询
2. **交易信号**: 按月分区，保持查询性能
3. **回测结果**: 按策略类型分区

## 🔧 数据库初始化

### 创建数据库
```sql
CREATE DATABASE quantitative_trading_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 用户权限设置
```sql
CREATE USER 'quant_user'@'%' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON quantitative_trading_system.* TO 'quant_user'@'%';
FLUSH PRIVILEGES;
```

---

**设计版本**: V2.0  
**创建时间**: 2025-07-27  
**基于**: ADATA接口文档 + 产品设计要求
